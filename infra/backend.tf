terraform {
  required_version = ">= v1.10.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.12.0"
    }
    google-beta = {
      source  = "hashicorp/google-beta"
      version = "~> 6.12.0"
    }
  }

  backend "gcs" {
  }
}

provider "google" {
  project = var.gcp_project_id
  region  = var.gcp_region
}

provider "google-beta" {
  project = var.gcp_project_id
  region  = var.gcp_region
}

data "google_project" "project" {
}

data "google_client_config" "config" {}
