module "test_topic" {
  source        = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/pub_sub?ref=main"
  should_create = var.tests_topic_should_create

  topic_name = "${local.gitlab.project_name}-${var.env_name}-tests"
  labels     = local.basic_labels
}

module "alerting_error_topic" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/pub_sub?ref=main"

  topic_name     = "alerting-error"
  sa_subscribers = [module.alerting_sa.sa_email]
  labels         = local.basic_labels
}

module "alerting_warning_topic" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/pub_sub?ref=main"

  topic_name     = "alerting-warning"
  sa_subscribers = [module.alerting_sa.sa_email]
  labels         = local.basic_labels
}

module "bm_api_worker" {
  source         = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/pub_sub?ref=main"
  topic_name     = "backmarket-api-worker"
  sa_subscribers = [module.bm_api_worker_sa.sa_email]
  sa_publishers  = [module.bm_api_worker_sa.sa_email, module.bm_product_starter_sa.sa_email]
  labels         = local.basic_labels
}

resource "google_pubsub_subscription" "product_crawler_subscription" {
  name  = "product-crawler-subscription"
  topic = module.bm_api_worker.topic_name

  retain_acked_messages = false

  message_retention_duration   = "3900s" # 65 minutes
  enable_exactly_once_delivery = true
}
