locals {
  at_minute_29_every_hour_subscribers = merge(
    {
      "revenue-sync" = {
        job_name = module.revenue_sync_job.job_name
        sa_email = module.revenue_sync_sa.sa_email
      }
    },
    {
      "google-ads-etl" = {
        job_name = module.google_ads_etl_job.job_name
        sa_email = module.google_ads_etl_sa.sa_email
      }
    }
  )

  daily_at_9_30am_CEST_subscribers = merge(
    {
      "microsoft-ads-etl" = {
        job_name = module.microsoft_ads_etl_job.job_name
        sa_email = module.microsoft_ads_etl_sa.sa_email
      }
    }
  )

}


module "daily_every_8_hours_schedule" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"
  enabled = var.daily_every_8_hours_schedule_enabled

  schedule_name        = "daily-every-8-hours"
  cron                 = "0 */8 * * *"
  function_subscribers = [module.google_analytics_sa.sa_email]

  labels = local.basic_labels
}

# At minute 0 and 30 past every hour from 6 through 23.
module "every_30_minutes_schedule" {
  source  = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"
  enabled = var.every_30_minutes_schedule_enabled

  schedule_name        = "every-30-minutes"
  cron                 = "0,30 6-23 * * *"
  function_subscribers = [module.query_terminator_sa.sa_email]

  labels = local.basic_labels
}

# At 29 minute past every hour from 8 through 23.
module "at_minute_29_past_every_hour_schedule" {
  source   = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"
  for_each = local.at_minute_29_every_hour_subscribers

  schedule_name  = "at-minute-29-every-hour-${each.key}"
  cron           = "29 8-23 * * *"
  time_zone      = "Europe/Vienna"
  job_subscriber = each.value
  enabled        = local.is_production

  labels = local.basic_labels
}

module "daily_at_9_30am_CEST_schedule" {
  source   = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_scheduler?ref=main"
  for_each = local.daily_at_9_30am_CEST_subscribers

  schedule_name  = "daily-at-9_30am-CEST-${each.key}"
  cron           = "30 9 * * *"
  time_zone      = "Europe/Vienna"
  job_subscriber = each.value
  enabled        = local.is_production

  labels = local.basic_labels
}
