# Overview
- Here we define the sequence, parallelization and dependencies of the Data Engineering pipelines.
- We are using GCP Workflows for this purpose.
- Our targets currently are
  - HTTP Cloud Functions (gen2 or Cloud Revision)
  - Cloud Run Jobs

## Common workflow
- Contains reusable sub-workflows to be used by main workflows (like the `analytics.yaml` workflow).
- It's by default attached to any main workflow as mentioned in the definition of the terraform resource `infra > modules > workflow > main.tf`
  - can be deactivated on the module level by using the `include_common` `bool` variable `infra > modules > workflow > variables.tf`
- Provides standardized HTTP request handling with configurable body parameters

## Analytics workflow
- The main workflow
  - It initiates the `analytics_db_restore` which is of top criticality and dependency for everything else
  - Then it executes pipelines according to the definitions in the `analytics.yaml`
  - Furthermore, it triggers other workflows like the `unmanaged.yaml` workflow with configurable parameters

## Unmanaged workflow
- This workflow consists of the pipelines that are not in the shared ownership of the Data Engineering team (i.e. this monorepo)
- **New**: Supports conditional execution based on configuration parameters passed via JSON body
- **New**: Functions are organized into logical groups with clear dependencies and execution order

### Configuration Parameters
The workflow accepts the following boolean parameters to control execution:
- `run_parallel_idempotent`: Controls parallel idempotent functions
- `run_parallel_non_idempotent`: Controls parallel non-idempotent functions
- `run_srp`: Controls SRP sequential functions
- `run_alerts_merchant_interface`: Controls merchant interface alerts
- `run_circuit_breakers`: Controls circuit breaker functions
- `run_sales_quota`: Controls sales quota calculations

### Function Groups

#### 1. Parallel Non-Idempotent Functions
- [db_schema_monitoring](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/db_schema_monitoring) - Schema monitoring with Mattermost alerts
- [analytics-daily-update-gen2](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/internal-and-tests/analytics_daily_update_gen2) - Daily analytics updates
- [bi-yuutel-job](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/bi-pipelines/bi_yuutel_job) - BI Yuutel processing
- [gcp-cost-monitoring](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/gcp/gcp-cost) - GCP cost monitoring (daily + Tuesday weekly Mattermost updates)

#### 2. Parallel Idempotent Functions
- [merchant-daily-sales](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/sales-quota/merchant_daily_sales) - Sales quota ecosystem
- [update-direct-costs](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/direct-costs) - Direct costs updates
- [update-seo-data](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/update_seo_data) - SEO data updates
- [cs-update-rti-flows-reporting](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/customer-service1/update_rti_flows_reporting) - RTI flows reporting

#### 3. SRP Sequential Functions (All Idempotent)
- [order_items_pdd_bd](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/srp/order_items_pdd_bd)
- [create_rti_and_zendesk_tables](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/rti_zendesk_ticket_union/-/tree/dev?ref_type=heads)
- [order_items_pdd_update](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/order-items-ppd-update)
- [mps_per_country](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/mps_per_country)
- [supplier_performance_handicap_exports](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/supplier_performance_handicap_exports)

#### 4. Weekday Conditional Functions
- **SRP Circuit Breakers** (Tuesday only): (monorepo implemented) - Sends messages to suppliers
- **Sales Quota** (Thursday only): [merchant-sales-quota](https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/sales-quota/merchant_sales_quota) - Recalculates seller sales quotas and sends Mattermost messages

### Execution Flow
1. **SRP Sequential Functions** (if `run_srp` is true)
2. **SRP Circuit Breakers** (Tuesday only, if `run_circuit_breakers` is true)
3. **Merchant Interface Alerts** (if `run_alerts_merchant_interface` is true)
4. **Parallel Idempotent Functions** (if `run_parallel_idempotent` is true)
5. **Parallel Non-Idempotent Functions** (if `run_parallel_non_idempotent` is true)
6. **Sales Quota** (Thursday only, if `run_sales_quota` is true)

## Platform export

See [README](../../src/platform_export/README.md) for details.
