# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        assign:
          - result: {}
          - input_dates:
              start_date: ${default(map.get(input, "start_date"), "")}
              end_date: ${default(map.get(input, "end_date"), "")}
          - workflow_dates_function:
              id: "workflow-dates"
              url: ${sys.get_env("workflow_dates_url")}
              body: ${input_dates}
          - workflow_execution:
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              bq_cost_report_date: ${text.substring(time.format(sys.now()), 0, 10)} # today
              result: {}
              error: null
    - run_workflow:
        # Grouping step to catch ETL non retryable errors and alert to Mattermost channel
        try:
          steps:
            - get_workflow_dates:
                call: run_http_function
                args:
                  function: ${workflow_dates_function}
                result: get_workflow_dates_result
            - assign_get_workflow_dates_result:
                assign:
                  - workflow_dates: ${json.decode(get_workflow_dates_result.body)}
                  - result[workflow_dates_function.id]: ${workflow_dates}
            - run_ranker:
                for:
                  value: date
                  in: ${workflow_dates}
                  steps:
                    - assign_ranker_function:
                        assign:
                          - ranker_function:
                              id: ${"ranker for " + date}
                              url: ${sys.get_env("ranker_url")}
                              body:
                                load_date: ${date}
                    - run_ranker_function:
                        call: run_http_function
                        args:
                          function: ${ranker_function}
                        result: ranker_function_result
                    - assign_run_result:
                        assign:
                          - result[ranker_function.id]: ${json.decode(ranker_function_result.body)}
        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - send_error_notification:
                call: run_http_function
                args:
                  function:
                    id: "workflow-finalizer"
                    url: ${sys.get_env("workflow_finalizer_url")}
                    body: ${workflow_execution}
                result: send_error_notification_result
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - send_success_notification:
        call: run_http_function
        args:
          function:
            id: "workflow-finalizer"
            url: ${sys.get_env("workflow_finalizer_url")}
            body: ${workflow_execution}
        result: send_success_notification_result
    - done:
        return: ${result}
