# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        assign:
          - result: {}
          - workflow_execution:
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              result: {}
              error: null
          - should_run_parallel_idempotent: ${default(map.get(input, "run_parallel_idempotent"), false)}
          - should_run_parallel_non_idempotent: ${default(map.get(input, "run_parallel_non_idempotent"), false)}
          - should_run_srp: ${default(map.get(input, "run_srp"), false)}
          - should_run_alerts_merchant_interface: ${default(map.get(input, "run_alerts_merchant_interface"), false)}
          - should_run_circuit_breakers: ${default(map.get(input, "run_circuit_breakers"), false)}
          - should_run_sales_quota: ${default(map.get(input, "run_sales_quota"), false)}
          - srp_sequential_functions:
              # All Idempotent
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/order-items-pdd-bd/
                  id: "order_items_pdd_bd"
                  url: ${sys.get_env("cloud_functions_url") + "order_items_pdd_bd"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/create-rti-and-zendesk-tables/
                  id: "create_rti_and_zendesk_tables"
                  url: ${sys.get_env("cloud_functions_url") + "create_rti_and_zendesk_tables"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/order-items-pdd-update/
                  id: "order_items_pdd_update"
                  url: ${sys.get_env("cloud_functions_url") + "order_items_pdd_update"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/mps-per-country/
                  id: "mps_per_country"
                  url: ${sys.get_env("cloud_functions_url") + "mps_per_country"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent
                  # https://console.cloud.google.com/run/detail/europe-west3/supplier-performance-handicap-exports/
                  id: "supplier_performance_handicap_exports"
                  url: ${sys.get_env("cloud_functions_url") + "supplier_performance_handicap_exports"}
                  dry_run: ${sys.get_env("dry_run")}
          - srp_circuit_breakers_tuesday_function:
              - function:
                  # If Tuesday, it will send messages to Suppliers! (monorepo)
                  id: "srp-circuit-breakers"
                  url: ${sys.get_env("srp_circuit_breakers_url")}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
          - srp_alerts_to_merchant_interface_function:
              - function:
                  # Sends messages to merchant interface!
                  # https://console.cloud.google.com/run/detail/europe-west3/send-alert-to-merchant-interface/
                  id: "send_alert_to_merchant_interface"
                  url: ${sys.get_env("cloud_functions_url") + "send_alert_to_merchant_interface"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
          - parallel_idempotent_functions:
              - function:
                  # Idempotent (sales quota ecosystem)
                  # https://console.cloud.google.com/run/detail/europe-west3/merchant-daily-sales/
                  id: "merchant_daily_sales"
                  url: ${sys.get_env("cloud_functions_url") + "merchant_daily_sales"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/update-direct-costs/
                  id: "update_direct_costs"
                  url: ${sys.get_env("cloud_functions_url") + "update_direct_costs"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/update-seo-data/
                  id: "update_seo_data"
                  url: ${sys.get_env("cloud_functions_url") + "update_seo_data"}
                  dry_run: ${sys.get_env("dry_run")}
              - function:
                  # Idempotent - https://console.cloud.google.com/run/detail/europe-west3/cs-update-rti-flows-reporting/
                  id: "cs_update_rti_flows_reporting"
                  url: ${sys.get_env("cloud_functions_url") + "cs_update_rti_flows_reporting"}
                  dry_run: ${sys.get_env("dry_run")}
          - parallel_non_idempotent_functions:
              - function:
                  # Will send message to mattermost channel if difference in schema
                  # https://console.cloud.google.com/run/detail/europe-west3/db-schema-monitoring/
                  id: "db_schema_monitoring"
                  url: ${sys.get_env("cloud_functions_url") + "db_schema_monitoring"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  # Will send message to mattermost channels
                  # https://console.cloud.google.com/run/detail/europe-west3/analytics-daily-update-gen2/
                  id: "analytics_daily_update_gen2"
                  url: ${sys.get_env("cloud_functions_url") + "analytics_daily_update_gen2"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  # Appending! - https://console.cloud.google.com/run/detail/europe-west3/bi-yuutel-job/
                  id: "bi_yuutel_job"
                  url: ${sys.get_env("cloud_functions_url") + "bi_yuutel_job"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
              - function:
                  # Idempotent, will send mattermost messages (daily and if Tuesday weekly ones)
                  # https://console.cloud.google.com/run/detail/europe-west3/gcp-cost-monitoring/
                  id: "gcp_cost_monitoring"
                  url: ${sys.get_env("cloud_functions_url") + "gcp_cost_monitoring"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
          - sales_quota_thursday_function:
              - function:
                  # It will recalculate the Seller Sales Quotas and send message to Mattermost
                  # https://console.cloud.google.com/run/detail/europe-west3/merchant-sales-quota/
                  id: "merchant_sales_quota"
                  url: ${sys.get_env("cloud_functions_url") + "merchant_sales_quota"}
                  dry_run: ${sys.get_env("dry_run")}
                  retries_num: 0
    - run_unmanaged_workflow:
    # Grouping step to catch ETL non-retryable errors and alert to a Mattermost channel
        try:
          steps:
            - get_current_time:
                call: run_http_function
                args:
                  function:
                    id: "current-time"
                    url: ${sys.get_env("get_current_time_url")}
                    body: {}
                result: current_time
            - run_srp_sequential:
                # 1. Run SRP flow first -- Idempotent
                switch:
                  - condition: ${should_run_srp}
                    steps:
                      - run_srp_sequential_functions:
                          call: run_sequential_functions
                          args:
                            sequential_functions: ${srp_sequential_functions}
                          result: srp_sequential_functions_result
                      - assign_srp_sequential_functions_result:
                          assign:
                            - result["run-srp-sequential-functions"]: ${srp_sequential_functions_result}
            - run_srp_circuit_breakers_tuesday:
                # 2a. Run SRP circuit breakers only on Tuesday (dependency on MPS per country function)
                switch:
                  - condition: ${current_time.body.day_of_week == "Tuesday" and should_run_circuit_breakers}
                    steps:
                      - run_srp_circuit_breakers_tuesday_function:
                          call: run_sequential_functions
                          args:
                            sequential_functions: ${srp_circuit_breakers_tuesday_function}
                          result: srp_circuit_breakers_tuesday_function_result
                      - assign_srp_circuit_breakers_tuesday_function_result:
                          assign:
                            - result["run-srp-circuit-breaker-tuesday-function"]: ${srp_circuit_breakers_tuesday_function_result}
            - run_srp_alerts_to_merchant_interface:
                # 2b. Run SRP alerts to merchant interface
                switch:
                  - condition: ${should_run_alerts_merchant_interface}
                    steps:
                      - run_srp_alerts_to_merchant_interface_function:
                          call: run_sequential_functions
                          args:
                            sequential_functions: ${srp_alerts_to_merchant_interface_function}
                          result: srp_alerts_to_merchant_interface_function_result
                      - assign_srp_alerts_to_merchant_interface_function_result:
                          assign:
                            - result["run-srp-alerts-to-merchant-interface"]: ${srp_alerts_to_merchant_interface_function_result}
            - run_parallel_idempotent:
                # 3. Run parallel idempotent functions
                switch:
                  - condition: ${should_run_parallel_idempotent}
                    steps:
                      - run_parallel_idempotent_functions:
                          call: run_parallel_functions
                          args:
                            parallel_functions: ${parallel_idempotent_functions}
                          result: parallel_idempotent_functions_result
                      - assign_parallel_idempotent_functions_result:
                          assign:
                            - result["run-parallel-idempotent-functions"]: ${parallel_idempotent_functions_result}
            - run_parallel_non_idempotent:
                # 4. Run parallel non idempotent functions
                switch:
                  - condition: ${should_run_parallel_non_idempotent}
                    steps:
                      - run_parallel_non_idempotent_functions:
                          call: run_parallel_functions
                          args:
                            parallel_functions: ${parallel_non_idempotent_functions}
                          result: parallel_non_idempotent_functions_result
                      - assign_parallel_non_idempotent_functions_result:
                          assign:
                            - result["run-parallel-non-idempotent-functions"]: ${parallel_non_idempotent_functions_result}
            - run_sales_quota_thursday:
                # 5. Run sales quota only on Thursday (dependency on merchant daily sales from parallel functions)
                switch:
                  - condition: ${current_time.body.day_of_week == "Thursday" and should_run_sales_quota}
                    steps:
                      - run_sales_quota_thursday_function:
                          call: run_sequential_functions
                          args:
                            sequential_functions: ${sales_quota_thursday_function}
                          result: sales_quota_thursday_function_result
                      - assign_sales_quota_thursday_function_result:
                          assign:
                            - result["run-sales-quota-thursday-function"]: ${sales_quota_thursday_function_result}

        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - send_error_notification:
                call: run_http_function
                args:
                  function:
                    id: ${sys.get_env("workflow_finalizer_name")}
                    url: ${sys.get_env("workflow_finalizer_url")}
                    body: ${workflow_execution}
                result: send_error_notification_result
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - send_success_notification:
        call: run_http_function
        args:
          function:
            id: ${sys.get_env("workflow_finalizer_name")}
            url: ${sys.get_env("workflow_finalizer_url")}
            body: ${workflow_execution}
        result: send_success_notification_result
    - return_result:
        return: ${result}
