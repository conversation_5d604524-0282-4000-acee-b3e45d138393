# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        assign:
          - threads: ${int(default(map.get(input, "threads"), 10))}
          - result: {}
          - workflow_execution:
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              result: {}
              error: null
    - run_product_crawler:
        try:
          steps:
            - run_starter:
                steps:
                  - run_starter_function:
                      call: run_http_function
                      args:
                        function:
                          id: "bm_product_starter"
                          url: ${sys.get_env("bm_product_starter_url")}
                          body: {}
                      result: bm_product_starter_result
                  - map_run_starter_function_result:
                      assign:
                        - starter_results: ${bm_product_starter_result.body}
                        - result["bm_product_starter"]: ${starter_results}
            - run_workers:
                parallel:
                  shared: [result]
                  for:
                    value: thread_id
                    range: ${[1,threads]}
                    steps:
                      - log_worker_batch:
                          call: sys.log
                          args:
                            text: ${"Product crawler worker batch - thread:" + thread_id +  " started"}
                            severity: INFO
                      - run_worker_job:
                          call: run_cloud_run_job
                          args:
                            job:
                              name: ${sys.get_env("bm_api_worker_job_name")}
                              retries_num: 0
                          result: bm_api_worker_job_result
                      - map_run_worker_job_result:
                          assign:
                            - result["backmarket_worker_job_" + thread_id]: ${bm_api_worker_job_result}
            - run_compaction:
                steps:
                  - assign_etl_job:
                      assign:
                        - etl_job:
                            name: ${sys.get_env("bm_compaction_job_name")}
                            env:
                              - name: "COMPACTION_DATE"
                                value: ${string(starter_results.run_date)}
                  - run_etl_job:
                      call: run_cloud_run_job
                      args:
                        job: ${etl_job}
                      result: etl_job_result
                  - assign_etl_result:
                      assign:
                        - result["bm_product_compaction"]: ${etl_job_result}

        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - send_error_notification:
                call: run_http_function
                args:
                  function:
                    id: "workflow-finalizer"
                    url: ${sys.get_env("workflow_finalizer_url")}
                    body: ${workflow_execution}
                result: send_error_notification_result
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - done:
        return: ${result}
