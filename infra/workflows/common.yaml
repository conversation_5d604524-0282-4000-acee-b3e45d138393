# RE-USABLE SUB-WORKFLOWS
# https://cloud.google.com/workflows/docs/reference/syntax/subworkflows#calling-subworkflow
run_http_function:
  params: [function]
  steps:
    - defaults:
        assign:
          - default_body: {}
          - default_retries: 5
          - default_dry_run: "false"
    - log_start:
        call: sys.log
        args:
          data: ${"Running -> " + function.id}
          severity: "INFO"
    - condition_dry_run:
      # https://cloud.google.com/workflows/docs/reference/syntax/conditions#conditional-steps
        switch:
          - condition: ${default(map.get(function, "dry_run"), default_dry_run) == default_dry_run}
            next: run_function
        next: dry_run_function
    - dry_run_function:
        assign:
          - run_function_result:
              code: 200
              body: ${"Dry run -> " + function.dry_run}
        next: log_finish
    - run_function:
        try:
          # https://cloud.google.com/workflows/docs/reference/stdlib/http/post
          call: http.post
          args:
            url: ${function.url}
            timeout: 1800 # 30 minutes in seconds
            body: ${default(map.get(function, "body"), default_body)}
            auth:
              type: OIDC
              audience: ${function.url}
          result: run_function_result
        retry:
          predicate: ${server_error_predicate}
          max_retries: ${default(map.get(function, "retries_num"), default_retries)}
          backoff:
            # Retries: 10, 30, 90 seconds
            initial_delay: 10
            max_delay: 300
            multiplier: 3
    - log_finish:
        call: sys.log
        args:
          data: ${"Finished -> " + function.id}
          severity: "INFO"
    - run_http_function_return:
        return: ${run_function_result}


run_cloud_run_job:
  params: [job]
  steps:
    - defaults:
        assign:
          - default_retries: 3
          - job.id: ${default(map.get(job, "id"), job.name)}
          - job.env: ${default(map.get(job, "env"), [])}
    - log_start:
        call: sys.log
        args:
          data: ${"Running " + json.encode_to_string(job)}
          severity: "INFO"
    - run_job:
        try:
          # https://cloud.google.com/workflows/docs/reference/googleapis/run/v1/namespaces.jobs/run
          call: googleapis.run.v1.namespaces.jobs.run
          args:
            name: ${job.name}
            location: ${sys.get_env("GOOGLE_CLOUD_LOCATION")}
            body:
              overrides:
                containerOverrides:
                  env: ${job.env}
            connector_params:
              timeout: 28800 # 8h in seconds
          result: run_job_result
        retry:
          predicate: ${server_error_predicate}
          max_retries: ${default(map.get(job, "retries_num"), default_retries)}
          backoff:
            # Retries: 10, 30, 90 seconds
            initial_delay: 10
            max_delay: 300
            multiplier: 3
    - log_finish:
        call: sys.log
        args:
          data: ${"Finished job -> " + job.id}
          severity: "INFO"
    - assign_result:
        assign:
          - result_status: ${run_job_result.status}
          - result_completion: ${map.delete(result_status, "conditions")}
    - run_job_return:
        return: ${result_completion}


run_parallel_functions:
  params: [parallel_functions]
  # Run in parallel
  # https://cloud.google.com/workflows/docs/execute-parallel-steps#aggregate_data_using_a_parallel_loop
  steps:
    - init_result:
        assign:
          - result: {}
    - parallel_execution:
        parallel:
          shared: [result]
          for:
            value: parallel_function
            in: ${parallel_functions}
            steps:
              - assign_function:
                  assign:
                    - function: ${parallel_function.function}
                    - result[function.id]: "Running..."
              - run_function:
                  call: run_http_function
                  args:
                    function: ${function}
                  result: run_function_result
              - map_run_function_result:
                  assign:
                    - result[function.id]: ${run_function_result.body}
    - return_result:
        return: ${result}


run_sequential_functions:
  params: [sequential_functions]
  steps:
    - init_result:
        assign:
          - result: {}
    - sequential_execution:
        for:
          value: sequential_function
          in: ${sequential_functions}
          steps:
            - assign_function:
                assign:
                  - function: ${sequential_function.function}
                  - result[function.id]: "Running..."
            - run_function:
                call: run_http_function
                args:
                  function: ${function}
                result: run_function_result
            - map_run_function_result:
                assign:
                  - result[function.id]: ${run_function_result.body}
    - return_result:
        return: ${result}


run_parallel_jobs:
  params: [parallel_jobs]
  # Run in parallel
  # https://cloud.google.com/workflows/docs/execute-parallel-steps#aggregate_data_using_a_parallel_loop
  steps:
    - init_result:
        assign:
          - result: {}
    - parallel_execution:
        parallel:
          shared: [result]
          for:
            value: parallel_job
            in: ${parallel_jobs}
            steps:
              - assign_job:
                  assign:
                    - job: ${parallel_job.job}
                    - result[job.id]: "Running..."
              - run_job:
                  call: run_cloud_run_job
                  args:
                    job: ${job}
                  result: run_job_result
              - map_run_job_result:
                  assign:
                    - result[job.id]: ${run_job_result}
    - return_result:
        return: ${result}


run_sequential_jobs:
  params: [sequential_jobs]
  steps:
    - init_result:
        assign:
          - result: {}
    - sequential_execution:
        for:
          value: sequential_job
          in: ${sequential_jobs}
          steps:
            - assign_job:
                assign:
                  - job: ${sequential_job.job}
                  - result[job.id]: "Running..."
            - run_job:
                call: run_cloud_run_job
                args:
                  job: ${job}
                result: run_job_result
            - map_run_job_result:
                assign:
                  - result[job.id]: ${run_job_result}
    - return_result:
        return: ${result}

run_workflow_async:
  params: [workflow]
  steps:
    - init_result:
        assign:
          - workflow_result:
              url: ${workflow.landing_page_url}
              result: "Not run"
    - log_workflow:
        call: sys.log
        args:
          data: ${"Should run " + workflow.id + " -> " + workflow.should_run}
          severity: "INFO"
    - condition_run_workflow:
        switch:
          - condition: ${workflow.should_run}
            steps:
              - execute_workflow:
                  call: http.post
                  args:
                    url: ${workflow.http_post_url}
                    auth:
                      type: OAuth2
                      scopes: ["https://www.googleapis.com/auth/cloud-platform"]
                    body:
                      argument: ${default(map.get(workflow, "body"), "")}
                  result: execute_workflow_result
              - assign_result:
                  assign:
                    - workflow_result.result:
                        code: ${execute_workflow_result.code}
    - return_result:
        return: ${workflow_result}

# https://cloud.google.com/workflows/docs/reference/syntax/retrying#sample-predicate
server_error_predicate:
  params: [e]
  steps:
    - init_error:
        assign:
          - error_code: ${default(map.get(e, "code"), 0)}
          - error_message: ${default(map.get(e, "body"), e)}
    - log_error:
        call: sys.log
        args:
          data: ${error_message}
          severity: "ERROR"
    - what_to_repeat:
        switch:
          # Retry exceptions from used connectors: http.post and job.run
          - condition: ${"ConnectionError" in e.tags}
            return: true
          - condition: ${"ConnectionFailedError" in e.tags}
            return: true
          - condition: ${"TimeoutError" in e.tags}
            return: true
          - condition: ${"OperationError" in e.tags}
            return: true
          # Retry HTTP status codes for service/network/gateway errors
          - condition: ${error_code in [429, 500, 502, 503, 504]}
            return: true
    - otherwise:
        return: false
