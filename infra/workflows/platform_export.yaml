# $schema: https://json.schemastore.org/workflows.json
main:
  params: [input]
  steps:
    - init:
        assign:
          - result: {}
          - export_concurrency: ${int(default(map.get(input, "export_concurrency"), 20))}
          - workflow_execution:
              execution_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID")}
              workflow_id: ${sys.get_env("GOOGLE_CLOUD_WORKFLOW_ID")}
              start: ${sys.now()}
              end: ${sys.now()}
              result: {}
              error: null
              pipeline_logging: true
          - starter_function:
              id: "workflow-starter"
              url: ${sys.get_env("workflow_starter_url")}
              body: ${workflow_execution}
          - ranker_workflow:
              # Ranker workflow should run by default
              id: "ranker-workflow"
              should_run: ${default(map.get(input, "run_ranker"), true)}
              landing_page_url: ${sys.get_env("ranker_landing_page_url")}
              http_post_url: ${sys.get_env("ranker_http_post_url")}
    - run_workflow:
        # Grouping step to catch ETL non-retryable errors and alert to a Mattermost channel
        try:
          steps:
            - run_starter_function:
                call: run_http_function
                args:
                  function: ${starter_function}
                result: starter_function_result
            - assign_starter_result:
                assign:
                  - pipeline_run: ${starter_function_result.body}
                  - result[starter_function.id]: ${pipeline_run}
                  - dispatcher_function:
                      id: "platform-export-dispatcher"
                      url: ${sys.get_env("platform_export_dispatcher_url")}
                      body: ${pipeline_run}
            - run_dispatcher_function:
                call: run_http_function
                args:
                  function: ${dispatcher_function}
                result: dispatcher_function_result
            - assign_dispatcher_result:
                assign:
                  - export_configs: ${dispatcher_function_result.body}
                  - result[dispatcher_function.id]: ${"Dispatched " + string(len(export_configs)) + " sources."}
            - run_in_parallel:
                parallel:
                  shared: [result]
                  concurrency_limit: ${export_concurrency}
                  for:
                    value: export_config
                    in: ${export_configs}
                    steps:
                      - assign_etl_job:
                          assign:
                            - schema_name: ${export_config.source.schema_name}
                            - table_name: ${export_config.source.table_name}
                            - result[table_name]: ${"Extracting " + table_name + " ..."}
                            - etl_job:
                                id: ${table_name}
                                name: ${sys.get_env("platform_export_job_full_name")}
                                env:
                                  - name: "EXPORT_TIMESTAMP"
                                    value: ${string(export_config.timestamp)}
                                  - name: "EXPORT_SOURCE"
                                    value: ${schema_name + "." + table_name}
                                  - name: "PIPELINE_NAME"
                                    value: ${pipeline_run.pipeline_name}
                                  - name: "RUN_ID"
                                    value: ${pipeline_run.run_id}
                      - run_etl_job:
                          call: run_cloud_run_job
                          args:
                            job: ${etl_job}
                          result: etl_job_result
                      - assign_etl_result:
                          assign:
                            - result[table_name]: ${etl_job_result}
            - run_ranker:
                steps:
                  - run_ranker_async:
                      call: run_workflow_async
                      args:
                        workflow: ${ranker_workflow}
                      result: ranker_workflow_result
                  - assign_ranker_result:
                      assign:
                        - result[ranker_workflow.id]: ${ranker_workflow_result}
        except:
          as: e
          steps:
            - assign_workflow_error:
                assign:
                  - workflow_execution.result: ${result}
                  - workflow_execution.error: ${e}
                  - workflow_execution.end: ${sys.now()}
            - send_error_notification:
                call: run_http_function
                args:
                  function:
                    id: "workflow-finalizer"
                    url: ${sys.get_env("workflow_finalizer_url")}
                    body: ${workflow_execution}
                result: send_error_notification_result
            - raiseError:
                raise: ${workflow_execution}
    - assign_workflow_result:
        assign:
          - workflow_execution.result: ${result}
          - workflow_execution.end: ${sys.now()}
    - send_success_notification:
        call: run_http_function
        args:
          function:
            id: "workflow-finalizer"
            url: ${sys.get_env("workflow_finalizer_url")}
            body: ${workflow_execution}
        result: send_success_notification_result
    - done:
        return: ${result}
