locals {
  storage_reader_and_writer_roles = [
    google_project_iam_custom_role.storage_reader.name, google_project_iam_custom_role.storage_writer.name
  ]
  looker_user_sa_email = "<EMAIL>"
}

module "artifact_bucket" {
  source          = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main"
  bucket_location = var.gcp_region
  bucket_name     = "${local.gitlab.project_name}-${var.env_name}-artifacts"

  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        age        = 30 # days
        with_state = "ANY"
      }
    }
  ]

  labels = local.basic_labels
}

module "test_bucket" {
  source          = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main"
  bucket_location = var.gcp_region
  bucket_name     = "${local.gitlab.project_name}-${var.env_name}-tests"
  should_create   = var.tests_bucket_should_create
  force_destroy   = local.is_staging

  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        age        = 3 # days
        with_state = "ANY"
      }
    }
  ]

  labels = local.basic_labels
}

module "raw_bucket" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main"

  bucket_location = var.gcp_multi_region
  bucket_name     = "${local.gitlab.project_name}-${var.env_name}-raw"

  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        age        = 90 # days
        with_state = "ANY"
      }
    }
  ]

  permissions = [
    {
      sa_email = module.dhl_shipping_sa.sa_email
      roles = [
        google_project_iam_custom_role.storage_writer.name
      ]
    },
    {
      sa_email = module.platform_export_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.adtriba_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.pipedrive_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.nrm_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.bm_api_worker_sa.sa_email
      roles = [
        google_project_iam_custom_role.storage_writer.name
      ]
    },
    {
      sa_email = module.category_slug_mapper_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.google_ads_etl_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.migrate_bq_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.microsoft_ads_etl_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
  ]
  force_destroy = local.is_staging

  labels = local.basic_labels
}

module "transformed_bucket" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main"

  bucket_location = var.gcp_multi_region
  bucket_name     = "${local.gitlab.project_name}-${var.env_name}-transformed"
  permissions = [
    {
      sa_email = module.dhl_shipping_sa.sa_email
      roles = [
        google_project_iam_custom_role.storage_writer.name
      ]
    },
    {
      sa_email = module.nrm_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = module.bm_api_worker_sa.sa_email
      roles = [
        google_project_iam_custom_role.storage_writer.name
      ]
    },
    {
      sa_email = module.bm_compaction_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
    {
      sa_email = local.looker_user_sa_email
      roles = [
        google_project_iam_custom_role.storage_reader.name
      ]
    },
    {
      sa_email = module.migrate_bq_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    },
  ]
  force_destroy = local.is_staging

  labels = local.basic_labels
}

# Bucket for storing database backups (like platform db)
module "backup_bucket" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main"

  bucket_location = var.gcp_multi_region
  bucket_name     = "${local.gitlab.project_name}-${var.env_name}-backup"
  force_destroy   = local.is_staging
  permissions = [
    {
      sa_email = module.analytics_db_refresh_sa.sa_email
      roles    = local.storage_reader_and_writer_roles
    }
  ]

  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        age        = 14 # days
        with_state = "ANY"
        # Ref. https://cloud.google.com/storage/docs/lifecycle#matchesprefix-suffix,
        #      https://stackoverflow.com/a/76825642/27543394
        matches_prefix = ["dumps/"]
      }
    }
  ]


  labels = local.basic_labels
}
