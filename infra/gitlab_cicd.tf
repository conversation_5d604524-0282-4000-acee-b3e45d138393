# References:
# https://about.gitlab.com/blog/2023/06/28/introduction-of-oidc-modules-for-integration-between-google-cloud-and-gitlab-ci/
# https://gitlab.com/gitlab-com/gl-security/security-operations/infrastructure-security-public/oidc-modules/-/blob/main/samples/terraform/oidc-gcp/main.tf

module "gitlab_oidc" {
  source                 = "gitlab.com/gitlab-com/gcp-oidc/google"
  version                = "3.2.1"
  google_project_id      = var.gcp_project_id
  workload_identity_name = "env-${var.env_name}"
  gitlab_project_id      = local.gitlab.project_id
  oidc_service_account = {
    "sa" = {
      sa_email  = module.cicd_gitlab.sa_email
      attribute = "attribute.project_id/${local.gitlab.project_id}"
    }
  }
}
