# analytics-pipelines infrastructure

Infrastructure is managed by [Terraform](https://developer.hashicorp.com/).

The code is split into directories for each environment with its own configuration.
The root `infra` directory is a place for GCP resources definition.
There is one definition only for the resource yet parametrized for each env, just to keep the configuration [DRY](https://en.wikipedia.org/wiki/Don%27t_repeat_yourself).

## Installation
- macOS and Linux via [Homebrew](https://brew.sh/):
```bash
brew tap hashicorp/tap
brew install hashicorp/tap/terraform
```
- Windows - follow this [link](https://brew.sh/)

## Development

### Code formatting
Run `terraform fmt -recursive` command in a root infra directory.
If you are using PyCharm, you can install `File Watchers` plugin and configure it as follows to run on a file save.

![terraform_watcher.png](../resources/misc/terraform_watcher.png)

## Module usage
We use dedicated [infrastructure-modules](https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules) repository with our home-grown Terraform modules.
Here is an example on how to use the `storage_bucket` module located in the `src` directory and the `main` branch:

```
module "my_bucket" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/storage_bucket?ref=main"

  ...args go here as usual...
}
```

### GitLab HTTPS resource access
During the first time when you run any terraform command with any module from  [infrastructure-modules](https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules)
Git will ask you for a username and password for the GitLab repository URL.
You can use any string for the username, but the password must be a GitLab personal access token.
You can store this token in a variable called `GITLAB_ACCESS_TOKEN`.
If you're using Windows or Mac, after first run, your Git credentials will be stored in a credential manager, so you do not have to repeat that step again.
#### For Linux users only
If you are Linux user and do not have any credential manager configured on your system, you can use following code snippet to set up `.git-credentials` file.
```
git config --global credential.helper store
echo "https://gitlab-ci-token:${GITLAB_ACCESS_TOKEN}@gitlab.com" >> ~/.git-credentials
```
