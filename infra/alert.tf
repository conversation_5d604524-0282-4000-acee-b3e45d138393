module "cloud_run_job_error_alert" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/alert?ref=main"

  resource_type = "cloud_run_job"
  severity      = "ERROR"

  topic_ids = [module.alerting_error_topic.topic_id]
}

module "cloud_run_job_warning_alert" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/alert?ref=main"

  resource_type = "cloud_run_job"
  severity      = "WARNING"

  topic_ids = [module.alerting_warning_topic.topic_id]
}

module "cloud_function_gen2_error_alert" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/alert?ref=main"

  resource_type = "cloud_run_revision"
  severity      = "ERROR"

  topic_ids = [module.alerting_error_topic.topic_id]
}

module "cloud_function_gen2_warning_alert" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/alert?ref=main"

  resource_type = "cloud_run_revision"
  severity      = "WARNING"

  topic_ids = [module.alerting_warning_topic.topic_id]
}
