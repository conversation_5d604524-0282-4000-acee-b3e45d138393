locals {
  # cloud run function consts: https://cloud.google.com/functions/docs/configuring/memory
  small_function = {
    memory  = "256Mi"
    cpu     = ".167"
    timeout = "300s" # 5 minutes
  }

  medium_function = {
    memory  = "512Mi"
    cpu     = ".333"
    timeout = "600s" # 10 minutes
  }

  big_function = {
    memory  = "2Gi"
    cpu     = "1"
    timeout = "1800s" # 30 minutes = max timeout for workflow http post
  }
}

module "alerting_error_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "alerting-error"
  function_root_path    = "../src/alerting"
  service_account_email = module.alerting_sa.sa_email

  container_memory = local.small_function.memory
  container_cpu    = local.small_function.cpu
  env_variables    = local.runtime_env_variables

  pubsub_trigger = {
    topic_name = module.alerting_error_topic.topic_id
  }

  labels = local.p2_labels
}

module "alerting_warning_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "alerting-warning"
  function_root_path    = "../src/alerting"
  service_account_email = module.alerting_sa.sa_email

  container_memory = local.small_function.memory
  container_cpu    = local.small_function.cpu
  function_timeout = local.small_function.timeout

  env_variables = local.runtime_env_variables

  pubsub_trigger = {
    topic_name = module.alerting_warning_topic.topic_id
  }

  labels = local.p2_labels
}

module "alerting_check_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "alerting-function-check"
  function_root_path    = "../src/alerting_check"
  service_account_email = module.alerting_sa.sa_email

  container_memory = local.small_function.memory
  container_cpu    = local.small_function.cpu
  function_timeout = local.small_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "dhl_shipping_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "dhl-etl-shipping"
  function_root_path    = "../src/dhl/dhl_shipping"
  function_shared_path  = "../src/dhl/dhl_shared"
  service_account_email = module.dhl_shipping_sa.sa_email

  container_memory = local.big_function.memory
  container_cpu    = local.big_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = merge(local.runtime_env_variables, {
    REMOVE_PROCESSED_FILES = var.dhl_remove_processed_files
  })

  labels = local.p2_labels
}

module "google_analytics_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "analytics-google-etl"
  function_root_path    = "../src/google_analytics"
  service_account_email = module.google_analytics_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.medium_function.timeout

  env_variables = local.runtime_env_variables

  pubsub_trigger = {
    topic_name = module.daily_every_8_hours_schedule.schedule_topic_id
  }

  labels = local.p2_labels
}

module "platform_export_dispatcher_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "platform-export-dispatcher-function"
  function_root_path    = "../src/platform_export/dispatcher"
  function_shared_path  = "../src/platform_export/export_shared"
  service_account_email = module.platform_export_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "adtriba_sphere_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "adtriba-etl-sphere"
  function_root_path    = "../src/adtriba/sphere"
  function_shared_path  = "../src/adtriba/adtriba_shared"
  service_account_email = module.adtriba_sa.sa_email

  container_memory = local.big_function.memory
  container_cpu    = local.big_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = merge(local.runtime_env_variables, {
    AWS_CREDENTIALS_SECRET = module.adtriba_aws_secret.secret_id
  })

  labels = local.p2_labels
}

module "adtriba_tcm_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "adtriba-tcm-etl"
  function_root_path    = "../src/adtriba/true_consent_mode"
  function_shared_path  = "../src/adtriba/adtriba_shared"
  service_account_email = module.adtriba_sa.sa_email

  container_memory = local.big_function.memory
  container_cpu    = local.big_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = merge(local.runtime_env_variables, {
    AWS_CREDENTIALS_SECRET = module.adtriba_aws_secret.secret_id,
    EXPORT_ENABLED         = local.is_production
  })

  labels = local.p2_labels
}

module "zendesk_auto_responder_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "zendesk-auto-responder"
  function_root_path    = "../src/zendesk_auto_responder"
  service_account_email = module.zendesk_auto_responder_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.small_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "nrm_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "nrm-forecast-etl"
  function_root_path    = "../src/nrm"
  service_account_email = module.nrm_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "bm_product_starter_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "backmarket-product-starter"
  function_root_path    = "../src/backmarket/product_starter"
  function_shared_path  = "../src/backmarket/bm_shared"
  service_account_email = module.bm_product_starter_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "query_terminator_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "query-terminator"
  function_root_path    = "../src/query_terminator"
  service_account_email = module.query_terminator_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.small_function.timeout

  env_variables = local.runtime_env_variables

  pubsub_trigger = {
    topic_name = module.every_30_minutes_schedule.schedule_topic_id
  }

  labels = local.p2_labels
}

module "proc_executor_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "proc-executor"
  function_root_path    = "../src/proc_executor"
  service_account_email = module.proc_executor_sa.sa_email

  max_instance_count = 10
  container_memory   = local.medium_function.memory
  container_cpu      = local.medium_function.cpu
  function_timeout   = local.medium_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "workflow_starter_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "workflow-starter"
  function_root_path    = "../src/workflow_functions/starter"
  function_shared_path  = "../src/workflow_functions/workflow_shared"
  service_account_email = module.workflow_function_sa.sa_email

  max_instance_count = 10
  container_memory   = local.medium_function.memory
  container_cpu      = local.medium_function.cpu
  function_timeout   = local.medium_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "workflow_finalizer_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "workflow-finalizer"
  function_root_path    = "../src/workflow_functions/finalizer"
  function_shared_path  = "../src/workflow_functions/workflow_shared"
  service_account_email = module.workflow_function_sa.sa_email

  max_instance_count = 10
  container_memory   = local.medium_function.memory
  container_cpu      = local.medium_function.cpu
  function_timeout   = local.medium_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "workflow_dates_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "workflow-dates"
  function_root_path    = "../src/workflow_functions/dates_provider"
  service_account_email = module.workflow_function_sa.sa_email

  max_instance_count = 10
  container_memory   = local.small_function.memory
  container_cpu      = local.small_function.cpu
  function_timeout   = local.small_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "category_slug_mapper_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "category-slug-mapper"
  function_root_path    = "../src/category_slug_mapper"
  service_account_email = module.category_slug_mapper_sa.sa_email

  container_memory = "1Gi"
  container_cpu    = ".583"
  function_timeout = local.big_function.timeout
  env_variables    = local.runtime_env_variables

  labels = local.p2_labels
}

module "ranker_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "ranker-elt"
  function_root_path    = "../src/ranker"
  service_account_email = module.ranker_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "srp_circuit_breakers_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "srp-circuit-breakers"
  function_root_path    = "../src/srp_circuit_breakers"
  service_account_email = module.srp_circuit_breakers_sa.sa_email

  container_memory = local.medium_function.memory
  container_cpu    = local.medium_function.cpu
  function_timeout = local.big_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}

module "workflow_current_time_function" {
  source       = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/cloud_run_function?ref=main"
  force_deploy = var.force_deploy

  artifact_registry_url = module.analytics_pipelines_artifacts.repository_url
  function_name         = "workflow-current-time"
  function_root_path    = "../src/workflow_functions/current_time"
  service_account_email = module.workflow_function_sa.sa_email

  max_instance_count = 10
  container_memory   = local.small_function.memory
  container_cpu      = local.small_function.cpu
  function_timeout   = local.small_function.timeout

  env_variables = local.runtime_env_variables

  labels = local.p2_labels
}
