locals {
  analytics_db_name = local.is_staging ? module.analytics_db.instance_name : "analytics-08ce"
  analytics_db_id   = "${local.project.id}:${var.gcp_region}:${local.analytics_db_name}"
}

/*
An authorized dataset lets you authorize all of the objects in a specified dataset (funnelio)
to access the data in a second dataset (in `refb-analytics-funnelio` project).

With an authorized dataset, you don't need to configure individual authorized views.

References:
- https://cloud.google.com/bigquery/docs/authorized-datasets#authorized_datasets
- https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_dataset_access#example-usage---bigquery-dataset-access-authorized-dataset
*/
resource "google_bigquery_dataset_access" "funnelio_access" {
  dataset_id = "Funnel_export_refurbed"
  project    = "refb-analytics-funnelio"
  dataset {
    dataset {
      project_id = local.project.id
      dataset_id = "funnelio"
    }
    target_types = ["VIEWS"]
  }
}

resource "google_bigquery_connection" "platform_export_connection" {
  connection_id = "platform-export"
  location      = var.gcp_multi_region
  description   = "Connection to Cloud SQL Postgres instance used by platform-export"

  cloud_sql {
    instance_id = local.analytics_db_id
    database    = "platform"
    type        = "POSTGRES"

    # Lack of `lifecycle` and ephemeral variables are not supported yet:
    # https://github.com/hashicorp/terraform-provider-google/issues/23152
    credential {
      username = "cf_reader"
      password = "to_be_entered_via_console"
    }
  }
}

# Manually grant cloud-sql access to the service agent
# https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules/-/work_items/13
# https://cloud.google.com/bigquery/docs/connect-to-sql#access-sql
# resource "google_service_account_iam_member" "gce-default-account-iam" {
#   service_account_id = google_bigquery_connection.platform_export_connection.cloud_sql[0].service_account_id
#   role               = "roles/iam.serviceAccountUser"
#   member             = "serviceAccount:${google_service_account.sa.email}"
# }
