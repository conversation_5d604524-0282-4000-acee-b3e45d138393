module "cicd_artifacts" {
  source        = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/artifact_registry?ref=main"
  should_create = var.cicd_repo_should_create

  repository_name            = "cicd-artifacts"
  delete_artifact_older_than = 3
  keep_artifact_count        = 1

  labels = local.basic_labels
}

module "analytics_pipelines_artifacts" {
  source = "git::https://gitlab.com/refurbed/analytics-and-ds/infrastructure-modules//src/artifact_registry?ref=main"

  repository_name            = "${local.gitlab.project_name}-artifacts"
  delete_artifact_older_than = 14
  keep_artifact_count        = 5

  labels = local.basic_labels
}
