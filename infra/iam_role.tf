resource "google_project_iam_custom_role" "storage_reader" {
  role_id = "cloudStorageReader"
  title   = "cloud-storage-reader"

  permissions = ["storage.objects.list", "storage.objects.get"]
}

resource "google_project_iam_custom_role" "storage_writer" {
  role_id = "cloudStorageWriter"
  title   = "cloud-storage-writer"

  permissions = [
    "storage.objects.create",
    "storage.objects.update",
    "storage.objects.delete"
  ]
}
