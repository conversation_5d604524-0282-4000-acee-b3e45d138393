# This is variables definition file.
# Ref. https://developer.hashicorp.com/terraform/language/values/variables#variable-definitions-tfvars-files
# DO NOT SPECIFY ANY SECRET HERE

env_name       = "staging"
gcp_project_id = "refb-analytics-staging"

# Cloud SQL Analytics
analytics_db_should_create = true
analytics_db_tier          = "db-g1-small"
analytics_db_pg_version    = "POSTGRES_15"

# Cloud Functions Schedules
daily_every_8_hours_schedule_enabled = false

cicd_repo_should_create    = true
tests_bucket_should_create = true
tests_topic_should_create  = true
