# This does not solve the chicken-and-egg problem of having state in a GCS bucket,
# but it keeps the definition within the repository. The state for this file is not stored anywhere.
# Therefore, every time you run this, it will attempt to create the bucket from scratch.
# To run it, navigate to the folder and execute: terraform init && terraform apply

locals {
  project = "refb-analytics-staging"
  region  = "europe-west3"
}

provider "google" {
  project = local.project
  region  = local.region
}

module "tf_state_bucket" {
  source          = "../../modules/storage_bucket"
  bucket_location = local.region
  bucket_name     = "${local.project}-tf-state"

  bucket_storage_class = "STANDARD"
  force_destroy        = false
  versioning_enabled   = true

  # Version count includes live and non-current versions.
  # Anything more than the 2 versions will be deleted: so the current state and the backup will be kept.
  lifecycle_rules = [
    {
      action = {
        type = "Delete"
      }
      condition = {
        num_newer_versions = 2
      }
    }
  ]

  labels = { owner = "data_engineering" }
}
