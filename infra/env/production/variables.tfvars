# This is variables definition file.
# Ref. https://developer.hashicorp.com/terraform/language/values/variables#variable-definitions-tfvars-files
# DO NOT SPECIFY ANY SECRET HERE

env_name       = "production"
gcp_project_id = "refb-analytics"

# Cloud SQL Analytics
analytics_db_should_create = false

# Cloud Functions Schedules
daily_every_8_hours_schedule_enabled = true
every_30_minutes_schedule_enabled    = true

cicd_repo_should_create    = false
tests_bucket_should_create = false
tests_topic_should_create  = false
dhl_remove_processed_files = true
