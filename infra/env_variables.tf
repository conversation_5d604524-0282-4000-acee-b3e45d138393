# This is env specific variables file.
# For resources or modules please use dedicated variables.tf file.

locals {
  gitlab = {
    project_id   = 54854811
    project_name = "analytics-pipelines"
  }
  project = {
    id     = data.google_client_config.config.project
    number = data.google_project.project.number
  }

  basic_labels = {
    owner = "data_engineering"
  }

  cloud_run_labels = merge(local.basic_labels, { alerting_enabled = true })
  p1_labels        = merge(local.cloud_run_labels, { criticality = "p1" })
  p2_labels        = merge(local.cloud_run_labels, { criticality = "p2" })

  runtime_env_variables = {
    ENV_NAME           = var.env_name
    GOOGLE_PROJECT_ID  = var.gcp_project_id
    RAW_BUCKET         = module.raw_bucket.bucket_name
    TRANSFORMED_BUCKET = module.transformed_bucket.bucket_name
    LOG_EXECUTION_ID   = true
  }

  is_staging    = var.env_name == "staging"
  is_production = var.env_name == "production"
}

variable "gcp_region" {
  description = "GCP region. Use it for computational workloads and any internal project resources."
  type        = string
  default     = "europe-west3"
}

variable "gcp_multi_region" {
  description = "GCP multi-region. Use it for any data intended for analytics."
  type        = string
  default     = "EU"
}

variable "gcp_project_id" {
  description = "GCP project id"
  type        = string
}

variable "env_name" {
  description = "Environment name"
  type        = string
  default     = "staging"
}
