# GCP
GOOGLE_PROD_PROJECT_ID=refb-analytics
GOOGLE_STAGING_PROJECT_ID=refb-analytics-staging
GCP_REGION=europe-west3
CICD_DOCKER_REGISTRY=${GCP_REGION}-docker.pkg.dev/${GOOGLE_STAGING_PROJECT_ID}/cicd-artifacts
INFRA_STAGING_PATH="env/staging"
INFRA_PROD_PATH="env/production"

# platform database dump
DUMP_NAME=preview_dump_2025_07_16.sql
DUMP_URL=gs://analytics-pipelines-staging-backup/platform-preview/${DUMP_NAME}
DUMP_PATH=./.temp/${DUMP_NAME}

# platform image
POSTGRES_IMAGE=${CICD_DOCKER_REGISTRY}/platform-db
POSTGRES_SERVICE_NAME=analytics_platform_db

# local db
ENV=local
POSTGRES_DB=platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_PORT=5433
POSTGRES_HOST=localhost
COMMAND=info

# local db refresh
ANALYTICS_CLONE_DB=analytics
PLATFORM_CLONE_DB=platform_clone

# terraform
TF_VAR_force_deploy=false
