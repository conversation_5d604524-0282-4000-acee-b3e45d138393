SHELL=./make.sh
$(DEBUG).SILENT: ; # no need for @, DEBUG=yes make ... disable silence
.ONESHELL: ;       # when a target is built all lines of the recipe will be given to a single invocation

include .env
export
COMMAND=

pg-up: ## Starts up empty local db
	@echo "Starting DB..."
	docker compose up postgres -d --remove-orphans

# Use target specific variables
pg-info: COMMAND = "info -infoOfState=Pending,Outdated"
pg-migrate: COMMAND = "info -infoOfState=Pending,Outdated repair migrate"

pg-info pg-migrate: pg-up ## Migrates local db schemas
	@echo "Migrating DB schemas..."
	COMMAND=$(COMMAND) docker compose run --env COMMAND flyway

pg-down: ## Stops the local db
	docker compose down --remove-orphans

pg-clean: ## Cleans local Docker volumes (all data)
	docker compose down postgres --remove-orphans --volumes

pg-clean-up: pg-clean pg-up ## Restart fresh copy of platform DB

pg-reboot: pg-clean pg-up pg-migrate ## Restart fresh copy of platform DB and apply flyway migrations

env: ## Installs all Python dependencies
	pip install --upgrade pip
	pip install -r requirements-dev.txt

code-check: ## Runs all linters and reformats code if possible
	pre-commit run --all-files

test: test-unit test-int ## Runs all tests

test-unit: ## Runs unit tests only
	pytest tests/unit -rA

test-int: pg-reboot ## Runs integration tests only
	pytest tests/integration -rA

test-sql: pg-reboot ## Runs local sql integration tests only
	pytest tests/integration/test_local_sql -rA

test-bq: ## Runs BigQuery integration tests only
	pytest tests/integration/test_big_query -rA

test-platform-export: ## Runs platform-export integration tests only
	pytest tests/integration/test_platform_export -rA

init-staging: ## Initializes terraform state for 'refb-analytics-staging' GCP project
	cd infra && terraform init -reconfigure -upgrade -backend-config=$(INFRA_STAGING_PATH)/backend.hcl

unlock-staging: init-staging ## Unlock terraform state at staging
	cd infra && terraform force-unlock -force $(args)

plan-staging: init-staging ## Shows terraform plan for 'refb-analytics-staging' GCP project
	cd infra && terraform plan -var-file=$(INFRA_STAGING_PATH)/variables.tfvars $(args)

deploy-staging: init-staging ## Deploys terraform changes to 'refb-analytics-staging' GCP project
	cd infra && terraform apply -var-file=$(INFRA_STAGING_PATH)/variables.tfvars $(args)

invoke-staging-function: ## Invokes a cloud function on 'refb-analytics-staging' GCP project
	@echo "Invoking '$(name)' function on '$(GOOGLE_STAGING_PROJECT_ID)'..."

	curl -X POST $$(gcloud run services describe $(name) --region $(GCP_REGION) --project $(GOOGLE_STAGING_PROJECT_ID) --format="value(status.url)") \
	-H "Authorization: bearer $$(gcloud auth print-identity-token)" \
	-H "Content-Type: application/json" && echo ""

gcloud-auth: ## Authenticate docker with GCP artifact registry
	gcloud auth configure-docker $(GCP_REGION)-docker.pkg.dev --quiet

# Use target specific variables
bq-migrate-info: COMMAND = info
bq-migrate-staging: COMMAND = repair migrate

bq-migrate-info bq-migrate-staging: gcloud-auth ## Migrates Biq Query on 'refb-analytics-staging' GCP project
	docker run --rm \
	-e GOOGLE_APPLICATION_CREDENTIALS=/home/<USER>/gcloud/application_default_credentials.json \
	--mount type=bind,source=./src/sql-bq,target=/sql \
	--mount type=bind,source=$(HOME)/.config/gcloud,target=/home/<USER>/gcloud \
	$(CICD_DOCKER_REGISTRY)/cicd-migrations \
	flyway -configFiles=/sql/flyway.toml -locations='filesystem:/sql' -environment=staging $(COMMAND)

build-migrations: gcloud-auth ## Builds only migrations image
	./.gitlab/build_image.sh $(CICD_DOCKER_REGISTRY)/cicd-migrations .gitlab/docker/migrations.Dockerfile false

push-db-image: gcloud-auth ## Builds and pushes platform database Docker image
	./.gitlab/download_dump.sh
	./.gitlab/build_image.sh $(CICD_DOCKER_REGISTRY)/platform-db .gitlab/docker/database.Dockerfile

push-cicd-images: gcloud-auth ## Builds and pushes CI/CD Docker images
	./.gitlab/build_image.sh $(CICD_DOCKER_REGISTRY)/cicd-app ./Dockerfile
	./.gitlab/build_image.sh $(CICD_DOCKER_REGISTRY)/cicd-build .gitlab/docker/build.Dockerfile
	./.gitlab/build_image.sh $(CICD_DOCKER_REGISTRY)/cicd-infra .gitlab/docker/infra.Dockerfile
	./.gitlab/build_image.sh $(CICD_DOCKER_REGISTRY)/cicd-migrations .gitlab/docker/migrations.Dockerfile

init-prod: ## Initializes terraform state for 'refb-analytics' GCP project
	cd infra && terraform init -reconfigure -backend-config=$(INFRA_PROD_PATH)/backend.hcl

plan-prod: init-prod ## Shows terraform plan for 'refb-analytics' GCP project
	cd infra && terraform plan -var-file=$(INFRA_PROD_PATH)/variables.tfvars $(args)

invoke-prod-function: ## Invokes a cloud function on 'refb-analytics' GCP project
	@echo "Invoking '$(name)' function on '$(GOOGLE_PROD_PROJECT_ID)'..."

	curl -X POST $$(gcloud run services describe $(name) --region $(GCP_REGION) --project $(GOOGLE_PROD_PROJECT_ID) --format="value(status.url)") \
	-H "Authorization: bearer $$(gcloud auth print-identity-token)" \
	-H "Content-Type: application/json" && echo ""

# Auto-document our Makefile using a trick from https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
.DEFAULT_GOAL := help
help: Makefile
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
