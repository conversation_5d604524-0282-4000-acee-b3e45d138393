#!/usr/bin/env bash
#
# refurbed platform database sync tool
# shellcheck disable=SC2129

set -euo pipefail

USERNAME="platform"
DATABASE_NAME="platform"
PLATFORM_DB_HOST="localhost"
PLATFORM_DB_PORT="2001"

DATETIME=$(date '+%Y%m%d%H%M%S')
TMPDIR="/var/data/exports"
EXPORT_FULLNAME="$TMPDIR/export-$DATABASE_NAME-$DATETIME"
PLATFORM_BACKUP_BUCKET="refb-backup"

log_file="$TMPDIR/export-$DATABASE_NAME-$DATETIME.log"

dump_database() {
  echo "$(date +%s)" "Dump started" >> "$log_file"

  pg_dump --username="$USERNAME" --host="$PLATFORM_DB_HOST" --port="$PLATFORM_DB_PORT" \
          --verbose \
          --jobs=8 \
          --no-owner \
          --no-acl \
          --exclude-table-data=import_failure_counters \
          --exclude-table-data=imports \
          --exclude-table-data=active_offers_eur_ng \
          --exclude-table-data=active_offers_dkk_ng \
          --exclude-table-data=active_offers_sek_ng \
          --exclude-table-data=active_offers_pln_ng \
          --exclude-table-data=active_offers_czk_ng \
          --exclude-table-data=active_offers_chf_ng \
          --format=directory \
          --file="$EXPORT_FULLNAME" \
          platform 2>> "$log_file"

  echo "$(date +%s)" "Dump finished" >> "$log_file"
}

upload_database_dump() {
  echo "$(date +%s)" "Upload started" >> "$log_file"

  /home/<USER>/google-cloud-sdk/bin/gsutil -m cp -r "$EXPORT_FULLNAME" gs://"$PLATFORM_BACKUP_BUCKET" 2>> "$log_file"

  echo "$(date +%s)" "Upload finished" >> "$log_file"
}

cleanup() {
  echo "$(date +%s)" "Cleaning temporary files" >> "$log_file"

  rm -rf "$EXPORT_FULLNAME"
}

trap cleanup EXIT

main() {
  echo "Job started" >> "$log_file"

  dump_database
  upload_database_dump

  echo "Job finished" >> "$log_file"
}

main
