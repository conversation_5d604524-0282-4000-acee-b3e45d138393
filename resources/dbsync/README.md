# Platform data dump

This is a step-by-step guide to how the Platform data dump works and how to trigger it manually when needed.

### Automation
The process runs on `refb-analytics` project on long-running `refurbed-database-sync` VM.
There are no logs available in GCP Logs.
Instead, they are stored directly as files on `refurbed-database-sync` in `/var/data/exports` location.

The database sync process runs daily at `00:05:00 UTC`.
The local time varies between `01:05:00` and `02:05:00` depending on daylight saving time.
The schedule is a crontab on the VM, to see it run following:
```bash
sudo crontab -u refurbed -l
```
The script is connecting to the database via `CloudSql Proxy` running as a service on the VM:
To see the status of the service:
```bash
sudo systemctl status cloud-sql-proxy.service
```
To see the service file:
```bash
sudo systemctl status cloud-sql-proxy.service
```
The whole process now takes around `1 hour` and it consists of following steps:
- Dump production platform db to local storage.
- Upload the dump to the cloud bucket `refb-backup` on platform `refb-platform-production` project.
- Clean temporary local dump files.

### On demand run

In some cases, the process may fail, requiring manual intervention.
To restart the process:
- Connect via SSH to the `refurbed-database-sync` VM on production project.
- Run `sudo su` and navigate to the `/home/<USER>/` directory, there should be a file called `db_dump.sh`.
- Run the script `db_dump.sh` :pray:
- To see the progress you can run following command from time to time:
 ```bash
cat /var/data/exports/export-platform-{your-timestamp}.log
```
