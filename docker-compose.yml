services:
  flyway:
    image: flyway/flyway:11.8.1-alpine
    command: >
      -configFiles=/flyway/conf/flyway.toml -environment=local
      -user=${POSTGRES_USER} -password=${POSTGRES_PASSWORD} ${COMMAND}
    volumes:
      - ${PWD}/src/sql-pg:/flyway/sql
      - ${PWD}/src/sql-pg/flyway.toml:/flyway/conf/flyway.toml
    depends_on:
      postgres:
        condition: service_healthy

  # Postgres image with platform database, see  .gitlab/docker/database.Dockerfile
  postgres:
    image: ${POSTGRES_IMAGE}
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    user: ${POSTGRES_USER}
    ports:
      - "5433:5432"
    volumes:
      - analytics_data:/var/lib/postgresql/data:rw
    healthcheck:
      # https://docs.docker.com/reference/dockerfile/#healthcheck
      # https://stackoverflow.com/questions/65115627/safe-ways-to-specify-postgres-parameters-for-healthchecks-in-docker-compose#comment138580051_78603097
      test: ["CMD-SHELL",
             "psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c 'select created_at from public.healthcheck' || exit 1"]
      start_period: 30s
      interval: 10s
      retries: 6

volumes:
  analytics_data:
