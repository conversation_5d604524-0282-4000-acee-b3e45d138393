[tool.black]
line-length = 120
exclude = '''
(
  /(
    \.git
    | \.mypy_cache
    | \.pytest_cache
    | .venv
    | venv
    | output
  )/
)
'''

[tool.isort]
profile = 'black'

[tool.mypy]
# an equivalent of `mypy . --strict` with some exclusions
disallow_any_generics = true
disallow_untyped_calls = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_unused_configs = true
no_implicit_reexport = true
strict_equality = true
extra_checks = true

[tool.pytest.ini_options]
# https://stackoverflow.com/questions/4673373/logging-within-pytest-tests
log_cli = true
log_cli_level = "INFO"
# Env variables override for testing purposes: https://pypi.org/project/pytest-env/
env = [
    "ENV_NAME=local",
    "EXPORT_CHUNK_SIZE=1000",
    "RUN_ID=test-run",
]
