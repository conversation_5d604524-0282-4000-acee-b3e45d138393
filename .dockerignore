# Git and version control
.git
.gitignore
.gitattributes

# Docker files
Dockerfile*
.dockerignore
docker-compose*.yml

# Documentation
README.md
*.md
docs/
*.txt
LICENSE

# IDE and editor files
.vscode/
.idea/
.DS_Store

# Python cache and build artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
.venv/
.env/

# Testing and coverage
.pytest_cache/

# Environments
.env

# Logs
*.log

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Local development files
.cache/

# Keep essential files for build
!requirements.txt
!requirements-dev.txt
!pyproject.toml
!.env.dist
!.flake8
!.pre-commit-config.yaml
