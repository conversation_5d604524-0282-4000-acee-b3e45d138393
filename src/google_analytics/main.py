import functions_framework
from cloudevents.abstract import CloudEvent
from repository import GAPurchasesAndClicksRepository
from scanner import PurchasesAndClicksScanner, PurchasesAndClicksScannerConfig

from common.cloud_events import create_http_event
from common.logger import setup_logger
from common.time_service import TimeService
from common.timing import timing
from common.typings import HttpResponse

config = PurchasesAndClicksScannerConfig()
pac_repository = GAPurchasesAndClicksRepository()

logger = setup_logger(config)


@timing(logger, "google_analytics")
@functions_framework.http
def run(_: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from `daily_every_8_hours_schedule`.

    :returns: the response object
    """
    # Request a new `TimeService` for each run to get up-to-date time!
    time_service = TimeService()
    scanner = PurchasesAndClicksScanner(config=config, time_service=time_service, pac_repository=pac_repository)

    logger.info("Scanning for stale partitions...")
    partitions = scanner.detect_stale_partitions()
    scanner.load_stale_partitions(partitions)

    return HttpResponse()


if __name__ == "__main__":
    run(create_http_event("Running locally..."))
