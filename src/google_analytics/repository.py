from typing import Optional

from common.bq_client import QueryResult
from common.bq_repository import (
    BigQueryRepository,
    BQProcedure,
    BQTablePartitionStatus,
    PartitionType,
)


class GARepositoryError(ValueError):
    """GARepository error"""


class GAPurchasesAndClicksRepository(BigQueryRepository):
    """
    Google Analytics repository serving `purchases_and_clicks` partitioned table
    """

    DATASET: str = "google_analytics"
    TABLE: str = "purchases_and_clicks"

    LOAD_PROCEDURE = BQProcedure(DATASET, "load_purchases_and_clicks")

    def get_target_partitions(self) -> dict[PartitionType, BQTablePartitionStatus]:
        """
        Get target `purchases_and_clicks` partition statuses:
        ```
        select * from google_analytics.purchases_and_clicks_partitions;
        ```

        :returns: Partition to TablePartitionStatus map ordered by partition (date)
        """
        view = self.get_table("purchases_and_clicks_partitions")
        result = self.select(table=view)

        partition_statuses: dict[PartitionType, BQTablePartitionStatus] = {}
        for row in result.rows:
            table_partition_status = BQTablePartitionStatus(
                dataset=row.table_schema,
                table=row.table_name,
                partition=row.table_partition,
                last_modified=row.last_modified,
                total_rows=row.total_rows,
            )
            partition_statuses[table_partition_status.partition] = table_partition_status

        return partition_statuses

    def get_source_partition(self, partition: PartitionType) -> Optional[BQTablePartitionStatus]:
        """
        Get source `events_fresh_` partition status for the given partition / shard.
        ```
        select * from google_analytics.events_fresh_partitions where partition_id = '20240511';
        ```

        :param partition: partition as date
        :returns: BQ table partition status if available; otherwise None
        """
        view = self.get_table("events_fresh_partitions")
        partition_id = self.to_partition_id(partition)
        where = f"partition_id='{partition_id}'"

        result = self.select(table=view, where=where)

        if result.rows:
            if len(result.rows) == 1:
                row = result.rows[0]
                return BQTablePartitionStatus(
                    dataset=row.table_schema,
                    table=row.table_name,
                    partition=row.table_partition,
                    last_modified=row.last_modified,
                    total_rows=row.total_rows,
                )
            else:
                raise GARepositoryError(f"More than one partition for {partition_id}?!")

        return None

    def load_partition(self, partition: PartitionType) -> QueryResult:
        """
        (Re)loads given `purchases_and_clicks` partition by means of stored procedure.
        ```
        call google_analytics.load_purchases_and_clicks('20240630');
        ```

        :param partition: partition to be (re)loaded
        :returns: query result (stats with number of affected rows)
        """
        partition_id = self.to_partition_id(partition)

        return self.call_procedure(self.LOAD_PROCEDURE, [partition_id])
