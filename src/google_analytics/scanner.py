from dataclasses import dataclass
from typing import Optional

from repository import GAPurchasesAndClicksRepository

from common.bq_repository import BQTablePartition, BQTablePartitionStatus, PartitionType
from common.config import Config
from common.logger import get_logger
from common.time_service import TimeService

logger = get_logger()


@dataclass(frozen=True)
class PurchasesAndClicksScannerConfig(Config):
    """
    PurchasesAndClicksScanner configuration:
    - reload_days: number of days for reload (default to 3 last days)
    """

    reload_days: int = 3


class PurchasesAndClicksScanner:
    """
    Scans `purchases_and_clicks` table for stale partitions.
    """

    def __init__(
        self,
        config: PurchasesAndClicksScannerConfig,
        time_service: TimeService,
        pac_repository: GAPurchasesAndClicksRepository,
    ) -> None:
        self._config = config
        self._time_service = time_service
        self._pac_repository = pac_repository

    def detect_stale_partitions(self) -> list[BQTablePartition]:
        """
        Detects stale partitions in the `purchases_and_clicks` partitioned table:
        - Get `partition_statuses` for the `purchases_and_clicks` target table
        - For each partition in `reload_days` configuration:
            - detect stale partition
        - Use iterator to yield asap in order to start loading data sooner than later

        :returns: list with table partitions to be refreshed
        """
        partition_statuses = self._pac_repository.get_target_partitions()
        partitions = self._time_service.last_days(days=self._config.reload_days)

        result = []
        for partition in partitions:
            logger.info(f"Partition={partition.isoformat()} scanning...")
            if table := self._detect_stale_partition(partition, partition_statuses):
                result.append(table)

        return result

    def _detect_stale_partition(
        self, partition: PartitionType, partition_statuses: dict[PartitionType, BQTablePartitionStatus]
    ) -> Optional[BQTablePartition]:
        """
        Detect if the given `partition` need to be refreshed:
        - Compare `purchases_and_clicks` partition last modified date with the source
        - If source was changed since the last recorded modified date, assume stale partition
        - If source lacks given partition, assume stale partition

        :param partition: partition (date) to be checked
        :param partition_statuses: source `purchases_and_clicks` partition statuses
        :returns: `purchases_and_clicks` partitions to be refreshed
        """
        if partition in partition_statuses:
            # 1. Get last recorded partition status
            last_status: BQTablePartitionStatus = partition_statuses[partition]

            # 2. Get current/live status for the source partition (shard)
            live_status = self._pac_repository.get_source_partition(partition)

            # 3. Check if the source was changed since the last modified time
            if live_status and live_status.last_modified > last_status.last_modified:
                logger.info(
                    f"Partition={partition.isoformat()} was modified at {last_status.last_modified.isoformat()} "
                    f"and will be reloaded."
                )
                return live_status.as_table_partition()
        else:
            # 4. No recorded status available, assume changes
            logger.info(f"Partition={partition.isoformat()} is new and will be loaded.")
            return self._pac_repository.get_table_partition(partition)

        logger.info(f"Partition={partition.isoformat()} is up to date. No need for reload.")
        return None

    def load_stale_partitions(self, partitions: list[BQTablePartition]) -> None:
        """
        Re(loads) stale partitions.

        :param partitions: list of partitions to refresh
        """
        if not partitions:
            logger.info("No partitions to reload!")
            return

        for partition in partitions:
            logger.info(f"Partition={partition.partition.isoformat()} is being reloaded...")
            self._pac_repository.load_partition(partition.partition)
