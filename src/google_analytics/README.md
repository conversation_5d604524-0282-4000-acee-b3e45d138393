## Google Analytics 4 - Smart Pricing Optimisation

### Goals
- Reduce operational cost and query complexity introduced by the new GA4 schema.
- Limit the data for smart pricing related queries by introducing new data model (table) that would be incrementally
loaded each day.

### Lessons learned
* Materialized views is not the right tool for incremental data load;
* Partitioned tables are preferred over [sharded](https://cloud.google.com/bigquery/docs/partitioned-tables#dt_partition_shard)
  tables as these are easier to maintain:
  * You can [delete](https://cloud.google.com/bigquery/docs/using-dml-with-partitioned-tables#deleting_data_in_partitioned_tables) a single partition;
  * You can enforce [partition filter](https://cloud.google.com/bigquery/docs/querying-partitioned-tables#require_a_partition_filter_in_queries) option;
* Original `events_fresh_*` table can be reduced from `15 GB` to `140 MB` (x10 times), due to:
  * filtering to `view_item` and `purchase` events;
  * reducing the number of columns and their content.

### Assumptions

* The source data will be fed from the [purchases_and_clicks_view](src/sql-bq/schema/views/R__3001_purchases_and_clicks.sql) view;
  * This is for internal use only, and is not intended for Looker reports;
  * It acts as an interface for the target `purchase_and_clicks` partitioned table;
* We need to store the last year of data (360 days);
* The `country` and `table_date` are most frequently used filters;
* Most calculations in Looker is done in python rather than in SQL;
* The last 72h of data may be changed at any time according to [this](https://medium.com/@aliiz/when-will-yesterdays-ga4-data-be-ready-bigquery-says-bye-bye-to-lag-b6e45aab36ca) and below [checks](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/51#note_1912236921);
* We aim to run ETL 2-3 times a day at maximum;
* Analytics will use the `purchases_and_clisks` table as the main source of their queries in place of existing GA4 table(s).

## High level design

1. Create `purchases_and_clisks` partitioned table using `SQL` migration script.
2. Refresh "hot" partitions, the ones that are refreshed by GA4 within last 72h by means of ELT process.
3. ELT is implemented in the single Cloud Function with the `Scanner` and `Loader` components.

```mermaid
sequenceDiagram
    Scanner->>BQ: Detect stale partitions
    Note left of BQ: Query information schema views
    Scanner->>Loader: Detects partitions for reload
    Loader->>BQ: (Re)Load given partitions
    Note right of BQ: Call BQ stored procedure
```

**Incrementally** would mean to fully reload missing or outdated partition:
* there is no plan to load incrementally within a partition due to its complexity:
  * `distinct` within a `purchases_and_clisks_view` source
  * lack of unique key on the source table;
  * the whole partition needs to be scanned anyway to calculate the diff;
  * higher cost of implementation not compensated by lower GCP cost.


### Cloud function: Scanner

**Uses `configuration` class to store**:
  * number of days to reload (default to 3 last days)

**Implements the following logic**:
1. Get `partition_statuses` for the `purchases_and_clicks` target table using [partitions view](https://cloud.google.com/bigquery/docs/information-schema-partitions)
to get last modified date for already loaded partitions;
2. Scan configured number of days to reload, for each day:
* If there is no partition on a given day, schedule a partition for reload;
* Otherwise, query [table storage view](https://cloud.google.com/bigquery/docs/information-schema-table-storage)
to get the last modified date for the source `events_fresh` shard:
    * Check if there were any changes since the last time (compare with `partition_statuses`)
    * If so, schedule this partition for reload

### Cloud function: Loader
* (Re)loads given daily partition by means of BigQuery stored procedure (delete followed by insert);
