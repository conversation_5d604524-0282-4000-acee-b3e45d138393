import dataclasses

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_model import SqlProcedure
from common.sql_repository import SqlRepository

logger = get_logger()


@dataclasses.dataclass(frozen=True)
class ProcedureRun(PipelineRun, SqlProcedure):
    """
    Sql procedure to run with pipeline logging metadata.
    """


class ProcedureExecutor(SupportsPipelineExecution):
    def __init__(self, procedure: ProcedureRun, repository: SqlRepository):
        self._procedure = procedure
        self._repository = repository

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._procedure.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._procedure.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._repository

    def get_step_name(self, function_name: str) -> str:
        """SupportsPipelineExecution interface"""
        return f"procedure:{self._procedure.procedure_name}"

    @log_pipeline_execution()
    def run(self) -> None:
        logger.info(f"Running '{self._procedure.full_name}'...")
        self._repository.call(procedure=self._procedure)
