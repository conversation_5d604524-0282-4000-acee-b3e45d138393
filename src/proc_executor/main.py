import functions_framework
from cloudevents.abstract import CloudEvent
from google.cloud.sql.connector import Connector

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.logger import setup_logger
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.sql_repository import SqlRepository
from common.timing import timing
from common.typings import HttpResponse
from proc_executor import ProcedureExecutor, ProcedureRun

config = Config()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)


@timing(logger, "proc-executor")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    message = get_event_message(request)
    logger.info(f"Running with {message=}")

    procedure = ProcedureRun.from_json(message)
    with Connector() as connector:
        repository = SqlRepository(db_config=pg_config, connector=connector)
        executor = ProcedureExecutor(procedure, repository)
        executor.run()

    return HttpResponse()


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()

    test_procedure = ProcedureRun(schema_name="maintenance", procedure_name="reload_full_order_item_refunds")
    run(create_http_event(test_procedure.to_json()))  # noqa
