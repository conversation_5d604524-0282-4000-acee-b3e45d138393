from datetime import date

from google.ads.googleads.client import GoogleAdsClient as GoogleClient
from google.ads.googleads.v20.services import GoogleAdsRow
from google_ads_client_model import (
    GoogleAdsClientConfig,
    GoogleAdsCost,
    GoogleAdsCustomer,
)

from common.logger import get_logger

logger = get_logger()


class GoogleAdsClient:
    """
    Client to be used for Google Ads related API calls.
    """

    def __init__(self, config: GoogleAdsClientConfig) -> None:
        self._config = config
        client = GoogleClient.load_from_dict(config.to_dict())
        self._service = client.get_service("GoogleAdsService")

    def get_customers(self) -> list[GoogleAdsCustomer]:
        """
        Gets Google Ads Customers by using login customer id.
        This is master account id that has access to all the customers.
        Returns only customers that are not managers, are enabled and are not test accounts.
        Ref. https://developers.google.com/google-ads/api/fields/v20/customer_client_query_builder
        :returns: list of Google Ads customers
        """

        raw_customers = self._run_query(
            query="""
                  select
                      customer_client.client_customer,
                      customer_client.id,
                      customer_client.descriptive_name
                  from
                      customer_client
                  where
                        customer_client.manager = false
                    and customer_client.status = 'ENABLED'
                    and customer_client.test_account = false
                  """,
            customer_id=self._config.login_customer_id,
        )

        return [
            GoogleAdsCustomer(id=str(customer.customer_client.id), name=customer.customer_client.descriptive_name)
            for customer in raw_customers
        ]

    def get_costs(self, customer: GoogleAdsCustomer, start_date: date, end_date: date) -> list[GoogleAdsCost]:
        """
        Gets Google Ads costs for the given customer and date range.
        Converts cost from micros to actual currency units (divide by 1,000,000)
        :param customer: Google Ads customer
        :param start_date: start date
        :param end_date: end date
        Ref. https://developers.google.com/google-ads/api/fields/v20/campaign_query_builder
        :returns: list of Google Ads costs
        :raises AttributeError: when start date is after end date
        """
        logger.info(f"Getting Google Ads costs for customer '{customer.id}'...")

        if start_date > end_date:
            raise AttributeError(f"Start date {start_date} is after end date {end_date}!")

        query = f"""
              select
                segments.date,
                segments.hour,
                campaign.name,
                metrics.cost_micros
             from
                campaign
             where
                segments.date between '{start_date.isoformat()}' and '{end_date.isoformat()}'
            """

        raw_costs = self._run_query(
            query=query,
            customer_id=customer.id,
        )

        costs = []
        for raw_cost in raw_costs:
            cost_date = date.fromisoformat(raw_cost.segments.date)
            cost_hour = raw_cost.segments.hour
            campaign_name = raw_cost.campaign.name
            # Converts cost from micros to actual currency units (divide by 1,000,000)
            cost_in_currency = raw_cost.metrics.cost_micros / 1_000_000

            cost = GoogleAdsCost(
                cost_date=cost_date,
                cost_hour=cost_hour,
                campaign_name=campaign_name,
                costs=cost_in_currency,
                client_account=customer.name,
            )
            costs.append(cost)

        return costs

    def _run_query(self, query: str, customer_id: str) -> list[GoogleAdsRow]:
        """
        Runs a query against the Google Ads API.
        :param query: query to run
        :param customer_id: customer id to run the query against
        :returns: list of Google Ads rows
        """
        return self._service.search(customer_id=customer_id, query=query)
