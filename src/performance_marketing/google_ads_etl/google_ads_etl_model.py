import tempfile
from dataclasses import dataclass, field
from pathlib import Path

from pandas import Float32Dtype, Int16Dtype, StringDtype

from common.config import Config
from common.pandas_utils import BaseDataFrameSchema, DataFrameSchema


@dataclass(frozen=True)
class GoogleAdsConfig(Config):
    file_prefix: str = "google_ads_costs"
    data_lake_base_path: str = "google_ads/costs"
    max_workers: int = 5
    local_temp_path: Path = field(default_factory=lambda: Path(tempfile.mkdtemp()))


class GoogleAdsDataSchema(BaseDataFrameSchema):
    """
    Schema definition for Google Ads costs data.
    """

    COST_DATE: str = "cost_date"
    COST_HOUR: str = "cost_hour"
    CLIENT_ACCOUNT: str = "client_account"
    CAMPAIGN_NAME: str = "campaign_name"
    COSTS: str = "costs"
    UPDATED_AT: str = "updated_at"

    SCHEMA = DataFrameSchema(
        date_columns=[COST_DATE],
        datetime_columns=[UPDATED_AT],
        data_types={
            COST_HOUR: Int16Dtype(),
            CLIENT_ACCOUNT: StringDtype(),
            CAMPAIGN_NAME: StringDtype(),
            COSTS: Float32Dtype(),
        },
    )
