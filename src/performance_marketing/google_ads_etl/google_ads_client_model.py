from dataclasses import dataclass
from datetime import date

from dataclasses_json import DataClassJsonMixin

GOOGLE_ADS_SECRET_ID = "google-ads"


@dataclass(frozen=True)
class GoogleAdsClientConfig(DataClassJsonMixin):
    developer_token: str
    client_id: str
    client_secret: str
    refresh_token: str
    login_customer_id: str
    use_proto_plus: bool


@dataclass(frozen=True)
class GoogleAdsCustomer(DataClassJsonMixin):
    id: str
    name: str


@dataclass(frozen=True)
class GoogleAdsCost(DataClassJsonMixin):
    cost_date: date
    cost_hour: int
    client_account: str
    campaign_name: str
    costs: float
