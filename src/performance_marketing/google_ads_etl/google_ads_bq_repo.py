from datetime import date
from typing import Optional

from google.api_core.exceptions import BadRequest

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger

logger = get_logger()


class GoogleAdsCostsBqRepository(BigQueryRepository):
    """
    Google Ads BigQuery repository
    """

    DATASET: str = "analytics_transformed"
    TABLE: str = "google_ads_costs"
    LOAD_PROCEDURE = BQProcedure(DATASET, "load_google_ads_costs")
    CHECK_PROCEDURE = BQProcedure(DATASET, "check_google_ads_costs")

    def get_last_run_date(self) -> Optional[date]:
        """
        Gets last updated_at if found
        returns: last updated_at date or None if not found
        """

        sql = f"select max(updated_at) as max_updated_at from {self.DATASET}.{self.TABLE};"
        results = self._client.run_sql(sql)

        updated_at = next(iter(results.rows)).get("max_updated_at", None) if results else None
        if updated_at:
            return updated_at.date()

        return None

    def load_costs_table(self, run_id: str) -> int:
        """
        Refreshes `analytics_transformed.google_ads_costs' table from the `analytics_raw` corresponding table
         Example call:
         `call analytics_transformed.load_google_ads_costs('20240712_121651107');`

         :param run_id: given run id
         :returns: total bytes billed
        """
        result = self.call_procedure(self.LOAD_PROCEDURE, [run_id])

        return result.total_bytes_billed

    def check_costs_table(self) -> int:
        """
        Checks `analytics_transformed.google_ads_costs` table for data anomalies.
        Example call:
        `call analytics_transformed.check_google_ads_costs();`
        :returns: total bytes billed
        """
        try:
            result = self.call_procedure(self.CHECK_PROCEDURE)
        except BadRequest as ex:
            logger.warning(f"Quality check failed for '{self.get_table().full_name}': {ex.errors}")
            return 0

        return result.total_bytes_billed
