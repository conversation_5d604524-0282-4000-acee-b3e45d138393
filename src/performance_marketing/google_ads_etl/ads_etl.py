from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from functools import cached_property
from pathlib import Path
from typing import Optional

from google_ads_bq_repo import GoogleAdsCostsBqRepository
from google_ads_client import GoogleAdsClient
from google_ads_client_model import GoogleAdsCost
from google_ads_etl_model import GoogleAdsConfig, GoogleAdsDataSchema
from pandas import DataFrame

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService
from common.typings import DataLakePartition
from common.utils import format_bytes_pretty, get_run_id

logger = get_logger()


class GoogleAdsEtl(BaseEtl):
    _config: GoogleAdsConfig

    def __init__(
        self,
        config: GoogleAdsConfig,
        google_ads_client: GoogleAdsClient,
        raw_data_lake_repository: DataLakeRepository,
        bq_repository: GoogleAdsCostsBqRepository,
        time_service: Optional[TimeService] = None,
    ):
        super().__init__(config)
        self._google_ads_client = google_ads_client
        self._raw_data_lake_repository = raw_data_lake_repository
        self._bq_repository = bq_repository
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run"""
        return get_run_id(self._time_service.now)

    @cached_property
    def local_file_path(self) -> Path:
        """Google Ads costs file path with 'run_id' identifier."""

        return Path(self._config.local_temp_path, f"{self._config.file_prefix}_{self.run_id}.parquet")

    @cached_property
    def data_lake_partition(self) -> DataLakePartition:
        """RAW data lake partition where data will be uploaded"""
        return DataLakePartition(base_path=f"{self._config.data_lake_base_path}/run_id={self.run_id}")

    def extract(self) -> list[GoogleAdsCost]:
        """
        Gets Google Ads costs for all customers in parallel.
        This method gets the last run date based on the updated_at column from the BigQuery final table.
        It is then used to construct a WHERE condition for the costs query.
        If, for any reason, the job does not run for several days, the next execution will backfill the missing days.
        If the table is empty and no last value can be found, the method falls back to using today's date.
        If the job is triggered multiple times, it always reloads the last full day rather than just the last hour.

        :returns: list of Google Ads costs
        """
        total_costs = []
        today = self._time_service.today
        last_run_date = self._bq_repository.get_last_run_date()

        if not last_run_date:
            logger.warning(f"No last run date found, falling back to '{today.isoformat()}'...")
            last_run_date = today

        start_date = min(last_run_date, today)

        customers = self._google_ads_client.get_customers()

        logger.info(
            f"Getting Google Ads costs for {len(customers)} customer(s) and the date range between "
            f"'{start_date.isoformat()}' and '{today.isoformat()}'..."
        )
        with ThreadPoolExecutor(max_workers=self._config.max_workers) as executor:
            futures = {
                executor.submit(self._google_ads_client.get_costs, customer, start_date, today): customer
                for customer in customers
            }
            for future in as_completed(futures):
                costs = future.result()
                total_costs.extend(costs)

        return total_costs

    def transform(self, costs: list[GoogleAdsCost]) -> DataFrameWithSchema:
        """
        Transforms the costs into a dataframe.
        Adds UPDATED_AT column.
        :param costs: list of Google Ads costs
        :returns: dataframe of Google Ads costs
        """

        df = DataFrame(costs)
        if df.empty:
            logger.warning("No Google Ads costs to transform.")
            return DataFrameWithSchema(schema=GoogleAdsDataSchema.SCHEMA, dataframe=DataFrame())

        df[GoogleAdsDataSchema.UPDATED_AT] = self._time_service.now

        return DataFrameWithSchema(schema=GoogleAdsDataSchema.SCHEMA, dataframe=df)

    def load(self, transformed: DataFrameWithSchema) -> None:
        """
        Loads the costs into the data lake and BigQuery.
        :param transformed: dataframe of Google Ads costs
        """
        self.load_data_lake(transformed)
        self.load_big_query()

    def load_data_lake(self, transformed: DataFrameWithSchema) -> None:
        """
        Loads the costs into the RAW data lake.
        :param transformed: dataframe of Google Ads costs
        """

        logger.info(
            f"Loading '{self.local_file_path.name}' file into "
            f"data lake RAW '{self.data_lake_partition.path}' partition ..."
        )

        transformed.dataframe_with_schema.to_parquet(self.local_file_path)
        self._raw_data_lake_repository.write_file(self.local_file_path, self.data_lake_partition)

        logger.info(f"File '{self.local_file_path.name}' loaded.")

    def load_big_query(self) -> int:
        """
        Loads the costs into BigQuery.
        """

        logger.info(f"Loading BigQuery '{self._bq_repository.TABLE}' table for given '{self.run_id}' run_id...")
        billed_bytes = self._bq_repository.load_costs_table(self.run_id)
        check_billed_bytes = self._bq_repository.check_costs_table()
        logger.info(f"Loaded into BigQuery and billed for {format_bytes_pretty(billed_bytes)}.")

        return billed_bytes + check_billed_bytes
