from ads_etl import GoogleAdsConfig, GoogleAdsEtl
from google_ads_bq_repo import GoogleAdsCostsBqRepository
from google_ads_client import GoogleAdsClient
from google_ads_client_model import GOOGLE_ADS_SECRET_ID, GoogleAdsClientConfig

from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.secret_manager_client import get_secret
from common.timing import timing

config = GoogleAdsConfig()
GOOGLE_ADS_SECRET = get_secret(GOOGLE_ADS_SECRET_ID, config.project_id)

client_config = GoogleAdsClientConfig.from_json(GOOGLE_ADS_SECRET)
google_ads_client = GoogleAdsClient(client_config)
bq_repo = GoogleAdsCostsBqRepository()
raw_data_lake_repo = DataLakeRepository(config.raw_bucket)

logger = setup_logger(config)


@timing(logger, GoogleAdsEtl.__name__)
def run() -> None:
    """
    Processing entry point, called by scheduler.
    """
    logger.info(f"Running '{GoogleAdsEtl.__name__}' on '{config.env}' with following config: {config}...")

    etl = GoogleAdsEtl(
        config=config,
        google_ads_client=google_ads_client,
        raw_data_lake_repository=raw_data_lake_repo,
        bq_repository=bq_repo,
    )

    etl.run()


if __name__ == "__main__":
    run()
