from functools import cached_property
from pathlib import Path
from typing import Op<PERSON>

from microsoft_ads_api_client import MicrosoftAdsApiClient
from microsoft_ads_api_model import MicrosoftAdsReportSchema
from microsoft_ads_etl_bq_repo import MicrosoftAdsBqRepository
from microsoft_ads_etl_model import MicrosoftAdsEtlConfig, MicrosoftAdsReportEtlSchema

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService
from common.typings import DataLakePartition
from common.utils import format_bytes_pretty, get_run_id

logger = get_logger()


class MicrosoftAdsEtlError(ValueError):
    """Base exception for Microsoft Ads ETL errors."""


class MicrosoftAdsEtl(BaseEtl):
    """
    ETL process for Microsoft Ads.
    It extracts data from the Microsoft Ads API, transforms it and loads it into BigQuery.
    It is triggered daily by the scheduler.
    It is also triggered manually to backfill data.

    The transformation:
    """

    _config: MicrosoftAdsEtlConfig

    def __init__(
        self,
        config: MicrosoftAdsEtlConfig,
        microsoft_ads_api_client: MicrosoftAdsApiClient,
        raw_data_lake_repository: DataLakeRepository,
        bq_repository: MicrosoftAdsBqRepository,
        time_service: Optional[TimeService] = None,
    ):
        super().__init__(config)
        self._microsoft_ads_api_client = microsoft_ads_api_client
        self._raw_data_lake_repository = raw_data_lake_repository
        self._bq_repository = bq_repository
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run"""
        return get_run_id(self._time_service.now)

    @cached_property
    def local_file_path(self) -> Path:
        """Microsoft Ads costs file path with 'run_id' identifier."""

        return Path(self._config.local_temp_path, f"{self._config.file_prefix}_{self.run_id}.parquet")

    @cached_property
    def data_lake_partition(self) -> DataLakePartition:
        """RAW data lake partition where data will be uploaded"""
        return DataLakePartition(base_path=f"{self._config.data_lake_base_path}/run_id={self.run_id}")

    def extract(self) -> DataFrameWithSchema:
        """
        Extract data from Microsoft Ads API
        """
        logger.info("Extracting data from Microsoft Ads API...")
        return self._microsoft_ads_api_client.get_performance_report()

    def transform(self, extracted: DataFrameWithSchema) -> DataFrameWithSchema:
        """
        Transform data from Microsoft Ads API
        - Ensure schema and data types
        - Filter for spend > 0
        - Critical runtime assertions
        """
        logger.info("Transforming data from Microsoft Ads API...")
        df = extracted.dataframe

        # Ensure not empty or nulls in dataframe
        if df.empty:
            raise MicrosoftAdsEtlError("No data to transform.")

        if df.isna().any().any():
            raise MicrosoftAdsEtlError("Nulls found in dataframe.")

        # Apply final schema
        df = df.rename(
            columns={
                MicrosoftAdsReportSchema.CAMPAIGN_NAME: MicrosoftAdsReportEtlSchema.CAMPAIGN_NAME,
                MicrosoftAdsReportSchema.IMPRESSIONS: MicrosoftAdsReportEtlSchema.IMPRESSIONS,
                MicrosoftAdsReportSchema.SPEND: MicrosoftAdsReportEtlSchema.SPEND,
                MicrosoftAdsReportSchema.TIME_PERIOD: MicrosoftAdsReportEtlSchema.HOUR_OF_DAY,
            }
        )

        # Add updated_at column
        df[MicrosoftAdsReportEtlSchema.UPDATED_AT] = self._time_service.today

        # Filter for spend > 0 (business requirement)
        df = df[df[MicrosoftAdsReportEtlSchema.SPEND] > 0]

        return DataFrameWithSchema(dataframe=df, schema=MicrosoftAdsReportEtlSchema.SCHEMA)

    def load(self, transformed: DataFrameWithSchema) -> None:
        """
        Loads the costs into the data lake and BigQuery.
        :param transformed: dataframe of Microsoft Ads costs
        """
        self.load_data_lake(transformed)
        self.load_big_query()

    def load_data_lake(self, transformed: DataFrameWithSchema) -> None:
        """
        Loads the costs into the RAW data lake.
        :param transformed: dataframe of Microsoft Ads costs
        """

        logger.info(
            f"Loading '{self.local_file_path.name}' file into "
            f"data lake RAW '{self.data_lake_partition.path}' partition ..."
        )

        transformed.dataframe_with_schema.to_parquet(self.local_file_path, index=False)
        self._raw_data_lake_repository.write_file(self.local_file_path, self.data_lake_partition)

        logger.info(f"File '{self.local_file_path.name}' loaded.")

    def load_big_query(self) -> int:
        """
        Loads the costs into BigQuery.
        """

        logger.info(f"Loading BigQuery '{self._bq_repository.TABLE}' table for given '{self.run_id}' run_id...")
        billed_bytes = self._bq_repository.load_costs_table(self.run_id)
        check_billed_bytes = self._bq_repository.check_costs_table()
        logger.info(f"Loaded into BigQuery and billed for {format_bytes_pretty(billed_bytes)}.")

        return billed_bytes + check_billed_bytes
