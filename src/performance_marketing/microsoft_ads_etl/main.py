from microsoft_ads_api_client import MicrosoftAdsApiClient
from microsoft_ads_api_model import MICROSOFT_ADS_SECRET_ID, MicrosoftAdsApiConfig
from microsoft_ads_etl_bq_repo import MicrosoftAdsBqRepository
from microsoft_ads_etl_etl import MicrosoftAdsEtl
from microsoft_ads_etl_model import MicrosoftAdsEtlConfig

from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.secret_manager_client import get_secret

microsoft_ads_etl_config = MicrosoftAdsEtlConfig()

MICROSOFT_ADS_SECRET = get_secret(secret_id=MICROSOFT_ADS_SECRET_ID, project_id=microsoft_ads_etl_config.project_id)
microsoft_ads_api_config = MicrosoftAdsApiConfig.from_json(MICROSOFT_ADS_SECRET)
microsoft_ads_api_client = MicrosoftAdsApiClient(config=microsoft_ads_api_config)

bq_repo = MicrosoftAdsBqRepository()
raw_data_lake_repo = DataLakeRepository(microsoft_ads_etl_config.raw_bucket)

logger = setup_logger(microsoft_ads_etl_config)


def run() -> None:
    """
    Processing entry point, called by scheduler.
    """
    logger.info(
        f"Running '{MicrosoftAdsEtl.__name__}' on '{microsoft_ads_etl_config.env}' with following config: "
        f"{microsoft_ads_etl_config}..."
    )

    etl = MicrosoftAdsEtl(
        config=microsoft_ads_etl_config,
        microsoft_ads_api_client=microsoft_ads_api_client,
        raw_data_lake_repository=raw_data_lake_repo,
        bq_repository=bq_repo,
    )

    etl.run()


if __name__ == "__main__":
    """Execute locally to append yesterday's Microsoft Ads cost report to BQ (if not already there)"""
    run()
