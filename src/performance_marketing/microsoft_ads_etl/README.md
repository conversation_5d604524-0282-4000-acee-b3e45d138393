# Microsoft Ads ETL Pipeline

This document provides an overview of the Microsoft Ads ETL (Extract, Transform, Load) pipeline.

## Overview

The ETL pipeline fetches yesterday's hourly performance reports from the Microsoft Ads API, validates the data, and loads it into BigQuery. This data provides insights into advertising spend and performance.

Runs on independent Google Schedule at 6:30am CEST.

## Configuration

Credentials are required and should be stored as a JSON object in Google Secret Manager.

### Secret Configuration

The Google Secret should be named `microsoft-ads-etl`. The JSON structure:

```json
{
  "tenant_id": "YOUR_TENANT_ID",
  "client_id": "YOUR_CLIENT_ID",
  "client_secret": "YOUR_CLIENT_SECRET",
  "refresh_token": "YOUR_REFRESH_TOKEN",
  "developer_token": "YOUR_DEVELOPER_TOKEN",
  "customer_id": "YOUR_CUSTOMER_ID",
  "account_id": "YOUR_ACCOUNT_ID"
}
```

## How to Run

The pipeline is designed for automated workflows but can be run manually for testing or ad-hoc report retrieval.

To run the pipeline locally:

```bash
python main.py
```

Ensure Google Cloud authentication is set up so the pipeline can access Google Secret Manager.

## Manual Scripts

- **get_new_token.py**: Obtain a new Microsoft Ads refresh token via OAuth2 code flow. Follow the interactive prompts.
  - Should only be necessary if the pipeline is broken because of invalid refresh token!, Usage :
      ```bash
      python microsoft_ads_api_get_refresh_token.py
      ```

## Report Schema

The ETL expects the following columns (see `microsoft_ads_etl_model.py`):
- `CampaignName` (string)
- `TimePeriod` (int)
- `Impressions` (int)
- `Spend` (float)

## Notes
- All API calls and report polling are handled via the ETL client (`microsoft_ads_api_client.py`).
- For advanced usage or debugging, see the code and docstrings in each script.
