from dataclasses import dataclass
from enum import StrEnum
from typing import Any, Optional

from dataclasses_json import DataClassJsonMixin
from pandas import Float32Dtype, Int8Dtype, Int32Dtype, StringDtype

from common.config import Config
from common.pandas_utils import BaseDataFrameSchema, DataFrameSchema

MICROSOFT_ADS_SECRET_ID: str = "microsoft-ads-etl"


@dataclass(frozen=True, kw_only=True)
class MicrosoftAdsApiConfig(Config):
    """
    Configuration for the Microsoft Ads API client.

    The process to get the following ids and tokens is:
    - For tenant id and client id, IT created a new app registration in Azure AD and provided the credentials.
    - The developer token is taken from a central user account, now using Felix K. token.
    - The customer id (cid) and account id (aid) are taken from the URL of the Microsoft Ads account:
      https://ads.microsoft.com/cc/Settings/DevSettings?aid=*********&cid=*********&uid=********
    - To get the refresh token we need to use the dedicated helper file microsoft_ads_api_get_refresh_token.py,
      run the __main__() using the ids (you can find the ids in the relevant Google Secret Manager),
    - It will open a browser window, ask for IT approval, and when you get it, you can copy the code from the URL
      and paste it back to the function run.
    - Copy the refresh token and update the secrets.

    All these credentials are stored and retrieved by the secret manager.
    """

    tenant_id: str
    client_id: str
    client_secret: str
    refresh_token: str
    developer_token: str
    customer_id: str
    account_id: str


class MicrosoftAdsReportSchema(BaseDataFrameSchema):
    """
    Schema for requesting Microsoft Ads Performance Report from the API.
    """

    CAMPAIGN_NAME: str = "CampaignName"
    IMPRESSIONS: str = "Impressions"
    SPEND: str = "Spend"
    TIME_PERIOD: str = "TimePeriod"

    SCHEMA = DataFrameSchema(
        data_types={
            CAMPAIGN_NAME: StringDtype(),
            IMPRESSIONS: Int32Dtype(),
            SPEND: Float32Dtype(),
            TIME_PERIOD: Int8Dtype(),
        }
    )


@dataclass(frozen=True, kw_only=True)
class AccessTokenRequestPayload(DataClassJsonMixin):
    """Token request payload for Microsoft Ads API authentication."""

    client_id: str
    client_secret: str
    refresh_token: str
    grant_type: str = "refresh_token"
    scope: str = "https://ads.microsoft.com/msads.manage offline_access"


@dataclass(frozen=True)
class AccessTokenResponse(DataClassJsonMixin):
    """Access token response for Microsoft Ads API."""

    access_token: str
    token_type: Optional[str] = None
    scope: Optional[str] = None
    expires_in: Optional[int] = None
    ext_expires_in: Optional[int] = None


class ReportRequestConstants(StrEnum):
    """Configuration constants for Microsoft Ads report request API fields."""

    SCOPE = "Scope"
    ACCOUNT_IDS = "AccountIds"
    TIME = "Time"
    PREDEFINED_TIME = "PredefinedTime"
    COLUMNS = "Columns"
    TYPE = "Type"
    REPORT_NAME = "ReportName"
    FORMAT = "Format"
    AGGREGATION = "Aggregation"

    PREDEFINED_TIME_YESTERDAY = "Yesterday"
    REPORT_FORMAT_CSV = "Csv"
    REPORT_TYPE_CAMPAIGN_PERFORMANCE = "CampaignPerformanceReportRequest"
    REPORT_AGGREGATION_HOUR_OF_DAY = "HourOfDay"

    REPORT_REQUEST = "ReportRequest"


@dataclass(frozen=True)
class ReportRequest:
    """Microsoft Ads report request payload."""

    account_ids: list[str]
    columns: list[str]
    predefined_time: str = ReportRequestConstants.PREDEFINED_TIME_YESTERDAY
    report_type: str = ReportRequestConstants.REPORT_TYPE_CAMPAIGN_PERFORMANCE
    report_name: str = "Microsoft Ads Performance Report"
    format: str = ReportRequestConstants.REPORT_FORMAT_CSV
    aggregation: str = ReportRequestConstants.REPORT_AGGREGATION_HOUR_OF_DAY

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary format for API requests."""
        return {
            ReportRequestConstants.SCOPE: {ReportRequestConstants.ACCOUNT_IDS: self.account_ids},
            ReportRequestConstants.TIME: {ReportRequestConstants.PREDEFINED_TIME: self.predefined_time},
            ReportRequestConstants.COLUMNS: self.columns,
            ReportRequestConstants.TYPE: self.report_type,
            ReportRequestConstants.REPORT_NAME: self.report_name,
            ReportRequestConstants.FORMAT: self.format,
            ReportRequestConstants.AGGREGATION: self.aggregation,
        }


@dataclass(frozen=True)
class ReportRequestResponse(DataClassJsonMixin):
    ReportRequestId: str


class HeaderKey(StrEnum):
    """Header keys for Microsoft Ads API requests."""

    AUTHORIZATION = "Authorization"
    CONTENT_TYPE = "Content-Type"
    DEVELOPER_TOKEN = "DeveloperToken"
    CUSTOMER_ID = "CustomerId"
    CUSTOMER_ACCOUNT_ID = "CustomerAccountId"


@dataclass(frozen=True)
class RequestHeaders:
    """Request headers for Microsoft Ads API."""

    access_token: str
    developer_token: str
    customer_id: str
    customer_account_id: str

    def to_dict(self) -> dict[str, str]:
        """Convert to dictionary format for API requests."""
        return {
            HeaderKey.AUTHORIZATION: f"Bearer {self.access_token}",
            HeaderKey.CONTENT_TYPE: "application/json",
            HeaderKey.DEVELOPER_TOKEN: self.developer_token,
            HeaderKey.CUSTOMER_ID: self.customer_id,
            HeaderKey.CUSTOMER_ACCOUNT_ID: self.customer_account_id,
        }


@dataclass(frozen=True)
class DownloadReportResponse:
    """Download report response for Microsoft Ads API."""

    header_key: str = "Content-Type"
    header_value: str = "application/zip"
    content_starts_with: bytes = b"PK\x03\x04"


class ReportStatus(StrEnum):
    """Report status enumeration for Microsoft Ads API."""

    SUCCESS = "Success"
    ERROR = "Error"
    PENDING = "Pending"


@dataclass(frozen=True)
class ReportRequestStatus(DataClassJsonMixin):
    Status: ReportStatus
    ReportDownloadUrl: str


@dataclass(frozen=True)
class PollReportResponse(DataClassJsonMixin):
    ReportRequestStatus: ReportRequestStatus
