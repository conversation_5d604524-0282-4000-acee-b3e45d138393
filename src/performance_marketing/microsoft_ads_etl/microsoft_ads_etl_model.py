import tempfile
from dataclasses import dataclass, field
from pathlib import Path

from pandas import Float32Dtype, Int8Dtype, Int32Dtype, StringDtype

from common.config import Config
from common.pandas_utils import BaseDataFrameSchema, DataFrameSchema


@dataclass(frozen=True)
class MicrosoftAdsEtlConfig(Config):
    file_prefix: str = "microsoft_ads_costs"
    data_lake_base_path: str = "microsoft_ads/costs"
    local_temp_path: Path = field(default_factory=lambda: Path(tempfile.mkdtemp()))


class MicrosoftAdsReportEtlSchema(BaseDataFrameSchema):
    """
    Schema for ETL transformation of Microsoft Ads Performance Report.
    """

    CAMPAIGN_NAME: str = "campaign_name"
    IMPRESSIONS: str = "impressions"
    SPEND: str = "spend"
    HOUR_OF_DAY: str = "hour_of_day"
    UPDATED_AT: str = "updated_at"

    SCHEMA = DataFrameSchema(
        date_columns=[UPDATED_AT],
        data_types={
            CAMPAIGN_NAME: StringDtype(),
            IMPRESSIONS: Int32Dtype(),
            SPEND: Float32Dtype(),
            HOUR_OF_DAY: Int8Dtype(),
        },
    )
