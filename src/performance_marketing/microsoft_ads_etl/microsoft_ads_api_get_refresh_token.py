import time
import urllib.parse

import requests

from common.logger import DEFAULT_CONFIG, setup_logger

logger = setup_logger(DEFAULT_CONFIG)


class MicrosoftAdsRefreshTokenGenerator:
    """
    A class to generate a new refresh token for the Microsoft Ads API.

    This is a manual, interactive process to get a new refresh token.
    It is used once to set up this app.
    No need to rerun this unless the function has failed because of invalid refresh token.

    The tenant id and client id required need to be provided from a created app registration in Azure AD.
    You need to ask IT for it.

    The process is:
    1. Run this file and follow the instructions.
    2. Go to the URL and ask for permission.
        - Once you ask for permission, close the browser and stop the function until you get approval from IT.
        - When IT approves, you can run the function again and continue to next steps.
    3. Copy the code from the URL.
    4. Run the function and paste the code.
    5. Copy the refresh token and update the secrets.
    """

    redirect_uri = "http://localhost:8080/callback"
    scope = "https://ads.microsoft.com/msads.manage offline_access"

    def __init__(self, tenant_id: str, client_id: str, client_secret: str):
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret

    def run_interactive_process(self) -> None:
        """
        Run interactive process to obtain refresh token.

        :raises ValueError: if no authorization code entered
        """
        auth_url = self._get_auth_url()
        # Prints instead of logging since this is local, manual interactive process and logger isn't configured.
        logger.info(f"Go to this URL: {auth_url}")
        logger.info("After authorization, you will be redirected to a blank page.")
        logger.info("Copy the 'code' value from the URL and paste it below.")
        time.sleep(1)  # to make sure input doesn't appear before the logs
        auth_code = input("Enter the authorization code: ")

        if not auth_code:
            raise ValueError("No authorization code entered. Aborting.")

        self._get_refresh_token(auth_code)

    def _get_auth_url(self) -> str:
        """
        Build authorization URL for refresh token.

        :returns: authorization URL
        """
        auth_url_params = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "response_type": "code",
            "redirect_uri": self.redirect_uri,
            "scope": self.scope,
            "response_mode": "query",
        }
        return (
            f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/"
            f"authorize?{urllib.parse.urlencode(auth_url_params)}"
        )

    def _construct_token_payload(self, auth_code: str) -> dict[str, str]:
        """
        Build token request payload from auth code.

        :param auth_code: OAuth2 authorization code
        :returns: token request payload dict
        """
        return {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "code": auth_code,
            "grant_type": "authorization_code",
            "redirect_uri": self.redirect_uri,
            "scope": self.scope,
        }

    def _get_refresh_token(self, auth_code: str) -> str:
        """
        Get refresh token from authorization code.

        :param auth_code: OAuth2 authorization code
        :returns: refresh token string
        :raises requests.exceptions.HTTPError: on HTTP error
        """
        # Construct the URL and payload for the token request.
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        payload = self._construct_token_payload(auth_code)
        response = requests.post(url, data=payload)
        response.raise_for_status()

        # Get the new refresh token.
        refresh_token = response.json()["refresh_token"]
        logger.info(f"✅ Success! New Refresh Token: {refresh_token}")
        return refresh_token


if __name__ == "__main__":
    MicrosoftAdsRefreshTokenGenerator(
        tenant_id="",
        client_id="",
        client_secret="",
    ).run_interactive_process()
