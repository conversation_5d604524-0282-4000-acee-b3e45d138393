import io
import json
import time
import zipfile

import requests
from microsoft_ads_api_model import (
    AccessTokenRequestPayload,
    AccessTokenResponse,
    DownloadReportResponse,
    MicrosoftAdsApiConfig,
    MicrosoftAdsReportSchema,
    PollReportResponse,
    ReportRequest,
    ReportRequestConstants,
    ReportRequestResponse,
    ReportRequestStatus,
    ReportStatus,
    RequestHeaders,
)
from pandas import read_csv

from common.logger import get_logger
from common.pandas_utils import DataFrameWithSchema

logger = get_logger(__name__)


class MicrosoftAdsApiError(ValueError):
    """Base exception for Microsoft Ads API errors."""


class MicrosoftAdsApiClient:
    """
    A client for interacting with the Microsoft Ads API to fetch performance reports.
    """

    report_url = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Submit"
    poll_url = "https://reporting.api.bingads.microsoft.com/Reporting/v13/GenerateReport/Poll"

    # Polling configuration
    MAX_POLL_ITERATIONS = 12  # 12 iterations * 5 seconds = 1 minute max
    POLL_INTERVAL_SECONDS = 5

    def __init__(self, config: MicrosoftAdsApiConfig):
        """
        Initializes the MicrosoftAdsApiClient.
        The access token is retrieved from the refresh token and stored in the class.
        This happens once every time the class is initialized.

        :param config: The configuration for the Microsoft Ads API.
        """
        self._config = config

    def get_performance_report(self) -> DataFrameWithSchema:
        """
        Only public method of this class.
        Fetch performance report as DataFrame.

        :returns: DataFrame
        :raises MicrosoftAdsApiError: on report request failure
        """
        logger.info("Getting access token...")
        access_token = self.get_access_token()

        logger.info("Submitting report request...")
        report_response = self.submit_report_request(access_token=access_token)

        logger.info(f"Report request submitted successfully. Request ID: {report_response.ReportRequestId}")
        return self.poll_and_download_report(report_response=report_response, access_token=access_token)

    def get_access_token(self) -> str:
        """
        Get access token from refresh token.

        :returns: access token string
        :raises MicrosoftAdsApiError: on authentication failure
        """
        logger.info("Getting new access token from refresh token...")
        url = f"https://login.microsoftonline.com/{self._config.tenant_id}/oauth2/v2.0/token"
        payload = AccessTokenRequestPayload(
            client_id=self._config.client_id,
            client_secret=self._config.client_secret,
            refresh_token=self._config.refresh_token,
        )
        response = requests.post(url, data=payload.to_dict())
        response.raise_for_status()

        get_access_token = AccessTokenResponse.from_dict(response.json())
        access_token = get_access_token.access_token
        logger.info("Access token retrieved successfully.")
        return access_token

    def submit_report_request(self, access_token: str) -> ReportRequestResponse:
        """
        Submit report request to API.

        :param access_token: OAuth2 access token
        :returns: ReportRequestResponse object
        """
        headers = self._get_request_headers(access_token=access_token)
        report_request = ReportRequest(
            account_ids=[self._config.account_id],
            columns=MicrosoftAdsReportSchema.SCHEMA.columns,
        )

        response = requests.post(
            self.report_url,
            headers=headers.to_dict(),
            data=json.dumps({ReportRequestConstants.REPORT_REQUEST: report_request.to_dict()}),
        )
        response.raise_for_status()

        return ReportRequestResponse.from_dict(response.json())

    def poll_and_download_report(
        self, report_response: ReportRequestResponse, access_token: str
    ) -> DataFrameWithSchema:
        """
        Poll and download report as DataFrame.

        :param report_response: ReportRequestResponse object
        :param access_token: OAuth2 access token
        :returns: DataFrame
        """
        report_status = self.poll_report_status(report_response=report_response, access_token=access_token)

        logger.info(f"Report is ready. Downloading from: {report_status.ReportDownloadUrl}")
        content_bytes = self.download_report(download_url=report_status.ReportDownloadUrl)
        return self.parse_zip_report(content=content_bytes)

    def poll_report_status(self, report_response: ReportRequestResponse, access_token: str) -> ReportRequestStatus:
        """
        Polls for the report status and returns the final status dict.
        https://learn.microsoft.com/en-us/advertising/reporting-service/reportrequeststatustype?view=bingads-13

        :param report_response: ReportRequestResponse object
        :param access_token: OAuth2 access token
        :returns: ReportRequestStatus object
        :raises MicrosoftAdsApiError: on error status or timeout
        """
        headers = self._get_request_headers(access_token=access_token)

        for iteration in range(self.MAX_POLL_ITERATIONS):
            logger.info(f"Polling for report status (iteration {iteration + 1}/{self.MAX_POLL_ITERATIONS})...")
            response = requests.post(self.poll_url, headers=headers.to_dict(), json=report_response.to_dict())
            response.raise_for_status()
            poll_response = PollReportResponse.from_dict(response.json())
            report_status = poll_response.ReportRequestStatus

            if report_status.Status == ReportStatus.SUCCESS:
                logger.info("Report generation completed successfully.")
                return report_status
            elif report_status.Status == ReportStatus.ERROR:
                raise MicrosoftAdsApiError(f"Report generation failed. Status: {report_status}")
            elif report_status.Status == ReportStatus.PENDING:
                if iteration < self.MAX_POLL_ITERATIONS - 1:  # Don't sleep on last iteration
                    logger.info(
                        f"Report still pending. Waiting {self.POLL_INTERVAL_SECONDS} seconds before next poll..."
                    )
                    time.sleep(self.POLL_INTERVAL_SECONDS)

        raise MicrosoftAdsApiError(
            f"Report polling timed out after {self.MAX_POLL_ITERATIONS} iterations "
            f"of {self.POLL_INTERVAL_SECONDS} seconds."
        )

    @staticmethod
    def download_report(download_url: str) -> bytes:
        """
        Download report from URL.

        :param download_url: report zip URL
        :returns: bytes
        """
        report_response = requests.get(download_url)
        report_response.raise_for_status()

        # Validate response content
        content = report_response.content
        if not content:
            raise MicrosoftAdsApiError("Download response is empty")

        # Validate content type
        content_type = report_response.headers.get(DownloadReportResponse.header_key, "")
        if DownloadReportResponse.header_value not in content_type.lower():
            raise MicrosoftAdsApiError(f"Expected ZIP file but got Content-Type: {content_type}")

        # Validate ZIP file magic bytes
        # https://en.wikipedia.org/wiki/ZIP_(file_format)
        if not content.startswith(DownloadReportResponse.content_starts_with):
            raise MicrosoftAdsApiError("Download response is not a valid ZIP file")

        return content

    @staticmethod
    def parse_zip_report(content: bytes) -> DataFrameWithSchema:
        """
        Parse ZIP report file and return DataFrame.

        :param content: ZIP file content
        :returns: DataFrame
        """
        with zipfile.ZipFile(io.BytesIO(content)) as zip_file:
            file_list = zip_file.namelist()
            if not file_list or not file_list[0]:
                raise MicrosoftAdsApiError("ZIP file is empty")

            with zip_file.open(file_list[0]) as file:
                df = read_csv(
                    file, skiprows=10, skipfooter=1, engine="python", dtype=MicrosoftAdsReportSchema.SCHEMA.data_types
                )

        return DataFrameWithSchema(dataframe=df, schema=MicrosoftAdsReportSchema.SCHEMA)

    def _get_request_headers(self, access_token: str) -> RequestHeaders:
        """
        Build API request headers. (used twice so internal method for DRY)

        :param access_token: OAuth2 access token
        :returns: request headers
        """
        return RequestHeaders(
            access_token=access_token,
            developer_token=self._config.developer_token,
            customer_id=self._config.customer_id,
            customer_account_id=self._config.account_id,
        )
