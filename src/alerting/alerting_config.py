from dataclasses import dataclass
from pathlib import Path

from dataclasses_json import DataClassJsonMixin

ALERTING_ROOT_DIR = Path(__file__).resolve().parent


def get_test_function_incident() -> str:
    """Returns test function incident for local testing"""

    with open(Path(ALERTING_ROOT_DIR, "resources/test_function_incident.txt"), "r") as f:
        function_incident = f.read()

    return function_incident


def get_test_job_incident() -> str:
    """Returns test job incident for local testing"""

    with open(Path(ALERTING_ROOT_DIR, "resources/test_job_incident.txt"), "r") as f:
        job_incident = f.read()

    return job_incident


def get_test_service_incident() -> str:
    """Returns test cloud run service incident for local testing"""

    with open(Path(ALERTING_ROOT_DIR, "resources/test_service_incident.txt"), "r") as f:
        incident = f.read()

    return incident


@dataclass(frozen=True)
class DataEngineeringLabels(DataClassJsonMixin):
    """
    Data Engineering resource labels
    - owner: owner of the resource
    - backup: backup owner of the resource
    - criticality: p1 or p2, determining the channel where to post the alert message
    - alerting_enabled: whether to post the alert message
    """

    owner: str
    backup: str
    criticality: str
    alerting_enabled: bool
