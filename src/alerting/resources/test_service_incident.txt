{\n  "incident": {\n    "condition": {\n      "conditionThreshold": {\n        "aggregations": [\n          {\n            "alignmentPeriod": "60s",\n            "perSeriesAligner": "ALIGN_COUNT"\n          }\n        ],\n        "comparison": "COMPARISON_GT",\n        "duration": "0s",\n        "filter": "resource.type=\\"cloud_run_revision\\" AND metric.type=\\"logging.googleapis.com/user/cloud-run-revision-warning\\"",\n        "trigger": {\n          "count": 1\n        }\n      },\n      "displayName": "cloud-run-revision-warning-alert-condition",\n      "name": "projects/test-project/alertPolicies/3802643546433321252/conditions/3802643546433320379"\n    },\n    "condition_name": "cloud-run-revision-warning-alert-condition",\n    "ended_at": null,\n    "incident_id": "service-incident-123",\n    "metadata": {\n      "system_labels": {},\n      "user_labels": {}\n    },\n    "metric": {\n      "displayName": "logging/user/cloud-run-revision-warning",\n      "labels": {\n        "log": "run.googleapis.com/stderr"\n      },\n      "type": "logging.googleapis.com/user/cloud-run-revision-warning"\n    },\n    "observed_value": "1.000",\n    "policy_name": "cloud-run-revision-warning-alert",\n    "resource": {\n      "labels": {\n        "configuration_name": "alerting-function-check",\n        "location": "europe-west3",\n        "project_id": "test-project",\n        "revision_name": "alerting-function-check-00001-245",\n        "service_name": "alerting-function-check"\n      },\n      "type": "cloud_run_revision"\n    },\n    "resource_id": "",\n    "resource_name": "test-project Cloud Run Revision labels {project_id=test-project, service_name=alerting-function-check, revision_name=alerting-function-check-00001-245, location=europe-west3, configuration_name=alerting-function-check}",\n    "resource_type_display_name": "Cloud Run Revision",\n    "scoping_project_id": "test-project",\n    "scoping_project_number": 205674098492,\n    "severity": "Warning",\n    "started_at": 1749103281,\n    "state": "open",\n    "summary": "logging/user/cloud-run-revision-warning for test-project Cloud Run Revision labels {project_id=test-project, service_name=alerting-function-check, revision_name=alerting-function-check-00001-245, location=europe-west3, configuration_name=alerting-function-check} with metric labels {log=run.googleapis.com/stderr} is above the threshold of 0.000 with a value of 1.000.",\n    "threshold_value": "0",\n    "url": "https://console.cloud.google.com"\n  },\n  "version": "1.2"\n}
