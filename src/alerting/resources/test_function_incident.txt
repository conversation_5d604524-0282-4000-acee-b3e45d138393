{\n  "incident": {\n    "condition": {\n      "conditionThreshold": {\n        "aggregations": [\n          {\n            "alignmentPeriod": "60s",\n            "perSeriesAligner": "ALIGN_COUNT"\n          }\n        ],\n        "comparison": "COMPARISON_GT",\n        "duration": "0s",\n        "filter": "resource.type=\\"cloud_run_revision\\" AND metric.type=\\"logging.googleapis.com/user/cloud-run-revision-error\\"",\n        "trigger": {\n          "count": 1\n        }\n      },\n      "displayName": "cloud-run-revision-error-alert-condition",\n      "name": "projects/test-project/alertPolicies/11369967669365270000/conditions/11369967669365271659"\n    },\n    "condition_name": "cloud-run-revision-error-alert-condition",\n    "ended_at": 1730194430,\n    "incident_id": "function-incident-123",\n    "metadata": {\n      "system_labels": {},\n      "user_labels": {}\n    },\n    "metric": {\n      "displayName": "logging/user/cloud-revision-error",\n      "labels": {\n        "log": "run.googleapis.com/varlog/system"\n      },\n      "type": "logging.googleapis.com/user/cloud-revision-error"\n    },\n    "observed_value": "1.000",\n    "policy_name": "cloud-revision-error-alert",\n    "resource": {\n      "labels": {\n        "service_name": "alerting-function-check",\n        "location": "europe-west3",\n        "project_id": "test-project"\n      },\n      "type": "cloud_run_revision"\n    },\n    "resource_id": "",\n    "resource_name": "test-project Cloud Run Function labels {project_id=test-project, service_name=alerting-function-check, location=europe-west3}",\n    "resource_type_display_name": "Cloud Run Revision",\n    "scoping_project_id": "test-project",\n    "scoping_project_number": 205674098492,\n    "severity": "Error",\n    "started_at": 1730192550,\n    "state": "closed",\n    "summary": "logging/user/cloud-run-revision-error for refb-analytics-staging Cloud Run Revision labels {project_id=refb-analytics-staging, service_name=alerting-error, revision_name=alerting-error-00062-heq, location=europe-west3, configuration_name=alerting-error} with metric labels {log=run.googleapis.com/stdout} is above the threshold of 0.000 with a value of 1.000.",\n    "threshold_value": "0",\n    "url": "https://console.cloud.google.com"\n  },\n  "version": "1.2"\n}
