import json
from datetime import UTC, datetime
from functools import cached_property
from os import environ
from typing import Op<PERSON>

from alerting_config import DataEngineering<PERSON>abels
from alerting_shared.notifications import (
    NotificationMessage,
    create_notification_markdown,
)
from cloudevents.abstract import CloudEvent
from incident_model import Alert

from common.cloud_events import get_event_message
from common.cloud_logging_client.client import CloudLoggingClient
from common.cloud_logging_client.model import (
    CloudRunFunction<PERSON>og<PERSON>ilter,
    CloudRunJobLogFilter,
    CloudRunResourceType,
    LogEntry,
)
from common.cloud_run_client.base_client import CloudRunResource
from common.cloud_run_client.function_client import CloudRunFunctionClient
from common.cloud_run_client.job_client import CloudRun<PERSON><PERSON>Client
from common.cloud_run_client.model import CloudRunJobExecution
from common.cloud_run_client.service_client import CloudRunServiceClient
from common.config import AlertCriticality, MattermostAlertingConfig
from common.logger import get_logger
from common.mattermost_client import Mattermost<PERSON>lient, MattermostMessage
from common.utils import string_to_bool

logger = get_logger()


class IncidentAlerting:

    def __init__(
        self,
        config: MattermostAlertingConfig,
        mattermost_client: MattermostClient,
        cloud_run_job_client: CloudRunJobClient,
        cloud_function_client: CloudRunFunctionClient,
        cloud_run_service_client: CloudRunServiceClient,
        cloud_logging_client: CloudLoggingClient,
    ):
        self._config = config
        self._mattermost_client = mattermost_client
        self._cloud_run_job_client = cloud_run_job_client
        self._cloud_function_client = cloud_function_client
        self._cloud_run_service_client = cloud_run_service_client
        self._cloud_logging_client = cloud_logging_client

    @cached_property
    def function_name(self) -> str:
        """
        Returns the name of Cloud Function running this code with a fallback to 'alerting-error' default name.
        Ref. https://cloud.google.com/functions/docs/configuring/env-var#runtime_environment_variables_set_automatically
        """

        return environ.get("K_SERVICE", "alerting-error")

    def run(self, event: CloudEvent) -> None:
        """
        Runs all the steps of the alerting process:
        - parses incident's alert,
        - describes Cloud Run resource (job for now, function to be implemented),
        - describes last completed execution for Cloud Run resource,
        - transforms incident into notification message,
        - transforms notification message into MM alerting markdown,
        - sends MM message.
        """
        alert = self.parse(event)
        resource_name = alert.incident.resource.name
        resource_type = alert.incident.resource.type

        if resource_name == self.function_name:
            logger.info(f"Ignoring alerts from '{self.function_name}' Cloud Function to prevent recursive calls!")
            return

        execution = None

        match resource_type:
            case CloudRunResourceType.JOB:
                resource = self._cloud_run_job_client.describe_resource(resource_name)
                execution = self._cloud_run_job_client.get_execution(
                    resource.name, resource.latest_created_execution.name
                )
            case CloudRunResourceType.REVISION:
                resource = self._cloud_run_service_client.describe_resource(resource_name)
                if not resource:
                    resource = self._cloud_function_client.describe_resource(resource_name)

            case _:
                raise ValueError(f"Not implemented resource '{resource_type} type!'")

        if not resource:
            logger.info(f"Function stopped due to non-existing Cloud Run '{resource_name}' resource.")
            return

        labels = self.get_labels(resource)
        top_3_log_entries = self._get_log_entries(
            alert=alert, max_results=3, execution=execution, resource_type=resource_type
        )

        notification_message = self.to_notification_message(alert, resource, labels, execution, top_3_log_entries)
        notification_markdown = create_notification_markdown(notification_message)

        channel = self._config.get_channel(
            severity=notification_message.severity, criticality=notification_message.criticality
        )
        mattermost_message = MattermostMessage(text=notification_markdown, channel=channel)

        self.send_message(mattermost_message, labels)

    @staticmethod
    def parse(event: CloudEvent) -> Alert:
        """
        Parses raw incident message by replacing some not needed signs
        :param event: cloud event
        :returns: MonitoringIncident object representation of parsed message
        """
        message = get_event_message(event)

        logger.info(f"Parsing incident {message=}...")
        # To avoid error: Expecting property name enclosed in double quotes
        parsed = message.replace("\\n", "").replace('\\\\"', "'")
        parsed_dict = json.loads(parsed)

        return Alert.from_dict(parsed_dict)

    @staticmethod
    def to_notification_message(
        alert: Alert,
        resource: CloudRunResource,
        labels: DataEngineeringLabels,
        execution: Optional[CloudRunJobExecution] = None,
        log_entries: Optional[list[LogEntry]] = None,
    ) -> NotificationMessage:
        """
        Transforms incident alert into NotificationMessage
        :param alert: object describing alert
        :param resource: object describing resource
        :param execution: object describing job's execution, empty for function
        :param labels: Data Engineering team labels
        :param log_entries: log entries
        :returns: NotificationMessage object
        """

        logger.info("Transforming incident's alert into notification message...")

        return NotificationMessage(
            started_at=datetime.fromtimestamp(alert.incident.started_at, tz=UTC),
            owner=labels.owner,
            backup=labels.backup,
            criticality=labels.criticality,
            resource_name=alert.incident.resource.name,
            resource_url=resource.uri,
            incident_url=alert.incident.url,
            logs_url=execution.log_uri if execution else resource.log_uri,
            log_entries=log_entries if log_entries else [],
            severity=alert.incident.severity.upper(),
        )

    @staticmethod
    def get_labels(resource: CloudRunResource) -> DataEngineeringLabels:
        """
        Get Data Engineering labels if specified, otherwise takes defaults.
        :param resource: object describing resource with labels
        :returns: DataEngineeringLabels object
        """

        owner = resource.labels.get("owner", "data_engineering")
        backup_owner = resource.labels.get("backup_owner", "N/A")
        criticality = resource.labels.get("criticality", AlertCriticality.P2)
        alerting_enabled = string_to_bool(resource.labels.get("alerting_enabled", False))

        return DataEngineeringLabels(
            owner=owner, backup=backup_owner, criticality=criticality, alerting_enabled=alerting_enabled
        )

    def send_message(self, mattermost_message: MattermostMessage, labels: DataEngineeringLabels) -> None:
        """
        Sends Mattermost message based on the labels `alerting_enabled` field
        :param mattermost_message: message
        :param labels: Data Engineering team labels
        """

        if labels.alerting_enabled:
            logger.info("Sending Mattermost message...")
            self._mattermost_client.send_message(mattermost_message)
        else:
            logger.info("Alerting disabled!")

    def _get_log_entries(
        self,
        alert: Alert,
        max_results: int,
        resource_type: CloudRunResourceType,
        execution: Optional[CloudRunJobExecution] = None,
    ) -> list[LogEntry]:
        """
        Gets GCP Logs entries for the resource that triggered the incident's alert.
        Filters the logs based on the execution name.
        :param alert: Alert object
        :param execution: object describing job's execution, none for function
        :param max_results: maximum number of log entries
        :returns: list of log entries
        """
        match resource_type:
            case CloudRunResourceType.JOB:
                log_filter = CloudRunJobLogFilter(
                    severity=alert.incident.severity.upper(),
                    job_name=alert.incident.resource.name,
                    job_execution_name=execution.short_name if execution else "",
                    max_results=max_results,
                )
            case CloudRunResourceType.REVISION:
                log_filter = CloudRunFunctionLogFilter(
                    severity=alert.incident.severity.upper(),
                    function_name=alert.incident.resource.name,
                    max_results=max_results,
                )
            case _:
                raise ValueError(f"Not implemented resource '{alert.incident.resource.type}!'")

        return self._cloud_logging_client.list_log_entries(log_filter)
