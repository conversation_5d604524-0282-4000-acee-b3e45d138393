import json
from dataclasses import dataclass, field
from datetime import datetime

import pytz
from dataclasses_json import DataClassJsonMixin
from tabulate2 import tabulate

from common.cloud_logging_client.model import LogEntry
from common.consts import AlertCriticality, AlertSeverity

TIME_ZONE = pytz.timezone("Europe/Vienna")


@dataclass(frozen=True)
class NotificationMessage(DataClassJsonMixin):
    """
    Common notification message for all cloud resources like Cloud Run Jobs or Functions.

    :HEADERS: Markdown table headers

    :started_at: Resource start date
    :resource_name: Resource name
    :resource_url: GCP console url to the resource page
    :incident_url: GCP console url to the incident page
    :logs_url: GCP console url to the logs page
    :owner: owner of the resource; for Mattermost use @username syntax
    :backup: backup person; for Mattermost use @username syntax
    :severity: incident severity
    :criticality: level of criticality, defaults to `p2`
    :log_entries: list of erroneous log entries, defaults to empty list
    """

    HEADERS = ["Start Date", "Resource Name", "Resource URL", "Incident URL", "Logs URL", "Owner", "Backup"]

    started_at: datetime
    resource_name: str
    resource_url: str
    incident_url: str
    logs_url: str
    owner: str
    backup: str
    severity: str
    criticality: str = str(AlertCriticality.P2)
    log_entries: list[LogEntry] = field(default_factory=lambda: [])


def create_notification_markdown(message: NotificationMessage) -> str:
    """
    Creates notification message in Markdown format compatible with the Mattermost.

    Note that Mattermost requires "|:---:|" for centered aligned columns:
    https://docs.mattermost.com/collaborate/format-messages.html#tables

    :param message: message to be rendered
    :returns: Markdown format string
    """
    data = [
        [
            message.started_at.astimezone(TIME_ZONE).strftime("%a, %d/%m/%y, %H:%M:%S"),
            message.resource_name,
            f"[resource]({message.resource_url})",
            f"[incident]({message.incident_url})",
            f"[logs]({message.logs_url})",
            message.owner,
            message.backup,
        ]
    ]
    markdown = tabulate(data, headers=message.HEADERS, tablefmt="pipe")

    logs = _logs_to_message(message.log_entries, message.severity.upper())

    return f"{markdown}\n{logs}"


def _logs_to_message(logs: list[LogEntry], severity: str) -> str:
    """
    Format logs as json string.

    :param logs: Cloud Log entries
    :param severity: logs severity
    :returns: MM formatted message
    """
    emoji = ":warning:" if severity == AlertSeverity.WARNING else ":x:"

    if not logs:
        return ""
    messages = []
    for log in logs:
        payload = log.payload
        result = json.dumps(payload, indent=1, sort_keys=True, check_circular=True, default=str)

        message = f"- {emoji} `{severity}` at `{log.timestamp.isoformat()}`"
        message += "\n```json\n"
        message += result
        message += "\n```"
        messages.append(message)

    return "\n".join(message for message in messages)
