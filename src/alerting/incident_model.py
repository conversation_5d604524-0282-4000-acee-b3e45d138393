from dataclasses import dataclass
from functools import cached_property

from dataclasses_json import DataClassJsonMixin

from common.cloud_logging_client.model import CloudRunResourceType


@dataclass(frozen=True)
class CloudRunResource(DataClassJsonMixin):
    location: str
    project_id: str


@dataclass(frozen=True)
class CloudRunJobResource(CloudRunResource):
    job_name: str


@dataclass(frozen=True)
class CloudRunFunctionResource(CloudRunResource):
    service_name: str


@dataclass(frozen=True)
class Resource(DataClassJsonMixin):
    """GCP Alerting incident resource"""

    labels: CloudRunJobResource | CloudRunFunctionResource
    type: CloudRunResourceType

    @cached_property
    def name(self) -> str:
        """Returns resource name that depends on the object type"""
        if isinstance(self.labels, CloudRunJobResource):
            resource_name = self.labels.job_name
        else:
            resource_name = self.labels.service_name

        return resource_name


@dataclass(frozen=True)
class Incident(DataClassJsonMixin):
    """GCP Monitoring Alerting incident"""

    incident_id: str
    severity: str
    state: str
    url: str
    started_at: int
    resource: Resource


@dataclass(frozen=True)
class Alert(DataClassJsonMixin):
    """GCP Monitoring Alert"""

    incident: Incident
