from google.cloud.sql.connector import Connector
from migrate_database import MigrateData<PERSON>
from migration_config import MigrationConfig, MigrationTarget
from migration_repository import MigratePgRepository

from common.config import DEFAULT_CONFIG
from common.logger import PASSWORD_MASKER, setup_logger
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig

pg_secret = get_secret("analytics-cloud-sql-config", DEFAULT_CONFIG.project_id)
db_config = DatabaseConfig.from_json(pg_secret)
migration_config = MigrationConfig()

logger = setup_logger(DEFAULT_CONFIG, PASSWORD_MASKER)


def get_latest_db_refresh_run_id() -> str:
    """
    Get the latest run_id from the database refresh job
    This is a special case for Postgres, as the DB refresh job updates the changeReason with every run
    To migrate only the delta, we need to reuse the changeReason from the last DB refresh run
    returns: The latest run_id
    """

    with Connector() as connector:
        pg_repo = MigratePgRepository(db_config=db_config, connector=connector)
        pipeline_name = "analytics db refresh"

        logger.info(f"Getting the latest changeReason from successful Postgres '{pipeline_name}' run...")

        refresh_run_id = pg_repo.get_latest_db_refresh_run_id(pipeline_name)
        logger.info(f"Latest DB refresh run_id: '{refresh_run_id}'")

        return refresh_run_id


if __name__ == "__main__":
    if DEFAULT_CONFIG.is_local:
        logger.info("Running locally...")
        db_config = DatabaseConfig()

    if migration_config.target == MigrationTarget.POSTGRES:
        run_id = get_latest_db_refresh_run_id()
        migration_config = MigrationConfig(db_refresh_run_id=run_id)

    migrate_db = MigrateDatabase(config=migration_config, database_config=db_config)

    migrate_db.run()
