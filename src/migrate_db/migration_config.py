from dataclasses import dataclass
from enum import StrEnum
from os import environ
from pathlib import Path
from typing import Optional

from common.config import Config
from common.utils import string_to_bool

SLEEP_SECONDS = 3


class MigrationTarget(StrEnum):
    """
    Migration targets
    """

    POSTGRES = "POSTGRES"
    BIGQUERY = "BIGQUERY"

    @classmethod
    def from_environ(cls) -> "MigrationTarget":
        if command := environ.get("MIGRATION_TARGET"):
            return cls[command]  # noqa
        return MigrationTarget.POSTGRES


class FlywayCommand(StrEnum):
    """
    Flyway commands
    Ref. https://documentation.red-gate.com/flyway/reference/commands
    """

    INFO = "info -infoOfState=Pending,Outdated -color=never"
    MIGRATE = "info -infoOfState=Pending,Outdated repair migrate -color=never"

    @classmethod
    def from_environ(cls) -> "FlywayCommand":
        if command := environ.get("FLYWAY_COMMAND"):
            return cls[command]  # noqa
        return FlywayCommand.INFO


@dataclass(frozen=True)
class MigrationConfig(Config):
    migrations_path: Path = Path(environ.get("SQL_MIGRATIONS_PATH", "../sql-pg")).resolve()
    run_repeatable_scripts: bool = string_to_bool(environ.get("RUN_REPEATABLE_SCRIPTS", False))
    db_refresh_run_id: Optional[str] = None
    flyway_command: FlywayCommand = FlywayCommand.from_environ()
    target: MigrationTarget = MigrationTarget.from_environ()

    @property
    def run_test(self) -> bool:
        """Whether to run the migration tests"""

        run_test_variable = environ.get("RUN_FLYWAY_TEST")

        if run_test_variable:
            return string_to_bool(run_test_variable)

        return not self.is_production
