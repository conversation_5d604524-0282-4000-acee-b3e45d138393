from functools import cached_property
from typing import Optional

from migration_config import (
    SLEEP_SECONDS,
    FlywayCommand,
    MigrationConfig,
    MigrationTarget,
)

from common.logger import get_logger
from common.migrate_db import FlywayBqParams, FlywayParams, FlywayPgParams, migrate_db
from common.sql_client import DatabaseConfig
from common.time_service import TimeService
from common.timing import background_task
from common.utils import get_run_id

logger = get_logger()


class MigrateDatabase:
    """
    Database migration using Flyway.
    """

    def __init__(
        self,
        config: MigrationConfig,
        database_config: DatabaseConfig,
        time_service: Optional[TimeService] = None,
    ) -> None:
        self._config = config
        self._database_config = database_config
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def runtime_placeholders(self) -> list[str]:
        """
        Flyway runtime placeholders for migration.
        Currently, only `runTest` and `changeReason` are used.
        These override the values defined in flyway.toml.
        The remaining placeholders in flyway.toml are still recognized and accepted by Flyway during migration.
        returns: list of placeholders
        """

        placeholders = [f"runTest={str(self._config.run_test).lower()}"]
        change_reason = self.change_reason

        if change_reason:
            placeholders.append(f"changeReason={change_reason}")

        logger.info(f"Flyway placeholders: '{placeholders}'")

        return placeholders

    @cached_property
    def change_reason(self) -> Optional[str]:
        """
        Change reason for migration.
        If `run_repeatable_scripts` is True, uses current timestamp.
        If `target` is POSTGRES, uses the latest run_id from the Postgres DB refresh run.
        returns: change reason or None if not applicable
        """

        change_reason = None

        if self._config.run_repeatable_scripts:
            logger.info(f"Using current timestamp '{change_reason}' as changeReason...")
            change_reason = get_run_id(self._time_service.now)
        elif MigrationTarget.from_environ() == MigrationTarget.POSTGRES and self._config.db_refresh_run_id:

            logger.info(
                f"Using the latest run_id '{self._config.db_refresh_run_id}' "
                f"from the Postgres DB refresh run as the changeReason..."
            )
            change_reason = self._config.db_refresh_run_id

        return change_reason

    def run(self) -> None:
        """
        Runs database migrations using Flyway.
        """
        flyway_params = self.get_flyway_params()
        self.migrate(flyway_params=flyway_params, flyway_command=self._config.flyway_command)

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def migrate(self, flyway_params: FlywayParams, flyway_command: FlywayCommand) -> None:
        """
        Runs database migrations using Flyway.
        Execution is handed off to a background thread to track its progress.
        :param flyway_params: see FlywayParams
        :param flyway_command: flyway command to run
        """

        logs = migrate_db(params=flyway_params, flyway_command=flyway_command)

        for log_entry in logs.splitlines():
            logger.info(log_entry)

    def get_flyway_params(self) -> FlywayParams:
        """
        Prepares Flyway parameters for migration.
        returns: FlywayParams
        """
        config_files = f"{self._config.migrations_path}/flyway.toml"
        locations = f"filesystem:{self._config.migrations_path}"
        placeholders = self.runtime_placeholders
        environment = self._config.env

        if self._config.target == MigrationTarget.POSTGRES:
            flyway_params = FlywayPgParams(
                host=self._database_config.private_ip,
                port=self._database_config.port,
                database=self._database_config.database,
                user=self._database_config.username,
                password=self._database_config.password,
                config_files=config_files,
                locations=locations,
                environment=environment,
                placeholders=placeholders,
            )
        elif self._config.target == MigrationTarget.BIGQUERY:
            flyway_params = FlywayBqParams(
                project_id=self._config.project_id,
                config_files=config_files,
                locations=locations,
                environment=environment,
                placeholders=placeholders,
            )
        else:
            raise ValueError(f"Unknown migration target: '{self._config.target}'")

        logger.info(f"Flyway params: '{flyway_params}'")

        return flyway_params
