from typing import Optional

from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError

from common.logger import get_logger
from common.sql_repository import SqlRepository

logger = get_logger()


class MigratePgRepository(SqlRepository):

    def get_latest_db_refresh_run_id(self, db_refresh_pipeline_name: str) -> Optional[str]:
        """
        Gets the latest db refresh run id from the database.
        Needed for SQL migrations change reason.
        param db_refresh_pipeline_name: Name of the db refresh pipeline
        returns:The latest run_id or None if not found
        """

        sql_query = text(
            f"""
            select
                run_id
            from
                log.pipeline_execution
            where
                pipeline_name = '{db_refresh_pipeline_name}'
            and succeeded = true
            order by
                finished desc
            limit 1;
            """
        )

        try:
            result = self._run_sql(sql_query)
            run_id = next(iter(row.run_id for row in result), None)
        except ProgrammingError as ex:
            logger.warning(f"Error getting latest '{db_refresh_pipeline_name}' run_id: {ex}")
            run_id = None

        if not run_id:
            logger.warning(f"No run_id found for latest '{db_refresh_pipeline_name}' run!")

        return run_id
