# IMAGE_VERSION is the Python image passed from the terraform module. (i.e. python:3.12-slim)
ARG IMAGE_VERSION

FROM flyway/flyway:11.8.1 as flyway
FROM $IMAGE_VERSION

COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk
ENV PATH="${PATH}:/opt/java/openjdk/bin:/flyway/"

# Validate Flyway installation
RUN echo "Validating Flyway and Java versions..." && \
    flyway --version && \
    java --version

RUN apt-get update && apt-get install -y curl unzip

# Downloads JDBC Big Query drivers and uzip the archive to the Flyway drivers directory
# Ref.: https://cloud.google.com/bigquery/docs/reference/odbc-jdbc-drivers#current_jdbc_driver
RUN curl -L https://storage.googleapis.com/simba-bq-release/jdbc/SimbaJDBCDriverforGoogleBigQuery42_1.6.3.1004.zip -o temp.zip && \
    unzip temp.zip -d /flyway/drivers/ && \
    rm temp.zip

# Set our working directory
WORKDIR /app

# Copy build files
COPY ./requirements.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements.txt

# Copy over the rest of the code
COPY . .

# Ensure python output i.e. the stdout and stderr streams are sent straight to terminal
ENV PYTHONUNBUFFERED=1
CMD ["python", "main.py"]
