import json
from dataclasses import dataclass
from datetime import date

import functions_framework
from cloudevents.abstract import CloudEvent
from dataclasses_json import DataClassJsonMixin
from ranker_repository import RankerBQRepository

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.logger import setup_logger
from common.timing import timing
from common.typings import HttpResponse
from ranker import LoadResult, Ranker

config = Config()
logger = setup_logger(config)

repository = RankerBQRepository()
ranker = Ranker(repository)


@dataclass(frozen=True)
class InputDate(DataClassJsonMixin):
    load_date: str


@timing(logger, "ranker")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Ranker ELT function entry point, called from GCP workflows.

    :param request: load date as ISO formatted string
    :returns: HttpResponse with list of load procedure results
    """
    message = get_event_message(request)
    logger.info(f"Running with {message=}")
    input_date = InputDate.from_json(message)
    logger.info(f"{input_date.load_date} is being loaded...")

    result = ranker.load(input_date.load_date)
    logger.info(f"{input_date.load_date} has been loaded.")

    return HttpResponse(body=json.dumps(result, default=LoadResult.to_dict))


if __name__ == "__main__":
    test_date = InputDate(date(2025, 7, 29).isoformat())
    run(create_http_event(test_date.to_json()))
