from datetime import date

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger
from common.utils import format_bytes_pretty

logger = get_logger()


class RankerBQRepository(BigQueryRepository):
    """
    Ranker repository to interact with BigQuery tables.
    """

    DATASET = "google_analytics"
    LOAD_CATEGORY_VIEW_ITEM_CLICKS = BQProcedure(DATASET, "load_category_view_item_clicks")
    LOAD_CATEGORY_PAGE_IMPRESSION_AND_CLICKS = BQProcedure(DATASET, "load_category_page_impression_and_clicks")
    LOAD_CATEGORY_VIEW_ITEM_CLICKS_PURCHASES = BQProcedure(DATASET, "load_category_view_item_clicks_purchases")

    def load_category_view_item_clicks(self, event_date: date) -> str:
        """
        Loads `google_analytics.category_view_item_clicks` table for the given date partition.

        :param event_date: Date partition to re-load
        :returns: Number of bytes billed
        """
        return self._load(self.LOAD_CATEGORY_VIEW_ITEM_CLICKS, event_date)

    def load_category_page_impression_and_clicks(self, event_date: date) -> str:
        """
        Loads `google_analytics.category_page_impression_and_clicks` table for the given date partition.

        This procedure has dependency on the `google_analytics.category_view_item_clicks` table.

        :param event_date: Date partition to re-load
        :returns: Number of bytes billed
        """
        return self._load(self.LOAD_CATEGORY_PAGE_IMPRESSION_AND_CLICKS, event_date)

    def load_category_view_item_clicks_purchases(self, event_date: date) -> str:
        """
        Loads `google_analytics.category_view_item_clicks_purchases` table for the given date partition.

        This procedure has dependency on the `google_analytics.category_page_impression_and_clicks` table.

        :param event_date: Date partition to re-load
        :returns: Number of bytes billed
        """
        return self._load(self.LOAD_CATEGORY_VIEW_ITEM_CLICKS_PURCHASES, event_date)

    def _load(self, procedure: BQProcedure, event_date: date) -> str:
        args = [event_date.isoformat()]
        result = self.call_procedure(procedure, args)

        return format_bytes_pretty(result.total_bytes_billed)
