# Ranker

Ranker function to support [DS ranking](https://www.notion.so/refurbed/Develop-ETL-For-Ranking-OKR-T1-25-159b6a8983ab8065ae09e70a7272c2f4).
- Owner: Data Science
- Contact person: <PERSON>, <PERSON><PERSON>

## Implementation

Ranker function is run from the [ranker-workflow](../infra/workflows/ranker.yaml) for the selected dates.

It uses ELT pattern and 3 SQL stored procedures to:
- extract source data from `GA4`, `analytics_transformed` and `analytics_reporting` datasets;
- transform and load it to target BigQuery native tables in `google_analytics` dataset.

See also [ranker.sql](../sql-bq/queries/ranker.sql) for the flow.
