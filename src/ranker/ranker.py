from dataclasses import dataclass
from datetime import date

from dataclasses_json import DataClassJsonMixin
from ranker_repository import RankerBQRepository

from common.logger import get_logger

logger = get_logger()


@dataclass(frozen=True)
class LoadResult(DataClassJsonMixin):
    procedure_name: str
    bytes_billed: str


class Ranker:
    """
    Ranker load runner.
    """

    def __init__(self, repository: RankerBQRepository) -> None:
        self._repository = repository

    def load(self, load_date: str) -> list[LoadResult]:
        """
        Loads all table partitions required by the ranker process.

        :param load_date: Date as iso string.
        :returns: Number of bytes processed.
        :raises ValueError: If `load_date` is invalid.
        """
        event_date = date.fromisoformat(load_date)
        result: list[LoadResult] = []

        load_result = self._load_category_view_item_clicks(event_date)
        result.append(load_result)

        load_result = self._load_category_page_impression_and_clicks(event_date)
        result.append(load_result)

        load_result = self._load_category_view_item_clicks_purchases(event_date)
        result.append(load_result)

        return result

    def _load_category_view_item_clicks(self, event_date: date) -> LoadResult:
        """Loads `category_view_item_clicks` table."""
        load_result = LoadResult(
            procedure_name=self._repository.LOAD_CATEGORY_VIEW_ITEM_CLICKS.full_name,
            bytes_billed=self._repository.load_category_view_item_clicks(event_date),
        )
        logger.info(load_result.to_dict())
        return load_result

    def _load_category_page_impression_and_clicks(self, event_date: date) -> LoadResult:
        """Loads `category_page_impression_and_clicks` table."""
        load_result = LoadResult(
            procedure_name=self._repository.LOAD_CATEGORY_PAGE_IMPRESSION_AND_CLICKS.full_name,
            bytes_billed=self._repository.load_category_page_impression_and_clicks(event_date),
        )
        logger.info(load_result.to_dict())
        return load_result

    def _load_category_view_item_clicks_purchases(self, event_date: date) -> LoadResult:
        """Loads `category_view_item_clicks_purchases` table."""
        load_result = LoadResult(
            procedure_name=self._repository.LOAD_CATEGORY_VIEW_ITEM_CLICKS_PURCHASES.full_name,
            bytes_billed=self._repository.load_category_view_item_clicks_purchases(event_date),
        )
        logger.info(load_result.to_dict())
        return load_result
