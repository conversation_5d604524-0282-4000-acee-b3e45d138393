from datetime import datetime, timedelta

from workflow_shared.workflow_model import WorkflowError, WorkflowExecution

from common.pipeline_logging.pipeline_model import <PERSON><PERSON>ine<PERSON><PERSON>


def get_test_execution(
    has_error: bool = False, has_bq_report: bool = False, pipeline_logging: bool = True
) -> WorkflowExecution:
    """
    Returns a WorkflowExecution object with predefined values for local testing purposes.
    Should be the same for starter and finalizer for log.finish_pipeline() to work correctly.
    """
    execution_date = datetime(2025, 6, 4, 15)
    end_date = execution_date.timestamp()
    start_date = end_date - 3600
    report_date = execution_date.date() - timedelta(days=1)

    run = PipelineRun()
    return WorkflowExecution(
        execution_id="8bc28cf5-53f1-4687-a2a7-af5bc1b77bef",
        workflow_id=run.pipeline_name,
        start=start_date,
        end=end_date,
        error=WorkflowError() if has_error else None,
        bq_cost_report_date=report_date if has_bq_report else None,
        pipeline_logging=pipeline_logging,
        result={
            "pipeline_name": run.pipeline_name,
            "run_id": run.run_id,
            "log_id": run.log_id,
        },
    )
