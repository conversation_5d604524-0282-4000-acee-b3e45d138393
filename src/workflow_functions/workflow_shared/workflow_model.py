from dataclasses import dataclass, field
from datetime import UTC, date, datetime
from enum import StrEnum
from functools import cached_property
from typing import Any, Optional

import dataclasses_json
from dataclasses_json import DataClassJsonMixin

from common.config import Config
from common.consts import GCP_CONSOLE_BASE_URL
from common.pipeline_logging.pipeline_model import SupportsPipelineRun
from common.utils import get_run_id

dataclasses_json.cfg.global_config.encoders[date] = date.isoformat
dataclasses_json.cfg.global_config.decoders[date] = date.fromisoformat


class ErrorTag(StrEnum):
    """
    Workflow Error Tags
    https://cloud.google.com/workflows/docs/reference/syntax/error-types?hl=en#error-tags
    """

    HTTP_ERROR = "HttpError"
    RUNTIME_ERROR = "RuntimeError"
    BRANCH_ERROR = "UnhandledBranchError"


@dataclass(frozen=True)
class HttpPayload(DataClassJsonMixin):
    body: str = "500 Internal Server Error"
    code: int = 500
    headers: dict[str, str] = field(default_factory=lambda: {"Content-Type": "text/html; charset=utf-8"})
    message: str = "HTTP server responded with error code 500"
    tags: list[str] = field(default_factory=lambda: [ErrorTag.HTTP_ERROR])


@dataclass(frozen=True)
class HttpError(DataClassJsonMixin):
    context: str = "RuntimeError"
    payload: HttpPayload = HttpPayload()


@dataclass(frozen=True)
class BranchError(DataClassJsonMixin):
    error: HttpError = HttpError()
    id: str = "0"


@dataclass(frozen=True)
class WorkflowError(DataClassJsonMixin):
    branches: list[BranchError] = field(default_factory=lambda: [BranchError()])
    message: str = "UnhandledBranchError: One or more branches or iterations encountered an unhandled runtime error"
    tags: list[str] = field(default_factory=lambda: ["RuntimeError"])
    truncated: bool = False


@dataclass(frozen=True, kw_only=True)
class WorkflowExecution(Config, SupportsPipelineRun):
    """
    Workflow execution properties allowing to construct URL and report on:
    - start, end and duration
    - result
    - details
    """

    execution_id: str
    workflow_id: str
    start: Optional[float] = None
    end: Optional[float] = None
    bq_cost_report_date: Optional[date] = None
    result: dict[str, Any] = field(default_factory=dict)
    error: Optional[WorkflowError] = None
    pipeline_logging: bool = False

    @cached_property
    def execution_url(self) -> str:
        """
        Returns execution URL, like below:
        https://console.cloud.google.com/workflows/workflow/europe-west3/test-workflow/execution/31314305-7ea5-43f7-b11e-992fbf884d54/summary?project=refb-analytics-staging
        """
        prefix = f"{GCP_CONSOLE_BASE_URL}/workflows/workflow"
        address = f"{self.location}/{self.workflow_id}/execution/{self.execution_id}"

        return f"{prefix}/{address}/summary?project={self.project_id}"

    @cached_property
    def start_date(self) -> Optional[datetime]:
        """Workflow start date in UTC"""
        if self.start:
            return datetime.fromtimestamp(float(self.start), tz=UTC)

        return None

    @cached_property
    def end_date(self) -> Optional[datetime]:
        """Workflow end date in UTC"""
        if self.end:
            return datetime.fromtimestamp(float(self.end), tz=UTC)

        return None

    @cached_property
    def pipeline_name(self) -> str:
        """SupportsPipelineRun implementation."""
        if self.pipeline_logging:
            return self.workflow_id

        return ""

    @cached_property
    def run_id(self) -> str:
        """SupportsPipelineRun implementation."""
        if self.pipeline_logging and self.start_date:
            return get_run_id(self.start_date)

        return ""

    @cached_property
    def error_message(self) -> Optional[str]:
        """
        Returns an error message, if any.
        """
        if self.error:
            return self.error.message

        return None
