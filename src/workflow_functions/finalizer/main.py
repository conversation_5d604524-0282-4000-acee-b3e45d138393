from typing import Optional

import functions_framework
from bigquery_cost_report import send_bq_cost_report
from cloudevents.http import CloudEvent
from google.cloud.sql.connector import Connector
from mattermost_notification import send_summary_report
from workflow_shared.local_execution import get_test_execution
from workflow_shared.workflow_model import WorkflowExecution

from common.bq_repository import BigQueryRepository
from common.cloud_events import create_http_event, get_event_message
from common.config import Config, MattermostAlertingConfig
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.logger import setup_logger
from common.mattermost_client import MattermostClient
from common.pipeline_logging.distributed_logger import DistributedPipelineLogger
from common.pipeline_logging.pipeline_reporter import <PERSON><PERSON><PERSON><PERSON>eporter
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.sql_repository import SqlRepository
from common.timing import timing
from common.typings import HttpResponse

config = Config()
logger = setup_logger(config)

mm_secret = get_secret(secret_id=MattermostAlertingConfig.MATTERMOST_ALERTING_SECRET_ID, project_id=config.project_id)
alerting_config = MattermostAlertingConfig.from_json(mm_secret)
mattermost_client = MattermostClient(alerting_config.webhook_url)
bq_repository = BigQueryRepository()

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)


@timing(logger, "workflow-finalizer")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Workflow finalizer, called from all workflows.
    """

    event = get_event_message(request)
    logger.debug(f"Invoked with {event=}")

    execution = WorkflowExecution.from_json(event)
    logger.info(f"Workflow {execution=}")

    execution_summary: Optional[str] = None
    if DistributedPipelineLogger.supports_pipeline_logging(execution):
        with Connector() as connector:
            repository = SqlRepository(db_config=pg_config, connector=connector)
            distributed_logger = DistributedPipelineLogger(execution, repository)
            distributed_logger.finish_logging(execution.error_message)
            reporter = PipelineReporter(distributed_logger, mattermost_client, config)
            execution_summary = reporter.get_execution_summary()
    else:
        # It is expected for non-monitored pipelines
        logger.info(f"Workflow '{execution.execution_url}' does not support pipeline logging.")

    channel = alerting_config.error_p1_channel if execution.error else alerting_config.info_channel
    send_summary_report(mattermost_client, execution, execution_summary, channel)
    send_bq_cost_report(bq_repository, mattermost_client, execution, channel)
    return HttpResponse()


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    # Ensure to run `starter` before finalizer to avoid `PipelineExecutionError`!
    pg_config = DatabaseConfig()

    test_execution = get_test_execution()
    run(create_http_event(test_execution.to_json()))
