import json
import time
from datetime import datetime
from typing import Optional

from workflow_shared.workflow_model import WorkflowError, WorkflowExecution

from common.mattermost_client import MattermostClient, MattermostMessage

TIME_FORMAT = "%H:%M:%S"
DATE_FORMAT = f"%Y-%m-%d {TIME_FORMAT}"


def send_summary_report(
    mattermost_client: MattermostClient,
    execution: WorkflowExecution,
    execution_summary: Optional[str] = None,
    channel: Optional[str] = None,
) -> None:
    """
    Sends a workflow execution summary report to a Mattermost channel.

    :param mattermost_client: Mattermost client instance
    :param execution: workflow execution details
    :param execution_summary: (optional) execution summary message
    :param channel: (optional) alerting channel
    """

    message = to_mattermost_message(execution, execution_summary, channel)
    mattermost_client.send_message(message)


def to_mattermost_message(
    execution: WorkflowExecution, execution_summary: Optional[str] = None, channel: Optional[str] = None
) -> MattermostMessage:
    """
    Converts workflow execution information to Mattermost format.
    If execution_summary is provided, it will replace the result JSON message.

    :param execution: Workflow execution
    :param channel: (optional) mattermost channel
    :param execution_summary: (optional) execution summary message
    :returns: Mattermost alert message
    """
    if execution.error:
        status = "failed!"
        icon_emoji = ":fire:"
    else:
        status = "succeeded."
        icon_emoji = ":information_source:"

    message = (
        f"Workflow `{execution.workflow_id}` execution [{execution.execution_id}]({execution.execution_url}) {status}"
    )
    message += _to_error_message(execution.error)

    if execution_summary:
        message += f"\n{execution_summary}"
    else:
        if execution.start_date and execution.end_date:
            message += _to_start_end_message(execution.start_date, execution.end_date)
        message += _to_result_message(execution.result)

    return MattermostMessage(text=message, icon_emoji=icon_emoji, channel=channel)


def _to_start_end_message(start_date: datetime, end_date: datetime) -> str:
    """
    To start, end, duration informative message.

    :param start_date: start date
    :param end_date: end_date
    :returns: MM formatted message
    """
    delta = end_date - start_date
    duration = time.strftime(TIME_FORMAT, time.gmtime(delta.total_seconds()))

    message = f"\n- **Start date [UTC]**: `{start_date.strftime(DATE_FORMAT)}`"
    message += f"\n- **Finish date [UTC]**: `{end_date.strftime(DATE_FORMAT)}`"
    message += f"\n- **Duration [hh:mm:ss]**: `{duration}`"

    return message


def _to_error_message(error: Optional[WorkflowError]) -> str:
    """
    To error message with unique set of stack trace errors (same errors can be duplicated).

    :param error: workflow error
    :returns: MM formatted message
    """
    if not error:
        return ""

    message = f"\n- **Error**: {error.message}!"

    if branches := error.branches:
        data = {"   - " + branch.error.context for branch in branches}
        message += "\n" + "\n".join(data)

    return message


def _to_result_message(result: Optional[dict[str, str]]) -> str:
    """
    To result message with result formatted as json string.

    :param result: optional result as dict
    :returns: MM formatted message
    """
    if not result:
        return ""

    result_as_json = json.dumps(result, indent=1, sort_keys=True, check_circular=True)

    message = "\n- **Result**:"
    message += "\n```json\n"
    message += result_as_json
    message += "\n```"

    return message
