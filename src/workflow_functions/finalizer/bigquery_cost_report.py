from datetime import date
from typing import Optional

from tabulate2 import tabulate
from workflow_shared.workflow_model import WorkflowExecution

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger
from common.mattermost_client import MattermostClient, MattermostMessage

logger = get_logger()


def send_bq_cost_report(
    bq_repository: BigQueryRepository, mattermost_client: MattermostClient, execution: WorkflowExecution, channel: str
) -> Optional[str]:
    """
    Sends BQ cost report if activated for a given date.
    """

    if not execution.bq_cost_report_date or execution.error:
        logger.info("BQ cost report deactivated.")
        return None

    logger.info(f"Generating BQ cost report for '{execution.bq_cost_report_date}' date...")
    report_data = _get_bq_cost_report(execution.bq_cost_report_date, bq_repository)

    if not report_data:
        logger.warning(f"No BQ cost report data found for the '{execution.bq_cost_report_date}' date!")
        return None

    message = _bq_report_to_mattermost(report_data)
    logger.info("Sending BQ cost report...")
    mattermost_client.send_message(MattermostMessage(text=message, channel=channel, icon_emoji=":bigquery:"))

    return message


def _bq_report_to_mattermost(report_data: list[list[str]]) -> str:
    """
    Converts BQ cost report data to MM notification message.
    """

    markdown = tabulate(
        report_data, headers=["Metric name", "Metric value"], tablefmt="simple_outline", colalign=("left", "right")
    )

    return f":moneybag: **BigQuery cost estimation:**\n\n{f"```\n{markdown}\n```"}"


def _get_bq_cost_report(report_date: date, bq_repo: BigQueryRepository) -> list[list[str]]:
    """
    Gets BQ cost report for the given a date.
    """
    proc = BQProcedure("maintenance", "costs_notification_report")

    bq_rows = bq_repo.call_procedure(procedure=proc, args=[f"{report_date.isoformat()}", False]).rows

    return [list(row) for row in bq_rows]  # noqa
