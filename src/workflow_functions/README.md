# GCP Workflows functions

GCP Workflows has a very limited [standard library](https://cloud.google.com/workflows/docs/reference/stdlib/overview).
Thus, a need for a helper library with cloud run, http function.

## Workflow finalizer

Sends Mattermost notification about workflow success or failure.

### Arguments
`WorkflowExecution`:
- `workflow_id`: `GOOGLE_CLOUD_WORKFLOW_ID` built-in environment variable
- `execution_id`: `GOOGLE_CLOUD_WORKFLOW_EXECUTION_ID` built-in environment variable
- `start`: workflow start timestamp
- `end`: workflow end timestamp
- `result`: result (object)
- `error`: optional error

### Returns
`None`, Mattermost message is being sent.

## Workflow Dates Provider

Generates list of dates for the given input.

### Arguments
`InputDates`:
- `start_date` - optional iso date string, if not provided defaults to yesterday - 3
- `end_date` - optional is date string, if not provided defaults to yesterday

### Returns
List of dates as iso strings, i.e.:
- "2025-02-01",
- "2025-02-02",
- "2025-02-03"

## Current Time

A simple helper function that returns the current time and day of the week.

### Arguments
None is required. The function accepts an empty JSON object.

### Returns
`CurrentTime` object:
- `day_of_week`: Current day of week as a string (e.g., "Monday", "Tuesday", etc.)
- `iso_date`: Current date in ISO format
- `iso_date_time`: Current timestamp in ISO format
