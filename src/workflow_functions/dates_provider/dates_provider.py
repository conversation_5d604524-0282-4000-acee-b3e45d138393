from dataclasses import dataclass
from datetime import date
from typing import Optional

from dataclasses_json import DataClassJsonMixin

from common.time_service import TimeService


@dataclass(frozen=True)
class InputDates(DataClassJsonMixin):
    start_date: Optional[str] = None
    end_date: Optional[str] = None


class DatesProvider:
    def __init__(self, time_service: Optional[TimeService] = None, run_days: int = 3):
        self._time_service = time_service or TimeService()
        self._run_days = run_days

    def get_dates(self, dates: InputDates) -> list[str]:
        """
        Gets list of dates for the given start and end dates.

        :param dates: input dates
        :returns: list of dates as iso strings
        """
        parse = date.fromisoformat
        start_date = parse(dates.start_date) if dates.start_date else self._time_service.days_ago(self._run_days)
        end_date = parse(dates.end_date) if dates.end_date else self._time_service.yesterday

        if start_date > end_date:
            raise ValueError(f"Start date {start_date} is before end date {end_date}!")

        days_range = self._time_service.days_range(start_date, end_date)
        return [day.isoformat() for day in days_range]
