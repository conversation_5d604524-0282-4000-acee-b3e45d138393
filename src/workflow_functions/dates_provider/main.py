import json

import functions_framework
from cloudevents.abstract import CloudEvent
from dates_provider import DatesProvider, InputDates

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.logger import setup_logger
from common.timing import timing
from common.typings import HttpResponse

config = Config()
logger = setup_logger(config)


@timing(logger, "dates_provider")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Helper function entry point, called from GCP workflows.
    """
    message = get_event_message(request)
    logger.info(f"Running with {message=}")
    dates = InputDates.from_json(message)

    provider = DatesProvider()
    result = provider.get_dates(dates)
    logger.info(f"Result dates: {result}")

    return HttpResponse(body=json.dumps(result))


if __name__ == "__main__":
    default_dates = InputDates()
    run(create_http_event(default_dates.to_json()))
