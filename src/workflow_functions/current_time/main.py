import dataclasses

import functions_framework
from cloudevents.abstract import CloudEvent
from dataclasses_json import DataClassJsonMixin

from common.cloud_events import create_http_event
from common.config import Config
from common.logger import setup_logger
from common.time_service import TimeService
from common.timing import timing
from common.typings import APPLICATION_JSON_HEADER, HttpResponse

config = Config()
logger = setup_logger(config)


@dataclasses.dataclass(frozen=True)
class CurrentTime(DataClassJsonMixin):
    """Current time response."""

    day_of_week: str
    iso_date: str
    iso_date_time: str


@timing(logger, "current_time")
@functions_framework.http
def run(_: CloudEvent) -> HttpResponse:
    """
    Helper function entry point, called from GCP workflows.
    Returns current time and day of week.
    """
    time_service = TimeService()
    result = CurrentTime(
        day_of_week=time_service.day_of_week,
        iso_date=time_service.today.isoformat(),
        iso_date_time=time_service.now.isoformat(),
    ).to_json()
    logger.info(result)

    return HttpResponse(body=result, headers=APPLICATION_JSON_HEADER)


if __name__ == "__main__":
    run(create_http_event("{}"))
