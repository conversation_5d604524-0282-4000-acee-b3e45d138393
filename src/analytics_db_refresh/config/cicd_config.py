import dataclasses
from functools import cached_property

from config.base_config import RefreshBaseConfig
from config.common_config import mm_alerting_config, refresh_time_service
from config.consts import db_flags, db_maintenance_window
from refresh_shared.restore_instance import (
    RestoreCloudSQLInstance,
    RestoreCloudSQLInstanceConfig,
)

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    DatabaseFlag,
    InstanceCreateSettings,
    IpConfiguration,
    MaintenanceWindow,
)
from common.config import DEFAULT_CONFIG, Config, MattermostAlertingConfig
from common.consts import DEFAULT_GOOGLE_PROJECT_ID, DEFAULT_GOOGLE_SQL_INSTANCE
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig


class CiCdConfig(RefreshBaseConfig):
    """Config for CICD runs only."""

    def __init__(
        self,
        config: Config,
        database_flags: list[DatabaseFlag],
        maintenance_window: MaintenanceWindow,
        alerting_config: MattermostAlertingConfig,
    ):
        super().__init__(config, alerting_config)
        self._database_flags = database_flags
        self._maintenance_window = maintenance_window

    @cached_property
    def source_db_config(self) -> DatabaseConfig:
        secret = get_secret("analytics-cloud-sql-config", self._config.project_id)
        db_config = DatabaseConfig.from_json(secret)

        return dataclasses.replace(db_config, host=db_config.host.replace(db_config.instance_name, "cicd-test-source"))

    @cached_property
    def target_db_config(self) -> DatabaseConfig:
        secret = get_secret("analytics-db-refresh-target-secret", self._config.project_id)
        db_config = DatabaseConfig.from_json(secret)

        return dataclasses.replace(
            db_config,
            host=db_config.host.replace(db_config.instance_name, DEFAULT_GOOGLE_SQL_INSTANCE),
        )

    @cached_property
    def instance_client_config(self) -> CloudSQLInstanceClientConfig:
        return CloudSQLInstanceClientConfig(sleep_in_seconds=3)

    @cached_property
    def instance_params(self) -> CloudSQLCreateInstanceParams:
        return CloudSQLCreateInstanceParams(
            name=self.source_db_config.instance_name,
            root_password=self.source_db_config.password,
            settings=InstanceCreateSettings(
                tier="db-custom-2-3840",  # 2 CPUs, 3 GB RAM
                data_disk_size_gb=15,
                backup_configuration=BackupConfiguration(),
                ip_configuration=IpConfiguration(),
                database_flags=self._database_flags,
                maintenance_window=self._maintenance_window,
            ),
        )

    @cached_property
    def instance_config(self) -> RestoreCloudSQLInstanceConfig:
        return RestoreCloudSQLInstanceConfig(
            new_instance_params=self.instance_params,
            backup_instance_name=DEFAULT_GOOGLE_SQL_INSTANCE,
            backup_project_name=DEFAULT_GOOGLE_PROJECT_ID,
        )

    @cached_property
    def source_restore_instance(self) -> RestoreCloudSQLInstance:
        return RestoreCloudSQLInstance(
            instance_client=CloudSQLInstanceClient(config=self.instance_client_config),
            config=self.instance_config,
            time_service=refresh_time_service,
        )

    @cached_property
    def target_restore_instance(self) -> RestoreCloudSQLInstance:
        return RestoreCloudSQLInstance(
            instance_client=CloudSQLInstanceClient(config=self.instance_client_config),
            config=self.instance_config,
            time_service=refresh_time_service,
        )


CICD_CONFIG = CiCdConfig(
    config=DEFAULT_CONFIG,
    database_flags=db_flags,
    maintenance_window=db_maintenance_window,
    alerting_config=mm_alerting_config,
)
