-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.clean_offers;
/**
  Cleans offers table
 */
create procedure maintenance.clean_offers(month_number_to_keep int = 6)
language plpgsql as
$$
declare
    sql                 text;
    rec                 record;
begin
    raise notice 'Create temp table for the last % months', month_number_to_keep;

    drop table if exists public.offers_tmp cascade;
    create table public.offers_tmp as
    select
        o.*
    from
        public.offers o
    where
        o.valid_from >= (current_date - (month_number_to_keep || ' months')::interval)
    or  o.valid_to = 'infinity';

    raise notice 'Dropping FK referencing public.offers unique constraints...';
    for rec in select
                   t.relnamespace::regnamespace::text || '.' || t.relname as table_name,
                   c.conname as constraint_name
               from
                   pg_constraint c
                   left join
                       pg_class t
                       on c.conrelid = t.oid
                   inner join
                       pg_constraint cu
                       on c.conindid = cu.conindid
                           and cu.contype = 'u'
               where
                     c.contype = 'f'
                 and c.connamespace::regnamespace::text = 'public'
                 and c.confrelid::regclass::text = 'offers'
        loop
            sql := format('alter table %s drop constraint %s;', rec.table_name, rec.constraint_name);
            raise notice '%', sql;
            execute sql;
        end loop;

    alter table public.offers
        rename to offers_old;

    raise notice 'Migrating PK constraint...';
    call maintenance.migrate_constraints('public.offers_old', 'public.offers_tmp', 'p');

    raise notice 'Migrating FK constraints...';
    call maintenance.migrate_constraints('public.offers_old', 'public.offers_tmp', 'f');

    raise notice 'Migrating views and materialized views...';
    call maintenance.migrate_views('public.offers_old', 'public.offers_tmp');

    raise notice 'Move temp to final table...';
    alter table public.offers_tmp
        rename to offers;
    drop table public.offers_old cascade;
end
$$;

-- call maintenance.clean_offers();
-- select * from public.offers limit 100;
-- select count(*) from public.offers;
