-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists maintenance.select_tables_count;
/**
  Gets real tables count.

  Based on: https://stackoverflow.com/a/2611745/27543394
*/
create function maintenance.select_tables_count(
    schemas text []
)
returns table
(
    schema_name text,
    table_name text,
    table_count bigint
)
language plpgsql
as
$$
begin
    return query
        select
            t.table_schema::text as schema_name,
            t.table_name::text as table_name,
            (xpath('/row/c/text()',
                   query_to_xml(format('select count(*) as c from %I.%I', t.table_schema, t.table_name), false, true,
                                '')))[1]::text::bigint as table_count
        from
            information_schema.tables t
        where
              t.table_type = 'BASE TABLE'
          and t.table_schema::text = any (schemas);
end;
$$ immutable
parallel safe;


-- select * from maintenance.select_tables_count(schemas => array ['public', 'zendesk']) as f limit 10;
