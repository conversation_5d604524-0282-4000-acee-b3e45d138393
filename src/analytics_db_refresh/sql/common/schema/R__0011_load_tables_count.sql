-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.persist_platform_tables_count;
/**
    Stores table count statistics in the platform database.
 */

create procedure maintenance.persist_platform_tables_count(
    run_id text,
    schemas text []
)
language plpgsql as
$$
begin
    drop table if exists public.refresh_tables_count;
    create table public.refresh_tables_count as
    select
        run_id,
        'platform_db' as db_name,
        f.schema_name,
        f.table_name,
        f.table_count
    from
        maintenance.select_tables_count(schemas) as f;
end
$$;

-- call maintenance.persist_platform_tables_count(run_id => '123abc', schemas => array ['public', 'zendesk']);
-- select * from public.refresh_tables_count limit 10;

drop procedure if exists maintenance.load_platform_tables_count;
/**
  Loads table count statistics from the platform database into tracking table.
 */

create procedure maintenance.load_platform_tables_count()
language plpgsql as
$$
begin
    insert into maintenance.refresh_tables_count(run_id, db_name, schema_name, table_name, table_count)
    select
        f.run_id,
        f.db_name,
        f.schema_name,
        f.table_name,
        f.table_count
    from
        public.refresh_tables_count as f;
end
$$;

-- call maintenance.load_platform_tables_count();
-- select * from maintenance.refresh_tables_count limit 10;

drop procedure if exists maintenance.load_analytics_tables_count;
/**
   Loads table count statistics from the analytics database into tracking table.
 */

create procedure maintenance.load_analytics_tables_count(
    run_id text,
    schemas text []
)
language plpgsql as
$$
begin
    insert into maintenance.refresh_tables_count(run_id, db_name, schema_name, table_name, table_count)
    select
        run_id,
        'analytics_db' as db_name,
        f.schema_name,
        f.table_name,
        f.table_count
    from
        maintenance.select_tables_count(schemas) as f
    where
        -- to exclude some technical tables in public schema
        f.table_name not in ('db_refresh', 'db_refresh_fixes', 'refresh_tables_count');
end
$$;

-- call maintenance.load_analytics_tables_count(run_id => '123abc', schemas => array ['public', 'zendesk']);
-- select * from maintenance.refresh_tables_count limit 10;
