-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.migrate_views;
/**
  Migrates views <source_table_name> to <target_table_name>.
 */
create procedure maintenance.migrate_views(
    source_full_table_name text, target_full_table_name text,
    debug bool = false
)
language plpgsql as
$$
declare
    source_table_name text := split_part(source_full_table_name, '.', 2);
    object_def text;
    sql text;
    rec record;
begin
    for rec in select distinct
                    case v.relkind when 'm' then 'materialized view' else 'view' end as object_type,
                    v.relnamespace::regnamespace::text || '.' || v.relname as object_name,
                    pg_get_viewdef(v.oid::regclass) as object_dev
                from
                    pg_depend as d -- objects that depend on the table
                    join pg_rewrite as r -- rules depending on the table
                    on r.oid = d.objid
                    join pg_class as v
                    on v.oid = r.ev_class
                where
                      v.relkind in ('v', 'm') -- only views and materialized views
                  and d.classid = 'pg_rewrite'::regclass
                  and d.refclassid = 'pg_class'::regclass
                  and d.deptype = 'n' -- normal dependency
                  and d.refobjid = source_full_table_name::regclass
        loop
            sql := format('drop %s if exists %s cascade;', rec.object_type, rec.object_name);
            if (debug) then
                raise notice '%', sql;
            else
                execute sql;
            end if;

            object_def := replace(rec.object_dev, source_table_name, target_full_table_name);
            sql := format('create %s %s as %s;', rec.object_type, rec.object_name, object_def);

            if (debug) then
                raise notice '%', sql;
            else
                execute sql;
            end if;
        end loop;
end
$$;

-- call maintenance.migrate_views('public.offers', 'public.offers_tmp', debug => true);
