-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.migrate_constraints;
/**
  Migrates constraints from <source_full_table_name> to <target_full_table_name>.
 */
create procedure maintenance.migrate_constraints(
    source_full_table_name text, target_full_table_name text,
    constraint_type char,
    debug bool = false
)
language plpgsql as
$$
declare
    source_table_schema text := split_part(source_full_table_name, '.', 1);
    source_table_name   text := split_part(source_full_table_name, '.', 2);
    target_table_name   text := split_part(target_full_table_name, '.', 2);
    constraint_name     text;
    constraint_def      text;
    sql                 text;
    rec                 record;
begin
    for rec in select
                   t.relnamespace::regnamespace::text || '.' || t.relname as table_name,
                   conname as constraint_name,
                   pg_get_constraintdef(c.oid) as constraint_def
               from
                   pg_constraint c
                   left join
                       pg_class t
                       on c.conrelid = t.oid
               where
                     contype = constraint_type
                 and connamespace::regnamespace::text = source_table_schema
                 and case contype when 'f' then confrelid::regclass::text else conrelid::regclass::text end =
                     source_table_name
        loop
            if constraint_type = 'f' then
                sql := format('alter table %s drop constraint %s;', rec.table_name, rec.constraint_name);
                if (debug) then
                    raise notice '%', sql;
                else
                    execute sql;
                end if;
            end if;

            constraint_def := replace(rec.constraint_def, source_table_name || '(', target_full_table_name || '(');
            if constraint_type = 'f' then
                sql := format('alter table %s add constraint %s %s not valid;', rec.table_name, rec.constraint_name,
                              constraint_def);
            else
                constraint_name := source_table_name || '_pk_' || to_char(now(), 'YYYYMMDDHH24MISS');
                sql := format('alter table %s add constraint %s %s;', target_full_table_name, constraint_name, constraint_def);
            end if;

            if (debug) then
                raise notice '%', sql;
            else
                execute sql;
            end if;
        end loop;
end
$$;

-- call maintenance.migrate_constraints('public.offers', 'public.offers_tmp', 'p', debug => true);
-- call maintenance.migrate_constraints('public.offers', 'public.offers_tmp', 'f', debug => true);
