-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.rename_routines_schema;
/**
  Migrates routines (functions and procedures) schema from <source_schema> to <target_schema>.
  Routines definitions are not updated by `ALTER SCHEMA name RENAME TO new_name`.
  Ref. https://stackoverflow.com/a/57294429/27543394
 */

create procedure maintenance.rename_routines_schema(
    source_schema text,
    target_schema text,
    debug bool = false
)
language plpgsql as
$$
declare
    func_def   text;
    sql        text;
    rec        record;
begin
    for rec in select pgp.oid
               from
                   pg_catalog.pg_proc pgp
               inner join
                    pg_catalog.pg_namespace pgn
                        on pgp.pronamespace = pgn.oid
               where
                   pgn.nspname in (source_schema, target_schema)
               and pgp.prosrc ~ ('\m' || source_schema || '\M')
               and pgp.prokind in ('f', 'p')
        loop
            sql := format('select pg_get_functiondef(%s)', rec.oid);
            execute sql into func_def;

            func_def = replace(func_def, source_schema || '.', target_schema || '.');
            func_def = 'set search_path=' || target_schema || '; ' || func_def || '; ';

            if (debug) then
                raise notice '%', func_def;
            else
                execute func_def;
            end if;
        end loop;
end
$$;

--call maintenance.rename_routines_schema(source_schema => 'public', target_schema => 'test', debug => true);
