-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.reload_order_item_offers;
/**
    Re-loads public.order_item_offers.

    !!! CAUTION !!!
    This procedure can only be run during the analytics DB refresh job and cannot be run manually.
    The reason for this is that it must run before cleaning the offers table.
    Another reason is that it drops all dependent objects in a cascading manner.
    Since this doesnt matter on the platform instance, it could seriously affect the analytics DB.
*/
create procedure maintenance.reload_order_item_offers()
language sql
as
$$
    drop table if exists public.order_item_offers cascade;
    with order_items as (select
                             -- order_items
                             order_items.id::integer as id,
                             order_items.created_at::timestamp with time zone as created_at,
                             order_items.updated_at::timestamp with time zone as updated_at,
                             order_items.order_id::integer as order_id,
                             order_items.offer_id::integer as offer_id,
                             order_items.offer_valid_from::timestamp as offer_valid_from,
                             order_items.type::text as type,

                             -- orders
                             orders.paid_at::timestamp with time zone as paid_at,
                             orders.state::text as state,
                             orders.country::text as country,
                             orders.presentment_currency::text as presentment_currency,
                             orders.payment_provider::text as payment_provider
                         from
                             public.order_items as order_items
                             inner join
                                 public.orders as orders
                                 on order_items.order_id = orders.id)
    select
        order_items.*,
        -- offers: nullable
        offers.valid_to::timestamp as valid_to,
        offers.instance_id::integer as instance_id,
        offers.merchant_id::integer as merchant_id,
        offers.grading::text as grading,
        offers.warranty::smallint as warranty
    into
        public.order_item_offers
    from
        order_items
        left join
            -- at most 1 offer for the given order_item!
                public.offers as offers
            on order_items.offer_id = offers.id
                and order_items.offer_valid_from = offers.valid_from;

    alter table public.order_item_offers
        add constraint order_item_offers_pk primary key (id);

    -- index FKs referenced columns
    create index order_item_offers_order_id_idx
        on public.order_item_offers (order_id);

    create index order_item_offers_offer_idx
        on public.order_item_offers (offer_id, offer_valid_from);

    create index order_item_offers_instance_id_idx
        on public.order_item_offers (instance_id);

    create index order_item_offers_merchant_id_idx
        on public.order_item_offers (merchant_id);

    -- index commonly queried fields
    create index order_item_offers_paid_at_idx
        on public.order_item_offers (paid_at);

    create index order_item_offers_state_idx
        on public.order_item_offers (state);

    create index order_item_offers_type_idx
        on public.order_item_offers (type);
    $$;
end

-- test me!
-- call maintenance.reload_order_item_offers();
