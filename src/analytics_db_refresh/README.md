# Analytics DB refresh
A process to refresh the analytics database with data from the platform database.
It creates a dump from the temporary (for the time of the refresh) Cloud SQL instance created from the latest platform Cloud SQL backup.
This instance is referred to as the `source` in the code.
The dump is then restored to the analytics database, which is called the `target` in the code.

![refresh-flow.png](resources/refresh-flow.png)

## Documentation
[Notion page](https://www.notion.so/refurbed/Analytics-DB-Daily-Restore-Optimization-0ffb6a8983ab80da94e4c5597e9eac74)

## Local development
⚠️ Before testing the process end-to-end on your local machine, ensure you have the necessary prerequisites in place.

### Flyway
You have to install Flyway command line tool. All supported systems you can find [here](https://documentation.red-gate.com/fd/installers-172490864.html).

Installing with [brew](https://formulae.brew.sh/formula/flyway#default):
```bash
brew install flyway
```

### Postgres utils
You have to install Postgres utilities like `pg_dump` and `pg_restore`.

On Debian based system you can do this as follows:
```bash
apt-get install -y postgresql-client postgresql-client-common libpq-dev
```

### How to run
- Navigate to the current directory in Bash and run `make pg-prepare-for-refresh` to prepare local databases for the refresh
- Run `main.py`


### CICD flow of the refresh
When running the refresh job test in the CI/CD pipeline (with the `CICD` variable set to `True`), the `InstanceProvider` is used to create a temporary Cloud SQL instance.
This instance is created from the latest staging instance backup.
The refresh process runs its operations on this temporary instance, which is then dropped.
Finally, the PG dump is restored to the existing staging instance (which serves as the target, not a newly created one for the final dump).
