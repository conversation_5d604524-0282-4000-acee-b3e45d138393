include ../../.env

pg-clone-analytics: # Private rule to clone analytics database to a new one
	$(MAKE) -C ../.. pg-migrate
	psql -U $(POSTGRES_USER) -d $(POSTGRES_DB) -h $(POSTGRES_HOST) -p $(POSTGRES_PORT) \
	-c 'drop database if exists $(ANALYTICS_CLONE_DB);' \
	-c 'create database $(ANALYTICS_CLONE_DB) with template $(POSTGRES_DB) owner $(POSTGRES_USER);'

pg-prepare-for-refresh: ## Prepares for running analytics db refresh locally
	$(MAKE) -C ../.. pg-clean-up
	docker run --rm --network=analytics-pipelines_default jwilder/dockerize -wait tcp://postgres:5432 -timeout 300s
	psql -U postgres -d $(POSTGRES_DB) -h $(POSTGRES_HOST) -p $(POSTGRES_PORT) -c 'create database $(PLATFORM_CLONE_DB) with template $(POSTGRES_DB) owner $(POSTGRES_USER);'
	$(MAKE) pg-clone-analytics
	psql -U postgres -d $(PLATFORM_CLONE_DB) -h $(POSTGRES_HOST) -p $(POSTGRES_PORT) \
	-c 'drop database if exists $(POSTGRES_DB);' \
	-c 'create database $(POSTGRES_DB) with template $(PLATFORM_CLONE_DB) owner $(POSTGRES_USER);'
	psql -U postgres -d $(POSTGRES_DB) -h $(POSTGRES_HOST) -p $(POSTGRES_PORT) -c 'drop database if exists $(PLATFORM_CLONE_DB);'

# Auto-document our Makefile using a trick from https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
.DEFAULT_GOAL := help
help: Makefile
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)
