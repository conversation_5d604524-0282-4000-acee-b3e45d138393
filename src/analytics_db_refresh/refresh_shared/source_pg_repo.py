from config.sleep_config import SLEEP_SECONDS

from common.logger import get_logger
from common.sql_model import SqlProcedure
from common.sql_repository import SqlRepository
from common.timing import background_task

logger = get_logger()


class SourcePgRepository(SqlRepository):
    """
    Analytics DB refresh PostgreSQL source repo
    """

    def drop_tables(self, tables: list[str], cascade: bool = False) -> None:
        """
        Drops given tables.
        :param tables: tables to be dropped
        :param cascade: whether to drop all dependent objects
        """
        for table in tables:
            self.drop_table(table, cascade)

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def persist_platform_tables_count(self, run_id: str, schemas: list[str]) -> None:
        """
        Persists table count statistics in the platform database.
        :param run_id: refresh unique run id
        :param schemas: schemas for which tables count will be persisted
        """
        schemas_arg = ", ".join([f"'{schema}'" for schema in schemas])

        logger.info(f"Persisting tables count statistics for platform db schemas: {schemas_arg}...")
        self.call(
            procedure=SqlProcedure(schema_name="maintenance", procedure_name="persist_platform_tables_count"),
            arguments=f"run_id => '{run_id}', schemas => array [{schemas_arg}]",
        )

    def rename_schemas(self, schemas: list[str], suffix: str) -> None:
        """
        Renames db schemas by adding temporary suffix.
        Needed for blue & green refresh.
        """

        for schema in schemas:
            new_schema_name = f"{schema}{suffix}"
            self.rename_schema(schema, new_schema_name)

            logger.info(f"Renaming routines schema '{schema}' to '{new_schema_name}'...")
            self.call(
                procedure=SqlProcedure(schema_name="maintenance", procedure_name="rename_routines_schema"),
                arguments=f"source_schema=>'{schema}', target_schema=>'{new_schema_name}'",
            )

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def reload_order_item_offers(self) -> None:
        """
        Reloads order item offers index table.
        """
        self.call(procedure=SqlProcedure(schema_name="maintenance", procedure_name="reload_order_item_offers"))

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def clean_offers(self) -> None:
        """
        Removes not needed data in `public.offers` table.
        """
        self.call(procedure=SqlProcedure(schema_name="maintenance", procedure_name="clean_offers"))
