from dataclasses import dataclass, field
from functools import cached_property

from refresh_shared.dump_restore import PgParams

from common.config import Config, MattermostAlertingConfig
from common.migrate_db import FlywayPgParams
from common.sql_client import DatabaseConfig


@dataclass(frozen=True, kw_only=True)
class DbRefreshConfig(Config):
    """
    - source_db_config: database config where pg_dump will run
    - target_db_config: database config where pg_restore will run
    - dump_path: location where pg_dump saves files
    - pg_params: pg_dump and pg_restore params, see more in PgParams
    - flyway_params: SQL migrations params used by Flyway, see more in FlywayParams
    - tables_to_drop: list of <schema>.<table> to be dropped on a source db before pg_dump will run
    - materialized_views_to_refresh: list of <schema>.<mat_view> to be refreshed on the target db after pg_restore run
    - temp_schema_suffix: suffix added to the schemas on a source db before pg_dump run (blue & green deployment)
    - backup_schema_suffix: suffix added to the schemas on a target db, just before switching temp_schemas
                            to final schemas. Needed in case of failure of the switch,
                            to rollback operation (blue & green deployment)

    - run_vacuum_analyze_weekday: a day of week when vacuum analyze should run, defaults to Sunday (ISO weekday)
    """

    source_db_config: DatabaseConfig
    target_db_config: DatabaseConfig
    alerting_config: MattermostAlertingConfig
    pg_params: PgParams
    flyway_params: FlywayPgParams
    tables_to_drop: list[str] = field(
        default_factory=lambda: [
            "public.import_failure_counters",
            "public.imports",
            "public.active_offers_eur_ng",
            "public.active_offers_dkk_ng",
            "public.active_offers_sek_ng",
            "public.active_offers_pln_ng",
            "public.active_offers_czk_ng",
            "public.active_offers_chf_ng",
        ]
    )
    materialized_views_to_refresh: list[str] = field(default_factory=lambda: ["public.merchant_balances_m"])

    temp_schema_suffix: str = "_temp"
    backup_schema_suffix: str = "_bak"
    run_vacuum_analyze_weekday: int = 7

    @cached_property
    def temp_materialized_views_to_refresh(self) -> list[str]:
        return [
            f"{schema_view[0]}{self.temp_schema_suffix}.{schema_view[1]}"
            for view in self.materialized_views_to_refresh
            if (schema_view := view.split("."))
        ]

    @cached_property
    def temp_schemas(self) -> list[str]:
        return [f"{schema}{self.temp_schema_suffix}" for schema in self.pg_params.schema]

    @cached_property
    def backed_up_schemas(self) -> list[str]:
        return [f"{schema}{self.backup_schema_suffix}" for schema in self.pg_params.schema]
