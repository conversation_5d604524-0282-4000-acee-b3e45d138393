from pathlib import Path
from typing import Optional

import sh
from config.sleep_config import SLEEP_SECONDS
from refresh_shared.dump_restore import DumpRestoreDb, PgParams
from sh import ErrorReturnCode

from common.logger import get_logger
from common.time_service import TimeService
from common.timing import background_task

logger = get_logger()


class DumpDb(DumpRestoreDb):
    """
    Python abstraction over PostgreSQL pg_dump command
    """

    def __init__(
        self,
        dump_base_path: Path,
        time_service: Optional[TimeService] = None,
    ) -> None:
        super().__init__(dump_base_path, time_service)

    @background_task(logger, sleep_seconds=SLEEP_SECONDS)
    def dump(
        self,
        pg_params: PgParams,
        connection_url: str,
    ) -> None:
        """
        Dumps PostgreSQL database by using `pg_dump` command.
        :param pg_params: see PgParams
        :param connection_url: url in a format `postgresql://<username>@<host>:<port>/<database>`
        Ref. https://www.postgresql.org/docs/15/app-pgdump.html
        """
        self.dump_full_path.mkdir(parents=True, exist_ok=True)

        command_params = [
            *pg_params.command_params,
            "--file",
            str(self.dump_full_path),
            connection_url,
        ]
        logger.info(f"pg_dump params: '{" ".join(map(str, command_params))}'")

        try:
            sh.pg_dump(*command_params)  # noqa
        except ErrorReturnCode as e:
            errors = self._filter_to_pg_errors(str(e.stderr))
            logger.warning(f"pg_dump: {errors}")

            raise
