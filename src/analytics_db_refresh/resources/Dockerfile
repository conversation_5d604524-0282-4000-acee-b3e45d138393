# IMAGE_VERSION is the Python image passed from the terraform module. (i.e. python:3.12-slim)
ARG IMAGE_VERSION

FROM flyway/flyway:11.8.1 as flyway
FROM postgres:15-alpine as postgres
FROM $IMAGE_VERSION

# Copy Flyway CLI and Java runtime from the Flyway image
COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk

# Set PATH to include Java and Flyway
ENV PATH="${PATH}:/opt/java/openjdk/bin:/flyway/"

# Validate Flyway installation
RUN echo "Validating Flyway and Java versions..." && \
    flyway --version && \
    java --version

# Copy PostgreSQL 15 client tools from official PostgreSQL image
COPY --from=postgres /usr/local/bin/pg_dump /usr/local/bin/
COPY --from=postgres /usr/local/bin/pg_restore /usr/local/bin/
COPY --from=postgres /usr/local/bin/psql /usr/local/bin/
COPY --from=postgres /usr/local/lib/libpq.so* /usr/local/lib/

# Install minimal dependencies and validate PostgreSQL 15 client tools
RUN apt-get update -y && apt-get upgrade -y && \
    apt-get install -y libpq-dev && \
    echo "Validating PostgreSQL 15 client tools..." && \
    pg_dump --version  && \
    pg_restore --version && \
    pg_dump --version | grep -q "15\." && pg_restore --version | grep -q "15\." && \
    echo "PostgreSQL 15 client tools validation passed!"

# Set our working directory
WORKDIR /app

# Copy build files
COPY ./requirements.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements.txt

# Copy over the rest of the code
COPY . .

# Ensure python output i.e. the stdout and stderr streams are sent straight to terminal
ENV PYTHONUNBUFFERED=1

CMD ["python", "main.py"]
