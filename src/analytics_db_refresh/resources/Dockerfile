# IMAGE_VERSION is the Python image passed from the terraform module. (i.e. python:3.12-slim)
ARG IMAGE_VERSION

FROM flyway/flyway:11.8.1 as flyway
FROM $IMAGE_VERSION

# Copy Flyway CLI and Java runtime from the Flyway image
COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk

# Set PATH to include Java and Flyway
ENV PATH="${PATH}:/opt/java/openjdk/bin:/flyway/"

# Validate Flyway installation
RUN echo "Validating Flyway and Java versions..." && \
    flyway --version && \
    java --version

# Install PostgreSQL 15 client tools to match target database version
RUN apt-get update -y && apt-get upgrade -y && \
    # Install prerequisites
    apt-get install -y wget ca-certificates gnupg && \
    # Add PostgreSQL repository key
    wget -qO- https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor > /usr/share/keyrings/postgresql.gpg && \
    # Add PostgreSQL repository
    echo "deb [signed-by=/usr/share/keyrings/postgresql.gpg] http://apt.postgresql.org/pub/repos/apt/ $(grep VERSION_CODENAME /etc/os-release | cut -d= -f2)-pgdg main" > /etc/apt/sources.list.d/postgresql.list && \
    # Update and install PostgreSQL 15 client
    apt-get update && \
    apt-get install -y postgresql-client-15 libpq-dev && \
    # Validate installation
    echo "Validating PostgreSQL 15 client tools..." && \
    pg_dump --version && \
    pg_restore --version && \
    pg_dump --version | grep -q "15\." && \
    echo "PostgreSQL 15 client tools validation passed!"

# Set our working directory
WORKDIR /app

# Copy build files
COPY ./requirements.txt .

# Then install all dependencies
RUN python -m venv venv
RUN . venv/bin/activate
RUN pip install -r requirements.txt

# Copy over the rest of the code
COPY . .

# Ensure python output i.e. the stdout and stderr streams are sent straight to terminal
ENV PYTHONUNBUFFERED=1

CMD ["python", "main.py"]
