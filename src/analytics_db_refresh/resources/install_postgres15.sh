#!/bin/bash
set -euo pipefail

echo "Installing PostgreSQL 15 client tools..."

# Update package lists
apt-get update -y && apt-get upgrade -y

# Install required packages for adding repository
apt-get install -y wget ca-certificates gnupg lsb-release

# Add PostgreSQL official repository
wget --quiet -O - https://www.postgresql.org/media/keys/ACCC4CF8.asc | \
    gpg --dearmor -o /usr/share/keyrings/postgresql-keyring.gpg

echo "deb [signed-by=/usr/share/keyrings/postgresql-keyring.gpg] http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > \
    /etc/apt/sources.list.d/pgdg.list

# Update package lists with new repository
apt-get update

# Install PostgreSQL 15 client tools
apt-get install -y postgresql-client-15 libpq-dev

# Validate installation
echo "Validating PostgreSQL 15 client tools..."
pg_dump --version
pg_restore --version

# Verify it's version 15
if pg_dump --version | grep -q "15\."; then
    echo "✓ PostgreSQL 15 client tools validation passed!"
else
    echo "✗ PostgreSQL 15 client tools validation failed!"
    exit 1
fi

echo "PostgreSQL 15 client tools installation completed successfully!"
