# Query Terminator

Cloud run function that runs every 30 minutes and terminates any queries running longer than time configured
in `maintenance.query_timeout_config` configuration table.

- Project requirements description in [notion](https://www.notion.so/refurbed/Query-Terminator-12eb6a8983ab8016b4f0eef72b67afeb).
- Development tasks in [gitlab](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/235).
