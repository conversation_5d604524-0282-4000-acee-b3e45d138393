from pandas import DataFrame
from query_timeout_model import TimeoutQueriesLog, TimeoutQueriesRow

from common.sql_model import SqlFunction, SqlTable
from common.sql_repository import SqlRepository


class QueryTimeoutRepository(SqlRepository):
    """
    PG Query Timeout Repository for terminating long-running queries:

    - `select * from maintenance.query_timeout_config`
    - `select * from maintenance.timeout_queries()`
    """

    QUERY_TIMEOUT_CONFIG_TABLE = SqlTable(schema_name="maintenance", table_name="query_timeout_config")
    TIMEOUT_QUERIES_FUNCTION = SqlFunction(schema_name="maintenance", function_name="timeout_queries")
    TIMEOUT_QUERIES_TABLE = SqlTable(schema_name="log", table_name="timeout_queries")

    def timeout_queries(self) -> list[TimeoutQueriesRow]:
        """
        Terminates long-running queries that timeouts according to `maintenance.query_timeout_config`.

        :returns: list of TimeoutQueriesRow
        """
        result = self.select_from_function(function=self.TIMEOUT_QUERIES_FUNCTION)

        return [TimeoutQueriesRow(*row) for row in result]

    def log_timeout_queries(self, data: list[TimeoutQueriesLog]) -> int:
        """
        Inserts timeout queries into `log.timeout_queries` table.

        :param data: rows to be inserted
        :returns: number of rows inserted
        """
        if not data:
            return 0

        df = DataFrame(data)
        return self.insert(df=df, table=self.TIMEOUT_QUERIES_TABLE)
