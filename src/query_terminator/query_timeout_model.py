from dataclasses import dataclass, field
from datetime import datetime
from enum import StrEnum
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from query_parser import get_query_slug, sanitize_line

from common.config import Config


@dataclass(frozen=True)
class QueryTerminatorConfig(Config):
    """
    Query Terminator configuration:

     - mattermost_channel: None for default MM channel from the webhook
     - mattermost_table_size: max number of elements in the MM table to not break the message size limit
     - analytics_db_secret: analytics database secret id to read database credentials
     - icon_emoji: custom terminator emoji
    """

    mattermost_channel: Optional[str] = None
    mattermost_table_size: int = 10
    analytics_db_secret: str = "analytics-db-query-terminator"
    icon_emoji: str = ":terminator:"


class TerminationStatus(StrEnum):
    """Values of `maintenance.timeout_queries` status column"""

    TERMINATED = "terminated"
    FAILED_TO_TERMINATE = "failed to terminate"
    REPORTED = "reported"


@dataclass(frozen=False, slots=True)
class TimeoutQueriesRow(DataClassJsonMixin):
    """`maintenance.timeout_queries` row schema"""

    HEADERS = [
        "Status",
        "hh:mm:ss",
        "Program",
        "Login",
        "History Slug",
        "SQL",
        "Start Time",
        "Session",
        "Host",
        "Config",
        "Description",
    ]

    status: TerminationStatus
    duration: str
    program_name: Optional[str]
    login_name: str
    history_slug: Optional[str] = field(init=False)
    sql_text: str
    start_time: datetime
    session_id: int
    host_ip: Optional[str]
    config_id: int
    description: str

    def __post_init__(self) -> None:
        """Gets Looker context and sanitizes SQL lines on load"""
        self.history_slug = None
        if self.sql_text:
            lines = self.sql_text.strip().splitlines()
            if lines:
                self.history_slug = get_query_slug(lines[0])
                sql_lines = lines[1:] if self.history_slug else lines
                self.sql_text = " ".join(sanitize_line(line) for line in sql_lines if line)


@dataclass(frozen=False, slots=True)
class TimeoutQueriesLog(DataClassJsonMixin):
    """`log.timeout_queries` log schema"""

    start_time: datetime
    duration: str
    terminated: bool
    login_name: str
    program_name: Optional[str]
    sql_text: str
    sql_hash: int
    history_slug: Optional[str]
    config_id: int
    host_ip: Optional[str]
