import json
import re
from typing import Optional

LOOKER_CONTEXT_PATTERN = re.compile(r"--\s*Looker Query Context\s*'(.+?)'")
LOOKER_HISTORY_SLUG = "history_slug"


def get_query_slug(query: Optional[str]) -> Optional[str]:
    """
    Gets Looker query context slug from a query text.
    Uses regex to find `Looker Query Context` and returns `history_slug`.

    Examples:
    ---------
    >>> txt = '-- Looker Query Context \'{"user_id":112090,"history_slug":"abc","instance_slug":"xyz"}\''
    >>> get_query_slug(txt)
    "abc"

    :param query: query input line
    :returns: Looker query context if found, None otherwise
    """
    if query:
        context = re.search(LOOKER_CONTEXT_PATTERN, query)
        if context and context.groups():
            return _get_history_slug(context.group(1))

    return None


def _get_history_slug(looker_query_context: Optional[str]) -> Optional[str]:
    """
    Gets Looker history slug from the looker query context.

    :param looker_query_context: Looker query context (can be None)
    :returns: Looker history slug if found, None otherwise
    """
    if looker_query_context:
        try:
            context: dict[str, str] = json.loads(looker_query_context)
            return context.get(LOOKER_HISTORY_SLUG)
        except json.decoder.JSONDecodeError:
            return None

    return None


def sanitize_line(line: Optional[str]) -> Optional[str]:
    """Sanitize line by trimming whitespace and escaping `|`"""
    if line:
        return line.strip().replace("|", "\\|")
    else:
        return line
