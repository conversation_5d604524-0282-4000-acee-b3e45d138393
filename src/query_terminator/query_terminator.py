from dataclasses import dataclass
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from query_timeout_model import (
    QueryTerminatorConfig,
    TerminationStatus,
    TimeoutQueriesLog,
    TimeoutQueriesRow,
)
from query_timeout_repository import QueryTimeoutRepository
from tabulate2 import tabulate

from common.base import BaseEtl
from common.logger import get_logger
from common.mattermost_client import MattermostClient, MattermostMessage

logger = get_logger()


@dataclass(frozen=False, slots=True)
class Transformed(DataClassJsonMixin):
    mattermost_markdown: list[str]
    queries_log: list[TimeoutQueriesLog]


class QueryTerminator(BaseEtl):
    """Terminates long-running queries and reports to MM channel."""

    _config: QueryTerminatorConfig

    def __init__(self, config: QueryTerminatorConfig, client: MattermostClient, repository: QueryTimeoutRepository):
        super().__init__(config)
        self._client = client
        self._repository = repository

    def extract(self) -> list[TimeoutQueriesRow]:
        """Extracts timeout queries to be reported on."""
        return self._repository.timeout_queries()

    def transform(self, extracted: list[TimeoutQueriesRow]) -> Optional[Transformed]:
        """
        Transforms timeout queries into:
        - mattermost markdown
        - timeout log

        :param extracted: timeout queries to be reported on
        :returns: transformed timeout queries
        """
        if extracted:
            logger.info(f"Found {len(extracted)} timeout queries.")
            return Transformed(
                mattermost_markdown=self.to_mattermost_markdown(extracted), queries_log=self.to_queries_log(extracted)
            )

        logger.info("No timeout queries found.")
        return None

    def load(self, transformed: Optional[Transformed]) -> bool:
        """
        Loads transformed timeout queries into:
        - mattermost channel as a notification
        - `log.timeout_queries` table for later reporting

        :param transformed: timeout queries to be reported on
        :returns: True if there were any timeout queries, False otherwise
        """
        if transformed:
            self._send_message(transformed.mattermost_markdown)
            self._repository.log_timeout_queries(transformed.queries_log)
            return True

        return False

    def to_mattermost_markdown(self, data: list[TimeoutQueriesRow]) -> list[str]:
        """
        Converts data rows to Markdown format compatible with the Mattermost.

        Splits the message to chunks of markdown to overcome MM limit of 16383 characters.
        Reference: https://www.geeksforgeeks.org/break-list-chunks-size-n-python/

        :param data: list of rows
        :returns: List of Markdown formatted strings per chunk of rows
        """
        if not data:
            return []

        size = self._config.mattermost_table_size
        chunks = [data[i : i + size] for i in range(0, len(data), size)]

        return [tabulate(chunk, headers=TimeoutQueriesRow.HEADERS, tablefmt="pipe") for chunk in chunks]

    @staticmethod
    def to_queries_log(data: list[TimeoutQueriesRow]) -> list[TimeoutQueriesLog]:
        """
        Converts data rows to `log.timeout_queries` format.

        :param data: list of rows
        :returns: timeout queries log
        """
        if not data:
            return []

        return [
            TimeoutQueriesLog(
                start_time=row.start_time,
                duration=row.duration,
                terminated=row.status == TerminationStatus.TERMINATED,
                login_name=row.login_name,
                program_name=row.program_name,
                sql_text=row.sql_text,
                sql_hash=hash(row.sql_text),
                history_slug=row.history_slug,
                config_id=row.config_id,
                host_ip=row.host_ip,
            )
            for row in data
        ]

    def _send_message(self, mattermost_markdown: list[str]) -> None:
        """
        Sends message to Mattermost channel.

        :param mattermost_markdown: markdown formatted string
        """
        if not mattermost_markdown:
            return

        logger.info(f"Sending {len(mattermost_markdown)} report(s) to Mattermost channel...")
        for markdown in mattermost_markdown:
            message = MattermostMessage(
                text=markdown, channel=self._config.mattermost_channel, icon_emoji=self._config.icon_emoji
            )
            self._client.send_message(message)
