/*
ORIGINAL SOURCE: etl_internal.instances

Current ETL:
https://console.cloud.google.com/functions/details/europe-west3
/bi_orders_job?env=gen1&inv=1&invt=AbnawA&project=refb-analytics

GOAL: rewrite into platform export ETL
*/

---------------------------------------------------------------------
-- 1. Create TEMP table to compare results with export_etl views:
---------------------------------------------------------------------
drop table if exists etl_internal_instances;
create temporary table etl_internal_instances
as
with colors as (
    select
        instances.id as instance_id,
        ev.value ->> 'de' as color
    from
        public.instances as instances
    inner join public.products as products on instances.product_id = products.id
    inner join public.instance_attribute_values as ia on instances.id = ia.instance_id
    left join public.attribute_values as av on ia.attribute_value_id = av.id
    inner join public.attributes as a on av.attribute_id = a.id
    left join public.attribute_enum_values as ev on av.value_enum_id = ev.id
    where
        a.name = 'Farbe'
),

memory as (
    select
        instances.id as instance_id,
        coalesce((ev.value ->> 'de'), av.value_numeric::text) || ' ' || a.unit as memory
    from
        public.instances as instances
    inner join public.products as products on instances.product_id = products.id
    inner join public.instance_attribute_values as ia on instances.id = ia.instance_id
    left join public.attribute_values as av on ia.attribute_value_id = av.id
    inner join public.attributes as a on av.attribute_id = a.id
    left join public.attribute_enum_values as ev on av.value_enum_id = ev.id
    where
        a.name = 'Speicherplatz'
),

categories_dt as (
    (
        select
            c.*,
            pc.name_en as product_category
        from
            categories as c
        left join categories as pc on pc.id = subpath(c.path, -1, 1)::text::int
        where c.path <> ''
    )

    union all

    (
        select
            *,
            name_en as product_category
        from categories
        where nlevel(path) = 0
    )
)

select
    i.id as instance_id,
    i.name::text as instance_name,
    p.id as product_id,
    p.name::text as product_name,
    categories.name::text as category_name,
    categories.product_category::text,
    categories.brand::text,
    c.color::text,
    m.memory::text,
    p.slug::text,
    categories.type::text as category_type
from instances as i
inner join products as p on i.product_id = p.id
inner join categories_dt as categories on p.category_id = categories.id
left join colors as c on i.id = c.instance_id
left join memory as m on i.id = m.instance_id
where (i.published) and (not coalesce((p.deleted_at is not null), false));

---------------------------------------------------------------------
-- 2. Build similar query using `export_etl` views and compare
---------------------------------------------------------------------
drop table if exists export_etl_instances;
create temporary table export_etl_instances
as
with colors as (
    select
        ia.instance_id,
        a.attribute_value as color
    from
        export_etl.v_instance_attribute_values as ia
    inner join
        export_etl.v_attributes as a
    on ia.attribute_value_id = a.attribute_value_id
    where
        a.attribute_name = 'Farbe'
),

memory as (
    select
        ia.instance_id,
        a.attribute_value as memory
    from
        export_etl.v_instance_attribute_values as ia
    inner join
        export_etl.v_attributes as a
    on ia.attribute_value_id = a.attribute_value_id
    where
        a.attribute_name = 'Speicherplatz'
)

select
    i.id as instance_id,
    i.name as instance_name,
    p.id as product_id,
    p.name as product_name,
    categories.category_name,
    categories.product_category,
    categories.brand,
    c.color,
    m.memory,
    p.slug,
    categories.category_type
from
    export_etl.v_instances as i
inner join
    export_etl.v_products as p
on i.product_id = p.id
inner join
    export_etl.v_categories as categories
on p.category_id = categories.id
left join
    colors as c
on i.id = c.instance_id
left join
    memory as m
on i.id = m.instance_id
where
    i.published
    and p.deleted_at is null;

---------------------------------------------------------------------
-- Compare 2 tables and expect no rows returned
---------------------------------------------------------------------
select * from export_etl_instances
except
select * from etl_internal_instances;

select * from etl_internal_instances
except
select * from export_etl_instances;
