# Daily refreshed queries

Analyse daily refreshed queries for performance marketing automation and map them to BigQuery datasets.

## Questions to be answered
Based on the daily refreshed platform [queries](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86) analyze the following:
1. Whether BQ analytics_transformed datasets contain all tables and columns mentioned.
2. Duplicate revenue sub-queries, can we reuse `order_financial_revenue` or we need a new base view?
3. `daily_gmv_and_revenue` ETL - who owns it, how we can migrate it to monorepo?
4. `Funnel_export_refurbed` dataset - how to handle it, create a view?

## Mapping of daily refreshed queries to BQ datasets

### 3307_hourly_revenue_7_days_ago
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab80c28b8ec008dad7be19)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3307_hourly_revenue_7_days_ago.sql](3307_hourly_revenue_7_days_ago.sql)

### 3338_hourly_revenue_4_days_ago
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab80ffb50ee9f5a1f9a632)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3338_hourly_revenue_4_days_ago.sql](3338_hourly_revenue_4_days_ago.sql)

### 3299_hourly_revenue_yesterday
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab8009bce4e386cce2f277)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3299_hourly_revenue_yesterday.sql](3299_hourly_revenue_yesterday.sql)

### 3297_hourly_revenue_last_4_weeks
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab8064b959c61c7088c5db)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3297_hourly_revenue_last_4_weeks.sql](3297_hourly_revenue_last_4_weeks.sql)

### 3295_hourly_revenue_shares_last_4_weeks
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab808f9c2dc6a2cdfa76dd)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3295_hourly_revenue_shares_last_4_weeks.sql](3295_hourly_revenue_shares_last_4_weeks.sql)

### 3304_15_minute_revenue_last_4_weekdays
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab800e8dd9ec560fb9b148)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3304_15_minute_revenue_last_4_weekdays.sql](3304_15_minute_revenue_last_4_weekdays.sql)

### 3311_daily_gmv_and_revenue
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab80ac9716c441aecad8cf)
- 100% matched with the BQ dataset, no changes needed.
- validation query: [3311_daily_gmv_and_revenue.sql](3311_daily_gmv_and_revenue.sql)

### 3313_funnel_revenue
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab80c0f1c5f2b4d8f7c0e1)
- Create a view on top of `refb-analytics-funnelio.Funnel_export_refurbed.all_funnel_data_view`
- Only 140 rows retrieved, so it is not a problem to query a view
- Bytes processed: 47.17 MB
- Bytes billed: 790 MB

### 3312_discount_per_country
- [source](https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab8090893ef84b89b8de6b)
- 100% matched with the BQ dataset, no changes needed.
- Validation query: [3312_discount_per_country.sql](3312_discount_per_country.sql)

## Conclusions

1. Most queries (including `daily_gmv_and_revenue`) can be rewritten with use of `order_financial_revenue` view,
which is already available in the BQ dataset as `analytics_transformed.order_financial_revenue` table.

2. Two queries (`3295` and `3297`) use 4 levels of different ranking functions,
which would need to be translated to BigQuery SQL.

3. One query, `3313_funnel_revenue`, is using `refb-analytics-funnelio` project.
We suggest to create a view on top of `Funnel_export_refurbed` dataset to shield from the changes in the source dataset.

4. Although most queries differ only in date level and filter, those need to be applied
before grouping and aggregating the data. Thus, creating a single view serving all queries is possible,
but would move complexity to the Monetization code.

## Estimation

Estimation contains DE team effort only, as Monetization team will need to estimate their effort separately.

### Option 1. Add funnel revenue view only [1 day]

For most cases use already available `analytics_transformed.order_financial_revenue` table,
and let the Monetization team handle date filtering and grouping in their repository.

**DE Tasks:**
- Create funnel data view: 0.5 day;
- Testing, code review, releasing: 0.5 day;

**Monetization Tasks:**
- Create repository with parametrized queries for each of 8 queries.
- Map 1 repository method to the funnel data view.

**Pros:**
- Minimal effort for DE team;
- Stable data contract with the Monetization team;
- Monetization team can handle parametrization and grouping in their repository;
- Monetization team can handle any future changes in the queries without DE team involvement.

**Cons:**
- Query complexity is moved to the Monetization team;
- Monetization team handles testing and validation of the queries;

### Option 2. Create like for like views for each query [4.5 days]

Create like for like views for each query, which does not require any parametrization.
All grouping and filtering are done in the view, so Monetization team can use it directly.

**DE Tasks:**
- Create funnel data view: 0.5 day;
- Create 8 revenue data views and translate them to BQ syntax: 3 days;
- Testing, code review, releasing: 1 day;

**Monetization Tasks:**
- Create repository with 9 methods mapped to each view.

**Pros:**
- Monetization team can use the views directly without any parametrization;
- The DE team handles query complexity in the views or via ETLs if required;
- The views are the contract with the Monetization team;

**Cons:**
- Any changes in the queries will require DE team involvement;
- More coordination between DE and Monetization teams in case of changes;
