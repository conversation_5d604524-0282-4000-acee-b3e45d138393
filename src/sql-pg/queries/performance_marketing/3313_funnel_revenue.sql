-----------------------------------------------------------------
-- Uses data from `refb-analytics-funnelio` project
-- <PERSON><PERSON><PERSON> needs to be in the US region to query it!
-----------------------------------------------------------------
create schema if not exists funnel_data options (location = "US");

-- The view returns 140 rows
drop view if exists funnel_data.performance_marketing_revenue;
create view funnel_data.performance_marketing_revenue
as
SELECT
    case when funnel_data.dim_1e81sjq0ist0a_Country_custom = "at - copy" then "at"
              else funnel_data.dim_1e81sjq0ist0a_Country_custom  end AS funnel_data_country,
        (funnel_data.Date ) AS funnel_data_date,
        COALESCE(SUM(CASE WHEN (UPPER( (CASE WHEN funnel_data.campaign LIKE '%WKZ%' THEN "Supplier Financing"
            WHEN funnel_data.campaign LIKE '%BAE%' OR funnel_data.campaign LIKE '%ORG |%' OR funnel_data.campaign LIKE '%| Reach%' OR funnel_data.campaign LIKE '%Video View%' OR  funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TV"  THEN "Brand Marketing"
            WHEN funnel_data.campaign LIKE '%| 050%' OR funnel_data.campaign LIKE '%Buyback%' THEN "Trade-in"
            WHEN funnel_data.campaign LIKE '%| 070%' THEN "App Marketing"
            WHEN funnel_data.traffic_source = 'Apple Search Ads' THEN "App Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TVM4E" THEN "Media for equity"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Events", "Market Research", "Tools") THEN "Marketing Tools"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Offline Media" OR (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Brand Marketing" THEN "Brand Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Influencer", "Online Audio", "Press", "Rebranding", "Content") THEN "Brand Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) IN ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1") or funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "Online Collaborations"
            OR  (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = 'Google Sheets - Sales Marketing' THEN "Sales Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Administrative" THEN "Administrative"
            WHEN (funnel_data.dim_1e81r9nnpnlc8_Stages IN ("Stage 1", "Stage 2", "Stage 0", "No Stage" , "No stage") ) AND ((CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Paid Social" OR (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Display"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Search - Brand"
          WHEN (funnel_data.sourceTypeName = 'Google Ads') AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' AND funnel_data.campaign LIKE '%- Brand%' THEN "Search - Brand"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' and funnel_data.campaign LIKE '%Shopping%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising'  THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          ELSE funnel_data.sourceTypeName END) = "Youtube") THEN "Upper Funnel Performance Marketing"
           ELSE "Lower Funnel Performance Marketing"
            END) ) = UPPER('Lower Funnel Performance Marketing') OR UPPER( (CASE WHEN funnel_data.campaign LIKE '%WKZ%' THEN "Supplier Financing"
            WHEN funnel_data.campaign LIKE '%BAE%' OR funnel_data.campaign LIKE '%ORG |%' OR funnel_data.campaign LIKE '%| Reach%' OR funnel_data.campaign LIKE '%Video View%' OR  funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TV"  THEN "Brand Marketing"
            WHEN funnel_data.campaign LIKE '%| 050%' OR funnel_data.campaign LIKE '%Buyback%' THEN "Trade-in"
            WHEN funnel_data.campaign LIKE '%| 070%' THEN "App Marketing"
            WHEN funnel_data.traffic_source = 'Apple Search Ads' THEN "App Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TVM4E" THEN "Media for equity"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Events", "Market Research", "Tools") THEN "Marketing Tools"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Offline Media" OR (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Brand Marketing" THEN "Brand Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Influencer", "Online Audio", "Press", "Rebranding", "Content") THEN "Brand Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) IN ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1") or funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "Online Collaborations"
            OR  (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = 'Google Sheets - Sales Marketing' THEN "Sales Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Administrative" THEN "Administrative"
            WHEN (funnel_data.dim_1e81r9nnpnlc8_Stages IN ("Stage 1", "Stage 2", "Stage 0", "No Stage" , "No stage") ) AND ((CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Paid Social" OR (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Display"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Search - Brand"
          WHEN (funnel_data.sourceTypeName = 'Google Ads') AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' AND funnel_data.campaign LIKE '%- Brand%' THEN "Search - Brand"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' and funnel_data.campaign LIKE '%Shopping%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising'  THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          ELSE funnel_data.sourceTypeName END) = "Youtube") THEN "Upper Funnel Performance Marketing"
           ELSE "Lower Funnel Performance Marketing"
            END) ) = UPPER('Upper Funnel Performance Marketing') OR UPPER( (CASE WHEN funnel_data.campaign LIKE '%WKZ%' THEN "Supplier Financing"
            WHEN funnel_data.campaign LIKE '%BAE%' OR funnel_data.campaign LIKE '%ORG |%' OR funnel_data.campaign LIKE '%| Reach%' OR funnel_data.campaign LIKE '%Video View%' OR  funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TV"  THEN "Brand Marketing"
            WHEN funnel_data.campaign LIKE '%| 050%' OR funnel_data.campaign LIKE '%Buyback%' THEN "Trade-in"
            WHEN funnel_data.campaign LIKE '%| 070%' THEN "App Marketing"
            WHEN funnel_data.traffic_source = 'Apple Search Ads' THEN "App Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TVM4E" THEN "Media for equity"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Events", "Market Research", "Tools") THEN "Marketing Tools"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Offline Media" OR (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Brand Marketing" THEN "Brand Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Influencer", "Online Audio", "Press", "Rebranding", "Content") THEN "Brand Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) IN ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1") or funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "Online Collaborations"
            OR  (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = 'Google Sheets - Sales Marketing' THEN "Sales Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Administrative" THEN "Administrative"
            WHEN (funnel_data.dim_1e81r9nnpnlc8_Stages IN ("Stage 1", "Stage 2", "Stage 0", "No Stage" , "No stage") ) AND ((CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Paid Social" OR (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Display"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Search - Brand"
          WHEN (funnel_data.sourceTypeName = 'Google Ads') AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' AND funnel_data.campaign LIKE '%- Brand%' THEN "Search - Brand"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' and funnel_data.campaign LIKE '%Shopping%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising'  THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          ELSE funnel_data.sourceTypeName END) = "Youtube") THEN "Upper Funnel Performance Marketing"
           ELSE "Lower Funnel Performance Marketing"
            END) ) = UPPER('Sales Marketing') OR UPPER( (CASE WHEN funnel_data.campaign LIKE '%WKZ%' THEN "Supplier Financing"
            WHEN funnel_data.campaign LIKE '%BAE%' OR funnel_data.campaign LIKE '%ORG |%' OR funnel_data.campaign LIKE '%| Reach%' OR funnel_data.campaign LIKE '%Video View%' OR  funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TV"  THEN "Brand Marketing"
            WHEN funnel_data.campaign LIKE '%| 050%' OR funnel_data.campaign LIKE '%Buyback%' THEN "Trade-in"
            WHEN funnel_data.campaign LIKE '%| 070%' THEN "App Marketing"
            WHEN funnel_data.traffic_source = 'Apple Search Ads' THEN "App Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "TVM4E" THEN "Media for equity"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Events", "Market Research", "Tools") THEN "Marketing Tools"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Offline Media" OR (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Brand Marketing" THEN "Brand Marketing"
            WHEN funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ IN ("Influencer", "Online Audio", "Press", "Rebranding", "Content") THEN "Brand Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) IN ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1") or funnel_data.dim_1i85je69h69r3_Channel_2_equivalent_ = "Online Collaborations"
            OR  (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = 'Google Sheets - Sales Marketing' THEN "Sales Marketing"
            WHEN (CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Google Sheets - Administrative" THEN "Administrative"
            WHEN (funnel_data.dim_1e81r9nnpnlc8_Stages IN ("Stage 1", "Stage 2", "Stage 0", "No Stage" , "No stage") ) AND ((CASE WHEN funnel_data.traffic_source IN ("Prisjagt", "Prisjakt", "Shopping24", "Kelkoo", "Idealo", "Marktplaats", "Oskenskyen", "Billiger.de", "PriceRunner") THEN "Price Comparison"
             WHEN funnel_data.traffic_source IN ("Google") THEN (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Google-gdn"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Google-cpc"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          WHEN funnel_data.sourceTypeName LIKE '%Campaign Manager 360%' THEN "Programmatic display"
          ELSE funnel_data.sourceTypeName END)
             WHEN funnel_data.traffic_source IN ("TikTok", "Facebook", "Twitter") THEN "Paid Social"
             WHEN funnel_data.traffic_source IN ("Adtraction", "Rakuten Affiliate Network") THEN "Affiliate"
             WHEN funnel_data.sourceTypeName IN ("Google Sheets") THEN concat("Google Sheets - ", (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')))
            ELSE funnel_data.traffic_source
            END) = "Paid Social" OR (CASE
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Display%' THEN "Display"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' AND UPPER(funnel_data.campaign) LIKE '%SEARCH - BRAND%' THEN "Search - Brand"
          WHEN (funnel_data.sourceTypeName = 'Google Ads') AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%Search%' THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%CSS%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Google Ads' AND (REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) LIKE '%YouTube%' THEN "Youtube"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' AND funnel_data.campaign LIKE '%- Brand%' THEN "Search - Brand"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising' and funnel_data.campaign LIKE '%Shopping%' THEN "Shopping"
          WHEN funnel_data.sourceTypeName = 'Microsoft Advertising'  THEN "Search - Non Brand"
          WHEN funnel_data.sourceTypeName = 'Facebook Ads' THEN "Facebook"
          ELSE funnel_data.sourceTypeName END) = "Youtube") THEN "Upper Funnel Performance Marketing"
           ELSE "Lower Funnel Performance Marketing"
            END) ) = UPPER('Trade-in')) THEN ( case when ((REPLACE(REPLACE( funnel_data.sourceName, ' - Sheet1', ''), ' - Funnel Input', '')) <> 'Offline Media') = true THEN (CASE
        WHEN (case when funnel_data.dim_1e81sjq0ist0a_Country_custom = "at - copy" then "at"
              else funnel_data.dim_1e81sjq0ist0a_Country_custom  end) = 'at' AND funnel_data.Date >= date(2020,11,1) AND (funnel_data.sourceTypeName = 'AdWords' OR funnel_data.sourceTypeName = 'Google Ads')then 1.05 * funnel_data.Cost
        WHEN (case when funnel_data.dim_1e81sjq0ist0a_Country_custom = "at - copy" then "at"
              else funnel_data.dim_1e81sjq0ist0a_Country_custom  end) = 'it' AND funnel_data.Date >= date(2024,07,1) AND (funnel_data.sourceTypeName = 'AdWords' OR funnel_data.sourceTypeName = 'Google Ads')then 1.025 * funnel_data.Cost
        WHEN (case when funnel_data.dim_1e81sjq0ist0a_Country_custom = "at - copy" then "at"
              else funnel_data.dim_1e81sjq0ist0a_Country_custom  end) = 'it' AND funnel_data.Date >= date(2021,10,1) AND (funnel_data.sourceTypeName = 'AdWords' OR funnel_data.sourceTypeName = 'Google Ads')then 1.02 * funnel_data.Cost
        ELSE funnel_data.Cost end)  ELSE 0 END  )  ELSE NULL END), 0) - COALESCE(SUM(funnel_data.cf1ftljmnkd6oub_Voucher_Amount ), 0) AS funnel_data_total_cost_online
FROM
    refb-analytics-funnelio.Funnel_export_refurbed.all_funnel_data_view as funnel_data
WHERE
    funnel_data.Date >= DATE_ADD(CURRENT_DATE('Europe/Vienna'), INTERVAL -7 DAY)
AND funnel_data.Date < DATE_ADD(DATE_ADD(CURRENT_DATE('Europe/Vienna'), INTERVAL -7 DAY), INTERVAL 7 DAY)
GROUP BY
    funnel_data_country,
    funnel_data_date;

-- 140 rows retrieved starting from 1 in 5 s 67 ms (execution: 4 s 861 ms, fetching: 206 ms)
-- Bytes processed: 47.17 MB
-- Bytes billed: 790 MB
select
    t.*
from funnel_data.performance_marketing_revenue t;

select
    DATE_ADD(CURRENT_DATE('Europe/Vienna'), INTERVAL -7 DAY) as start_date,
    current_date()-7 as start_date_2,
    DATE_ADD(DATE_ADD(CURRENT_DATE('Europe/Vienna'), INTERVAL -7 DAY), INTERVAL 7 DAY) as end_date,
    current_date() as end_date_2
;
