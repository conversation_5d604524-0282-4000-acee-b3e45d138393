-------------------------------------------------------------
-- 1. Original query: removed country filter and order by
-------------------------------------------------------------
drop table if exists test.discount_per_country cascade;
create table test.discount_per_country
as
select
    date(orders.paid_at::timestamptz at time zone 'Europe/Vienna') as paid_date,
    orders.country::text as country,
    coalesce(
        sum(
            (
                order_items.discount
                * ((1 / (exchange_rates.exchange_rate)))
                / order_items.presentment_exchange_rate_modifier
            )
        ),
        0
    )::numeric as total_discount_eur
from analytics.order_item_offers as order_item_offers
left join public.order_items as order_items on (order_item_offers.id) = order_items.id
left join public.orders as orders on order_items.order_id = (orders.id)
left join public.merchants as merchants on (order_item_offers.merchant_id) = (merchants.id)
left join analytics.order_item_exchange_rate as exchange_rates on
    (order_item_offers.id) = (exchange_rates.order_item_id)
where
    (
        (orders.paid_at)
        >= (
            (
                (
                    date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
        and (orders.paid_at)
        < (
            (
                (
                    (date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval)
                    + (7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
    )
    and order_item_offers.state in ('released', 'released.failed')
group by
    paid_date,
    orders.country;

-- test me:
select * from test.discount_per_country limit 10;

-------------------------------------------------------------
-- Re-write with analytics.order_financial_revenue
-------------------------------------------------------------
drop table if exists test.discount_per_country_analytics cascade;
create table test.discount_per_country_analytics
as
select
    date(orders.paid_at::timestamptz at time zone 'Europe/Vienna') as paid_date,
    orders.country::text as country,
    coalesce(sum(ofr.discount_eur), 0) as total_discount_eur
from
    analytics.order_financial_revenue as ofr
inner join
    public.orders as orders
on ofr.order_id = orders.id
where
    (
        (orders.paid_at)
        >= (
            (
                (
                    date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
        and (orders.paid_at)
        < (
            (
                (
                    (date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval)
                    + (7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
    )
    and orders.state in ('released', 'released.failed')
group by
    paid_date,
    orders.country;

-------------------------------------------------------------
-- Test queries: 114 rows
-------------------------------------------------------------
select count(*) from test.discount_per_country;
select count(*) from test.discount_per_country_analytics;

select
    orig.country,
    orig.paid_date,

    -- discounts
    orig.total_discount_eur,
    anal.total_discount_eur as total_discount_eur_anal,
    round(orig.total_discount_eur - anal.total_discount_eur, 2) as discount_diff
from
    test.discount_per_country as orig
inner join
    test.discount_per_country_analytics as anal
on orig.country = anal.country and orig.paid_date = anal.paid_date
where
    round(orig.total_discount_eur - anal.total_discount_eur, 2) > 0
order by
    discount_diff desc
limit 100;
