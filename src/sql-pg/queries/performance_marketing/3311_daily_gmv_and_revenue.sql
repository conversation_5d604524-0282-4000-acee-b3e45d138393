-------------------------------------------------------------
-- 1. Original query: removed country filter and order by
-------------------------------------------------------------
drop table if exists test.daily_gmv_and_revenue cascade;
create table test.daily_gmv_and_revenue
as
select
    date(daily_gmv_and_revenue.order_date::timestamptz at time zone 'Europe/Vienna') as order_date_date,
    daily_gmv_and_revenue.country,
    coalesce(sum((daily_gmv_and_revenue.total_revenue_eur::numeric)), 0) as total_revenue_eur
from
    analytics.daily_gmv_and_revenue as daily_gmv_and_revenue
where (
    (
        daily_gmv_and_revenue.order_date
        >= (
            (
                (
                    date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
        and daily_gmv_and_revenue.order_date
        < (
            (
                (
                    (date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval)
                    + (7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
    )
)
group by
    order_date_date,
    country;

-- test me:
select * from test.daily_gmv_and_revenue limit 10;

-------------------------------------------------------------
-- Re-write with analytics.order_financial_revenue
-------------------------------------------------------------
drop table if exists test.daily_gmv_and_revenue_analytics cascade;
create table test.daily_gmv_and_revenue_analytics
as
select
    date(orders.paid_at::timestamptz at time zone 'Europe/Vienna') as order_date_date,
    orders.country::text as country,
    coalesce(sum(ofr.revenue), 0) as total_revenue_eur
from
    analytics.order_financial_revenue as ofr
inner join
    public.orders as orders
on ofr.order_id = orders.id
where (
    (
        orders.paid_at
        >= (
            (
                (
                    date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
        and orders.paid_at
        < (
            (
                (
                    (date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-7 || ' day')::interval)
                    + (7 || ' day')::interval
                ) at time zone 'Europe/Vienna'
            )
        )
    )
)
group by
    order_date_date,
    country;

alter table test.daily_gmv_and_revenue_analytics
add constraint daily_gmv_and_revenue_analytics_pk primary key (order_date_date, country);

-------------------------------------------------------------
-- Test queries: 114 rows
-------------------------------------------------------------
select count(*) from test.daily_gmv_and_revenue;
select count(*) from test.daily_gmv_and_revenue_analytics;

select
    orig.country,
    orig.order_date_date,

    -- revenue
    orig.total_revenue_eur,
    anal.total_revenue_eur as total_revenue_eur_anal,
    round(orig.total_revenue_eur - anal.total_revenue_eur, 2) as revenue_diff
from
    test.daily_gmv_and_revenue as orig
inner join
    test.daily_gmv_and_revenue_analytics as anal
on orig.country = anal.country and orig.order_date_date = anal.order_date_date
where
    round(orig.total_revenue_eur - anal.total_revenue_eur, 2) > 0
order by
    revenue_diff desc
limit 100;
