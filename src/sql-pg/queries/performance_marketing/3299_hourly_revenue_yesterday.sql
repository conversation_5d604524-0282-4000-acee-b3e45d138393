-------------------------------------------------------------
-- 1. Original query: removed country filter and order by
-------------------------------------------------------------
drop table if exists test.hourly_revenue_yesterday cascade;
create table test.hourly_revenue_yesterday
as
select
    extract(hour from orders.paid_at::timestamptz at time zone 'Europe/Vienna') as paid_hour_of_day,
    orders.country::text as country,
    coalesce(sum((case
        when (
            merchants.is_addon_support
        )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier
                    )
                else (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
                    / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
        when
            (order_items.type = 'offer')
            then (
                order_items.presentment_base_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_target_price_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_payout_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_payment_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
        when (
            order_items.type = 'addon'
        )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier
                    )
                else (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
                    / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
            - (
                case
                    when
                        (order_items.addon_details ->> 'type') = 'extended-warranty'
                        then (
                            coalesce(((order_items.addon_details ->> 'cost'))::numeric, 0)
                            * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                    else 0
                end
            )
        when (order_items.type = 'service-fee') then (case
            when
                order_items.net
                then (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
            else (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
                / (1 + (order_items.vat / 100))
        end)
    end)), 0) as total_revenue_eur,
    coalesce(
        sum((order_items.discount * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)), 0
    ) as total_discount_eur
from public.order_items as order_items
inner join public.orders as orders on order_items.order_id = orders.id
left join public.offers as offers on
    order_items.offer_id = offers.id and order_items.offer_valid_from = offers.valid_from
left join public.merchants as merchants on offers.merchant_id = (merchants.id)
inner join
    public.exchange_rates as exchange_rates on orders.presentment_currency = exchange_rates.target
    and exchange_rates.base = 'EUR'
    and orders.created_at < exchange_rates.valid_to
    and orders.created_at >= exchange_rates.valid_from
where
    orders.paid_at
    >= (
        (
            (
                date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-1 || ' day')::interval
            ) at time zone 'Europe/Vienna'
        )
    )
    and orders.paid_at
    < (
        (
            (
                (date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-1 || ' day')::interval)
                + (1 || ' day')::interval
            ) at time zone 'Europe/Vienna'
        )
    )
    and orders.state in ('released', 'released.failed')
group by
    paid_hour_of_day,
    country;

-------------------------------------------------------------
-- Re-write with analytics.order_financial_revenue
-------------------------------------------------------------
drop table if exists test.hourly_revenue_yesterday_analytics cascade;
create table test.hourly_revenue_yesterday_analytics
as
select
    orders.country::text as country,
    extract(hour from orders.paid_at::timestamptz at time zone 'Europe/Vienna') as paid_hour_of_day,
    coalesce(sum(ofr.revenue), 0) as total_revenue_eur,
    coalesce(sum(ofr.discount_eur), 0) as total_discount_eur,
    coalesce(sum(ofr.addon_revenue), 0) as total_addon_eur,
    coalesce(sum(ofr.service_fee_revenue_eur), 0) as total_service_fee_eur
from
    analytics.order_financial_revenue as ofr
inner join
    public.orders as orders
on ofr.order_id = orders.id
where
    orders.paid_at
    >= (
        (
            (
                date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-1 || ' day')::interval
            ) at time zone 'Europe/Vienna'
        )
    )
    and orders.paid_at
    < (
        (
            (
                (date_trunc('day', current_timestamp at time zone 'Europe/Vienna') + (-1 || ' day')::interval)
                + (1 || ' day')::interval
            ) at time zone 'Europe/Vienna'
        )
    )
    and orders.state in ('released', 'released.failed')
group by
    paid_hour_of_day,
    country;

alter table test.hourly_revenue_yesterday_analytics
add constraint hourly_revenue_yesterday_analytics_pk primary key (country, paid_hour_of_day);

-------------------------------------------------------------
-- Test queries: 286 rows
-------------------------------------------------------------
select count(*) from test.hourly_revenue_yesterday;
select count(*) from test.hourly_revenue_yesterday_analytics;

select
    orig.country,
    orig.paid_hour_of_day,

    -- revenue
    orig.total_revenue_eur,
    anal.total_revenue_eur as total_revenue_eur_anal,
    round(orig.total_revenue_eur - anal.total_revenue_eur, 2) as revenue_diff,

    -- discounts
    orig.total_discount_eur,
    anal.total_discount_eur as total_discount_eur_anal,
    round(orig.total_discount_eur - anal.total_discount_eur, 2) as discount_diff,

    anal.total_addon_eur as total_addon_eur_anal,
    anal.total_service_fee_eur as total_service_fee_eur_anal
from
    test.hourly_revenue_yesterday as orig
inner join
    test.hourly_revenue_yesterday_analytics as anal
on orig.country = anal.country
    and orig.paid_hour_of_day = anal.paid_hour_of_day
where
    round(orig.total_revenue_eur - anal.total_revenue_eur, 2) > 0
    or
    round(orig.total_discount_eur - anal.total_discount_eur, 2) > 0
order by
    revenue_diff desc,
    discount_diff desc
limit 100;
