/*
 Optimizing `analytics.order_financial_revenue`
*/

-- 1. Save original `analytics.order_financial_revenue` rows
-- limited to loaded `analytics.order_item_exchange_rate`
drop table if exists order_financial_revenue_original;
create table order_financial_revenue_original
as
select ofr.*
from analytics.order_financial_revenue as ofr
inner join analytics.order_item_exchange_rate as oie
on ofr.order_item_id = oie.order_item_id;

-- 2. Update view definition to the new one
-- 3. Save new order_financial_revenue
drop table if exists order_financial_revenue_new;
create table order_financial_revenue_new
as
select ofr.*
from analytics.order_financial_revenue as ofr;

-- 4. Compare original vs new
create table temp_revenue_original_diff
as
select * from order_financial_revenue_original
except
select * from order_financial_revenue_new;

-- 290 differences, rounding ~0,01 difference
select
    t.*,
    ofrn.revenue as new_revenue,
    ofrn.addon_revenue as addon_revenu_new,
    ofrn.gmv as gmv_new
from
    temp_revenue_original_diff as t
left join
    order_financial_revenue_new as ofrn
on t.order_item_id = ofrn.order_item_id;

-- 290 differences, as above
create table temp_revenue_new_diff
as
select * from order_financial_revenue_new
except
select * from order_financial_revenue_original;

-- Expect no differences in order_item_id
select order_item_id from order_financial_revenue_new
except
select order_item_id from order_financial_revenue_original
except
select order_item_id from order_financial_revenue_new;

-- 5. Measure performance of export_etl.v_order_item_offers
-- Original: >> 15 minutes...
-- New: 15,901,024 rows affected in 2 m 6 s 376 ms
-- size: 2258 MB
drop table if exists temp_order_item_offers;
create temp table temp_order_item_offers
as
select * from export_etl.v_order_item_offers;
