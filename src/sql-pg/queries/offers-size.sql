-- Database schema sizes
drop table if exists import.schema_size;
create table import.schema_size
as
select
    t.schema_name,
    pg_size_pretty(sum(t.table_size)::bigint) as schema_size,
    (sum(t.table_size) / pg_database_size(current_database())) * 100 as relative_size
from (
    select
        n.nspname as schema_name,
        pg_relation_size(c.oid) as table_size
    from
        pg_catalog.pg_class as c
    inner join
        pg_catalog.pg_namespace as n
    on c.relnamespace = n.oid
) as t
group by t.schema_name
order by t.schema_name;

select * from import.schema_size
order by relative_size;

-- Offers table size
drop table if exists import.offers_table_size;
create table import.offers_table_size
as
select
    current_date as query_date,
    -- The size of the table including indexes: 101 GB
    pg_size_pretty(pg_total_relation_size('public.offers')) as total_table_size,
    -- The size of the table data only: 54 GB
    pg_size_pretty(pg_relation_size('public.offers')) as data_size,
    -- Indexes size: 46 GB
    pg_size_pretty(pg_indexes_size('public.offers')) as index_size;

select * from import.offers_table_size;

-- offers table index sizes
drop table if exists import.offers_index_size;
create table import.offers_index_size
as
select
    i.indexrelname as index_name,
    pg_size_pretty(pg_relation_size(i.indexrelid)) as index_size,
    pg_relation_size(i.indexrelid) / 1024.0 / 1024.0 as index_size_mb,
    pg_relation_size(i.indexrelid) / 1024.0 / 1024.0 / 1024.0 as index_size_gb
/*
    pg_size_pretty(pg_total_relation_size(i.relid)) as total_table_size,
    pg_size_pretty(pg_indexes_size(i.relid)) as all_indexes_size,
    pg_size_pretty(pg_relation_size(i.relid)) as table_data_size,
*/
from pg_stat_all_indexes as i
inner join
    pg_class as c
on i.relid = c.oid
where i.schemaname = 'public'
    and i.relname = 'offers';

select * from import.offers_index_size
order by index_size;

-- offers data by MONTH of created_at
drop table if exists import.offers_size_by_month;
create table import.offers_size_by_month
as
select
    to_char(valid_from, 'YYYY-MM') as valid_from_month,
    pg_size_pretty(sum(pg_column_size(t.*))) as row_size,
    count(*) as row_count
from
    public.offers
group by
    valid_from_month;

select * from import.offers_size_by_month
order by valid_from_month;

-- offers data by YEAR of created_at
drop table if exists import.offers_size_by_year;
create table import.offers_size_by_year
as
select
    date_part('year', valid_from) as valid_from_year,
    pg_size_pretty(sum(pg_column_size(t.*))) as row_size,
    count(*) as row_count
from
    public.offers
group by
    valid_from_year;

select * from import.offers_size_by_year
order by valid_from_year;
