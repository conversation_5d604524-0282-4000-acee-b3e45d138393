/*
    Notion: https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4?pvs=4#193b6a8983ab80419b85eb11eb3526d0 -- noqa

*/
-- noqa: disable=all
select
    orders.id as "orders.id",
    (date(orders.paid_at)) as "orders.paid_date",
    (to_char(date_trunc('second', orders.paid_at), 'YYYY-MM-DD HH24:MI:SS')) as "orders.paid_time",
    cast(orders.country as text) as "orders.country",
    order_items.id as "order_items.id",
    instances.product_id as "instances.product_id",
    instances.id as "instances.id",
    offers.grading as "offers.grading",
    offers.warranty as "offers.warranty",
    offers.id as "offers.id",
    order_items.charged
    * (1 / exchange_rates.rate)
    / order_items.presentment_exchange_rate_modifier as "order_items.charged_eur",
    order_items.presentment_price_gross
    * (1 / exchange_rates.rate)
    / order_items.presentment_exchange_rate_modifier as "order_items.price_gross_eur",
    order_items.presentment_shipping_costs_gross
    * (1 / exchange_rates.rate)
    / order_items.presentment_exchange_rate_modifier as "order_items.shipping_costs_gross_eur",
    (order_items.presentment_price_gross * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
    + (
        order_items.presentment_shipping_costs_gross
        * (1 / exchange_rates.rate)
        / order_items.presentment_exchange_rate_modifier
    ) as "order_items.gmv_eur",
    (case
        when (
            merchants.is_addon_support
        )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.price
                    * (1 / exchange_rates.rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
        when
            (order_items.type = 'offer')
            then (
                order_items.presentment_base_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_target_price_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_payout_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
            + (
                order_items.presentment_payment_commission
                * (1 / exchange_rates.rate)
                / order_items.presentment_exchange_rate_modifier
            )
        when (
            order_items.type = 'addon'
        )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier
                    )
                else (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
                    / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
            - (
                case
                    when
                        (order_items.addon_details ->> 'type') = 'extended-warranty'
                        then (
                            coalesce(cast((order_items.addon_details ->> 'cost') as numeric), 0)
                            * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                    else 0
                end
            )
        when (order_items.type = 'service-fee') then (case
            when
                order_items.net
                then (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
            else (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
                / (1 + (order_items.vat / 100))
        end)
    end) as "order_items.revenue_eur",
    users.id as "users.id",
    case when (order_items.type = 'addon') or (
                merchants.is_addon_support
            )
            then (case
                when
                    order_items.net
                    then (
                        order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier
                    )
                else (order_items.price * (1 / exchange_rates.rate) / order_items.presentment_exchange_rate_modifier)
                    / (1 + (order_items.vat / 100))
            end) + (case
                when
                    order_items.net
                    then (
                        order_items.shipping_costs
                        * (1 / exchange_rates.rate)
                        / order_items.presentment_exchange_rate_modifier
                    )
                else (
                    order_items.shipping_costs
                    * (1 / exchange_rates.rate)
                    / order_items.presentment_exchange_rate_modifier
                )
                / (1 + (order_items.vat / 100))
            end)
            - (
                case
                    when
                        (order_items.addon_details ->> 'type') = 'extended-warranty'
                        then (
                            coalesce(cast((order_items.addon_details ->> 'cost') as numeric), 0)
                            * (1 / exchange_rates.rate)
                            / order_items.presentment_exchange_rate_modifier
                        )
                    else 0
                end
            )
        else 0
    end as "order_items.addon_revenue_eur",
    merchants.id as "merchants.id"
from public.order_items as order_items
inner join public.orders as orders on order_items.order_id = orders.id
left join public.offers as offers on
    order_items.offer_id = offers.id and order_items.offer_valid_from = offers.valid_from
left join public.instances as instances on offers.instance_id = instances.id
left join public.merchants as merchants on offers.merchant_id = (merchants.id)
left join users as users on orders.user_id = users.id
inner join
    public.exchange_rates as exchange_rates on orders.presentment_currency = exchange_rates.target
    and exchange_rates.base = 'EUR'
    and orders.created_at < exchange_rates.valid_to
    and orders.created_at >= exchange_rates.valid_from
where (
    (
        (orders.paid_at)
        >= ((date_trunc('day', current_timestamp at time zone 'UTC') + cast((-1 || ' day') as interval)))
        and (orders.paid_at)
        < (
            (
                (date_trunc('day', current_timestamp at time zone 'UTC') + cast((-1 || ' day') as interval))
                + cast((1 || ' day') as interval)
            )
        )
    )
)
and (orders.state in ('released', 'released.failed')
)
order by
    2 desc
-- noqa: enable=all
