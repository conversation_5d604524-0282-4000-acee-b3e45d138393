/*
    ORIGINAL QUERY: transaction_db
    - https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4?pvs=4#161b6a8983ab80358964c1fd3e3e79dc -- noqa
    - https://console.cloud.google.com/functions/details/europe-west3/bi_orders_job?env=gen1&inv=1&invt=AbpRJQ&project=refb-analytics&tab=source -- noqa

transaction_db AS (
SELECT
  order_date,
  country,
  CAST(product_id AS STRING) AS product_id,
  CAST(transaction_id AS STRING) AS transaction_id,
  CAST(revenue_eur AS float64) AS revenue_eur,
  CAST(addon_revenue_eur AS float64) AS addon_revenue_eur,
  CAST(gmv_eur AS float64) AS gmv_eur,
  COUNT(*) AS nbr_purchase
FROM
  `refb-analytics.etl_internal.orders`
WHERE
  order_date BETWEEN FORMAT_DATE("%Y-%m-%d", query_start_date)
  AND FORMAT_DATE("%Y-%m-%d", query_end_date)
GROUP BY  ALL;
*/

-- Rewrite using analytics tables/views
select
    oio.paid_at as order_date,
    oio.country,
    i.product_id,
    oio.order_id,
    oio.id as order_item_id,
    ofr.revenue as revenue_eur,
    ofr.addon_revenue as addon_revenue_eur,
    ofr.gmv as gmv_eur
    -- count(*) as nbr_purchase
from
    export_etl.v_order_item_offers as oio
left join
    export_etl.v_instances as i
on oio.instance_id = i.id
left join
    analytics.order_financial_revenue as ofr
on oio.id = ofr.order_item_id
where
    oio.order_id = 6173129;
