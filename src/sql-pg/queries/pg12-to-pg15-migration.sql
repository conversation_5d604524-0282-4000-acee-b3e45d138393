/**
# MIGRATE PG12 to PG15

1. Ensure you have your buddy looking at your shoulders all the time!
2. Remember to write M<PERSON> message before and after the upgrade!
*/

-- 1. LOGIN as `platform` user!
select current_user;
select version();

-- 2. Lock all users (except platform)
call maintenance.lock_non_system_users();

-- 3. Change platform user password
-- https://console.cloud.google.com/security/secret-manager/secret/
-- analytics-cloud-sql-config/versions?hl=en&project=refb-analytics
-- alter role platform with login password 'new password'

-- !!! RECONNECT with new PASSWORD !!!

-- 4. Kill all running sessions (except yours)
select * from monitoring.who_is_active();
select distinct pg_terminate_backend(session_id) from monitoring.who_is_active();

-- 5. Upgrade instance to PG 15.7 via GCP console
-- 20 minutes
-- https://console.cloud.google.com/sql/instances/analytics-08ce/overview?hl=en&project=refb-analytics

-- automatic backup is created as part of in place upgrade:
-- https://cloud.google.com/sql/docs/postgres/upgrade-major-db-version-inplace

-- 6. Run ANALYZE on your primary instance to update the system statistics after the upgrade.
-- completed in 4 m 43 s 845 ms
analyse verbose;

-- 6. Release platform user
-- alter role platform with login password 'old password'

-- !!! RECONNECT with new PASSWORD !!!

-- 7. Unlock all users
call maintenance.unlock_non_system_users();
