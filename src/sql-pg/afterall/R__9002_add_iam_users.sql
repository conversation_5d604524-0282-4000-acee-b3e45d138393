-- Flyway will re-run this script if it is changed or ${changeReason} is updated

do
/**
    For now, this script can run on staging only, where we have Cloud SQL with IAM authentication enabled
    the list of IAM users can be <NAME_EMAIL> group
    once this bug is fixed: # https://github.com/hashicorp/terraform-provider-google/issues/17040
*/
$do$
    declare
        sql       text;
        env       text = '${flyway:environment}';
        de_group  text = '<EMAIL>';
        da_group  text = '<EMAIL>';
    begin
        if not exists(select * from pg_catalog.pg_settings where name = 'cloudsql.iam_authentication' and setting = 'on') then
            raise notice 'R__9002_add_iam_users: skipping the script, `cloudsql.iam_authentication` not enabled...';
            return;
        end if;

        if env = 'production' then
            raise notice 'R__9002_add_iam_users: skipping the script for `production`...';
            return;
        end if;

        sql := format('grant platform to "%s";', de_group);
        raise notice '%', sql;
        execute sql;

        sql := format('grant read_write to "%s";', da_group);
        raise notice '%', sql;
        execute sql;
    end
$do$;
