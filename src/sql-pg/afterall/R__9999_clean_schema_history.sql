/*
    Flyway runs this script on every migration: ${flyway:timestamp}.

    Remove repeatable entries if there are more than 2 versions of each
    to keep the history table within a reasonable size.

    Keep historical versions in analytics.schema_history_rank.
*/

-- Rank schema_history entries by installed date
truncate table analytics.schema_history_rank;
insert into analytics.schema_history_rank
select
    *,
    row_number() over (
        partition by script
        order by installed_on desc
    ) as rank
from
    analytics.schema_history
where
    version is null;

-- About to delete
select
    script,
    rank,
    installed_on
from
    analytics.schema_history_rank
where
    rank > 2
order by
    script,
    rank,
    installed_on;

-- Delete all versions except the last 2
delete from analytics.schema_history
where
    installed_rank in (
        select installed_rank
        from analytics.schema_history_rank
        where rank > 2
    );
