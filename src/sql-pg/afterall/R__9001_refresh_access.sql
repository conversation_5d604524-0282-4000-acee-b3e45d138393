-- Flyway runs this script on every migration: ${flyway:timestamp}
-- The definitions for the routines used below are in the infrastructure-iam repository

-- Refreshes reader and writer roles
call maintenance.configure_read_write_role('read_only');
call maintenance.configure_read_write_role('read_write');

--  Create analytics-pipelines technical users
call maintenance.create_user('db_refresh');
call maintenance.create_user('query_terminator');
call maintenance.create_user('cf_reader');
call maintenance.create_user('cf_writer');

-- Grants read_only to users
grant read_only to cf_reader;

-- Grants read_write to users
grant read_write to cf_writer;

-- Grants platform to technical users (special case)
grant platform to db_refresh;
grant platform to query_terminator;
