-- Flyway will re-run this script if ${changeReason} is updated
do
$do$
    begin
        if '${flyway:environment}' = 'production' then
            raise notice 'Skipping the script...';
            return;
        end if;

        truncate table analytics.connected_customers_dev;
        insert into analytics.connected_customers_dev
        select
            created_at,
            null,
            id,
            paid_at as order_date,
            user_id,
            trunc(random() * 9)+2 as order_number, -- just to have value always greater than 1 (used by sql test)
            null
        from
            public.orders;
    end
$do$;
