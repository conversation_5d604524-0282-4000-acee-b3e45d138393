-- Flyway will re-run this script if ${changeReason} is updated
do
$do$
    begin
        if '${flyway:environment}' = 'local' then
            raise notice 'Inserting dummy data for zendesk_auto_responder...';

            -- Truncate the tables
            truncate table analytics.pipedrive;
            truncate table analytics.missing_pipedrive;

            -- Insert dummy data into pipedrive
            insert into analytics.pipedrive (merchant_name, merchant_id, has_performance_role, primary_email, account_manager, account_manager_email)
            values
                ('Merchant 1', 1, true, '<EMAIL>', '<PERSON>', '<EMAIL>'),
                ('Merchant 2', 2, false, '<EMAIL>', '<PERSON>', '<EMAIL>');

            -- Insert dummy data into missing_pipedrive
            insert into analytics.missing_pipedrive (merchant_name, merchant_id, primary_email, account_manager, account_manager_email)
            values
                ('Missing Merchant 3', 3, null, '<PERSON>', '<EMAIL>'),
                ('Missing Merchant 4', 4, '<EMAIL>', '<PERSON>', '<EMAIL>');

            -- Insert dummy data into merchants, if no data is there
            if not exists(select from public.merchants) then
                insert into public.merchants (id, name, state)
                values
                    (1, 'Merchant 1', 'active'),
                    (2, 'Merchant 2', 'active'),
                    (3, 'Missing Merchant 3', 'inactive'),
                    (4, 'Missing Merchant 4', 'active');
            end if;
        end if;
    end
$do$;
