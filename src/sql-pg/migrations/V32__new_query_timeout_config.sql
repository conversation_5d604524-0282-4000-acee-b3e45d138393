-- Clear initial test configuration
truncate table maintenance.query_timeout_config restart identity;

insert into maintenance.query_timeout_config (
    created_by,
    description,
    terminate,
    timeout_sec,
    login_name
)
values (
    'Data Engineering',
    'Report on all platform queries running longer than 20 minutes',
    false,
    1200,
    'platform'
);

insert into maintenance.query_timeout_config (
    created_by,
    description,
    terminate,
    timeout_sec,
    program_name
)
values (
    'Data Engineering',
    'Terminate all looker queries running longer than 30 minutes',
    true,
    1800,
    'looker'
);

-- test me: maintenance.query_timeout_config
select * from maintenance.query_timeout_config;
