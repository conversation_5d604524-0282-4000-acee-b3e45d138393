do
$do$
    begin
        -- pg_terminate_backend(pid int) user
        -- https://stackoverflow.com/questions/50806254/how-do-i-terminate-a-session-in-google-cloud-sql-for-postgresql
        if not exists (select * from pg_catalog.pg_user where usename = 'query_terminator') then
            create user query_terminator;

            grant platform to query_terminator;
            grant pg_signal_backend to query_terminator;
            -- To create password run this manually:
            -- alter user query_terminator with password 'password-here';
        end if;
    end
$do$;
