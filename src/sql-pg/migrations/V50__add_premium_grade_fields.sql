-- Add premium grade fields to `analytics.mps_per_country_combined_history` table.
-- Following this MR: https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/mps_per_country/-/merge_requests/84
alter table if exists analytics.mps_per_country_combined_history
add column if not exists n_order_items_aa numeric,
add column if not exists count_condition_aa numeric,
add column if not exists condition_rate_aa numeric,
add column if not exists share_sales_aa numeric,
add column if not exists condition_points_aa numeric;
