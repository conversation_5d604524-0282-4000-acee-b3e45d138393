------------------------------------------------------------
-- dhl_order_items
------------------------------------------------------------
drop table if exists import.dhl_order_items;
create unlogged table import.dhl_order_items
(
    shipment_number text,
    order_item_id int
);

------------------------------------------------------------
-- dhl_shipping_billing
-- https://stackoverflow.com/questions/********/which-datatype-should-be-used-for-currency
-- select '999999.9999'::decimal(12, 4) as number, pg_column_size('999999.9999'::decimal(12, 4));
------------------------------------------------------------
drop table if exists import.dhl_shipping_billing;
create unlogged table import.dhl_shipping_billing
(
    line_type text,
    billing_source text,
    billing_account text,
    billing_country_code text,
    invoice_date date,
    invoice_number text,
    shipment_number text,
    shipment_date date,
    shipment_reference_1 text,
    shipment_reference_2 text,
    shipment_reference_3 text,
    weight_kg decimal(12, 4),
    weight_flag text,
    senders_name text,
    senders_country text,
    receivers_country text,
    currency text,
    amount_net decimal(12, 4),
    amount_gross decimal(12, 4),
    xc1_code text,
    xc1_net decimal(12, 4),
    xc1_gross decimal(12, 4),
    xc2_code text,
    xc2_net decimal(12, 4),
    xc2_gross decimal(12, 4),
    xc3_code text,
    xc3_net decimal(12, 4),
    xc3_gross decimal(12, 4),
    xc4_code text,
    xc4_net decimal(12, 4),
    xc4_gross decimal(12, 4),
    xc5_code text,
    xc5_net decimal(12, 4),
    xc5_gross decimal(12, 4),
    xc6_code text,
    xc6_net decimal(12, 4),
    xc6_gross decimal(12, 4),
    xc7_code text,
    xc7_net decimal(12, 4),
    xc7_gross decimal(12, 4),
    xc8_code text,
    xc8_net decimal(12, 4),
    xc8_gross decimal(12, 4),
    xc9_code text,
    xc9_net decimal(12, 4),
    xc9_gross decimal(12, 4),
    file_path text,
    file_created timestamp with time zone,
    file_size int,
    package_type text,
    remote_area_delivery_net decimal(12, 4),
    remote_area_delivery_gross decimal(12, 4),
    remote_area_pickup_net decimal(12, 4),
    remote_area_pickup_gross decimal(12, 4),
    fuel_surcharge_net decimal(12, 4),
    fuel_surcharge_gross decimal(12, 4),
    change_of_billing_net decimal(12, 4),
    change_of_billing_gross decimal(12, 4),
    duty_tax_paid_net decimal(12, 4),
    duty_tax_paid_gross decimal(12, 4),
    emergency_situation_net decimal(12, 4),
    emergency_situation_gross decimal(12, 4),
    export_declaration_net decimal(12, 4),
    export_declaration_gross decimal(12, 4),
    carbon_reduced_fe_net decimal(12, 4),
    carbon_reduced_fe_gross decimal(12, 4),
    carbon_reduced_fd_net decimal(12, 4),
    carbon_reduced_fd_gross decimal(12, 4),
    carbon_reduced_ft_net decimal(12, 4),
    carbon_reduced_ft_gross decimal(12, 4),
    import_export_duties_net decimal(12, 4),
    import_export_duties_gross decimal(12, 4),
    import_export_taxes_net decimal(12, 4),
    import_export_taxes_gross decimal(12, 4),
    oversize_piece_net decimal(12, 4),
    oversize_piece_gross decimal(12, 4),
    overweight_piece_net decimal(12, 4),
    overweight_piece_gross decimal(12, 4),
    plastic_flyer_net decimal(12, 4),
    plastic_flyer_gross decimal(12, 4),
    shipment_insurance_net decimal(12, 4),
    shipment_insurance_gross decimal(12, 4)
);
