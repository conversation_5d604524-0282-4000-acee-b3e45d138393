do
$do$
    -- run it in one transaction
    begin
        drop table if exists analytics.pipedrive_bk;
        create unlogged table pipedrive_bk as
        select * from analytics.pipedrive;

        drop table if exists analytics.pipedrive;
        create table analytics.pipedrive
        (
            new_sales_manager text,
            new_sales_manager_email text,
            merchant_id integer,
            merchant_name text,
            primary_email text,
            all_emails json,
            account_manager text,
            account_manager_email text,
            account_name text,
            first_name text,
            last_name text,
            contact_roles text[] null,
            has_performance_role bool null,
            live_selling timestamptz,
            date_entered date,
            date_closed date
        );

        insert into analytics.pipedrive
        (new_sales_manager,
         merchant_id,
         merchant_name,
         primary_email,
         all_emails,
         account_manager,
         account_name,
         first_name,
         last_name,
         live_selling,
         date_entered,
         date_closed)
        select
            new_sales_manager,
            merchant_id,
            merchant_name,
            primary_email,
            all_emails,
            account_manager,
            account_name,
            first_name,
            last_name,
            live_selling,
            date_entered,
            date_closed
        from
            pipedrive_bk;

        drop table if exists analytics.pipedrive_bk;

    end
$do$;
