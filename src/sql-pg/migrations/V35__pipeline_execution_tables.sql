drop table if exists log.pipeline_execution_step cascade;
drop table if exists log.pipeline_execution cascade;

create table log.pipeline_execution
(
    id int constraint pipeline_execution_pk primary key generated by default as identity,
    pipeline_name text not null,
    run_id text not null,
    started timestamp with time zone not null default (now() at time zone 'utc'),
    finished timestamp with time zone null,
    error text null,
    succeeded bool generated always as (finished is not null and error is null) stored,

    constraint pipeline_execution_uq unique (pipeline_name, run_id)
);

create table log.pipeline_execution_step
(
    id int constraint pipeline_execution_step_pk primary key generated by default as identity,
    pipeline_execution_id int not null
    constraint pipeline_execution_step_pipeline_execution references log.pipeline_execution (id) on delete cascade,
    step_name text not null,
    started timestamp with time zone not null default (now() at time zone 'utc'),
    finished timestamp with time zone null,
    error text null,
    succeeded bool generated always as (finished is not null and error is null) stored,

    constraint pipeline_execution_step_uq unique (pipeline_execution_id, step_name)
);
