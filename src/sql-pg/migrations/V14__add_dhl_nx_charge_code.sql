-- IMPORT: add demand_surcharge net/gross
alter table import.dhl_shipping_billing
add column if not exists demand_surcharge_net decimal(12, 4),
add column if not exists demand_surcharge_gross decimal(12, 4);

-- ANALYTICS: add demand_surcharge net/gross
alter table analytics.dhl_shipping_charges
add column if not exists demand_surcharge_net decimal(12, 4),
add column if not exists demand_surcharge_gross decimal(12, 4),
drop column if exists total_net cascade,
drop column if exists total_gross cascade;

-- ANALYTICS: update total net/gross
alter table analytics.dhl_shipping_charges
add column total_net numeric(12, 4) generated always as (
    remote_area_delivery_net
    + remote_area_pickup_net
    + fuel_surcharge_net
    + change_of_billing_net
    + duty_tax_paid_net
    + emergency_situation_net
    + export_declaration_net
    + carbon_reduced_fe_net
    + carbon_reduced_fd_net
    + carbon_reduced_ft_net
    + import_export_duties_net
    + import_export_taxes_net
    + oversize_piece_net
    + overweight_piece_net
    + plastic_flyer_net
    + shipment_insurance_net
    + demand_surcharge_net
) stored,
add column total_gross numeric(12, 4) generated always as (
    remote_area_delivery_gross
    + remote_area_pickup_gross
    + fuel_surcharge_gross
    + change_of_billing_gross
    + duty_tax_paid_gross
    + emergency_situation_gross
    + export_declaration_gross
    + carbon_reduced_fe_gross
    + carbon_reduced_fd_gross
    + carbon_reduced_ft_gross
    + import_export_duties_gross
    + import_export_taxes_gross
    + oversize_piece_gross
    + overweight_piece_gross
    + plastic_flyer_gross
    + shipment_insurance_gross
    + demand_surcharge_gross
) stored;
