-- STAGING table for fast import
drop table if exists import.order_item_offers;
create unlogged table import.order_item_offers
(
    id integer not null,
    created_at timestamp with time zone not null,
    updated_at timestamp with time zone not null,
    order_id integer not null,
    offer_id integer,
    offer_valid_from timestamp,
    type text not null,
    paid_at timestamp with time zone,
    state text not null,
    country text not null,
    presentment_currency text not null,
    payment_provider text not null,
    valid_to timestamp,
    bq_valid_to timestamp,
    instance_id integer,
    merchant_id integer,
    grading text,
    warranty smallint
);

-- TARGET table exposed to Analytics team
drop table if exists analytics.order_item_offers;
create table analytics.order_item_offers
(
    id integer constraint order_item_offers_pk primary key,
    created_at timestamp with time zone not null,
    updated_at timestamp with time zone not null,
    order_id integer not null,
    offer_id integer,
    offer_valid_from timestamp,
    type text not null,
    paid_at timestamp with time zone,
    state text not null,
    country text not null,
    presentment_currency text not null,
    payment_provider text not null,
    valid_to timestamp,
    instance_id integer,
    merchant_id integer,
    grading text,
    warranty smallint
);

-- index FKs referenced columns
drop index if exists analytics.order_item_offers_order_id_idx;
create index order_item_offers_order_id_idx
on analytics.order_item_offers (order_id);

drop index if exists analytics.order_item_offers_offer_idx;
create index order_item_offers_offer_idx
on analytics.order_item_offers (offer_id, offer_valid_from);

drop index if exists analytics.order_item_offers_instance_id_idx;
create index order_item_offers_instance_id_idx
on analytics.order_item_offers (instance_id);

drop index if exists analytics.order_item_offers_merchant_id_idx;
create index order_item_offers_merchant_id_idx
on analytics.order_item_offers (merchant_id);

-- index commonly queried fields
drop index if exists analytics.order_item_offers_paid_at_idx;
create index order_item_offers_paid_at_idx
on analytics.order_item_offers (paid_at);

drop index if exists analytics.order_item_offers_state_idx;
create index order_item_offers_state_idx
on analytics.order_item_offers (state);

drop index if exists analytics.order_item_offers_type_idx;
create index order_item_offers_type_idx
on analytics.order_item_offers (type);
