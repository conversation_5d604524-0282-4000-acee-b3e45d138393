-- This table already exists in PROD!
create table if not exists analytics.shipping_billing
(
    id serial primary key,
    shipping_provider varchar(5),
    account_number text,
    account_country varchar(2),
    invoice_date date,
    invoice_number text,
    account_tax_id text,
    invoice_currency_code varchar(3),
    invoice_amount numeric,
    transaction_date date,
    lead_shipment_number text,
    shipment_reference_1 text,
    shipment_reference_2 text,
    package_quantity integer,
    tracking_number text,
    package_reference_1 text,
    package_reference_2 text,
    entered_weight real,
    entered_weight_unit text,
    billed_weight real,
    billed_weight_unit text,
    billed_weight_type text,
    package_dimensions text,
    charge_category_code text,
    charge_classification_code text,
    charge_description_code text,
    charge_description text,
    charged_units integer,
    tax_indicator text,
    incentive_amount numeric,
    net_amount numeric,
    misc_currency_code varchar(3),
    misc_incentive_amount numeric,
    misc_net_amount numeric,
    alt_currency_code varchar(3),
    alt_invoice_amount numeric,
    invoice_due_date date,
    sender_company_name text,
    sender_country text,
    receiver_country text
);
