create table if not exists public.order_item_offers
(
    id integer constraint order_item_offers_pk primary key,
    created_at timestamp with time zone not null,
    updated_at timestamp with time zone not null,
    order_id integer not null,
    offer_id integer,
    offer_valid_from timestamp,
    type text not null,
    paid_at timestamp with time zone,
    state text not null,
    country text not null,
    presentment_currency text not null,
    payment_provider text not null,
    valid_to timestamp,
    instance_id integer,
    merchant_id integer,
    grading text,
    warranty smallint
);

-- index FKs referenced columns
create index if not exists order_item_offers_order_id_idx
on public.order_item_offers (order_id);

create index if not exists order_item_offers_offer_idx
on public.order_item_offers (offer_id, offer_valid_from);

create index if not exists order_item_offers_instance_id_idx
on public.order_item_offers (instance_id);

create index if not exists order_item_offers_merchant_id_idx
on public.order_item_offers (merchant_id);

-- index commonly queried fields
create index if not exists order_item_offers_paid_at_idx
on public.order_item_offers (paid_at);

create index if not exists order_item_offers_state_idx
on public.order_item_offers (state);

create index if not exists order_item_offers_type_idx
on public.order_item_offers (type);
