/*
Re-create existing `full_order_item_refunds` index table with the following changes:
- new columns: `order_item_refund_id`, `updated_at`
- `refunded_at` converted from date to timestamp
- new commission columns
*/
drop table if exists analytics.full_order_item_refunds cascade;
create table analytics.full_order_item_refunds
(
    order_item_refund_id integer constraint full_order_item_refunds_pk primary key,
    order_item_id integer not null,
    refunded_at timestamp with time zone not null,
    updated_at timestamp with time zone not null,
    refunded decimal(12, 4) not null,

    -- Nullable columns
    refunded_charge decimal(12, 4),
    refunded_eur decimal(12, 4),
    refunded_charge_eur decimal(12, 4),
    min_reversed_commission_date date,

    held_base_commission decimal(12, 4),
    kept_base_commission decimal(12, 4),
    executed_base_commission decimal(12, 4),

    held_payment_commission decimal(12, 4),
    kept_payment_commission decimal(12, 4),
    executed_payment_commission decimal(12, 4),

    held_base_commission_eur decimal(12, 4),
    kept_base_commission_eur decimal(12, 4),
    executed_base_commission_eur decimal(12, 4),

    held_payment_commission_eur decimal(12, 4),
    kept_payment_commission_eur decimal(12, 4),
    executed_payment_commission_eur decimal(12, 4)
);
