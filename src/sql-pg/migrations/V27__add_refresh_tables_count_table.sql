drop table if exists maintenance.refresh_tables_count;
create table maintenance.refresh_tables_count
(
    id int constraint refresh_tables_count_pk primary key generated by default as identity,
    run_id text not null,
    db_name text not null,
    schema_name text not null,
    table_name text not null,
    table_count bigint
);

drop index if exists maintenance.refresh_tables_run_id_idx;
create index refresh_tables_run_id_idx
on maintenance.refresh_tables_count (run_id);
