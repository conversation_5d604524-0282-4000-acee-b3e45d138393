/*
This table already exists in PROD
created daily from here
https://gitlab.com/refurbed/analytics-and-ds/cloud-functions/mps_per_country/-/blob/main/utils/queries/unnest_and_join_json_histories.sql?ref_type=heads#L5 -- noqa
It's full schema can change if we change the json data columns (rare-ish) (but it's a process we have control on)
however for circuit breakers (goal of this migration), we need specific fields

>> Query to get the schema:

select
    column_name,
    data_type
from information_schema.columns
where table_schema = 'analytics'
  and table_name   = 'mps_per_country_combined_history'
order by ordinal_position;

 */

create table if not exists analytics.mps_per_country_combined_history
(
    -- general fields
    date timestamp without time zone,
    id bigint,
    country text,
    name text,
    first_order_date timestamp without time zone,
    n_order_items numeric,

    -- condition score
    count_condition numeric,
    condition_points_per_grading numeric,

    -- defect score
    defect_points numeric,
    count_defect numeric,

    -- urgent score
    mid_term_urgents_points numeric,

    -- response time
    s_messages numeric,
    ten_h numeric,

    -- csat
    csat_points numeric
);

-- create index if not exists
create unique index if not exists uq_date_country_id
on analytics.mps_per_country_combined_history (date, country, id);
