drop table if exists maintenance.query_timeout_config;
create table maintenance.query_timeout_config
(
    id int constraint query_timeout_config_pk primary key generated by default as identity,
    created_at timestamp with time zone not null default current_timestamp,
    created_by text not null,
    description text not null,
    -- whether to terminate or just report on long-running query
    terminate boolean default false,
    -- default timeout of 30 minutes
    timeout_sec int not null default 1800,
    valid_from timestamp with time zone not null default current_timestamp,
    valid_to timestamp with time zone,

    -- Conditions on fields returned by monitoring.who_is_active()
    program_name text,
    login_name text,
    sql_text text,

    constraint query_timeout_config_unique
    unique nulls not distinct (program_name, login_name, sql_text),

    constraint query_timeout_config_check
    check (program_name is not null or login_name is not null or sql_text is not null)
);

-- Report on all `looker` queries running for longer than 30 minutes
insert into maintenance.query_timeout_config (
    created_by, description, program_name, login_name, sql_text
)
values (
    'Data Engineering',
    'Report on looker queries running longer than 30 minutes',
    'looker',
    null,
    null
);

-- test me: maintenance.query_timeout_config
select * from maintenance.query_timeout_config;
