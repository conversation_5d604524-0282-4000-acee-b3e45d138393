drop table if exists analytics.dhl_shipping_config;

-- Configuration table to fill `dhl.dhl_shared.ShippingConfig` class
create table analytics.dhl_shipping_config
(
    -- Keys: https://www.danieleteti.it/post/postgresql-identities-vs-serials/
    id int constraint dhl_shipping_config_pk primary key generated by default as identity,

    -- Control fields
    created_at timestamp with time zone not null default (now() at time zone 'utc'),
    updated_at timestamp with time zone,
    is_active boolean not null default true,

    -- Mandatory fields without defaults
    country text constraint dhl_shipping_config_uq unique,
    support_email text not null,
    outbound_account text not null,
    inbound_account text not null,

    -- Mandatory fields with defaults
    file_suffix text not null default '.csv',
    encoding text not null default 'utf-8',
    quoted_char text not null default '"',

    constraint account_check check (outbound_account != inbound_account)
);

-- https://refurbed-my.sharepoint.com/:x:/p/to<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>/EdT32jA4NthOmUyVi6DYn1QBvsLRNq6AeAT1Ow5XhqRVCQ?e=XEDcdg
insert into analytics.dhl_shipping_config (
    country,
    support_email,
    outbound_account,
    inbound_account,
    is_active
)
values
('ES', '<EMAIL>', '*********', '*********', true),
('NL', '<EMAIL>', '*********', '*********', false),
('DE', '<EMAIL>', '', '*********', false);

-- test me: analytics.dhl_shipping_config
select * from analytics.dhl_shipping_config;
