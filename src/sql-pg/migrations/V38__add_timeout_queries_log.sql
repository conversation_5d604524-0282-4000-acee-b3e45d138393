-- Logs output from `maintenance.timeout_queries()` function
drop table if exists log.timeout_queries;
create table log.timeout_queries
(
    id int constraint timeout_queries_pk primary key generated by default as identity,
    created_at timestamp with time zone not null default current_timestamp,

    start_time timestamp with time zone not null,
    duration text not null,
    terminated bool not null,

    login_name text not null,
    program_name text,
    sql_text text not null,

    sql_hash bigint not null,
    history_slug text,
    config_id int not null,

    host_ip text
);

-- test me: log.timeout_queries
select * from log.timeout_queries;
