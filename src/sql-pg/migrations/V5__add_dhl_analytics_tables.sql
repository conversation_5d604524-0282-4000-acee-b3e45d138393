-----------------------------------------------------------------
-- DROPs in the reverse order (from child to parent)
-----------------------------------------------------------------
drop table if exists analytics.dhl_order_items cascade;
drop table if exists analytics.dhl_shipping_charges cascade;
drop table if exists analytics.dhl_shipping_billing cascade;
drop table if exists analytics.source_files cascade;

-----------------------------------------------------------------
-- SOURCE FILES
-----------------------------------------------------------------
create table analytics.source_files
(
    -- Keys: https://www.danieleteti.it/post/postgresql-identities-vs-serials/
    id int constraint source_files_pk primary key generated by default as identity,
    file_path text not null constraint source_files_uq unique,

    -- Fields
    file_created timestamp with time zone not null,
    file_size integer not null
);

-----------------------------------------------------------------
-- DHL SHIPPING BILLING: main table
-----------------------------------------------------------------
create table analytics.dhl_shipping_billing
(
    -- Keys
    id int constraint dhl_shipping_billing_pk primary key generated by default as identity,
    shipment_number text not null constraint dhl_shipping_billing_uq unique,

    -- FKs
    source_files_id integer not null
    constraint dhl_shipping_billing_source_files_fk references analytics.source_files (id) on delete cascade,

    -- Calculated fields
    package_type text,

    -- Original fields
    line_type text,
    billing_source text,
    billing_account text,
    billing_country_code text,
    invoice_date date,
    invoice_number text,
    shipment_date date,
    shipment_reference_1 text,
    shipment_reference_2 text,
    shipment_reference_3 text,
    weight_kg numeric(12, 4),
    weight_flag text,
    senders_name text,
    senders_country text,
    receivers_country text,
    currency text,
    amount_net numeric(12, 4),
    amount_gross numeric(12, 4)
);

-----------------------------------------------------------------
-- DHL SHIPPING BILLING: shipping charges (parsed charge codes)
-----------------------------------------------------------------
create table analytics.dhl_shipping_charges
(
    -- FK == PK
    shipping_billing_id integer not null
    constraint dhl_shipping_charges_fk references analytics.dhl_shipping_billing (id) on delete cascade,

    -- Fields
    remote_area_delivery_net numeric(12, 4) not null,
    remote_area_delivery_gross numeric(12, 4) not null,
    remote_area_pickup_net numeric(12, 4) not null,
    remote_area_pickup_gross numeric(12, 4) not null,
    fuel_surcharge_net numeric(12, 4) not null,
    fuel_surcharge_gross numeric(12, 4) not null,
    change_of_billing_net numeric(12, 4) not null,
    change_of_billing_gross numeric(12, 4) not null,
    duty_tax_paid_net numeric(12, 4) not null,
    duty_tax_paid_gross numeric(12, 4) not null,
    emergency_situation_net numeric(12, 4) not null,
    emergency_situation_gross numeric(12, 4) not null,
    export_declaration_net numeric(12, 4) not null,
    export_declaration_gross numeric(12, 4) not null,
    carbon_reduced_fe_net numeric(12, 4) not null,
    carbon_reduced_fe_gross numeric(12, 4) not null,
    carbon_reduced_fd_net numeric(12, 4) not null,
    carbon_reduced_fd_gross numeric(12, 4) not null,
    carbon_reduced_ft_net numeric(12, 4) not null,
    carbon_reduced_ft_gross numeric(12, 4) not null,
    import_export_duties_net numeric(12, 4) not null,
    import_export_duties_gross numeric(12, 4) not null,
    import_export_taxes_net numeric(12, 4) not null,
    import_export_taxes_gross numeric(12, 4) not null,
    oversize_piece_net numeric(12, 4) not null,
    oversize_piece_gross numeric(12, 4) not null,
    overweight_piece_net numeric(12, 4) not null,
    overweight_piece_gross numeric(12, 4) not null,
    plastic_flyer_net numeric(12, 4) not null,
    plastic_flyer_gross numeric(12, 4) not null,
    shipment_insurance_net numeric(12, 4) not null,
    shipment_insurance_gross numeric(12, 4) not null,

    -- Calculated total charges
    total_net numeric(12, 4) generated always as (
        remote_area_delivery_net
        + remote_area_pickup_net
        + fuel_surcharge_net
        + change_of_billing_net
        + duty_tax_paid_net
        + emergency_situation_net
        + export_declaration_net
        + carbon_reduced_fe_net
        + carbon_reduced_fd_net
        + carbon_reduced_ft_net
        + import_export_duties_net
        + import_export_taxes_net
        + oversize_piece_net
        + overweight_piece_net
        + plastic_flyer_net
        + shipment_insurance_net
    ) stored,

    total_gross numeric(12, 4) generated always as (
        remote_area_delivery_gross
        + remote_area_pickup_gross
        + fuel_surcharge_gross
        + change_of_billing_gross
        + duty_tax_paid_gross
        + emergency_situation_gross
        + export_declaration_gross
        + carbon_reduced_fe_gross
        + carbon_reduced_fd_gross
        + carbon_reduced_ft_gross
        + import_export_duties_gross
        + import_export_taxes_gross
        + oversize_piece_gross
        + overweight_piece_gross
        + plastic_flyer_gross
        + shipment_insurance_gross
    ) stored,

    -- PK
    constraint dhl_shipping_charges_pk primary key (shipping_billing_id)
);

-----------------------------------------------------------------
-- DHL SHIPPING BILLING: order items
-----------------------------------------------------------------
create table analytics.dhl_order_items
(
    -- FK
    shipping_billing_id integer not null
    constraint dhl_order_items_fk references analytics.dhl_shipping_billing (id) on delete cascade,

    -- Fields
    order_item_id integer not null,

    -- PK
    constraint dhl_order_items_pk primary key (shipping_billing_id, order_item_id)
);
