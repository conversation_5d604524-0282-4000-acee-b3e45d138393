do
$do$
    begin
    -- To create passwords run this manually: alter user cf_writer with password 'password-here';

    -- cf_reader
    if not exists (select * from pg_catalog.pg_user where usename = 'cf_reader') then
        create user cf_reader;
    end if;

    -- cf_writer
    if not exists (select * from pg_catalog.pg_user where usename = 'cf_writer') then
        create user cf_writer;
    end if;

    end
$do$;
