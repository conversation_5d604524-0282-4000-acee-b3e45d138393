drop procedure if exists maintenance.create_user;
/**
  Helper procedure to create db user without access and password.
  To create passwords run this manually: alter user <user_name> with password 'password-here'
 */
create procedure maintenance.create_user(user_name name, debug bool = false)
language plpgsql as
$$
declare
    sql       text;
begin
    if not exists(select * from pg_catalog.pg_user where usename = user_name) then
        sql := format('create user %s;', user_name);
        if (debug) then raise notice '%', sql; end if;
        execute sql;
    end if;
end
$$;
