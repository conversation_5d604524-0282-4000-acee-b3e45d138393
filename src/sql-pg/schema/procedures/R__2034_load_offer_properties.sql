-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.load_offer_properties;
/**
    Loads analytics.offer_properties from:
    - public.offer_properties for new battery_condition
    - public.offers, public.instances for historical `neuer Akku`

    Uses intermediate temp tables in order to:
    - minimise lock time on the target caused by truncate;
    - safeguard against data type issues caused during insert
    (that would be caught by the temp table)
*/
create procedure maintenance.load_offer_properties()
language plpgsql as
$$
begin
    -- 1. Load new properties into the temp table
    drop table if exists new_offer_properties;
    create temp table new_offer_properties (
        like analytics.offer_properties
    ) on commit drop;

    insert into new_offer_properties (
        offer_id, offer_valid_from, created_at, battery_condition
    )
    select distinct on (offer_id) -- PK
        offer_id,
        offer_valid_from::timestamp with time zone,
        created_at,
        battery_condition
    from
        public.offer_properties
    order by
        offer_id asc,
        offer_valid_from desc,  -- Get the latest offer_valid_from
        created_at desc         -- If multiple records for same offer_valid_from, get latest created
    ;

    -- 2. Load old properties into the temp table
    drop table if exists old_offer_properties;
    create temp table old_offer_properties (
        like analytics.offer_properties
    ) on commit drop;

    insert into old_offer_properties(
        offer_id, offer_valid_from, created_at, battery_condition
    )
    select distinct on (o.id) -- PK
        o.id as offer_id,
        o.valid_from::timestamp with time zone as offer_valid_from,
        i.created_at,
        'new' as battery_condition
    from
        public.offers as o
    inner join
        public.instances as i
    on o.instance_id = i.id
    left join
        new_offer_properties as op
    on o.id = op.offer_id
    where
        i.name like '%neuer Akku%'
    and op.offer_id is null
    order by
        offer_id asc,
        offer_valid_from desc,  -- Get the latest offer_valid_from
        created_at desc         -- If multiple records for same offer_valid_from, get latest created
    ;

    -- 3. Reload via insert on conflict update
    insert into analytics.offer_properties(
        offer_id, offer_valid_from, created_at, battery_condition
    )
    select
        offer_id, offer_valid_from, created_at, battery_condition
    from
        new_offer_properties
    union all
    select
        offer_id, offer_valid_from, created_at, battery_condition
    from
        old_offer_properties
    on conflict(offer_id) do update
    set (offer_valid_from, created_at, battery_condition) = (
        excluded.offer_valid_from, excluded.created_at, excluded.battery_condition
    );
end
$$;

-- test: maintenance.load_offer_properties()
-- select * from analytics.offer_properties limit 5;
-- 50 MB
-- select pg_size_pretty(pg_relation_size('analytics.offer_properties')) as size;
do
$test$
begin
    if ('${runTest}' = 'true') then
        truncate table analytics.offer_properties;
        call maintenance.load_offer_properties();
        perform * from analytics.offer_properties limit 5;
    end if;
end;
$test$;
