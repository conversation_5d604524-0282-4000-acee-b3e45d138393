-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics.upsert_dhl_shipping_charges;
/**
  Insert or update analytics.dhl_shipping_billing from the staging import.dhl_shipping_billing table.
*/
create or replace procedure analytics.upsert_dhl_shipping_charges()
language SQL
as $$
    insert into analytics.dhl_shipping_charges(
        shipping_billing_id,
        remote_area_delivery_net,
        remote_area_delivery_gross,
        remote_area_pickup_net,
        remote_area_pickup_gross,
        fuel_surcharge_net,
        fuel_surcharge_gross,
        change_of_billing_net,
        change_of_billing_gross,
        duty_tax_paid_net,
        duty_tax_paid_gross,
        emergency_situation_net,
        emergency_situation_gross,
        export_declaration_net,
        export_declaration_gross,
        carbon_reduced_fe_net,
        carbon_reduced_fe_gross,
        carbon_reduced_fd_net,
        carbon_reduced_fd_gross,
        carbon_reduced_ft_net,
        carbon_reduced_ft_gross,
        import_export_duties_net,
        import_export_duties_gross,
        import_export_taxes_net,
        import_export_taxes_gross,
        oversize_piece_net,
        oversize_piece_gross,
        overweight_piece_net,
        overweight_piece_gross,
        plastic_flyer_net,
        plastic_flyer_gross,
        shipment_insurance_net,
        shipment_insurance_gross,
        demand_surcharge_net,
        demand_surcharge_gross
    )
    select
        distinct on (asb.id) id,
        isb.remote_area_delivery_net,
        isb.remote_area_delivery_gross,
        isb.remote_area_pickup_net,
        isb.remote_area_pickup_gross,
        isb.fuel_surcharge_net,
        isb.fuel_surcharge_gross,
        isb.change_of_billing_net,
        isb.change_of_billing_gross,
        isb.duty_tax_paid_net,
        isb.duty_tax_paid_gross,
        isb.emergency_situation_net,
        isb.emergency_situation_gross,
        isb.export_declaration_net,
        isb.export_declaration_gross,
        isb.carbon_reduced_fe_net,
        isb.carbon_reduced_fe_gross,
        isb.carbon_reduced_fd_net,
        isb.carbon_reduced_fd_gross,
        isb.carbon_reduced_ft_net,
        isb.carbon_reduced_ft_gross,
        isb.import_export_duties_net,
        isb.import_export_duties_gross,
        isb.import_export_taxes_net,
        isb.import_export_taxes_gross,
        isb.oversize_piece_net,
        isb.oversize_piece_gross,
        isb.overweight_piece_net,
        isb.overweight_piece_gross,
        isb.plastic_flyer_net,
        isb.plastic_flyer_gross,
        isb.shipment_insurance_net,
        isb.shipment_insurance_gross,
        isb.demand_surcharge_net,
        isb.demand_surcharge_gross
    from
        import.dhl_shipping_billing isb
    inner join
        analytics.dhl_shipping_billing as asb
    on  isb.shipment_number = asb.shipment_number
    order by
        -- in case of duplicate shipment_number favour most recent shipment
        asb.id, isb.shipment_date desc
    on conflict(shipping_billing_id) do update
    set (
        remote_area_delivery_net,
        remote_area_delivery_gross,
        remote_area_pickup_net,
        remote_area_pickup_gross,
        fuel_surcharge_net,
        fuel_surcharge_gross,
        change_of_billing_net,
        change_of_billing_gross,
        duty_tax_paid_net,
        duty_tax_paid_gross,
        emergency_situation_net,
        emergency_situation_gross,
        export_declaration_net,
        export_declaration_gross,
        carbon_reduced_fe_net,
        carbon_reduced_fe_gross,
        carbon_reduced_fd_net,
        carbon_reduced_fd_gross,
        carbon_reduced_ft_net,
        carbon_reduced_ft_gross,
        import_export_duties_net,
        import_export_duties_gross,
        import_export_taxes_net,
        import_export_taxes_gross,
        oversize_piece_net,
        oversize_piece_gross,
        overweight_piece_net,
        overweight_piece_gross,
        plastic_flyer_net,
        plastic_flyer_gross,
        shipment_insurance_net,
        shipment_insurance_gross,
        demand_surcharge_net,
        demand_surcharge_gross
    ) = (
        excluded.remote_area_delivery_net,
        excluded.remote_area_delivery_gross,
        excluded.remote_area_pickup_net,
        excluded.remote_area_pickup_gross,
        excluded.fuel_surcharge_net,
        excluded.fuel_surcharge_gross,
        excluded.change_of_billing_net,
        excluded.change_of_billing_gross,
        excluded.duty_tax_paid_net,
        excluded.duty_tax_paid_gross,
        excluded.emergency_situation_net,
        excluded.emergency_situation_gross,
        excluded.export_declaration_net,
        excluded.export_declaration_gross,
        excluded.carbon_reduced_fe_net,
        excluded.carbon_reduced_fe_gross,
        excluded.carbon_reduced_fd_net,
        excluded.carbon_reduced_fd_gross,
        excluded.carbon_reduced_ft_net,
        excluded.carbon_reduced_ft_gross,
        excluded.import_export_duties_net,
        excluded.import_export_duties_gross,
        excluded.import_export_taxes_net,
        excluded.import_export_taxes_gross,
        excluded.oversize_piece_net,
        excluded.oversize_piece_gross,
        excluded.overweight_piece_net,
        excluded.overweight_piece_gross,
        excluded.plastic_flyer_net,
        excluded.plastic_flyer_gross,
        excluded.shipment_insurance_net,
        excluded.shipment_insurance_gross,
        excluded.demand_surcharge_net,
        excluded.demand_surcharge_gross
    );
$$;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        call analytics.upsert_dhl_shipping_charges();
    end if;
end;
$test$;
