-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.manage_user_access;
/**
  Restore Analytics DB helper procedure to manage user access to the instance during the restore.
  By default (lock bool = false) it will enable connections for every user.
  When lock set to true, blocks all non system users (except postgres and db_refresh user)
*/
create procedure maintenance.manage_user_access(
    debug bool = true,
    lock bool = false
)
language plpgsql as
$$
declare
    sql             text;
    sql_lock_access text := '-1';
    rec             record;
begin
    if lock then
        sql_lock_access := '0';
    end if;

    for rec in select usename
               from pg_catalog.pg_user
               where usename not in ('postgres', 'db_refresh') and usename not like 'cloudsql%'
        loop
            sql := format('alter user "%s" connection limit %s', rec.usename, sql_lock_access);

            if (debug) then raise notice '%', sql; end if;
            execute sql;
        end loop;
end
$$;


drop procedure if exists maintenance.lock_non_system_users;
/**
  Restore Analytics DB helper procedure to lock all non system users (except platform user)
*/
create procedure maintenance.lock_non_system_users()
language plpgsql as
$$
begin
    raise notice 'Locking all non system users...';
    call maintenance.manage_user_access(lock=>true);
end
$$;


drop procedure if exists maintenance.unlock_non_system_users;
/**
  Restore Analytics DB helper procedure to unlock all non system users (except platform user)
*/
create procedure maintenance.unlock_non_system_users()
language plpgsql as
$$
begin
    raise notice 'Unlocking all non system users...';
    call maintenance.manage_user_access(lock=>false);
end
$$;
