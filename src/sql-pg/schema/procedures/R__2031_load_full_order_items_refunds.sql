-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists maintenance.reload_full_order_item_refunds;
/**
    Re-loads analytics.full_order_item_refunds (102 MB).

    Uses intermediate temp table in order to:
    - minimise lock time on the target caused by truncate;
    - safeguard against data type issues caused during insert
    (that would be caught by the temp table)
*/
create procedure maintenance.reload_full_order_item_refunds()
language plpgsql as
$$
begin
    -- 1. Create a temp table like the target.
    create temp table temp_order_item_refunds (
        like analytics.full_order_item_refunds
    ) on commit drop;

    -- 2. Load into a temp table, it can take a while, but it does not hurt if it fails.
    insert into temp_order_item_refunds
    select
        order_item_refund_id,
        order_item_id,
        refunded_at,
        updated_at,
        refunded,

        -- Nullable columns
        refunded_charge,
        refunded_eur,
        refunded_charge_eur,
        min_reversed_commission_date,

        held_base_commission,
        kept_base_commission,
        executed_base_commission,

        held_payment_commission,
        kept_payment_commission,
        executed_payment_commission,

        held_base_commission_eur,
        kept_base_commission_eur,
        executed_base_commission_eur,

        held_payment_commission_eur,
        kept_payment_commission_eur,
        executed_payment_commission_eur
    from
        export_etl.full_order_item_refunds;

    -- 3. Reload via truncate/insert.
    truncate table analytics.full_order_item_refunds;
    insert into analytics.full_order_item_refunds
    select * from temp_order_item_refunds;
end
$$;

-- test: maintenance.reload_full_order_item_refunds()
-- select * from analytics.full_order_item_refunds limit 5;
-- select pg_size_pretty(pg_relation_size('analytics.full_order_item_refunds')) as size;
do
$test$
begin
    if ('${runTest}' = 'true') then
        call maintenance.reload_full_order_item_refunds();
        perform * from analytics.full_order_item_refunds limit 5;
    end if;
end;
$test$;
