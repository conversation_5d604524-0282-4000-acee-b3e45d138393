-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics.upsert_dhl_order_items;
/**
  Insert or update analytics.dhl_order_items from the staging import.dhl_order_items table.
*/
create or replace procedure analytics.upsert_dhl_order_items()
language SQL
as $$
    insert into analytics.dhl_order_items(
        shipping_billing_id,
        order_item_id
    )
    select distinct
        asb.id,
        ioi.order_item_id
    from
        import.dhl_order_items as ioi
    inner join
        analytics.dhl_shipping_billing asb
    on  ioi.shipment_number = asb.shipment_number
    on conflict on constraint dhl_order_items_pk do nothing;
$$;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        call analytics.upsert_dhl_order_items();
    end if;
end;
$test$;
