-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics.upsert_source_files;
/**
  Insert or update analytics.source_files from the staging import.dhl_shipping_billing table.
  Use `on conflict` statement to update should the conflict occur:
  - https://www.postgresql.org/docs/12/sql-insert.html
  - https://www.alibabacloud.com/help/en/analyticdb-for-postgresql/developer-reference/use-insert-on-conflict-to-overwrite-data -- noqa: LT05

  Remember to use SELECT DISTINCT ON to avoid `ON CONFLICT DO UPDATE command cannot affect row a second time` error:
  - https://www.postgresql.org/docs/12/sql-select.html#distinct
*/
create or replace procedure analytics.upsert_source_files()
language SQL
as $$
    insert into analytics.source_files(
        file_path, file_created, file_size
    )
    select distinct on (file_path) file_path, file_created, file_size
    from
        import.dhl_shipping_billing as isb
    where
        trim(isb.file_path) != ''
    and isb.file_created is not null
    and isb.file_size is not null
    order by
        -- in case of duplicate file_path favour most recent file_created
        file_path, file_created desc
    on conflict(file_path) do update
    set (file_created, file_size) = (excluded.file_created, excluded.file_size);
$$;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        call analytics.upsert_source_files();
    end if;
end;
$test$;
