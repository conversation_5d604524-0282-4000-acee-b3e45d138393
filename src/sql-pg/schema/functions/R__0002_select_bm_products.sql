-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists analytics.select_products_bm_listing();
/**
  return list of products to be used in bm_listing matching
*/
create or replace function analytics.select_products_bm_listing()
returns table (product_id int, product_name text) as
$function$
begin
    return query
    select
        id as product_id,
        name_en::text as product_name
    from
        public.products
    where
        name_en is not null;
end;
$function$ language plpgsql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_products_bm_listing() limit 10;
    end if;
end;
$test$;
