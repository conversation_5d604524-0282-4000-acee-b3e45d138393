-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists analytics.select_dhl_shipping_config;
create function analytics.select_dhl_shipping_config(active boolean default true)
/*
    Provides input for DHL `ShippingConfig` class.

    :active: whether to select only active configuration, defaults to true.
*/
returns table (
    country text,
    support_email text,
    outbound_account text,
    inbound_account text,
    file_suffix text,
    encoding text,
    quoted_char char(1),
    decimal_sep char(1)
)
as
$$
    select
        country,
        support_email,
        outbound_account,
        inbound_account,
        file_suffix,
        encoding,
        quoted_char,
        decimal_sep
    from analytics.dhl_shipping_config
    where
        is_active = active;
$$
language sql;

-- test: analytics.select_dhl_shipping_config()
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_dhl_shipping_config() limit 10;
    end if;
end;
$test$;
