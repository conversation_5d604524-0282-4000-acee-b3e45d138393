-- <PERSON>way will re-run this script if it is changed or ${changeReason} is updated
drop function if exists maintenance.timeout_queries;
create function maintenance.timeout_queries()
/*
    Terminates long-running queries that timeouts according to `maintenance.query_timeout_config`.
*/
returns table (
    status text,
    duration text,
    program_name text,
    login_name text,
    sql_text text,
    start_time timestamp with time zone,
    session_id int,
    host_ip inet,
    config_id int,
    description text
)
as
$$
select
    case
        when qtc.terminate then
            case when pg_terminate_backend(wia.session_id) then
                'terminated'
            else
                'failed to terminate'
            end
        else
            'reported'
    end as status,
    left(wia."hh:mm:ss.msssss", 8) as duration,
    wia.program_name,
    wia.login_name,
    wia.sql_text,
    wia.start_time,
    wia.session_id,
    wia.host_ip,
    qtc.id as config_id,
    qtc.description
from
    monitoring.who_is_active() wia
inner join
    maintenance.query_timeout_config qtc
on (coalesce(qtc.program_name, '') = '' or  wia.program_name like qtc.program_name)
    and (coalesce(qtc.login_name, '') = '' or  wia.login_name like qtc.login_name)
    and (coalesce(qtc.sql_text, '') = '' or wia.sql_text like qtc.sql_text)
    and wia.duration_sec > qtc.timeout_sec
    and current_timestamp between qtc.valid_from and coalesce(qtc.valid_to, current_timestamp)
$$
language sql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from maintenance.timeout_queries() limit 10;
    end if;
end;
$test$;
