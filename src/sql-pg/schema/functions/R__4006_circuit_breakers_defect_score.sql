-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.circuit_breakers_defect_score;
drop function if exists analytics.select_circuit_breakers_defect_score;
create or replace function analytics.select_circuit_breakers_defect_score(
    sda_merchant_ids integer [] default array[
        385, 585, 487, 386, 157, 387, 529, 590, 565, 389, 586, 357, 470, 435, 259
    ],
    date_join interval default interval '2 day',
    p_date date default current_date - interval '2 day',
    p_n_order_items numeric default 100,
    p_count_defect numeric default 5,
    p_country text default 'all',
    p_defect_points numeric default 8,
    p_merchant_actual_defect_rate numeric default 3.0
)
/*
    :sda_merchant_ids: array of merchant IDs that are considered SDA merchants, with default values.
    :date_join: interval used to join defect alert history with MPS history, defaults to 2 days.
    :p_date: the date for which to retrieve data, defaults to 2 days ago.
    :p_n_order_items: minimum number of order items a merchant must have to be included, defaults to 100.
    :p_count_defect: minimum count of defects a merchant must have to be included, defaults to 5.
    :p_country: which country of the MPS per country to query, defaults to all.
    :p_defect_points: maximum defect points allowed for normal merchants, defaults to 8.
    :p_merchant_actual_defect_rate: minimum defect rate for SDA merchants to be included, defaults to 3.0.
*/
returns table (merchant_id int, merchant_name text, primary_email text)
as
$$
with merchant_type as (
    select
        mh.id as merchant_id,
        mh.name as merchant_name,
        mh.defect_points,
        mh.defect_rate as merchant_actual_defect_rate,
        mh.n_order_items,
        case
            when mh.id = any(sda_merchant_ids)
                then 'sda_merchant'
            else 'normal_merchant'
        end as merchant_type
    from
        analytics.mps_per_country_combined_history as mh
    where
        mh.date = date(p_date)
        and mh.n_order_items >= p_n_order_items
        and mh.count_defect > p_count_defect
        and mh.country = p_country
    group by mh.id, mh.name, mh.defect_points, mh.defect_rate, mh.n_order_items
),

merchants_to_send_orders as (
    select
        merchant_id,
        merchant_name
    from
        merchant_type
    where
        (merchant_type = 'normal_merchant' and defect_points <= p_defect_points)
        or (merchant_type = 'sda_merchant' and merchant_actual_defect_rate >= p_merchant_actual_defect_rate)
)

select
    mo.merchant_id,
    mo.merchant_name,
    p.primary_email
from
    analytics.active_merchants as m
inner join
    merchants_to_send_orders as mo
on m.id = mo.merchant_id
left join
    analytics.pipedrive as p
on m.id = p.merchant_id
where
    p.has_performance_role = True;
$$ language sql;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_circuit_breakers_defect_score() limit 10;
    end if;
end;
$test$;
