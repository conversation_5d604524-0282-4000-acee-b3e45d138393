-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop function if exists analytics.select_revenue_countries;
/**
  Gets revenue by country, required by NRM forecast project.
  Dependent on `analytics.order_financial_revenue` view updated on 2025-07-15.

  Based on:
  https://gitlab.com/refurbed/ds/crystal-ball/-/blob/feat/hierarchy/src/queries/queries_sql/revenue_countries.sql
*/
create or replace function analytics.select_revenue_countries(
    start_date date default current_date, end_date date default current_date + interval '1 day'
)
returns table (paid_at date, country text, revenue numeric, gmv numeric, n_orders int, n_order_items int)
as
$$
select
    order_item_offers.paid_at::date as paid_at,
    order_item_offers.country,
    sum(revenue.revenue) as revenue,
    sum(revenue.gmv) as gmv,
    count (distinct order_item_offers.order_id) as n_orders,
    count(
        distinct (
          case when (order_item_offers.type = 'service-fee') then null else order_item_offers.id end
        )
    ) as n_order_items
from
    public.order_item_offers as order_item_offers
inner join
    analytics.order_financial_revenue as revenue
on  order_item_offers.id = revenue.order_item_id
where
    order_item_offers.paid_at between start_date and end_date
group by
    order_item_offers.paid_at::date,
    order_item_offers.country
$$ language sql;

-- test: select * from analytics.select_revenue_countries('2025-01-01', '2025-02-01') limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.select_revenue_countries('2025-01-01', '2025-02-01') limit 5;
    end if;
end;
$test$;
