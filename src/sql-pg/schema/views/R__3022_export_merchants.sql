-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_merchants;
create or replace view export_etl.v_merchants
as
select
    id,
    created_at,
    updated_at,
    null::timestamp with time zone as deleted_at,

    country::text,
    state::text,
    is_addon_support,
    name::text,
    email::text,
    public_id::text,

    handicap::numeric(5, 4),
    payment_commission_pc::numeric(5, 4),
    payout_commission_pc::numeric(5, 4)
from public.merchants;

-- test: select * from export_etl.v_merchants limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 1712 kB
        -- select pg_size_pretty(pg_relation_size('public.merchants')) as merchants_size;
        perform * from export_etl.v_merchants limit 5;
    end if;
end;
$test$;
