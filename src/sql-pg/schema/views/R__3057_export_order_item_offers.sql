-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_order_item_offers;
create or replace view export_etl.v_order_item_offers
as
/*
    Dependency updated on 2025-07-30:
    - src/sql-pg/schema/views/R__3005_analytics_order_item_offers.sql
*/
select
    -- order_items: change tracking attributes
    oio.id,
    oio.created_at,
    oio.updated_at,

    -- order_items: business attributes
    oio.order_id,
    oio.offer_id,
    oio.offer_valid_from::timestamp with time zone,
    oio.type,

    -- orders
    oio.paid_at,
    oio.state,
    oio.country,
    oio.presentment_currency,
    oio.payment_provider,

    -- offers: nullable
    export_etl.infinity_to_timestamp(oio.valid_to) as valid_to,
    oio.instance_id,
    oio.merchant_id,
    oio.grading,
    oio.warranty,

    -- offer_properties: nullable
    oio.battery_condition,
    oio.is_new_battery
from
    analytics.order_item_offers as oio;

-- test: select * from export_etl.v_order_item_offers where is_new_battery is not null limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 2364 MB
        -- select pg_size_pretty(pg_relation_size('public.order_item_offers')) as order_item_offers_size;
        perform * from export_etl.v_order_item_offers limit 5;
    end if;
end;
$test$;
