-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_categories;
create or replace view export_etl.v_categories
as
select
    c.id,
    c.created_at,
    c.updated_at,
    null::timestamp with time zone as deleted_at,

    c.name::text as category_name,
    c.brand::text,
    c.type::text as category_type,
    pc.name_en::text as product_category
from
    public.categories as c
left join
    public.categories as pc
on pc.id = subpath(c.path, -1, 1)::text::int
where c.path <> ''
union all
select
    c.id,
    c.created_at,
    c.updated_at,
    null::timestamp with time zone as deleted_at,

    c.name::text as category_name,
    c.brand::text,
    c.type::text as category_type,
    c.name_en::text as product_category
from
    public.categories as c
where nlevel(c.path) = 0;

-- test: export_etl.v_categories
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 368 kB
        -- select pg_size_pretty(pg_relation_size('public.categories')) as categories_size;
        perform * from export_etl.v_categories limit 10;

        -- PK: id
        perform id, count(*) as duplicate_rows
        from export_etl.v_categories
        group by id
        having count(*) > 1;
    end if;
end;
$test$;
