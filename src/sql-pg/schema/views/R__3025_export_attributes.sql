-- <PERSON>way will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_attributes;
create or replace view export_etl.v_attributes
as
select
    -- PK
    a.id as attribute_id,
    av.id as attribute_value_id,

    -- attributes
    a.name::text as attribute_name,
    case
        when a.unit is null
            then
                coalesce((ev.value ->> 'de'), av.value_numeric::text)
        else
            coalesce((ev.value ->> 'de'), av.value_numeric::text) || ' ' || a.unit
    end::text as attribute_value,
    a.type::text,
    a.filterable,
    a.precision,
    a.unit::text,

    -- attribute_values
    av.value_bool,
    av.value_numeric,

    -- attribute_enum_values
    ev.id as value_enum_id,
    ev.value
from
    public.attributes as a
inner join
    public.attribute_values as av
on a.id = av.attribute_id
left join
    public.attribute_enum_values as ev
on av.value_enum_id = ev.id;

-- test: export_etl.v_attributes
do
$test$
begin
    if ('${runTest}' = 'true') then
        -- size: 15 MB
        /*
        select pg_size_pretty(
           pg_relation_size('public.attributes')
           + pg_relation_size('public.attribute_values')
           + pg_relation_size('attribute_enum_values')
        ) as attributes_size;
        */

        -- PK check
        /*
        select attribute_id, attribute_value_id, count(*) as unique_count
        from export_etl.v_attributes
        group by attribute_id, attribute_value_id
        having count(*) > 1;
        */

        perform * from export_etl.v_attributes limit 10;
    end if;
end;
$test$;
