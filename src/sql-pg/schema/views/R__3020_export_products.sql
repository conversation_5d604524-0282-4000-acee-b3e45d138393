-- Flyway will re-run this script if it is changed or ${changeReason} is updated
do
$$
begin
    if ('${flyway:environment}' in ('local', 'cicd-docker-build')) then
    -- Use physical table instead of view for testing purposes (mocking view content)

        if exists (select * from pg_views where viewname = 'v_products' and schemaname = 'export_etl') then
            drop view export_etl.v_products;
        end if;

        if exists (select * from pg_tables where tablename = 'v_products' and schemaname = 'export_etl') then
            drop table export_etl.v_products;
        end if;

        create unlogged table export_etl.v_products
        (
            id integer primary key,
            created_at timestamp with time zone not null,
            updated_at timestamp with time zone not null,
            deleted_at timestamp with time zone,

            category_id integer not null,
            listing_mode text not null,
            name text not null,
            name_en text,
            slug text
        );

        insert into export_etl.v_products
        select
            id,
            created_at,
            updated_at,
            deleted_at,
            category_id,
            listing_mode,
            name,
            name_en,
            slug
        from public.products;
    else
        drop view if exists export_etl.v_products;
        create or replace view export_etl.v_products
        as
        select
            id,
            created_at,
            updated_at,
            deleted_at,

            category_id::integer,
            listing_mode::text,
            name::text,
            name_en::text,
            slug::text
        from public.products;
    end if;
end
$$;

-- test: export_etl.v_products
do
$test$
begin
    if ('${runTest}' = 'true') then
        -- size: 30 MB
        -- select pg_size_pretty(pg_relation_size('public.products')) as products_size;
        perform * from export_etl.v_products limit 10;
    end if;
end;
$test$;
