-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_exchange_rates;
create or replace view export_etl.v_exchange_rates
as
select
    t.id,
    t.valid_from::timestamp with time zone,
    -- `infinity` is not supported in BigQuery!
    export_etl.infinity_to_timestamp(t.valid_to) as valid_to,
    t.base::text,
    t.target::text,
    t.rate::numeric(24, 9)
from public.exchange_rates as t;

-- test: select * from export_etl.v_exchange_rates where base = 'EUR' order by valid_from desc limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 7424 kB
        -- select pg_size_pretty(pg_relation_size('public.exchange_rates')) as exchange_rates_size;
        perform * from export_etl.v_exchange_rates limit 5;
    end if;
end;
$test$;
