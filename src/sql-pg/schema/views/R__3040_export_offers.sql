-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_offers;
create or replace view export_etl.v_offers
as
select
    -- PK: id, valid_from
    o.id,
    o.created_at,
    o.valid_from::timestamp with time zone,
    export_etl.infinity_to_timestamp(o.valid_to) as valid_to,

    -- offers
    o.state::text,
    o.instance_id,
    o.merchant_id,
    o.warranty::smallint,
    o.stock,
    o.grading::text,
    o.hidden,
    o.tax_difference,

    o.shipping_profile_id,
    o.shipping_profile_valid_from::timestamp with time zone,
    o.reference_currency_code::text,
    o.reference_price::numeric(14, 4),
    o.reference_min_flex_price::numeric(14, 4),
    o.sku::text
from
    public.offers as o;

-- test: select * from export_etl.v_offers limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        /*
            -- 16 GB
            select pg_size_pretty(pg_relation_size('public.offers')) as offers_size;

            -- Count greatly varies per month: from 93 to 17,656,144
            -- Count per year:
                -- 2017: 14
                ...
                -- 2023: 111,198
                -- 2024: 46,374,112
                -- 2025: 63,887,975
            select date_trunc('year', valid_from) as year, count(*)
            from public.offers
            group by
                year
            order by
                year
            ;
        */

        perform * from export_etl.v_offers limit 5;
    end if;
end;
$test$;
