-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_instances;
create or replace view export_etl.v_instances
as
select
    id,
    created_at,
    updated_at,
    null::timestamp with time zone as deleted_at,

    published,
    product_id,
    name::text,
    name_en::text,
    srp::numeric(14, 4)
from public.instances;

-- test: select * from export_etl.v_instances limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 427 MB
        -- select pg_size_pretty(pg_relation_size('public.instances')) as instances_size;
        perform * from export_etl.v_instances limit 10;
    end if;
end;
$test$;
