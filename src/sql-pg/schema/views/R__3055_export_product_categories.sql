-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_product_categories;
create or replace view export_etl.v_product_categories
/*
    Product and all its unnested categories where it can be shown/listed.
*/
as
select
    p.id as product_id,
    p.category_id as main_category_id,
    unnest(array[(p.category_id)::text] || string_to_array(c.path::text, '.'))::integer as show_category_id
from
    public.products as p
inner join
    public.categories as c
on p.category_id = c.id

union

select
    p.id as product_id,
    p.category_id as main_category_id,
    show_category_id
from
    public.products as p,
    unnest(p.related_category_ids::integer []) as show_category_id
where
    show_category_id is not null;

-- test: select * from export_etl.v_product_categories
/*
    -- PK: product_id, show_category_id
    select product_id, show_category_id, count(*) as duplicate_rows
    from export_etl.v_product_categories
    group by product_id, show_category_id
    having count(*) > 1;
*/
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- 31 MB
        -- select pg_size_pretty(pg_relation_size('public.products') + pg_relation_size('public.categories')) as size;
        perform * from export_etl.v_product_categories limit 10;
    end if;
end;
$test$;
