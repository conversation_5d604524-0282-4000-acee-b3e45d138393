-- Flyway will re-run this script if ${changeReason} is updated
/**
    Data source for the `maintenance.reload_full_order_item_refunds` procedure.

    PG dependencies:
    - V43__refactor_order_item_refund_commission.sql
    - R__2031_load_full_order_items_refunds.sql

    BQ dependencies:
    - R__1057_platform_export_full_order_item_refunds.sql
    - V057__platform_export_full_order_item_refunds.sql
    - R__3057_platform_export_load_full_order_item_refunds.sql
*/

drop view if exists export_etl.full_order_item_refunds;
create view export_etl.full_order_item_refunds
as
with refunds as (
    select
        oir.id as order_item_refund_id,
        oir.order_item_id,
        r.order_id,
        r.created_at,
        r.updated_at,
        sum(oir.refunded)::decimal(12, 4) as refunded,
        sum(case
            when r.type = 'charge'
                then oir.refunded
        end)::decimal(12, 4) as refunded_charge
    from
        public.order_item_refunds as oir
    inner join
        public.order_refunds as r
    on oir.order_refund_id = r.id
    where
        r.status = 'succeeded'
    group by
        oir.id,
        oir.order_item_id,
        r.order_id,
        r.created_at,
        r.updated_at
),

commission_reversals as (
    select
        oir.id as order_item_refund_id,
        (
            sum(
                coalesce(
                    case
                        when p.status = 'held' and p.type in ('commissionBase', 'addonCommissionBase') then p.amount
                    end,
                    0
                )
            )
            + sum(
                coalesce(
                    case
                        when
                            sr.status = 'held' and s.type in ('commissionBase', 'addonCommissionBase')
                            then sr.presentment_amount
                    end,
                    0
                )
            )
        )
        ::decimal(12, 4)
            as held_base_commission,
        (
            sum(
                coalesce(
                    case
                        when
                            p.status = 'cancelled' and p.type in ('commissionBase', 'addonCommissionBase')
                            then p.amount
                    end,
                    0
                )
            )
            + sum(
                coalesce(
                    case
                        when
                            sr.status = 'cancelled' and s.type in ('commissionBase', 'addonCommissionBase')
                            then sr.presentment_amount
                    end,
                    0
                )
            )
        )
        ::decimal(12, 4)
            as kept_base_commission,
        (
            sum(
                coalesce(
                    case
                        when p.status = 'executed' and p.type in ('commissionBase', 'addonCommissionBase') then p.amount
                    end,
                    0
                )
            )
            + sum(
                coalesce(
                    case
                        when
                            sr.status = 'executed' and s.type in ('commissionBase', 'addonCommissionBase')
                            then sr.presentment_amount
                    end,
                    0
                )
            )
        )::decimal(12, 4
        ) as executed_base_commission,
        (
            sum(coalesce(case when p.status = 'held' and p.type = 'commissionPayment' then p.amount end, 0))
            + sum(
                coalesce(
                    case when sr.status = 'held' and s.type = 'commissionPayment' then sr.presentment_amount end, 0
                )
            )
        )::decimal(12, 4
        ) as held_payment_commission,
        (
            sum(coalesce(case when p.status = 'cancelled' and p.type = 'commissionPayment' then p.amount end, 0))
            + sum(
                coalesce(
                    case when sr.status = 'cancelled' and s.type = 'commissionPayment' then sr.presentment_amount end, 0
                )
            )
        )::decimal(12, 4
        ) as kept_payment_commission,

        (
            sum(coalesce(case when p.status = 'executed' and p.type = 'commissionPayment' then p.amount end, 0))
            + sum(
                coalesce(
                    case when sr.status = 'executed' and s.type = 'commissionPayment' then sr.presentment_amount end, 0
                )
            )
        )::decimal(12, 4
        ) as executed_payment_commission,
        min(
            coalesce(
                case when p.type in ('commissionBase', 'addonCommissionBase') then p.transferred_at end,
                case when s.type in ('commissionBase', 'addonCommissionBase') then sr.reversed_at end
            )
        )::date as min_reversed_commission_date
    from
        public.order_item_refunds as oir
    inner join
        public.order_refunds as r
    on oir.order_refund_id = r.id
    left join
        public.order_transfers_hyperwallet as p
    on oir.id = p.order_item_refund_id
    left join
        public.order_transfer_reversals as sr
    on oir.id = sr.order_item_refund_id
    left join
        public.order_transfers as s
    on sr.order_transfer_id = s.id
    where
        r.status = 'succeeded'
    group by oir.id
)

select
    r.order_item_refund_id,
    r.order_item_id,
    r.created_at as refunded_at,
    r.updated_at,
    r.refunded,
    r.refunded_charge,
    (r.refunded * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(12, 4) as refunded_eur,
    (r.refunded_charge * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as refunded_charge_eur,
    cr.min_reversed_commission_date,
    cr.held_base_commission,
    cr.kept_base_commission,
    cr.executed_base_commission,
    cr.held_payment_commission,
    cr.kept_payment_commission,
    cr.executed_payment_commission,
    (cr.held_base_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as held_base_commission_eur,
    (cr.kept_base_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as kept_base_commission_eur,
    (cr.executed_base_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as executed_base_commission_eur,
    (cr.held_payment_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as held_payment_commission_eur,
    (cr.kept_payment_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as kept_payment_commission_eur,
    (cr.executed_payment_commission * coalesce(1 / er.rate, 1) / oi.presentment_exchange_rate_modifier)::decimal(
        12, 4
    ) as executed_payment_commission_eur
from
    refunds as r
inner join
    public.orders as o
on r.order_id = o.id
left join
    public.order_items as oi
on r.order_item_id = oi.id
left join
    commission_reversals as cr
on r.order_item_refund_id = cr.order_item_refund_id
left join
    public.exchange_rates as er
on r.created_at >= er.valid_from
    and r.created_at < er.valid_to
    and er.base = 'EUR'
    and o.presentment_currency = er.target;

-- test: export_etl.full_order_item_refunds
-- select * from export_etl.full_order_item_refunds limit 5;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from export_etl.full_order_item_refunds limit 5;
    end if;
end;
$test$;
