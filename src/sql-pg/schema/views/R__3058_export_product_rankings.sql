-- Flyway will re-run this script if it is changed or ${changeReason} is updated
create or replace view export_etl.v_product_rankings
as
select
    product_id,
    category_id,
    market_country::text,
    -- to be added by platform team in the future
    current_timestamp as updated_at,
    rank,
    rank_b
from public.product_rankings;

/*
select * from export_etl.v_product_rankings;
select pg_size_pretty(pg_relation_size('public.product_rankings')) as size;
*/
