-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.order_data_layer_revenue;
create view analytics.order_data_layer_revenue
/**
  Gets data layer revenue
*/
as
select
    oio.id as order_item_id,
    oio.order_id,
    round(coalesce((
        case
            when (
                m.is_addon_support
            )
                then (case
                    when (o.net) then ((o.price) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    else ((o.price) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier) / (1 + ((o.vat) / 100))
                end) + (case
                    when (o.net) then ((o.shipping_costs) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    else ((o.shipping_costs) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                        / (1 + ((o.vat) / 100))
                end)
            when
                (o.type = 'offer')
                then (o.presentment_base_commission * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    + (o.presentment_target_price_commission * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    + (o.presentment_payout_commission * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    + (o.presentment_payment_commission * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
            when (
                o.type = 'addon'
            )
                then (case
                    when (o.net) then ((o.price) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    else ((o.price) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier) / (1 + ((o.vat) / 100))
                end) + (case
                    when (o.net) then ((o.shipping_costs) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                    else ((o.shipping_costs) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                        / (1 + ((o.vat) / 100))
                end)
            when (o.type = 'service-fee') then (case
                when (o.net) then ((o.price) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier)
                else ((o.price) * ((1 / (er.rate))) / o.presentment_exchange_rate_modifier) / (1 + ((o.vat) / 100))
            end)
        end
    ), 0), 2) * 10 as data_layer_revenue
from
    public.order_item_offers as oio
left join
    -- use left join for better plan, but there must be 1 order_items for each order_item_offers
    public.order_items as o
on oio.id = o.id
left join
    -- use left join for better plan, but there must be 1 exchange_rates for each order_item_offers
    public.exchange_rates as er
on oio.presentment_currency = er.target::text
    and er.base::text = 'EUR'
    and oio.created_at < er.valid_to
    and oio.created_at >= er.valid_from
left join
    -- must be left join as there can be no merchant for given order_item_offers!
    public.merchants as m
on oio.merchant_id = m.id
where
    oio.state in ('released', 'released.failed');

-- test: select * from analytics.order_data_layer_revenue limit 10;
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.order_data_layer_revenue limit 10;
    end if;
end;
$test$;
