-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists analytics.zendesk_auto_responder_pipedrive;
create view analytics.zendesk_auto_responder_pipedrive
as
with pipedrive as (
    select
        merchant_name,
        merchant_id,
        coalesce(
            case
                when has_performance_role = true then primary_email
            end, 'joinme'
        ) as primary_email,
        account_manager,
        case
        -- Temporary until Pipedrive data gets complete
            when account_manager = 'Sarafina Dalipi' then '<EMAIL>'
            else account_manager_email
        end as account_manager_email
    from
        analytics.pipedrive
),

missing as (
    -- Temporary manual patch fix, until Pipedrive data gets complete
    select
        merchant_name,
        merchant_id,
        -- Joining with nulls isn't so fun
        coalesce(primary_email, 'joinme') as primary_email,
        account_manager,
        account_manager_email
    from
        analytics.missing_pipedrive
)

select distinct
    coalesce(p.merchant_name, m.merchant_name, mh.name) as merchant_name,
    coalesce(p.merchant_id, m.merchant_id) as merchant_id,
    case
        when coalesce(p.primary_email, m.primary_email) = 'joinme' then null
        else coalesce(p.primary_email, m.primary_email)
    end as primary_email,
    coalesce(p.account_manager, m.account_manager) as account_manager,
    coalesce(p.account_manager_email, m.account_manager_email) as account_manager_email,
    mh.state
from
    pipedrive as p
full outer join
    missing as m
on
    p.merchant_id = m.merchant_id and p.primary_email = m.primary_email
inner join
    public.merchants as mh
on
    coalesce(p.merchant_id, m.merchant_id) = mh.id
where
    mh.state != 'inactive';

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.zendesk_auto_responder_pipedrive limit 10;
    end if;
end;
$test$;
