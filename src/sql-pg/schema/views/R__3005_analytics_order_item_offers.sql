/* Flyway will re-run this script if it is changed or ${changeReason} is updated

   Update dependent scripts:
    - src/sql-pg/schema/functions/R__4002_backend_conversions.sql
    - src/sql-pg/schema/views/R__3057_export_order_item_offers.sql
*/
create or replace view analytics.order_item_offers as
select
    -- order_item_offers
    oio.id,
    oio.created_at,
    oio.updated_at,
    oio.order_id,
    oio.offer_id,
    oio.offer_valid_from,
    oio.type,
    oio.paid_at,
    oio.state,
    oio.country,
    oio.presentment_currency,
    oio.payment_provider,
    oio.valid_to,
    oio.instance_id,
    coalesce(oio.merchant_id, addons.transfer_merchant_id) as merchant_id,
    oio.grading,
    oio.warranty,

    -- offer_properties: nullable
    op.battery_condition,
    op.is_new_battery
from
    public.order_item_offers as oio
left join
    analytics.offer_properties as op
on oio.offer_id = op.offer_id
left join
    public.order_items as oi
on oio.id = oi.id
left join public.addons as addons
on oi.addon_id = addons.id and oi.addon_valid_from = addons.valid_from;

/*
    explain (analyze, verbose)
    select * from analytics.order_item_offers
    where merchant_id is not null;

    explain (analyze, verbose)
    select * from export_etl.v_order_item_offers
    where merchant_id is not null;
*/
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        perform * from analytics.order_item_offers limit 5;
    end if;
end;
$test$;
