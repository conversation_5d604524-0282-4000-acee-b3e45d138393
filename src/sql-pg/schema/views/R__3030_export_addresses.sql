-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_addresses;
create or replace view export_etl.v_addresses
as
select
    -- PK / time travel
    id,
    valid_from::timestamp with time zone,
    export_etl.infinity_to_timestamp(valid_to) as valid_to,
    -- Mandatory address fields
    owner_id,
    first_name::text,
    family_name::text,
    country::text,
    post_code::text,
    town::text,
    street_name::text,
    house_no::text,
    phone::text,
    -- Optional address fields
    male,
    address_type::text,
    supplement::text,
    company::text,
    vatin::text,
    personal_vatin::text,
    -- Calculated fields
    coalesce(male is null, false) as is_company,
    case
        when male then 'Male'
        when not male then 'Female'
        else 'Company'
    end as gender
from public.addresses;

-- test me!
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 4577 MB (4.5 GB)
        -- select pg_size_pretty(pg_relation_size('public.addresses')) as addresses_size;
        perform * from export_etl.v_addresses limit 5;
    end if;
end;
$test$;
