-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_countries;
create or replace view export_etl.v_countries
as
select
    c.code::text,
    c.name::text,
    c.main_language::text,
    c.currency::text,
    c.is_site
from public.countries as c;

-- test: select * from export_etl.v_countries
-- select * from public.countries
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- 8 kB
        -- select pg_size_pretty(pg_relation_size('public.countries')) as size;
        perform * from export_etl.v_countries limit 10;
    end if;
end;
$test$;
