-- Flyway will re-run this script if it is changed or ${changeReason} is updated
drop view if exists export_etl.v_instance_attribute_values;
create or replace view export_etl.v_instance_attribute_values
as
select
    instance_id,
    attribute_id,
    attribute_value_id
from
    public.instance_attribute_values;

-- test: export_etl.v_instance_attribute_values
do
$test$
begin
    if ('${runTest}' = 'true') then -- noqa
        -- size: 104 MB
        -- select pg_size_pretty(pg_relation_size('public.instance_attribute_values')) as instance_attribute_values_size;

        -- PK: instance_id, attribute_value_id
        perform
            instance_id,
            attribute_value_id,
            count(*) as duplicate_rows
        from export_etl.v_instance_attribute_values
        group by instance_id, attribute_value_id
        having count(*) > 1;
    end if;
end;
$test$;
