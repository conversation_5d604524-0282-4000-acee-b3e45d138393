from dataclasses import dataclass
from os import environ

from dataclasses_json import DataClassJsonMixin

from common.config import Config

AWS_CREDS_VARIABLE_NAME = "AWS_CREDENTIALS_SECRET"
DEFAULT_AWS_CREDS_SECRET_NAME = "adtriba-aws-secret"


@dataclass(frozen=True)
class AdtribaAwsCredentials(DataClassJsonMixin):
    access_key_id: str
    secret_access_key: str


@dataclass(frozen=True)
class AdtribaConfig(Config):
    """
    - boto_max_pool_connections: boto max connection config
      ref. https://botocore.amazonaws.com/v1/documentation/api/latest/reference/config.html
    - adtriba_secret: adtriba aws secret
    """

    boto_max_pool_connections: int = 20
    adtriba_secret: str = environ.get(AWS_CREDS_VARIABLE_NAME, DEFAULT_AWS_CREDS_SECRET_NAME)
    remove_local_files: bool = True
