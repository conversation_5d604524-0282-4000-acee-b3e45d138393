from datetime import date
from typing import Optional

from common.bq_repository import BigQueryRepository


class AdtribaTcmBqRepository(BigQueryRepository):
    """
    Adtriba True Consent Mode BigQuery repository
    """

    DATASET: str = "analytics_raw"
    TABLE: str = "adtriba_tcm"

    def get_last_export_date(self) -> Optional[date]:
        """Gets last export date if found parsed from the run_id"""

        sql = f"select safe.parse_date('%Y-%m-%d', max(date)) as date from {self.DATASET}.{self.TABLE};"
        results = self._client.run_sql(sql)

        return next(iter(results.rows)).get("date", None) if results else None
