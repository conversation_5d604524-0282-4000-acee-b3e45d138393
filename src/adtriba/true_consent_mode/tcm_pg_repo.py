from datetime import date, timed<PERSON><PERSON>
from typing import Optional

from sqlalchemy import Connection
from tcm_model import BackendConversions

from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository


class AdtribaTcmPgRepository(SqlRepository):
    """
    Adtriba TCM PostgreSQL Analytics DB repo
    """

    FUNCTION = SqlFunction(schema_name="analytics", function_name="select_backend_conversions")

    def get_backend_conversions(
        self, start_date: date, end_date: date, transaction: Optional[Connection] = None
    ) -> list[BackendConversions]:
        """
        Gets Adtriba True Consent Mode backend conversions dataset
        :param start_date: start date
        :param end_date: end date
        :param transaction: optional transaction
        :returns: export dataset
        """
        sql_start_date = start_date.isoformat()
        sql_end_date = (
            end_date + timedelta(days=1)
        ).isoformat()  # adding one day to have correct results for between condition on a datetime column

        return [
            BackendConversions(*row)
            for row in self.select_from_function(
                function=self.FUNCTION, args=[sql_start_date, sql_end_date], transaction=transaction
            )
        ]
