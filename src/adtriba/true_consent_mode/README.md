# Adtriba True Consent Mode Export
Adtriba needs to know how many conversions are registered in refurbed application and refurbed has to export the data to their AWS S3 bucket.
Comparing the numbers from the exported data with the tracking logs Adtriba is able to obtain the number of conversions that was not tracked due to consent.
Then these conversions are distributed proportionally (with an extrapolation method) to the attributions on the day the conversion took place.

Reference: https://help.adtriba.com/en/articles/6855714-true-consent-mode

## High level architecture
Every night at 23 UTC, refurbed data is transferred to the Adtriba S3 bucket. Typically, only the data from the previous day is exported. However, if the last export was unsuccessful, the next run will process both the missed data and the data from the previous day.

![Adtriba TCM ETL diagram](resources/adtriba_tcm_diagram.png)

For every exported day, a parquet file with following name is transferred to Adtriba S3 bucket: `backend-conversions-{YYYY-MM-DD}.parquet` where `{YYYY-MM-DD}` represents the day of the exported data.

## Available tables
### `analytics_raw.adtriba_tcm`
This table contains exported data represents the full history of all ETL runs.
The data is exported incrementally and for this reason can be aggregated between ETL runs.
However, to retrieve data for a specific ETL run, use the following query:

```sql
select *
from analytics_raw.adtriba_tcm
where
    run_id = "20240821_231559123"
limit 100;
```
⚠️ WARNING: This table presents raw data exported to Adtriba, and it is for debugging and experimental purposes only.
Do not use it for regular reporting.


## Stakeholders
@felix.schoenleitner
