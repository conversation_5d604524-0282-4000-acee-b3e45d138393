from datetime import date, <PERSON><PERSON><PERSON>
from functools import cached_property
from pathlib import Path
from typing import Optional, TypeAlias

from pyarrow import Table, parquet
from tcm_bq_repo import AdtribaTcmBqRepository
from tcm_model import (
    ADTRIBA_TCM_DATA_LAKE_PARTITION,
    AdtribaTcmConfig,
    BackendConversions,
    ExportDates,
)
from tcm_pg_repo import AdtribaTcmPgRepository

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.s3_client import S3Client
from common.sql_repository import SqlRepository
from common.time_service import TimeService

ExtractedType: TypeAlias = list[BackendConversions]
TransformedType: TypeAlias = list[Path]
LoadedType: TypeAlias = tuple[int, int]


logger = get_logger()


class AdtribaTcmEtl(BaseEtl, SupportsPipelineExecution):
    _config: AdtribaTcmConfig

    def __init__(
        self,
        config: AdtribaTcmConfig,
        pipeline_run: PipelineRun,
        s3_client: S3Client,
        raw_data_lake_repository: DataLakeRepository,
        pg_repository: AdtribaTcmPgRepository,
        bq_repository: AdtribaTcmBqRepository,
        local_temp_path: Path,
        time_service: Optional[TimeService] = None,
    ) -> None:
        super().__init__(config)
        self._pipeline_run = pipeline_run
        self._s3_client = s3_client
        self._raw_data_lake_repository = raw_data_lake_repository
        self._pg_repository = pg_repository
        self._bq_repository = bq_repository
        self._time_service = time_service if time_service else TimeService()
        self._local_temp_path = local_temp_path

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._pg_repository

    @cached_property
    def run_suffix(self) -> str:
        """Run partition suffix"""
        return f"run_id={self.run_id}"

    @cached_property
    def local_path(self) -> Path:
        """Temporary local path where data will be temporarily dumped"""
        return Path(self._local_temp_path, self.run_suffix)

    @cached_property
    def data_lake_path(self) -> Path:
        """Adtriba TCM raw data lake partition path where data will be uploaded"""
        return Path(ADTRIBA_TCM_DATA_LAKE_PARTITION.base_path, self.run_suffix)

    @log_pipeline_execution(step_name="adtriba tcm:extract")
    def extract(self) -> ExtractedType:
        """
        Extracts refurbed backend conversions from Analytics DB incrementally,
        based on the max date from already exported data.
        """
        last_db_export_date = self._bq_repository.get_last_export_date()
        export_dates = self.get_export_dates(last_db_export_date, self._config.max_number_of_export_dates)

        logger.info(
            f"Extracting backend conversions for orders between "
            f"{export_dates.start_date.isoformat()} and {export_dates.end_date.isoformat()}..."
        )
        extracted = self._pg_repository.get_backend_conversions(export_dates.start_date, export_dates.end_date)

        return extracted

    @log_pipeline_execution(step_name="adtriba tcm:transform")
    def transform(self, extracted: ExtractedType) -> TransformedType:
        """
        Partitions extracted data by date and exports to parquet files.
        Renames files to have export date in the name.
        :param extracted: extracted data
        :returns: paths to local partitioned files
        """
        table = Table.from_pylist([ext.to_dict() for ext in extracted])  # noqa
        parquet.write_to_dataset(table, root_path=self.local_path, partition_cols=["date"])
        self._rename_files()

        file_paths = [
            path.relative_to(self._local_temp_path) for path in self.local_path.rglob("*.parquet") if path.is_file()
        ]

        return file_paths

    @log_pipeline_execution(step_name="adtriba tcm:load")
    def load(self, transformed: TransformedType) -> LoadedType:
        """
        Load the data to raw data lake and export to Adtriba S3 bucket,
        :returns: number of loaded and exported files
        """
        export_file_count = 0
        if self._config.export_enabled:
            export_file_count = self._export_to_adtriba(transformed)
        else:
            logger.warning("Export to Adtriba S3 bucket disabled!")

        data_lake_file_count = self._load_data_lake(transformed)

        return data_lake_file_count, export_file_count

    def run(self) -> None:
        """
        Runs all the steps of the etl
        """
        logger.info(f"Starting Adtriba True Consent Mode export with {self.run_suffix}...")
        extracted = self.extract()
        if not extracted:
            logger.warning("No backend conversions to be exported. Finishing.")
            return
        else:
            logger.info(
                "Found backend conversions for following date(s): "
                f"{', '.join({ed.date.isoformat() for ed in extracted})}"
            )

        transformed = self.transform(extracted)
        data_lake_file_count, export_file_count = self.load(transformed)

        logger.info(
            f"Finished with {data_lake_file_count} file(s) loaded to data lake, "
            f"{export_file_count} file(s) exported to Adtriba!"
        )

    def get_export_dates(self, start_export_date: Optional[date], max_number_of_dates: int) -> ExportDates:
        """
        Calculates export dates where end date is limited by the 'max_number_of_dates' days.
        By default, it fallbacks to ExportDates(start_date=yesterday, end_date=yesterday).
        :param start_export_date: start date
        :param max_number_of_dates: maximum number of days, cannot be negative
        :returns: export dates
                  Example:
                    start_export_date = 2024-08-10
                    max_number_of_export_dates = 7
                    yesterday = 2024-08-20
                    export_dates = ExportDates(start_date=2024-08-10, end_date=2024-08-16)

        """
        logger.info("Calculating export dates...")
        # 'max_number_of_dates - 1' is needed since we have inclusive set od dates from the left.
        # e.g. including yesterday whole day
        max_number_of_dates = max(0, max_number_of_dates - 1)

        start_date = (
            min(self._time_service.yesterday, start_export_date) if start_export_date else self._time_service.yesterday
        )
        end_date = min(self._time_service.yesterday, start_date + timedelta(days=max_number_of_dates))
        dates = ExportDates(start_date, end_date)

        logger.info(
            "Following dates calculated: "
            f"start_date={dates.start_date.isoformat()}, end_date={dates.end_date.isoformat()}"
        )

        return dates

    def _load_data_lake(self, transformed: TransformedType) -> int:
        """
        Uploads the processed files to the raw data lake
        :returns: number of loaded files
        """

        files_count = len(transformed)
        logger.info(
            f"Loading TCM {files_count} file(s) to '{self._raw_data_lake_repository.bucket_name}' "
            f"data lake '{self.data_lake_path}' location..."
        )
        self._raw_data_lake_repository.write_directory(
            local_directory=self._local_temp_path,
            partition=ADTRIBA_TCM_DATA_LAKE_PARTITION,
        )

        return files_count

    def _export_to_adtriba(self, transformed: TransformedType) -> int:
        """
        Exports the processed files to the Adtriba S3 bucket
        :returns: number of exported files
        """

        files_count = len(transformed)
        logger.info(
            f"Exporting TCM {files_count} file(s) to Adtriba S3 "
            f"'{self._s3_client.bucket_name}{self.data_lake_path}' location..."
        )
        for file_path in transformed:
            full_path = Path(self._local_temp_path, file_path)
            self._s3_client.upload_file(file_path=full_path, s3_prefix=self._config.export_path)

        return files_count

    def _rename_files(self) -> None:
        """
        Renames already generated parquet files to have an export date in the name.
        This is just for easier debugging on the Adtriba S3 export bucket.
        """
        for file_path in self.local_path.rglob("*.parquet"):
            if file_path.is_file():
                export_date = str(file_path.parent).split("=")[-1]
                file_path.rename(Path(file_path.parent, f"backend-conversions-{export_date}.parquet"))
