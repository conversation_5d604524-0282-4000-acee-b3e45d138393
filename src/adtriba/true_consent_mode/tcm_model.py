from dataclasses import dataclass
from datetime import date

import dataclasses_json
from adtriba_shared.adtriba_model import AdtribaConfig
from dataclasses_json import DataClassJsonMixin

from common.typings import DataLakePartition

dataclasses_json.cfg.global_config.encoders[date] = date.isoformat
dataclasses_json.cfg.global_config.decoders[date] = date.fromisoformat


@dataclass(frozen=True, kw_only=True)
class AdtribaTcmConfig(AdtribaConfig):
    """
    - export_enabled: whether to export data to Adtriba S3 bucket
    - max_number_of_export_dates: upper limit of days for single export
    - export_bucket: Adtriba S3 bucket name where to export refurbed data
    - export_path: Adtriba S3 bucket path where to export refurbed data
                   Ref. https://help.adtriba.com/en/articles/6855714-true-consent-mode
    """

    export_enabled: bool
    max_number_of_export_dates: int = 7
    export_bucket: str = "adtriba-import-ce4f606d-fafe-47e3-adde-8edbb95b44d0"
    export_path: str = "dwh/backend_conversions"


ADTRIBA_TCM_DATA_LAKE_PARTITION = DataLakePartition(base_path="adtriba/tcm")


@dataclass(frozen=True)
class ExportDates(DataClassJsonMixin):
    start_date: date
    end_date: date


@dataclass(frozen=True, slots=True)
class BackendConversions(DataClassJsonMixin):
    date: date
    # do not change the typo in 'costumer_type' field name, it's in line with the Adtriba spec.
    # see here: https://help.adtriba.com/en/articles/6855714-true-consent-mode
    costumer_type: str
    device_type: str
    total_conversions: int
    total_revenue: float
    brand: str
    country: str
    conversion_name: str
    currency: str
