# Adtriba Sphere ETL
Adtriba is a platform that includes a service called Sphere. Sphere allows us to simulate the best-case scenario for budget distribution across different marketing channels for a specific market.


Data can be exported from the Sphere application. This export is automated by Adtriba, and the data is stored in an AWS S3 bucket.
However, to load the data into BigQuery (BQ), the following flow has been created:

![Adtriba Sphere ETL diagram](resources/adtriba_etl_diagram.png)

## Available tables
### `analytics_raw.adtriba_sphere`
This table is where data is appended only for each ETL run. Therefore, it contains the full history of all ETL runs.

To retrieve data for a specific ETL run, use the following query:

```sql
select *
from analytics_raw.adtriba_sphere
where
    run_id = "20240715_081541340"
    and brand = "refurbed"
    and country = "at"
    and conversion_event = "transaction"
    and device_type = "default"
limit 100;
```
⚠️ WARNING: This table is for debugging and experimental purposes only. Do not use it for regular reporting.

### `analytics_transformed.adtriba`
This table displays only the data from the most recent ETL run.
For example, if `20240715_081541340` represents the latest ETL run, you'll find the corresponding data here.
```sql
select * from analytics_transformed.adtriba limit 100;
```
✅ This table can be used for regular reporting.

## Stakeholders
@felix.schoenleitner
