from dataclasses import dataclass, field

from adtriba_shared.adtriba_model import AdtribaConfig

from common.typings import DataLakePartition


@dataclass(frozen=True)
class AdtribaSphereConfig(AdtribaConfig):
    """
    - boto_max_pool_connections: boto max connection config
      ref. https://botocore.amazonaws.com/v1/documentation/api/latest/reference/config.html
    - source_bucket: Adtriba S3 bucket name when data is exported
    - source_path: S3 prefix where Adtriba exports the data
    """

    source_bucket: str = "adtriba-export-ce4f606d-fafe-47e3-adde-8edbb95b44d0"
    source_path: str = "sphere/performance/"
    file_names_to_ignore: list[str] = field(default_factory=lambda: ["_SUCCESS"])


ADTRIBA_SPHERE_DATA_LAKE_PARTITION = DataLakePartition(base_path="adtriba/sphere")
