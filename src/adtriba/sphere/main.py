import dataclasses
import tempfile
from pathlib import Path

import functions_framework
from adtriba_shared.adtriba_client import get_s3_client
from cloudevents.abstract import CloudEvent
from google.cloud.sql.connector import Connector
from sphere_bq_repo import AdtribaSphereBqRepository
from sphere_etl import AdtribaSphereEtl
from sphere_model import AdtribaSphereConfig

from common.cloud_events import create_http_event, get_event_message
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.sql_repository import SqlRepository
from common.timing import timing
from common.typings import HttpResponse

config = AdtribaSphereConfig()
logger = setup_logger(config)

# AWS
s3_client = get_s3_client(config, config.source_bucket)

# BigQuery
raw_data_lake_repo = DataLakeRepository(config.raw_bucket)
bq_repo = AdtribaSphereBqRepository()

# PostgresSQL
pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)


@timing(logger, "adtriba-sphere-etl")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    message = get_event_message(request)
    logger.info(f"Running Adtriba Sphere Etl with {message=}...")

    pipeline_run = PipelineRun.from_json(message)
    with Connector() as connector, tempfile.TemporaryDirectory(delete=config.remove_local_files) as local_path:
        pg_repository = SqlRepository(db_config=pg_config, connector=connector)
        etl = AdtribaSphereEtl(
            config=config,
            pipeline_run=pipeline_run,
            s3_client=s3_client,
            raw_data_lake_repository=raw_data_lake_repo,
            bq_repository=bq_repo,
            pg_repository=pg_repository,
            local_temp_path=Path(local_path),
        )
        etl.run()

    logger.info("Finished Adtriba Sphere Etl.")
    return HttpResponse()


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()
    config = dataclasses.replace(config, remove_local_files=False)
    test_pipeline = PipelineRun()
    run(create_http_event(test_pipeline.to_json()))
