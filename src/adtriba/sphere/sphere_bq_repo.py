from common.bq_repository import BigQueryRepository, BQProcedure


class AdtribaSphereBqRepository(BigQueryRepository):
    """
    Adtriba BigQuery repository
    """

    DATASET: str = "analytics_transformed"
    TABLE: str = "adtriba"

    LOAD_PROCEDURE = BQProcedure(DATASET, "load_adtriba")

    def load_adtriba_table(self, run_id: str) -> int:
        """
        Full reloads `analytics_transformed.adtriba' table from the `analytics_raw` corresponding table
         Example call:
         `call analytics_transformed.load_adtriba('20240712_121651107');`

         :param run_id: given run id
         :returns: total bytes processed
        """
        result = self.call_procedure(self.LOAD_PROCEDURE, [run_id])

        return result.total_bytes_processed
