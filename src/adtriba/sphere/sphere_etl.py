from functools import cached_property
from pathlib import Path
from typing import TypeAlia<PERSON>

from sphere_bq_repo import AdtribaSphereBqRepository
from sphere_model import ADTRIBA_SPHERE_DATA_LAKE_PARTITION, AdtribaSphereConfig

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.s3_client import S3Client
from common.sql_repository import SqlRepository

logger = get_logger()

ExtractedType: TypeAlias = list[Path]
TransformedType: TypeAlias = list[Path]
LoadedType: TypeAlias = tuple[int, int]


class AdtribaSphereEtl(BaseEtl, SupportsPipelineExecution):
    _config: AdtribaSphereConfig

    def __init__(
        self,
        config: AdtribaSphereConfig,
        pipeline_run: PipelineRun,
        s3_client: S3Client,
        raw_data_lake_repository: DataLakeRepository,
        bq_repository: AdtribaSphereBqRepository,
        pg_repository: SqlRepository,
        local_temp_path: Path,
    ) -> None:
        super().__init__(config)
        self._pipeline_run = pipeline_run
        self._s3_client = s3_client
        self._raw_data_lake_repository = raw_data_lake_repository
        self._bq_repository = bq_repository
        self._pg_repository = pg_repository
        self._local_temp_path = local_temp_path

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._pg_repository

    @cached_property
    def run_suffix(self) -> str:
        """Run partition suffix"""
        return f"run_id={self.run_id}"

    @cached_property
    def local_path(self) -> Path:
        """Temporary local path where data will be downloaded"""
        return Path(self._local_temp_path, self.run_suffix)

    @cached_property
    def data_lake_path(self) -> Path:
        """Adtriba Sphere raw data lake partition path where data will be uploaded"""
        return Path(ADTRIBA_SPHERE_DATA_LAKE_PARTITION.base_path, self.run_suffix)

    @log_pipeline_execution(step_name="adtriba sphere:extract")
    def extract(self) -> ExtractedType:
        """
        Downloads all files from Adtriba Sphere S3 export bucket
        :returns: list of downloaded file paths
        """
        logger.info(
            f"Getting data from Adtriba Sphere S3 '{self._s3_client.bucket_name}/{self._config.source_path}' "
            f"bucket location..."
        )
        self._s3_client.download_files(
            s3_prefix=self._config.source_path, local_path=self.local_path, include_prefix_in_local_path=False
        )
        file_paths = [path.relative_to(self._local_temp_path) for path in self.local_path.rglob("*") if path.is_file()]

        logger.info(f"Found {len(file_paths)} Adtriba Sphere file(s)!")

        return file_paths

    @log_pipeline_execution(step_name="adtriba sphere:transform")
    def transform(self, extracted_path: Path) -> TransformedType:
        """
        Transform downloaded data by removing not needed files
        :returns: list of file paths excluding deleted ones
        """
        for file_name_to_ignore in self._config.file_names_to_ignore:
            logger.info(f"Removing not needed '{file_name_to_ignore}' files...")
            files_to_removes = [path for path in extracted_path.rglob(file_name_to_ignore) if path.is_file()]
            for file_to_remove in files_to_removes:
                file_to_remove.unlink()

        return [path.relative_to(self._local_temp_path) for path in extracted_path.rglob("*") if path.is_file()]

    @log_pipeline_execution(step_name="adtriba sphere:load")
    def load(self, transformed: TransformedType) -> LoadedType:
        """
        Load the data to raw data lake and BigQuery DWH table
        :returns: number of loaded files to data lake and bytes processed by BQ insert
        """
        files_loaded = self._load_data_lake(transformed)
        bytes_processed = self._load_biq_query()

        return files_loaded, bytes_processed

    def run(self) -> None:
        """
        Runs all the steps of the etl
        """
        logger.info(f"Starting Adtriba Sphere ETL with {self.run_suffix}...")
        extracted = self.extract()
        if not extracted:
            logger.warning("No Adtriba Sphere files to be loaded. Finishing.")
            return

        transformed = self.transform(self.local_path)
        files_loaded, bytes_processed = self.load(transformed)

        logger.info(
            f"Finished with {files_loaded} data lake file(s) loaded and {bytes_processed} BigQuery bytes processed!"
        )

    def _load_data_lake(self, transformed: TransformedType) -> int:
        """
        Uploads the processed files to the raw data lake
        :returns: number of loaded files
        """
        files_count = len(transformed)
        logger.info(
            f"Loading {files_count} file(s) to '{self._raw_data_lake_repository.bucket_name}' "
            f"data lake '{self.data_lake_path}' location..."
        )
        self._raw_data_lake_repository.write_directory(
            local_directory=self._local_temp_path,
            partition=ADTRIBA_SPHERE_DATA_LAKE_PARTITION,
        )

        return files_count

    def _load_biq_query(self) -> int:
        """
        Full reloads BigQuery table for given run_id.
        """
        logger.info(f"Refreshing BigQuery '{self._bq_repository.TABLE}' table for given '{self.run_id}' run_id...")
        bytes_processed = self._bq_repository.load_adtriba_table(run_id=self.run_id)

        return bytes_processed
