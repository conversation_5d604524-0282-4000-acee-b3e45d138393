"""
DISPATCHER starts platform ETL by returning source tables for extraction.
"""

from typing import Optional

from export_shared.export_model import ExportConfig
from export_shared.export_target import SOURCE_TO_TARGET

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository
from common.time_service import TimeService

logger = get_logger()


class PlatformExportDispatcher(SupportsPipelineExecution):
    """
    Dispatches CONFIGURED_SOURCES (platform export views) for extraction and load.
    """

    CONFIGURED_SOURCES: set[SqlTable] = set(SOURCE_TO_TARGET.keys())

    def __init__(
        self, pipeline_run: PipelineRun, repository: SqlRepository, time_service: Optional[TimeService] = None
    ) -> None:
        self.set_environment(pipeline_run)
        self._repository = repository
        self._time_service = time_service if time_service else TimeService()

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution implementation."""
        return self._repository

    @log_pipeline_execution()
    def dispatch(self) -> list[ExportConfig]:
        """
        Dispatches data for extraction to `platform-export` workflow.

        :returns: Export configurations.
        """
        result: list[ExportConfig] = []
        timestamp = self._time_service.timestamp_in_sec
        now = self._time_service.now.isoformat()

        logger.info(
            f"Dispatching {len(self.CONFIGURED_SOURCES)} source tables for extraction with {timestamp=} ({now})..."
        )
        for sql_table in self.CONFIGURED_SOURCES:
            config = ExportConfig(timestamp=timestamp, source=sql_table)
            result.append(config)

        return result
