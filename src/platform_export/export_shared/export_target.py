"""
SOURCE Postgres export view to TARGET BigQuery table mapping.
"""

from export_shared.export_model import ExportTarget, ExportType

from common.sql_model import SqlTable

# src/sql-pg/schema/views/R__3030_export_addresses.sql
ADDRESSES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_addresses")
ADDRESSES_TARGET = "addresses"

# src/sql-pg/schema/views/R__3020_export_products.sql
PRODUCTS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_products")
PRODUCTS_TARGET = "products"

# src/sql-pg/schema/views/R__3021_export_categories.sql
CATEGORIES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_categories")
CATEGORIES_TARGET = "categories"

# src/sql-pg/schema/views/R__3022_export_merchants.sql
MERCHANTS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_merchants")
MERCHANTS_TARGET = "merchants"

# src/sql-pg/schema/views/R__3023_export_instances.sql
INSTANCES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_instances")
INSTANCES_TARGET = "instances"

# src/sql-pg/schema/views/R__3024_export_exchange_rates.sql
EXCHANGE_RATES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_exchange_rates")
EXCHANGE_RATES_TARGET = "exchange_rates"

# src/sql-pg/schema/views/R__3025_export_attributes.sql
ATTRIBUTES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_attributes")
ATTRIBUTES_TARGET = "attributes"

# src/sql-pg/schema/views/R__3026_export_instance_attribute_values.sql
INSTANCE_ATTRIBUTE_VALUES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_instance_attribute_values")
INSTANCE_ATTRIBUTE_VALUES_TARGET = "instance_attribute_values"

# src/sql-pg/schema/views/R__3027_export_product_attribute_values.sql
PRODUCT_ATTRIBUTE_VALUES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_product_attribute_values")
PRODUCT_ATTRIBUTE_VALUES_TARGET = "product_attribute_values"

# src/sql-pg/schema/views/R__3031_export_shipping_profiles.sql
SHIPPING_PROFILES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_shipping_profiles")
SHIPPING_PROFILES_TARGET = "shipping_profiles"

# src/sql-pg/schema/views/R__3032_export_shipping_profile_destinations.sql
SHIPPING_PROFILE_DESTINATIONS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_shipping_profile_destinations")
SHIPPING_PROFILE_DESTINATIONS_TARGET = "shipping_profile_destinations"

# src/sql-pg/schema/views/R__3057_export_order_item_offers.sql
ORDER_ITEM_OFFERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_item_offers")
ORDER_ITEM_OFFERS_TARGET = "order_item_offers"

# src/sql-pg/schema/views/R__3040_export_offers.sql
OFFERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_offers")
OFFERS_TARGET = "offers"

# sql-pg/schema/views/R__3041_export_orders.sql
ORDERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_orders")
ORDERS_TARGET = "orders"

# src/sql-pg/schema/views/R__3042_export_order_items.sql
ORDER_ITEMS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_order_items")
ORDER_ITEMS_TARGET = "order_items"

# src/sql-pg/migrations/V37__add_order_item_exchange_rate.sql
ORDER_ITEM_EXCHANGE_RATE_SOURCE = SqlTable(schema_name="analytics", table_name="order_item_exchange_rate")
ORDER_ITEM_EXCHANGE_RATE_TARGET = "order_item_exchange_rate"

# src/sql-pg/schema/views/R__3008_order_financial_revenue.sql
ORDER_FINANCIAL_REVENUE_SOURCE = SqlTable(schema_name="analytics", table_name="order_financial_revenue")
ORDER_FINANCIAL_REVENUE_TARGET = "order_financial_revenue"

# src/sql-pg/migrations/V43__refactor_order_item_refund_commission.sql
FULL_ORDER_ITEM_REFUNDS_SOURCE = SqlTable("analytics", "full_order_item_refunds")
FULL_ORDER_ITEM_REFUNDS_TARGET = "full_order_item_refunds"

# src/sql-pg/schema/views/R__3013_export_countries.sql
COUNTRIES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_countries")
COUNTRIES_TARGET = "countries"

# src/sql-pg/schema/views/R__3055_export_product_categories.sql
PRODUCT_CATEGORIES_SOURCE = SqlTable(schema_name="export_etl", table_name="v_product_categories")
PRODUCT_CATEGORIES_TARGET = "product_categories"

# src/sql-pg/migrations/V44__offer_properties.sql
OFFER_PROPERTIES_SOURCE = SqlTable(schema_name="analytics", table_name="offer_properties")
OFFER_PROPERTIES_TARGET = "offer_properties"

# src/sql-pg/schema/views/R__3058_export_product_rankings.sql
PRODUCT_RANKINGS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_product_rankings")
PRODUCT_RANKINGS_TARGET = "product_rankings"

# src/sql-pg/schema/views/R__3028_export_users.sql
USERS_SOURCE = SqlTable(schema_name="export_etl", table_name="v_users")
USERS_TARGET = "users"

# Mapping of source PG SQL views to their target export type and BQ table
SOURCE_TO_TARGET: dict[SqlTable, ExportTarget] = {
    # INCREMENTAL:
    CATEGORIES_SOURCE: ExportTarget(CATEGORIES_TARGET, ExportType.INCREMENTAL),
    INSTANCES_SOURCE: ExportTarget(INSTANCES_TARGET, ExportType.INCREMENTAL),
    MERCHANTS_SOURCE: ExportTarget(MERCHANTS_TARGET, ExportType.INCREMENTAL),
    ORDERS_SOURCE: ExportTarget(ORDERS_TARGET, ExportType.INCREMENTAL),
    ORDER_ITEMS_SOURCE: ExportTarget(ORDER_ITEMS_TARGET, ExportType.INCREMENTAL),
    PRODUCTS_SOURCE: ExportTarget(PRODUCTS_TARGET, ExportType.INCREMENTAL),
    USERS_SOURCE: ExportTarget(USERS_TARGET, ExportType.INCREMENTAL),
    # TIME_TRAVEL:
    ADDRESSES_SOURCE: ExportTarget(ADDRESSES_TARGET, ExportType.TIME_TRAVEL),
    EXCHANGE_RATES_SOURCE: ExportTarget(EXCHANGE_RATES_TARGET, ExportType.TIME_TRAVEL),
    OFFERS_SOURCE: ExportTarget(OFFERS_TARGET, ExportType.TIME_TRAVEL),
    SHIPPING_PROFILES_SOURCE: ExportTarget(SHIPPING_PROFILES_TARGET, ExportType.TIME_TRAVEL),
    SHIPPING_PROFILE_DESTINATIONS_SOURCE: ExportTarget(SHIPPING_PROFILE_DESTINATIONS_TARGET, ExportType.TIME_TRAVEL),
    # SNAPSHOT:
    ATTRIBUTES_SOURCE: ExportTarget(ATTRIBUTES_TARGET, ExportType.SNAPSHOT),
    COUNTRIES_SOURCE: ExportTarget(COUNTRIES_TARGET, ExportType.SNAPSHOT),
    FULL_ORDER_ITEM_REFUNDS_SOURCE: ExportTarget(FULL_ORDER_ITEM_REFUNDS_TARGET, ExportType.SNAPSHOT),
    INSTANCE_ATTRIBUTE_VALUES_SOURCE: ExportTarget(INSTANCE_ATTRIBUTE_VALUES_TARGET, ExportType.SNAPSHOT),
    OFFER_PROPERTIES_SOURCE: ExportTarget(OFFER_PROPERTIES_TARGET, ExportType.SNAPSHOT),
    ORDER_FINANCIAL_REVENUE_SOURCE: ExportTarget(ORDER_FINANCIAL_REVENUE_TARGET, ExportType.SNAPSHOT),
    ORDER_ITEM_EXCHANGE_RATE_SOURCE: ExportTarget(ORDER_ITEM_EXCHANGE_RATE_TARGET, ExportType.SNAPSHOT),
    ORDER_ITEM_OFFERS_SOURCE: ExportTarget(ORDER_ITEM_OFFERS_TARGET, ExportType.SNAPSHOT),
    PRODUCT_ATTRIBUTE_VALUES_SOURCE: ExportTarget(PRODUCT_ATTRIBUTE_VALUES_TARGET, ExportType.SNAPSHOT),
    PRODUCT_CATEGORIES_SOURCE: ExportTarget(PRODUCT_CATEGORIES_TARGET, ExportType.SNAPSHOT),
    PRODUCT_RANKINGS_SOURCE: ExportTarget(PRODUCT_RANKINGS_TARGET, ExportType.SNAPSHOT),
}
