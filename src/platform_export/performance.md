# Performance of platform export

## 2025.08.04 Platform Jobs Timing

1. Use BQ federated queries to extract data from `analytics-db-refresh-target` cloud-sql into BQ staging tables.
2. Measure `extract` time from a source PG database, as `load` time is negligible.
3. `public.offers` table is limited to last 6 months of data: 16 GB.

| BQ Table                      | Export Type | 1st [hh:mm:ss] | 2nd [hh:mm:ss] |
|-------------------------------|-------------|----------------|----------------|
| offers                        | time travel | [00:28:21]     | 00:03:10       |
| order_items                   | incremental | [00:11:32]     | 00:00:11       |
| order_item_offers             | snapshot    | [00:05:35]     | 00:04:25       |
| orders                        | incremental | [00:05:35]     | 00:00:11       |
| addresses                     | time travel | [00:05:29]     | 00:06:34       |
| order_financial_revenue       | snapshot    | [00:01:30]     | 00:01:38       |
| full_order_item_refunds       | snapshot    | [00:00:46]     | 00:00:24       |
| users                         | incremental | [00:00:45]     | 00:00:06       |
| order_item_exchange_rate      | snapshot    | [00:00:28]     | 00:00:17       |
| offer_properties              | incremental | [00:00:16]     | 00:00:11       |
| instances                     | incremental | [00:00:12]     | 00:00:05       |
| instance_attribute_values     | incremental | [00:00:07]     | 00:00:09       |
| product_attribute_values      | incremental | [00:00:06]     | 00:00:06       |
| merchants                     | incremental | [00:00:05]     | 00:00:06       |
| product_categories            | incremental | [00:00:05]     | 00:00:06       |
| shipping_profiles             | incremental | [00:00:05]     | 00:00:05       |
| shipping_profile_destinations | incremental | [00:00:05]     | 00:00:05       |
| attributes                    | incremental | [00:00:05]     | 00:00:05       |
| product_rankings              | incremental | [00:00:05]     | 00:00:05       |
| products                      | incremental | [00:00:05]     | 00:00:05       |
| exchange_rates                | incremental | [00:00:05]     | 00:00:05       |
| categories                    | incremental | [00:00:04]     | 00:00:04       |
| countries                     | incremental | [00:00:04]     | 00:00:05       |

Legend:

- 1st run: full export after `truncate` of all target BQ tables (run_id=20250804_115519538);
- 2nd run: incremental export (run_id=20250804_125836784);

### Extract full offers from last PROD backup

* `analytics-db-refresh-source`: 32 cpu, 128 GB RAM, 1 TB SSD, work_mem=131072
* `platform` database restored from last backup to `analytics-db-refresh-source` instance:
    * Backup: id='1754352000000', instance='platform-19af-15', start_time=datetime.datetime(2025, 8, 5, 0, 57, 19,
      807000)
* Create `export_etl.v_offers` in `platform` database
* Run and measure the time of the following query:
  ```sql
  create or replace table test.offers
  as
  select *
  from external_query(
    "refb-analytics-staging.eu.db-refresh-source",
    "select * from export_etl.v_offers;"
  );
  ```
* Offers table metrics (measured on 05/08/2025):
    * PG table size: 84 GB
    * BQ job duration: 2 hr 16 min
    * BQ bytes processed: 96.4 GB
    * BQ bytes billed: 96.4 GB
    * BQ slot time consumed: 3 days 11 hr

## 2025.05.29 Platform Jobs Timing

Using `pandas.read_sql` to extract data from `analytics-db-refresh-target` cloud-sql into BQ staging tables.
Selecting data in `chunks` using server side cursor (`stream_results`) to minimize memory usage.

- 1st run (27.05): full export after `truncate` of all target BQ tables;
- 2nd run (28.05): incremental export;
- 3rd run (29.05):
    - `order_item_offers` changed to snapshot due to data discrepancies;
    - `order_item_offers` assigned to Large job size;
    - `EXPORT_CHUNK_SIZE` increased to 1,000,000;
    - [00:31:38.01s] in medium vs [00:27:32.01s] in large job size.

| Job Size | BQ Table                      | Export Type | 1st [hh:mm:ss.ff]  | 2nd [hh:mm:ss.ff]  |
|----------|-------------------------------|-------------|--------------------|--------------------|
| Large    | offers                        | time travel | **[03:06:38.01s]** | **[00:06:37.01s]** |
| Large    | order_items                   | incremental | **[00:51:15.01s]** | [00:00:22.01s]     |
| Medium   | order_item_offers             | incremental | **[00:32:30.01s]** | [00:00:18.01s]     |
| Large    | orders                        | incremental | **[00:26:43.01s]** | [00:00:42.01s]     |
| Medium   | order_financial_revenue       | snapshot    | [00:09:04.01s]     | **[00:08:12.01s]** |
| Medium   | order_item_exchange_rate      | snapshot    | [00:02:21.01s]     | [00:02:33.01s]     |
| Medium   | full_order_item_refunds       | snapshot    | [00:01:42.01s]     | [00:01:36.01s]     |
| Small    | offer_properties              | snapshot    | [00:00:57.01s]     | [00:00:56.01s]     |
| Small    | instance_attribute_values     | snapshot    | [00:00:37.01s]     | [00:00:34.01s]     |
| Medium   | instances                     | incremental | [00:00:28.01s]     | [00:00:09.01s]     |
| Small    | attributes                    | snapshot    | [00:00:18.01s]     | [00:00:15.01s]     |
| Small    | exchange_rates                | time travel | [00:00:14.01s]     | [00:00:07.01s]     |
| Small    | product_attribute_values      | snapshot    | [00:00:11.01s]     | [00:00:11.01s]     |
| Small    | shipping_profile_destinations | time travel | [00:00:09.01s]     | [00:00:07.01s]     |
| Small    | shipping_profiles             | time travel | [00:00:08.01s]     | [00:00:06.01s]     |
| Small    | product_categories            | snapshot    | [00:00:08.01s]     | [00:00:07.01s]     |
| Small    | merchants                     | incremental | [00:00:06.01s]     | [00:00:06.01s]     |
| Small    | categories                    | incremental | [00:00:06.01s]     | [00:00:05.01s]     |
| Small    | countries                     | snapshot    | [00:00:05.01s]     | [00:00:06.01s]     |
| Small    | products                      | incremental | [00:00:08.01s]     | [00:00:06.01s]     |

### Legend

**Large jobs:**

- [platform-export-large-job](https://console.cloud.google.com/run/jobs/details/europe-west3/platform-export-large-job/executions?hl=en&inv=1&invt=AbyIzw&project=refb-analytics)
- RAM: 16 GiB
- CPU: 4

**Medium jobs:**

- [platform-export-medium-job](https://console.cloud.google.com/run/jobs/details/europe-west3/platform-export-medium-job/executions?hl=en&inv=1&invt=AbyIzw&project=refb-analytics)
- RAM: 4 GiB
- CPU: 1

**Small jobs:**

- [platform-export-small-job](https://console.cloud.google.com/run/jobs/details/europe-west3/platform-export-small-job/executions?hl=en&inv=1&invt=AbyIzw&project=refb-analytics)
- RAM: 2 GiB
- CPU: 1

## 2025.03.05 Performance testing of extract (as a cloud run function)

### RAM: 8GB, CPU: 2, no VPC

- chunk_size=500000, max_row_buffer=1000 => [00:24:41.01s]
- chunk_size=500000, max_row_buffer=500000 => [00:22:05.01s]
- chunk_size=500000, max_row_buffer=500000 => [00:21:49.01s] (second run)
- chunk_size=100000, max_row_buffer=100000 => [00:25:48.01s]
- chunk_size=1000000,max_row_buffer=1000000 => [00:29:05.01s]
- chunk_size=500000, max_row_buffer=1000000 => [00:28:57.01s]
- chunk_size=2000000,max_row_buffer=2000000 => [00:20:59.01s]

### RAM: 16GB, CPU: 4, no VPC

- chunk_size=2000000,max_row_buffer=2000000 => [00:20:14.01s]

### RAM: 8GB, CPU: 2, VPC: direct, route all traffic to the VPC

- chunk_size=500000, max_row_buffer=500000 => [00:23:24.01s]
- chunk_size=1000000,max_row_buffer=1000000 => [00:19:36.01s]

### RAM: 8GB, CPU: 2, no VPC, pool_size: = 10

- chunk_size=500000, max_row_buffer=500000 => [00:23:53.01s]

### RAM: 8GB, CPU: 2, no VPC, no analytics.order_financial_revenue

- chunk_size=500000, max_row_buffer=500000 => [00:20:38.01s]

## 2025.03.07 Performance testing of extract and load (as cloud run job)

### RAM: 8GB, CPU: 2, no VPC

- chunk_size=500000, max_row_buffer=500000 => [00:33:09.01s]
