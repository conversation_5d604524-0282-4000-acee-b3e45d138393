# Platform data export into Analytics DWH

The solution is based on
the [PoC on incremental load](https://www.notion.so/refurbed/PoC-on-incremental-load-d81362e9da8c4a46ab2fcd4cebe5a2d5)
findings.

It was updated according
to [POC with federated queries to cloud-sql](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/557)
using federated queries to cloud-sql as a better alternative to in memory ingestion with `pandas.read_sql`.

It exports platform data from `export_etl` views into BQ datasets that form a lakehouse:

- `platform_export` - incremental export of platform data into BQ staging tables;
- `analytics_transformed` - target native BQ tables with data merged daily;

## Current scope

The source is `analytics` database and is dependent on daily analytics database refresh.
Currently configured sources are configured
in [export_dispatcher](https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/blob/27f8011ecec7033b56c84c693f98412d8ac25cc7/src/platform_export/dispatcher/export_dispatcher.py#L31).

| BQ `analytics_transformed` table |                       PG export view/table | Logical Size | Physical Size |
|:---------------------------------|-------------------------------------------:|-------------:|--------------:|
| addresses                        |                     export_etl.v_addresses |      3.24 GB |        741 MB |
| attributes                       |                    export_etl.v_attributes |     14.85 MB |        3.8 MB |
| categories                       |                    export_etl.v_categories |     24.66 KB |      11.46 KB |
| countries                        |                     export_etl.v_countries |        753 B |       2.89 KB |
| exchange_rates                   |                export_etl.v_exchange_rates |      7.28 MB |       2.91 MB |
| full_order_item_refunds          |          analytics.full_order_item_refunds |    286.14 MB |       31.9 MB |
| instance_attribute_values        |     export_etl.v_instance_attribute_values |     62.72 MB |       6.31 MB |
| instances                        |                     export_etl.v_instances |     55.73 MB |       8.42 MB |
| merchants                        |                     export_etl.v_merchants |    269.24 KB |     235.76 KB |
| offers                           |                        export_etl.v_offers |     25.24 GB |       2.75 GB |
| offer_properties                 |                 analytics.offer_properties |     30.91 MB |        8.7 MB |
| order_financial_revenue          |          analytics.order_financial_revenue |      1.84 GB |     272.72 MB |
| order_item_exchange_rate         |         analytics.order_item_exchange_rate |    435.83 MB |      50.15 MB |
| order_item_offers                |             export_etl.v_order_item_offers |      2.41 GB |     758.62 MB |
| order_items                      |                   export_etl.v_order_items |     10.68 GB |     834.57 MB |
| orders                           |                        export_etl.v_orders |       2.5 GB |     699.39 MB |
| product_attribute_values         |      export_etl.v_product_attribute_values |      7.97 MB |     837.05 KB |
| product_categories               |            export_etl.v_product_categories |      1.69 MB |     172.38 KB |
| product_rankings                 |              export_etl.v_product_rankings |      9.49 MB |       3.23 MB |
| products                         |                      export_etl.v_products |      4.31 MB |       1.66 MB |
| shipping_profile_destinations    | export_etl.v_shipping_profile_destinations |      2.85 MB |      661.6 KB |
| shipping_profiles                |             export_etl.v_shipping_profiles |      2.15 MB |     792.42 KB |
| users                            |                         export_etl.v_users |    518.44 MB |     210.78 MB |

## High level architecture

```mermaid
sequenceDiagram
    loop FOR EVERY SOURCE
        ETL job ->> BQ (analytics_staging): Get data changes from cloud-sql
        Note right of ETL job: Using federated queries
        ETL job ->> BQ (analytics_transformed): MERGE changes into target table
        Note right of ETL job: Using BQ/SQL Merge
    end
```

The process is triggered by the [platform-export](/infra/workflows/platform_export.yaml) workflows and consists of the
following components:

**Dispatcher**

Cloud Run Function (512Mi), which returns PG source name to be extracted;

**ETL**

- Cloud Run Job per source that:
    - Extracts given export view as a snapshot/increment into BQ `platform_export` staging dataset;
    - Loads extracted data into BQ `analytics_transformed` dataset by sql procedure;

## How to extend platform export with new source

1. Create new `export_etl.view` in `analytics` database:

- ensure to cast all PG custom types to primitive types accepted by BQ,
- measure size of the source tables using `pg_relation_size`,
- check what is the primary key,

2. Create relevant entries in the `export_target.py` file:

- SOURCE view name,
- TARGET table name,
- add mapping to `SOURCE_TO_TARGET` variable

3. Create BQ staging table in `platform_export` dataset

4. Create BQ target table in `analytics_transformed` dataset:

- always add primary key constraint,
- if relevant, add foreign key constraints,
- if table is over 10 GB consider partitioning,
- always add `cluster by` with columns frequently used in filters,

5. Create load procedure in `analytics_transformed` dataset:

- use `MERGE` statement for incremental and time travel tables,
- use `TRUNCATE` and `INSERT` for fully reloaded tables,
- add related `check` procedure that checks for data anomalies,

6. Run integration test to ensure ETL is working as expected:

- run `test_platform_export` in `tests/integration` directory
