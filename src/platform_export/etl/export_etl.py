"""
Platform Export ETL
1. Extracts data from a source platform database via export_etl views.
2. Loads extracted data into BigQuery.
"""

from analytics_bq_repository import AnalyticsBQRepository
from export_shared.export_model import ExportConfig
from export_shared.export_target import SOURCE_TO_TARGET
from platform_repository import PlatformRepository

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.sql_repository import SqlRepository
from common.utils import format_bytes_pretty

logger = get_logger()


class PlatformExportEtlError(ValueError):
    """Platform Export ETL Error"""


class PlatformExportEtl(SupportsPipelineExecution):
    _config: ExportConfig

    def __init__(
        self,
        config: ExportConfig,
        platform_repository: PlatformRepository,
        analytics_bq_repository: AnalyticsBQRepository,
    ) -> None:
        self._config = config
        self._platform_repository = platform_repository
        self._analytics_bq_repository = analytics_bq_repository

        self._source = self._config.source
        self._export_type = SOURCE_TO_TARGET[self._source].export_type
        self._target = SOURCE_TO_TARGET[self._source].table_name

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution implementation."""
        return self._platform_repository

    def get_step_name(self, function_name: str) -> str:
        """SupportsPipelineExecution implementation."""
        return f"{self._target}:{function_name}"

    def run(self) -> int:
        export_status = self._analytics_bq_repository.get_export_status(table=self._target)
        condition = self._platform_repository.build_change_condition(
            export_type=self._export_type, export_status=export_status
        )
        bytes_billed = self.extract(condition=condition)
        bytes_billed += self.load()

        return bytes_billed

    @log_pipeline_execution()
    def extract(self, condition: str) -> int:
        bytes_billed = self._analytics_bq_repository.extract_from_source(
            source=self._source,
            target=self._target,
            condition=condition,
        )
        billed = format_bytes_pretty(bytes_billed)
        logger.info(f"Postgres '{self._source.full_name}' extracted: {billed=}.")

        return bytes_billed

    @log_pipeline_execution()
    def load(self) -> int:
        bytes_billed = self._analytics_bq_repository.load_from_platform(table_name=self._target)
        qa_passed = self._analytics_bq_repository.check_table(table_name=self._target)

        billed = format_bytes_pretty(bytes_billed)
        logger.info(f"BigQuery table '{self._target}' loaded: {billed=}, {qa_passed=}.")

        return bytes_billed
