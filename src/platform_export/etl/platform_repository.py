from export_shared.export_model import ExportStatus, ExportType

from common.logger import get_logger
from common.sql_repository import SqlRepository

logger = get_logger()


class PlatformRepositoryError(ValueError):
    """Platform repository related errors"""


class PlatformRepository(SqlRepository):
    """
    Source postgres platform database export repository type:
    https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/94#note_1967922911
    """

    # id is unique, so we are taking id > last_id
    INCREMENTAL_CONDITION: str = "id > {0} or updated_at >= '{1}' or deleted_at >= '{1}'"

    # (id, valid_from) is unique, so we need to take (id > last_id) or (id >= last_id and valid_from >= last_modified)
    # note valid_to `infinity` is converted into `analytics.infinity_to_timestamp(valid_to)`
    TIME_TRAVEL_CONDITION: str = "id > {0} or valid_from >= '{1}' or valid_to >= '{1}'"

    @classmethod
    def build_change_condition(cls, export_type: ExportType, export_status: ExportStatus) -> str:
        """
        Builds change condition from the given export configuration.

        Note that conditions on iso formatted datetime will be converted into:
        `created_at >= '2023-02-28T10:00:00+00'::timestamp with time zone`

        :param export_type: export type
        :param export_status: export status
        :returns: sql where condition
        """
        if export_type == ExportType.SNAPSHOT or export_status.last_id == 0:
            # Optimisation for snapshot / first snapshot in incremental exports
            return ""

        if export_type == ExportType.INCREMENTAL:
            return cls.INCREMENTAL_CONDITION.format(export_status.last_id, export_status.last_modified.isoformat())
        elif export_type == ExportType.TIME_TRAVEL:
            return cls.TIME_TRAVEL_CONDITION.format(export_status.last_id, export_status.last_modified.isoformat())
        else:
            raise PlatformRepositoryError(f"Unsupported export type: {export_type=}!")
