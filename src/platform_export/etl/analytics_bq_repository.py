from functools import cached_property
from pathlib import Path

from export_shared.export_model import ExportStatus
from google.api_core.exceptions import BadRequest

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger
from common.sql_model import SqlTable

logger = get_logger()

CURRENT_DIR = Path(__file__).resolve().parent


class AnalyticsBQRepository(BigQueryRepository):
    """
    Analytics BQ repository for loading tables exported from a Postgres platform database.
    """

    DATASET = "analytics_transformed"
    TABLE = "platform_export_status"
    EXTERNAL_QUERY = CURRENT_DIR / "external_query.sql"

    PLATFORM_EXPORT_STATUS = BQProcedure(DATASET, TABLE)

    @cached_property
    def external_connection_id(self) -> str:
        """External connection to cloud-sql"""
        return f"{self._config.project_id}.eu.platform-export"

    def get_export_status(self, table: str) -> ExportStatus:
        """
        Gets platform export status of the given `table` returning:
        - last_id: last loaded `id`
        - last_modified: last `modified_at` timestamp
        - default to ChangeTracking() default values if target table was empty and returned Nulls

        :param table: platform table being checked
        :returns: change tracking object
        """
        result = self.call_procedure(self.PLATFORM_EXPORT_STATUS, [table])

        if (rows := result.rows) and len(rows) == 1:
            row = rows[0]
            default = ExportStatus()

            return ExportStatus(
                last_id=row.last_id if row.last_id else default.last_id,
                last_modified=row.last_modified if row.last_modified else default.last_modified,
            )
        else:
            raise ValueError(
                f"'{self.PLATFORM_EXPORT_STATUS}' returned no rows! "
                f"Possibly '{table}' is not supported by platform export?!"
            )

    def extract_from_source(self, source: SqlTable, target: str, condition: str = "") -> int:
        """
        Extracts data from the source platform database into BigQuery staging table.
        :param source: Source SQL table/view being ingested
        :param target: Target BigQuery table name
        :param condition: Optional condition to filter data from source
        :returns: Total bytes billed for the query
        """
        condition = condition.strip() or "1=1"
        result = self.select_from_query_file(
            self.EXTERNAL_QUERY,
            connection_id=self.external_connection_id,
            source=source.full_name,
            target=target,
            condition=condition,
        )

        return result.total_bytes_billed

    def load_from_platform(self, table_name: str) -> int:
        """
        Loads data from the intermediate `platform_export` table into the target BigQuery table.
        :param table_name: Target BigQuery table name
        :returns: Total bytes billed for the load procedure
        """
        procedure = BQProcedure(self.DATASET, f"load_platform_{table_name}")
        result = self.call_procedure(procedure)

        return result.total_bytes_billed

    def check_table(self, table_name: str) -> bool:
        """
        Checks table for data anomalies.

        :param table_name: Table with data already loaded
        :returns: `true` if table has no anomalies, `false` otherwise
        """
        procedure = BQProcedure(self.DATASET, f"check_{table_name}")
        try:
            result = self.call_procedure(procedure)
        except BadRequest as ex:
            logger.warning(f"Quality check failed for {table_name=}: {ex.errors}")
            return False

        return result.total_rows == 0
