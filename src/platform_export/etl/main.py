from os import environ

from analytics_bq_repository import AnalyticsBQRepository
from export_etl import PlatformExportEtl
from export_shared.export_model import ExportConfig
from export_shared.export_target import PRODUCT_RANKINGS_SOURCE
from google.cloud.sql.connector import Connector
from platform_repository import PlatformRepository

from common.bq_client import BigQueryClient, BigQueryConfig
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.timing import timing
from common.utils import format_bytes_pretty

config = Config()
logger = setup_logger(config)


MAX_BYTES_BILLED = int(environ.get("MAX_BYTES_BILLED", 200 * 1024 * 1024 * 1024))  # 200 GB
TIMEOUT_IN_SEC = int(environ.get("TIMEOUT_IN_SEC", 3600))  # 1 hour

bq_client = BigQueryClient(BigQueryConfig(timeout_in_sec=TIMEOUT_IN_SEC, maximum_bytes_billed=MAX_BYTES_BILLED))
analytics_bq_repository = AnalyticsBQRepository(bq_client)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)


@timing(logger, "platform-export-etl")
def run(export_config: ExportConfig) -> None:
    """
    Job entry point, called from `platform-export` workflow.

    Note that input parameters in a cloud run job are passed via environment variables. See `ExportConfig` for details.
    """
    logger.info(
        f"logging server={pg_config.host}, "
        f"source={export_config.source.full_name}, "
        f"timestamp={export_config.timestamp}, "
    )

    with Connector() as connector:
        platform_repository = PlatformRepository(db_config=pg_config, connector=connector)
        etl = PlatformExportEtl(
            config=export_config,
            platform_repository=platform_repository,
            analytics_bq_repository=analytics_bq_repository,
        )
        total_billed = format_bytes_pretty(etl.run())
        logger.info(f"ETL for '{export_config.source.full_name}' finished: {total_billed=}.")


if __name__ == "__main__":
    if config.is_local:
        # Overwrite db_config with local docker db to test your SQL before releasing to staging
        pg_config = DatabaseConfig()
        runtime_config = ExportConfig(source=PRODUCT_RANKINGS_SOURCE)

        # Set the pipeline run environment for local execution
        pipeline_run = PipelineRun()
        SupportsPipelineExecution.set_environment(pipeline_run)
    else:
        # Cloud run job configuration should read from container env variables
        runtime_config = ExportConfig()

    # Ensure that the required environment variables are set
    SupportsPipelineExecution.supports_pipeline_logging(runtime_config, raise_error=True)
    run(export_config=runtime_config)
