import functions_framework
from cloudevents.abstract import CloudEvent
from cs_bq_repository import CategorySlugsBQRepository
from cs_etl import CategorySlugEtl
from cs_model import CategorySlugsConfig
from cs_pg_repository import CategorySlugsPGRepository
from google.cloud.sql.connector import Connector

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.timing import timing
from common.typings import HttpResponse

config = Config()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)

raw_data_lake = DataLakeRepository(config.raw_bucket)
bq_repository = CategorySlugsBQRepository()


@timing(logger, "category-slugs-mapper")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    message = get_event_message(request)
    pipeline_run = PipelineRun.from_json(message)

    cs_config = CategorySlugsConfig()
    logger.info(f"Starting load for '{cs_config.load_date.isoformat()}' with {pipeline_run=} ...")

    with Connector() as connector:
        pg_repository = CategorySlugsPGRepository(db_config=pg_config, connector=connector)
        etl = CategorySlugEtl(
            config=cs_config,
            pipeline_run=pipeline_run,
            pg_repository=pg_repository,
            bq_repository=bq_repository,
            raw_data_lake=raw_data_lake,
        )
        etl.run()

    logger.info(f"Finished load for '{cs_config.load_date.isoformat()}'.")
    return HttpResponse()


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()
    test_pipeline = PipelineRun()
    run(create_http_event(test_pipeline.to_json()))
