# Category SLUG mapping in BigQuery (for ranking)

## Specification
See description in notion [page](https://www.notion.so/refurbed/Create-Category-SLUG-mapping-in-BigQuery-159b6a8983ab80e7bee7e357bd7a956c).
Contact person: <PERSON>, <PERSON><PERSON>

## Design

### Dependencies
- `analytics` database refresh

### Source
- Postgres `analytics` database table (created using dynamic sql):
  - `export_etl.category_slugs` - categories with slug columns per country
- Postgres `analytics` database views:
  - `export_etl.v_countries` - countries
  - `export_etl.v_categories` - product categories

### Target
- DataLake GCS / BigQuery external `analytics_raw` dataset tables:
  - `analytics_raw.ranking_countries` - daily load of country codes / language configuration
  - `analytics_raw.category_slugs_mapping` - daily load of category slugs per country
- BigQuery native `analytics_transformed` dataset tables:
  - `analytics_transformed.category_slugs_mapping` - aggregated category slugs per country

## Implementation

### Timing
- ETL runs daily as part of `analytics-workflow`

### Runtime
- ETL runs as HTTP Cloud Run Function

### Extract
1. Re-create category slugs to ensure a new language is added when needed.
2. Load all category slugs into `pandas` DataFrame.
3. Load countries (country to language map) into `pandas` DataFrame.

### Transform
4. Unpivot slug columns into rows of slug per language.
5. Create `language` column from the slug column suffix.

### Load
6. Load extracted countries into GCS RAW bucket under `ranking/countries` folder.
7. Load transformed category slugs per language into GCS RAW bucket under `ranking/category_slugs_mapping` folder.
8. Merge a daily load of category slugs per language with countries
into BigQuery `analytics_transformed.category_slugs_mapping` table.
