from datetime import date

from google.api_core.exceptions import BadRequest

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger

logger = get_logger()


class CategorySlugsBQRepository(BigQueryRepository):
    """
    Abstraction over BigQuery `analytics_transformed.category_slugs_mapping` table handling.
    """

    DATASET = "analytics_transformed"
    TABLE = "category_slugs_mapping"

    TABLE_NAME = f"{DATASET}.{TABLE}"
    LOAD_PROCEDURE = BQProcedure(DATASET, "load_category_slugs_mapping")
    CHECK_PROCEDURE = BQProcedure(DATASET, "check_category_slugs_mapping")

    def load_from_raw(self, load_date: date) -> int:
        """
        Loads `analytics_transformed.category_slugs_mapping` target table:
        - from the `analytics_raw` corresponding source
        - for the given load date

        Example SQL call for load_date = `2025-01-08`:
        `call analytics_transformed.load_category_slugs_mapping (2025, 1, 8);`

        :param load_date: load date
        :returns: total bytes processed
        """
        result = self.call_procedure(self.LOAD_PROCEDURE, [load_date.year, load_date.month, load_date.day])

        return result.total_bytes_processed

    def check_table(self, load_date: date) -> bool:
        """
        Checks `category_slugs_mapping` table for data anomalies.

        :param load_date: load date
        :returns: true if table has no anomalies, false otherwise
        """
        try:
            result = self.call_procedure(self.CHECK_PROCEDURE, [load_date.year, load_date.month, load_date.day])
        except BadRequest as ex:
            logger.warning(f"Quality check failed for '{self.TABLE_NAME}': {ex.errors}")
            return False

        return result.total_rows == 0
