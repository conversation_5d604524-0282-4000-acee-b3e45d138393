from dataclasses import dataclass

from cs_bq_repository import CategorySlugsBQRepository
from cs_model import (
    CATEGORY_SLUGS_MAPPING_SCHEMA,
    CATEGORY_SLUGS_SCHEMA,
    COUNTRIES_SCHEMA,
    CategorySlugsColumns,
    CategorySlugsConfig,
)
from cs_pg_repository import CategorySlugsPGRepository
from pandas import DataFrame

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pandas_utils import DataFrameWithSchema
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_repository import SqlRepository

logger = get_logger()


@dataclass(frozen=True)
class Extracted:
    """
    Extracted raw data
    """

    category_slugs: DataFrameWithSchema = DataFrameWithSchema(DataFrame(), schema=CATEGORY_SLUGS_SCHEMA)
    countries: DataFrameWithSchema = DataFrameWithSchema(DataFrame(), schema=COUNTRIES_SCHEMA)


@dataclass(frozen=True)
class Transformed:
    """
    Transformed and extracted raw data
    """

    extracted: Extracted = Extracted()
    category_slugs_mapping: DataFrameWithSchema = DataFrameWithSchema(DataFrame(), schema=CATEGORY_SLUGS_MAPPING_SCHEMA)


class CategorySlugEtlError(ValueError):
    """Category Slug Etl Error"""


class CategorySlugEtl(BaseEtl, SupportsPipelineExecution):
    _config: CategorySlugsConfig

    def __init__(
        self,
        config: CategorySlugsConfig,
        pipeline_run: PipelineRun,
        pg_repository: CategorySlugsPGRepository,
        bq_repository: CategorySlugsBQRepository,
        raw_data_lake: DataLakeRepository,
    ):
        super().__init__(config)
        self._pipeline_run = pipeline_run
        self._pg_repository = pg_repository
        self._bq_repository = bq_repository
        self._raw_data_lake = raw_data_lake

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._pg_repository

    @log_pipeline_execution(step_name="category slugs:extract")
    def extract(self) -> Extracted:
        """
        Extracts the following data from Postgres:
        - category slugs, re-creates the table using dynamic sql
        - countries, country to language mapping

        :returns: extracted data
        """
        self._pg_repository.create_category_slugs()

        return Extracted(
            category_slugs=self._pg_repository.select_category_slugs(), countries=self._pg_repository.select_countries()
        )

    @log_pipeline_execution(step_name="category slugs:transform")
    def transform(self, extracted: Extracted) -> Transformed:
        """
        Un-pivots slug columns into rows using `pandas.melt`:
        - NEW `language` column is created from the `slug` column suffix
        - NEW `category_slug` column is created as the value of `slug_co` column

        :param extracted: extracted data
        :returns: transformed data
        """
        df = extracted.category_slugs.dataframe

        if df.empty:
            raise CategorySlugEtlError("No category slugs found! This is unexpected.")

        unpivoted = (
            # Drop ID column
            df.drop(columns=[CategorySlugsColumns.ID])
            # Unpivot columns into rows of slug per language
            .melt(
                id_vars=[
                    CategorySlugsColumns.CREATED_AT,
                    CategorySlugsColumns.UPDATED_AT,
                    CategorySlugsColumns.CATEGORY_ID,
                ],
                # language = slug, slug_en, slug_de, ...
                var_name=CategorySlugsColumns.LANGUAGE,
                # category_slug = phone, phone, telefon
                value_name=CategorySlugsColumns.CATEGORY_SLUG,
            )
        )

        # Extract language code from the `slug_CO` suffix
        unpivoted[CategorySlugsColumns.LANGUAGE] = (
            unpivoted[CategorySlugsColumns.LANGUAGE]
            .str.replace(CategorySlugsColumns.SLUG, "", case=False)
            .str.replace("_", "")
        )

        return Transformed(
            extracted=extracted,
            category_slugs_mapping=DataFrameWithSchema(dataframe=unpivoted, schema=CATEGORY_SLUGS_MAPPING_SCHEMA),
        )

    @log_pipeline_execution(step_name="category slugs:load")
    def load(self, transformed: Transformed) -> int:
        """
        Loads extracted countries and category slugs into DataLake RAW bucket:
        - countries: `analytics-pipelines-staging-raw/ranking/countries`
        - category slugs mapping: `analytics-pipelines-staging-raw/ranking/category_slugs_mapping`

        Then loads via sql MERGE target `analytics_transformed.category_slugs_mapping` BQ table.

        :param transformed: transformed DataFrame with schema
        :returns: number of loaded category_slugs_mapping rows
        """
        # Load into RAW bucket
        self._load_into_raw(transformed.extracted.countries, "countries")
        rows = self._load_into_raw(transformed.category_slugs_mapping, "category_slugs_mapping")

        # Load into BQ table
        bytes_processed = self._bq_repository.load_from_raw(load_date=self._config.load_date)
        qa_passed = self._bq_repository.check_table(load_date=self._config.load_date)
        logger.info(f"'{self._bq_repository.TABLE_NAME}' loaded with {bytes_processed=}, {qa_passed=}.")

        return rows

    def _load_into_raw(self, dfs: DataFrameWithSchema, table_name: str) -> int:
        """
        Loads given `dfs` into GCS RAW bucket under `ranking` folder daily table partition.

        :param dfs: transformed data
        :returns: number of loaded rows
        """
        bucket = self._raw_data_lake.bucket_name
        rows = len(dfs.dataframe)

        path = self._raw_data_lake.upload_table_partition(
            data_frame=dfs.dataframe_with_schema,
            table_name=table_name,
            load_date=self._config.load_date,
            root_folder=self._config.root_folder,
        )
        if path:
            logger.info(f"Loaded {rows} rows into '{table_name}' partition of {bucket=} at '{str(path)}'.")
        else:
            raise CategorySlugEtlError(f"No data in {table_name=}!")

        return rows
