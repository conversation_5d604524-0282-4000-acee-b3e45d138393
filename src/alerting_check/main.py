import json
import logging
from datetime import datetime, timezone

import functions_framework
from cloudevents.http import CloudEvent

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.logger import PASSWORD_MASKER, setup_logger
from common.timing import timing
from common.typings import HttpResponse

logger = setup_logger(Config(log_level=logging.getLevelName(logging.DEBUG)), PASSWORD_MASKER)


@timing(logger)
@functions_framework.http
def run(event: CloudEvent) -> HttpResponse:
    """
    Simple code to check GCP alerting - based on the logger entries severities.
    """

    message = get_event_message(event)
    logger.debug(f"Cloud Run Function invoked with {message=}")

    logger.debug("This is DEBUG message")
    logger.info("This is INFO message")
    logger.warning("This is WARNING message")
    logger.error("This is ERROR message")
    logger.error(
        json.dumps(
            {"foo": "bar", "some": "long error description", "baz": 42, "bar": datetime.now(tz=timezone.utc)},
            default=str,
        )
    )
    logger.critical("This is CRITICAL message")

    logger.info("This is INFO message with password=qwerty123")
    logger.info("This is INFO message with password='qwerty123'")
    logger.info("This is INFO message with password='@*&&#$%@#/")
    logger.info("This is INFO message with serialized JSON: {'password': 'foo'}")
    logger.info("This is INFO message with serialized JSON: {'rootPassword': 'foo', 'bar': 'baz'}")

    return HttpResponse()


if __name__ == "__main__":
    run(create_http_event("Cloud Run job is running..."))
