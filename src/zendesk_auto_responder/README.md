# Zendesk Auto Responder - Pipedrive + DB data to Google Sheet
- Please find all the information about this project in this Notion page:
  - [Zendesk Auto Responder Operational Guide](https://www.notion.so/refurbed/Zendesk-Auto-Responder-Operational-Guide-aa45a393327a4bc8a47e2e131cc19c7f?pvs=4)
- This code here is referring to a cloud function that is running daily at 7am UTC and is responsible for refreshing the data in the [[Automated] Zendesk Auto Responder - Supplier Info](https://docs.google.com/spreadsheets/d/1WDGYQMNHk0ehYVFIKNjUphw9Rsed7eqrWTM_rtP1THw/edit?gid=0#gid=0) Google Sheet.
- The data for the mentioned refresh are a combination of:
  - Supplier data from Pipedrive
    - Merchant name
    - Merchant ID
    - Primary email (only with `Performance` role)
    - Account Manager
    - Account Manager email
  - Supplier data from Analytics DB
    - Merchant name
    - Merchant ID
    - State (filtering out `inactive` state)
