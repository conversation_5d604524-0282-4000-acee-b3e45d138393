from pandas import Int16Dtype, StringDtype

from common.pandas_utils import BaseDataFrameSchema, DataFrameSchema


class PipedriveData(BaseDataFrameSchema):
    """
    Schema definition for Pipedrive data.

    This class defines the expected schema for the pipedrive data, including the
    data types for each column.
    """

    MERCHANT_NAME: str = "merchant_name"
    MERCHANT_ID: str = "merchant_id"
    PRIMARY_EMAIL: str = "primary_email"
    ACCOUNT_MANAGER: str = "account_manager"
    ACCOUNT_MANAGER_EMAIL: str = "account_manager_email"
    STATE: str = "state"

    SCHEMA = DataFrameSchema(
        data_types={
            MERCHANT_NAME: StringDtype(),
            MERCHANT_ID: Int16Dtype(),
            PRIMARY_EMAIL: StringDtype(),
            ACCOUNT_MANAGER: StringDtype(),
            ACCOUNT_MANAGER_EMAIL: StringDtype(),
            STATE: StringDtype(),
        },
    )


class AggregatedEmails(BaseDataFrameSchema):
    """
    Schema definition for Aggregated Emails data.

    This class defines the expected schema for aggregated email data, which results
    from the transformation process.
    """

    MERCHANT_NAME = "merchant_name"
    MERCHANT_ID = "merchant_id"
    ACCOUNT_MANAGER = "account_manager"
    EMAILS = "emails"
    STATE: str = "state"

    SCHEMA = DataFrameSchema(
        data_types={
            MERCHANT_NAME: StringDtype(),
            MERCHANT_ID: Int16Dtype(),
            ACCOUNT_MANAGER: StringDtype(),
            EMAILS: StringDtype(),
            STATE: StringDtype(),
        },
    )
