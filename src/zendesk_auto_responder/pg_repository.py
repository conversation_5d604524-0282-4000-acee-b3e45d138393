import pandas as pd
from model import PipedriveData

from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository


class ZendeskAutoResponderPgRepository(SqlRepository):
    """
    Repository for accessing Analytics Postgres DB data required by the Zendesk Auto Responder.

    This class provides a method to retrieve pipedrive data for valid merchants
    from Analytics DB.
    """

    table = SqlTable(table_name="zendesk_auto_responder_pipedrive", schema_name="analytics")

    def select_pipedrive_date(self) -> DataFrameWithSchema:
        """
        Select pipedrive data from a view in Analytics DB.

        This method selects data from a view to retrieve pipedrive data,
        returning it as a DataFrame with an associated schema.

        :return: DataFrameWithSchema containing pipedrive data.
        """
        result = self.select(table=self.table, limit=None)
        return DataFrameWithSchema(schema=PipedriveData.SCHEMA, dataframe=pd.DataFrame(result))
