import pandas as pd
from pandas import DataFrame
from pg_repository import ZendeskAutoResponderPgRepository
from transform import aggregate_merchant_emails
from zendesk_auto_responder_config import (
    ZendeskAutoResponderConfig,
    ZendeskAutoResponderError,
)

from common.base import BaseEtl
from common.google_sheet_client import GoogleSheetsClient
from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_repository import SqlRepository

logger = get_logger()


class ZendeskAutoResponderEtl(BaseEtl, SupportsPipelineExecution):
    """
    ETL process for the Zendesk Auto Responder.

    This class handles the extraction of data from BigQuery, the transformation of that data,
    and the loading of the transformed data into a Google Sheet. It ensures that the data
    meets the required criteria and handles errors related to data quality and availability.
    """

    def __init__(
        self,
        config: ZendeskAutoResponderConfig,
        pipeline_run: PipelineRun,
        pg_repo: ZendeskAutoResponderPgRepository,
        google_sheets_client: GoogleSheetsClient,
    ):
        """
        Initialize the ETL process with the necessary repository and configuration.

        :param pg_repo: Repository for accessing Analytics DB data.
        :param config: Configuration settings for the ETL process.
        :param google_sheets_client: Google Sheets client.
        """
        super().__init__(config)
        self._pipeline_run = pipeline_run
        self._pg_repo = pg_repo
        self._google_sheets_client = google_sheets_client

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._pg_repo

    @log_pipeline_execution(step_name="zendesk auto responder:extract")
    def extract(self) -> DataFrame:
        """
        Extract data from BigQuery.

        This method retrieves the necessary data from BigQuery using the provided repository.
        It returns an AutoResponderExtracted object containing the extracted data.

        :return: AutoResponderExtracted containing pipedrive data and valid merchants data.
        """
        return self._pg_repo.select_pipedrive_date().dataframe_with_schema

    @log_pipeline_execution(step_name="zendesk auto responder:transform")
    def transform(self, extracted: pd.DataFrame) -> DataFrame:
        """
        Executes the data transformation process.

        This function coordinates the aggregation of emails and the merging with valid merchants
        to produce the final dataset ready for loading.

        :param extracted: AutoResponderExtracted object containing the extracted data.
        :return: DataFrameWithSchema containing the final transformed data.
        """
        # Aggregate the relevant emails per merchant (+ account manager)
        if extracted.empty:
            raise ZendeskAutoResponderError("Input data is empty")

        return aggregate_merchant_emails(extracted).dataframe_with_schema

    @log_pipeline_execution(step_name="zendesk auto responder:load")
    def load(self, transformed: DataFrame) -> None:
        """
        Load the transformed data into a Google Sheet.

        This method writes the transformed data into the specified Google Sheet, replacing
        any existing data.

        :param transformed: Transformed data to be loaded into the Google Sheet.
        """
        if transformed.empty:
            raise ZendeskAutoResponderError("Transformed data is empty")

        self._google_sheets_client.reload_google_sheet(df=transformed)
