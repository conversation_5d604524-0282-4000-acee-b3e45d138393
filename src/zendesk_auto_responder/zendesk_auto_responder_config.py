from dataclasses import dataclass

from common.config import Config


@dataclass(frozen=True)
class ZendeskAutoResponderConfig(Config):
    """
    Configuration settings for the Zendesk Auto Responder ETL process.

    This class contains necessary configuration such as the Google Sheet ID and sheet name
    where the transformed data will be loaded.
    """

    # Google Sheet
    spreadsheet_id: str = "1WDGYQMNHk0ehYVFIKNjUphw9Rsed7eqrWTM_rtP1THw"
    production_sheet_name: str = "Sheet1"
    test_sheet_name: str = "test_sheet"
    starting_cell: str = "A1"

    @property
    def sheet_name(self) -> str:
        return self.test_sheet_name if self.is_test else self.production_sheet_name


class ZendeskAutoResponderError(ValueError):
    """Custom exception for handling errors in the Zendesk Auto Responder ETL process."""
