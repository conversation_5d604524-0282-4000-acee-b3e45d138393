import pandas as pd
from model import AggregatedEmails, PipedriveData
from zendesk_auto_responder_config import ZendeskAutoResponderError

from common.pandas_utils import DataFrameWithSchema


def aggregate_merchant_emails(pipedrive_data: pd.DataFrame) -> DataFrameWithSchema:
    """
    Aggregate emails for each merchant in the pipedrive data.

    This function groups the pipedrive data by merchant and account manager, aggregates
    their email addresses, and formats them as required for further processing.

    :param pipedrive_data: DataFrame containing pipedrive data.
    :return: DataFrameWithSchema containing aggregated emails.
    :raises ZendeskAutoResponderError: If the input data is empty or None.
    """
    if pipedrive_data.empty:
        raise ZendeskAutoResponderError("Pipedrive data is empty")

    aggregated_emails = pipedrive_data.copy()
    # Remove NATypes (due to `NAType found` error)
    aggregated_emails = aggregated_emails.fillna("")

    # Aggregate the emails based on merchant - account manager
    aggregated_emails = (
        aggregated_emails.groupby(
            [
                PipedriveData.MERCHANT_NAME,
                PipedriveData.MERCHANT_ID,
                PipedriveData.ACCOUNT_MANAGER,
                PipedriveData.ACCOUNT_MANAGER_EMAIL,
                PipedriveData.STATE,
            ]
        )
        .agg(
            primary_emails=(
                PipedriveData.PRIMARY_EMAIL,
                lambda x: ",".join([email for email in sorted(set(x)) if email]),
            )
        )
        .reset_index()
    )

    # Add the account_manager_email to the aggregated primary_emails
    # and assign the final emails list to the DataFrame
    aggregated_emails[AggregatedEmails.EMAILS] = aggregated_emails[
        ["primary_emails", PipedriveData.ACCOUNT_MANAGER_EMAIL]
    ].apply(lambda x: ",".join(x[x != ""]), axis=1)

    # Drop columns no longer needed
    aggregated_emails = aggregated_emails.drop(columns=["primary_emails", PipedriveData.ACCOUNT_MANAGER_EMAIL])

    # Order columns and sort rows (case-insensitive)
    aggregated_emails = (
        aggregated_emails[
            [
                AggregatedEmails.MERCHANT_NAME,
                AggregatedEmails.MERCHANT_ID,
                AggregatedEmails.ACCOUNT_MANAGER,
                AggregatedEmails.EMAILS,
                AggregatedEmails.STATE,
            ]
        ]
        .sort_values(by=AggregatedEmails.MERCHANT_NAME, key=lambda x: x.str.lower())
        .reset_index(drop=True)
    )

    return DataFrameWithSchema(dataframe=aggregated_emails, schema=AggregatedEmails.SCHEMA)
