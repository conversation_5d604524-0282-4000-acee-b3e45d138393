"""
SQL client is a set of low level function providing connection to sql database.
It was tested and configured towards PostgreSQL, but it's extensible to cover other engines like MySQL.

Application code should not directly interact with database using these functions,
but rather via repositories that encapsulate feature domain to ease testability.

References:
- https://cloud.google.com/sql/docs/postgres/connect-functions#connect-connectors
- https://cloud.google.com/sql/docs/postgres/connect-connectors#python
- https://github.com/GoogleCloudPlatform/cloud-sql-python-connector?tab=readme-ov-file#how-to-use-this-connector
- https://docs.sqlalchemy.org/en/20/core/connections.html#basic-usage
- https://www.cosmicpython.com/book/chapter_02_repository.html
"""

from dataclasses import dataclass
from os import environ
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from google.cloud.sql.connector import Connector
from pg8000.dbapi import Connection
from sqlalchemy import URL, Engine, create_engine


@dataclass(frozen=True)
class DatabaseConfig(DataClassJsonMixin):
    """
    https://docs.sqlalchemy.org/en/20/core/engines.html#database-urls

    :host: is either postgres or cloud-sql `project:region:instance`
    """

    host: str = environ.get("POSTGRES_HOST", "localhost")
    private_ip: str = environ.get("POSTGRES_PRIVATE_IP", "127.0.0.1")
    database: str = environ.get("POSTGRES_DB", "platform")
    username: str = environ.get("POSTGRES_USER", "postgres")
    password: str = environ.get("POSTGRES_PASSWORD", "postgres")
    backend: str = "postgresql"
    driver: str = "pg8000"
    port: int = int(environ.get("POSTGRES_PORT", 5433))
    application_name: str = "analytics-pipelines"

    @property
    def drivername(self) -> str:
        """
        Driver name in a format of `URL.create`
        """
        return f"{self.backend}+{self.driver}"

    @property
    def is_cloud_instance(self) -> bool:
        """
        True if host is a cloud sql instance, e.g. 'project:region:instance'
        """
        return self.host.count(":") == 2

    @property
    def instance_name(self) -> str:
        """
        Returns instance name if host is a cloud sql instance 'project:region:instance' otherwise returns host
        """
        return self.host.split(":")[-1] if self.is_cloud_instance else self.host

    @property
    def connection_url(self) -> str:
        """
        Connection url in PostgreSQL format
        """
        return f"postgresql://{self.username}@{self.host}:{self.port}/{self.database}"


@dataclass(frozen=True)
class ConnectionPoolConfig(DataClassJsonMixin):
    """
    Connection pooling configuration:

    https://cloud.google.com/sql/docs/postgres/connect-functions#connection-pools
    https://docs.sqlalchemy.org/en/20/core/engines.html#sqlalchemy.create_engine
    https://github.com/GoogleCloudPlatform/python-docs-samples/blob/main/cloud-sql/postgres/sqlalchemy/connect_connector.py#L64
    """

    # Pool size is the maximum number of permanent connections to keep.
    pool_size: int = 1

    # Temporarily exceeds the set pool_size if no connections are available.
    max_overflow: int = 1

    # The total number of concurrent connections for your application will be
    # a total of pool_size and max_overflow.
    # 'pool_timeout' is the maximum number of seconds to wait when retrieving a
    # new connection from the pool. After the specified amount of time, an
    # exception will be thrown.
    pool_timeout_in_sec: int = 10

    # 'pool_recycle' is the maximum number of seconds a connection can persist.
    # Connections that live longer than the specified amount of time will be
    # re-established
    pool_recycle_in_sec: int = 300  # 5 minutes


class DatabaseError(ValueError):
    """Database error"""


def get_database_engine(
    db_config: DatabaseConfig, connector: Optional[Connector] = None, pool_config: Optional[ConnectionPoolConfig] = None
) -> Engine:
    """
    Creates database engine with pooled connection for either local or cloud sql instance.

    :param db_config: database configuration
    :param connector: Cloud SQL connector (required if host is a cloud sql)
    :param pool_config: Connection pool configuration

    :returns: database engine
    """
    if db_config.is_cloud_instance and not connector:
        raise DatabaseError(f"Missing connector for Cloud SQL instance: {db_config.host}!")

    pool_config = pool_config if pool_config else ConnectionPoolConfig()

    if db_config.is_cloud_instance:
        return get_cloud_engine(connector, db_config, pool_config)
    else:
        return get_local_engine(db_config, pool_config)


def get_cloud_connection(connector: Connector, db_config: DatabaseConfig) -> Connection:
    """
    Gets database connection using Cloud SQL Connector object
    ref: https://github.com/GoogleCloudPlatform/cloud-sql-python-connector?tab=readme-ov-file#how-to-use-this-connector

    :param connector: Cloud SQL connector
    :param db_config: database configuration

    :returns: pg8000 connection
    """
    return connector.connect(
        instance_connection_string=db_config.host,
        driver=db_config.driver,
        user=db_config.username,
        password=db_config.password,
        db=db_config.database,
        application_name=db_config.application_name,
    )


def get_cloud_engine(connector: Connector, db_config: DatabaseConfig, pool_config: ConnectionPoolConfig) -> Engine:
    """
    Creates database engine with pooled connection for cloud sql using Cloud SQL connector
    ref: https://cloud.google.com/sql/docs/postgres/connect-functions#connection-pools

    :param connector: Cloud SQL connector
    :param db_config: database configuration
    :param pool_config: Connection pool configuration

    :returns: database engine
    """
    engine = create_engine(
        url=f"{db_config.drivername}://",
        creator=lambda: get_cloud_connection(connector, db_config),
        pool_size=pool_config.pool_size,
        max_overflow=pool_config.max_overflow,
        pool_timeout=pool_config.pool_timeout_in_sec,
        pool_recycle=pool_config.pool_recycle_in_sec,
        logging_name=db_config.application_name,
        connect_args={"application_name": db_config.application_name},
    )
    # https://github.com/sqlalchemy/sqlalchemy/issues/5645
    engine.dialect.description_encoding = None

    return engine


def get_local_engine(db_config: DatabaseConfig, pool_config: ConnectionPoolConfig) -> Engine:
    """
    Creates database engine with pooled connection for local/test postgres instance
    ref: https://docs.sqlalchemy.org/en/20/core/engines.html#creating-urls-programmatically

    :param db_config: database configuration
    :param pool_config: Connection pool configuration

    :returns: database engine
    """

    engine = create_engine(
        url=URL.create(
            drivername=db_config.drivername,
            username=db_config.username,
            password=db_config.password,
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
        ),
        pool_size=pool_config.pool_size,
        max_overflow=pool_config.max_overflow,
        pool_timeout=pool_config.pool_timeout_in_sec,
        pool_recycle=pool_config.pool_recycle_in_sec,
        logging_name=db_config.application_name,
        connect_args={"application_name": db_config.application_name},
    )
    # https://github.com/sqlalchemy/sqlalchemy/issues/5645
    engine.dialect.description_encoding = None

    return engine
