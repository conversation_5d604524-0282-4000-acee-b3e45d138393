from abc import ABC
from dataclasses import dataclass
from datetime import date, datetime
from functools import cached_property
from pathlib import Path
from typing import TYPE_CHECKING, Any, Optional, TypeAlias

from dataclasses_json import DataClassJsonMixin

from common.bq_client import BigQueryClient, BigQueryConfig, QueryResult
from common.utils import format_file

PartitionType: TypeAlias = date

if TYPE_CHECKING:
    from pandas import DataFrame


@dataclass(frozen=True)
class BQTable(DataClassJsonMixin):
    dataset: str
    table: str

    @cached_property
    def full_name(self) -> str:
        return f"{self.dataset}.{self.table}"


@dataclass(frozen=True)
class BQTablePartition(BQTable):
    partition: PartitionType


@dataclass(frozen=True)
class BQTablePartitionStatus(BQTablePartition):
    last_modified: datetime
    total_rows: int

    def as_table_partition(self) -> BQTablePartition:
        return BQTablePartition(self.dataset, self.table, self.partition)


@dataclass(frozen=True)
class BQProcedure(DataClassJsonMixin):
    dataset: str
    procedure: str

    @cached_property
    def full_name(self) -> str:
        return f"{self.dataset}.{self.procedure}"


class BigQueryRepository(ABC):
    """
    Abstract BigQuery repository to be used as a base class in every repository
    """

    DATASET: str = NotImplemented
    TABLE: str = NotImplemented
    PARTITION_FORMAT: str = "%Y%m%d"

    def __init__(self, bq_client: Optional[BigQueryClient] = None):
        self._client = bq_client if bq_client else BigQueryClient()

    @property
    def _config(self) -> BigQueryConfig:
        return self._client.config

    @classmethod
    def get_table(cls, table: Optional[str] = None) -> BQTable:
        """
        Gets BQ table.

        :param table: table or view name
        :returns: BQ table
        """
        table = table if table else cls.TABLE
        return BQTable(dataset=cls.DATASET, table=table)

    @classmethod
    def get_table_partition(cls, partition: PartitionType) -> BQTablePartition:
        """
        Gets given BQ table partition.

        :param partition: partition as date
        :returns: BQ table partition
        """
        return BQTablePartition(dataset=cls.DATASET, table=cls.TABLE, partition=partition)

    @classmethod
    def to_partition_id(cls, partition: PartitionType) -> str:
        """
        Convert partition `date` to string `id` format.

        :param partition: partition as date
        :returns: partition as string
        """
        return partition.strftime(cls.PARTITION_FORMAT)

    def drop_table(self, table: Optional[BQTable] = None) -> QueryResult:
        """
        Drops given table.

        :param table: BQ table if specified, otherwise default DATASET.TABLE
        :returns: query result
        """
        table = table if table else self.get_table()
        sql = f"drop table if exists {table.full_name};"
        return self._client.run_sql(sql)

    def truncate_table(self, table: Optional[BQTable] = None) -> QueryResult:
        """
        Truncates given table.

        :param table: BQ table if specified, otherwise default DATASET.TABLE
        :returns: query result
        """
        table = table if table else self.get_table()
        sql = f"truncate table {table.full_name};"
        return self._client.run_sql(sql)

    def load_table(self, data_frame: "DataFrame") -> QueryResult:
        """
        Loads dataframe into BQ table.
        :param data_frame: data frame that matches `TABLE` schema
        :returns: job results with number of loaded rows and bytes
        """
        table = self.get_table()

        job = self._client.base_client.load_table_from_dataframe(dataframe=data_frame, destination=table.full_name)
        job_result = job.result(timeout=self._client.config.timeout_in_sec)

        return QueryResult(
            job_result=job_result,
            total_rows=job_result.output_rows or 0,
            total_bytes_billed=job_result.output_bytes or 0,
            total_bytes_processed=job_result.output_bytes or 0,
        )

    def select(
        self,
        table: Optional[BQTable] = None,
        limit: Optional[int] = 1000,
        columns: Optional[list[str]] = None,
        where: Optional[str] = None,
    ) -> QueryResult:
        """
        Select all columns from the given `table` or `view` using optional `limit` and `columns` list.

        :param table: table to select from, or default table if None
        :param limit: optional limit, default to 1000
        :param columns: optional list of columns to select
        :param where: optional where clause

        :returns: query result
        """
        table = table if table else self.get_table()
        columns_sql = ", ".join(columns) if columns else "*"
        where_sql = f" where {where}" if where else ""

        sql = f"select {columns_sql} from {table.full_name}{where_sql};"
        return self._client.run_sql(sql, limit)

    def select_from_query_file(self, query_file: Path, **kwargs: Any) -> QueryResult:
        """
        Select all rows from the given `query_file` template formatted with optional arguments.

        :param query_file: sql query file with optional keyword arguments identified by braces ('{' and '}')
        :param kwargs: keyword arguments

        :returns: query result
        """
        sql = format_file(query_file, **kwargs)
        return self._client.run_sql(sql)

    def call_procedure(self, procedure: BQProcedure, args: Optional[list[str | int | bool]] = None) -> QueryResult:
        """
        Calls procedure with given (optional) arguments.

        :param procedure: BQ procedure to call
        :param args: optional arguments

        :returns: query result
        """
        if args is None:
            arg_values = ""
        else:
            arg_values = ",".join([f"'{arg}'" if (isinstance(arg, str)) else f"{arg}" for arg in args])

        sql = f"call {procedure.full_name}({arg_values});"
        return self._client.run_sql(sql)
