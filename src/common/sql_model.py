from dataclasses import dataclass
from functools import cached_property
from typing import Self

from dataclasses_json import DataClassJsonMixin


@dataclass(frozen=True)
class SqlSchema(DataClassJsonMixin):
    schema_name: str


@dataclass(frozen=True)
class SqlTable(SqlSchema):
    table_name: str

    @cached_property
    def full_name(self) -> str:
        return f"{self.schema_name}.{self.table_name}"

    @cached_property
    def select_all_from(self) -> str:
        return f"select * from {self.full_name}"

    @classmethod
    def from_full_name(cls, full_name: str) -> Self:
        if not full_name or not full_name.strip():
            raise ValueError("Empty full name!")

        names = full_name.split(".")
        if len(names) != 2 or not names[0].strip() or not names[1].strip():
            raise ValueError(f"Invalid full name: {full_name}!")

        return cls(schema_name=names[0], table_name=names[1])


@dataclass(frozen=True)
class SqlProcedure(SqlSchema):
    procedure_name: str

    @cached_property
    def full_name(self) -> str:
        return f"{self.schema_name}.{self.procedure_name}"


@dataclass(frozen=True)
class SqlFunction(SqlSchema):
    function_name: str

    @cached_property
    def full_name(self) -> str:
        return f"{self.schema_name}.{self.function_name}"
