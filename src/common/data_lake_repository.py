import tempfile
from datetime import date
from pathlib import Path
from typing import TYPE_CHECKING, Literal, Optional

from google.cloud.storage import Bucket

from common.cloud_storage_client import CloudStorageClient, CloudStorageClientConfig
from common.logger import get_logger
from common.typings import DataLakePartition, DataLakeTimePartition

if TYPE_CHECKING:
    from pandas import DataFrame

logger = get_logger()


class DataLakeRepository:
    """Abstraction over GCS data lake"""

    def __init__(self, cs_client_or_bucket: CloudStorageClient | str) -> None:
        if isinstance(cs_client_or_bucket, CloudStorageClient):
            self._cs_client = cs_client_or_bucket
        elif isinstance(cs_client_or_bucket, str):
            self._cs_client = CloudStorageClient(config=CloudStorageClientConfig(bucket_name=cs_client_or_bucket))
        else:
            raise ValueError(f"CloudStorageClient or bucket expected {cs_client_or_bucket=}!")

    @property
    def bucket(self) -> Bucket:
        """GCS bucket object"""
        return self._cs_client.bucket

    @property
    def bucket_name(self) -> str:
        """Name of the data lake bucket"""
        return self._cs_client.bucket_name

    def write_file(self, local_path: Path, partition: DataLakePartition) -> Path:
        """
        Writes file to the data lake bucket specified by CloudStorageClient
        :param local_path: path to the local file
        :param partition: a data lake path partition where the file will be placed, e.g. /some/path/year=2024/month=4/

        :returns: file location on the bucket
        """
        bucket_path = self._cs_client.upload_file(local_path, partition.path)

        return bucket_path

    def write_directory(self, local_directory: Path, partition: DataLakePartition) -> list[Path]:
        """
        Writes directory preserving its structure with all the files to the bucket specified by CloudStorageClient
        :param local_directory: local directory with all the files to be uploaded
        :param partition: a data lake path partition where the file will be placed, e.g. /some=path/

        :returns: file location on the bucket
        """
        bucket_paths = self._cs_client.upload_directory(local_directory, f"{partition.path}/")

        return bucket_paths

    def read_file(
        self,
        file_name: str,
        partition: DataLakePartition,
        local_folder: Path,
    ) -> Path:
        """Reads file from the data lake bucket specified by CloudStorageClient

        :param file_name: file name to read
        :param partition: data lake partition where file is placed
        :param local_folder: local folder to save file
        :return: local file path
        """
        bucket_path = partition.path / file_name
        local_path = self._cs_client.download_file(bucket_path, local_folder)

        return local_path

    def write_data_frame(
        self,
        data_frame: "DataFrame",
        partition_cols: list[str],
        bucket_directory: str,
        engine: Literal["auto", "pyarrow", "fastparquet"] = "pyarrow",
        compression: str | None = "snappy",
    ) -> list[Path]:
        """
        Writes data frame to the data lake bucket specified by CloudStorageClient
        :param data_frame: Pandas data frame
        :param partition_cols: a data lake partition columns that must exist in data frame schema
        :param bucket_directory: a bucket path where files will be placed, root by default
        :param engine: a Parquet library to use, defaults to 'pyarrow'
        :param compression: a compression to use, defaults to 'snappy'

        :returns: list of files location on the bucket
        """

        with tempfile.TemporaryDirectory() as temp_path:
            data_frame.to_parquet(
                temp_path,
                engine=engine,
                compression=compression,
                index=False,
                partition_cols=partition_cols,
            )
            bucket_paths = self._cs_client.upload_directory(
                local_directory=Path(temp_path), bucket_directory=str(bucket_directory)
            )

        return bucket_paths

    def upload_table_partition(
        self, data_frame: "DataFrame", table_name: str, load_date: date, root_folder: Optional[str] = None
    ) -> Optional[Path]:
        """
        Uploads given `df` into specific `base_path/table_name` folder under `load_date` partition.

        Examples:
        --------
        >>> self.upload_table_partition(data_frame, load_date=date(2024, 8, 1), table_name="nrm")
        PosixPath('nrm/year=2024/month=8/day=1/nrm_2024_08_01.parquet')

        >>> self.upload_table_partition(data_frame, load_date=date(2024, 12, 12), table_name="revenue_countries", \
            root_folder="crystal_ball")
        PosixPath('crystal_ball/revenue_countries/year=2024/month=12/day=12/revenue_countries_2024_12_12.parquet')

        :param data_frame: input dataframe
        :param table_name: table (dataset) name
        :param load_date: load date
        :param root_folder: optional root folder name, defaults to None

        :returns: file path on the bucket
        """
        if data_frame.empty:
            return None

        folder_path = f"{root_folder}/{table_name}" if root_folder else table_name
        file_name = f"{table_name}_{load_date.year}_{load_date.month:02d}_{load_date.day:02d}.parquet"

        partition = DataLakeTimePartition(
            base_path=folder_path,
            year=load_date.year,
            month=load_date.month,
            day=load_date.day,
        )

        with tempfile.TemporaryDirectory() as tmp_dir:
            file_path = Path(tmp_dir) / file_name
            data_frame.to_parquet(file_path, index=False)

            return self.write_file(file_path, partition)
