from dataclasses import dataclass
from datetime import datetime
from enum import StrEnum
from functools import cached_property
from typing import Optional

import dataclasses_json
from dataclasses_json import DataClassJsonMixin

from common.consts import GCP_REGION

dataclasses_json.cfg.global_config.encoders[datetime] = datetime.isoformat
dataclasses_json.cfg.global_config.decoders[datetime] = datetime.fromisoformat


class CamelCaseJsonMixin(DataClassJsonMixin):
    """
    To ignore None values in the serialized object.
    Ref. https://github.com/lidatong/dataclasses-json/issues/187#issuecomment-919992503
    """

    dataclass_json_config = dataclasses_json.config(
        letter_case=dataclasses_json.LetterCase.CAMEL,
        undefined=dataclasses_json.Undefined.EXCLUDE,
        exclude=lambda f: f is None,
    )["dataclasses_json"]


@dataclass(frozen=True)
class DatabaseFlag(CamelCaseJsonMixin):
    """
    name: The name of the flag
    value: The value of the flag

    Ref. https://cloud.google.com/sql/docs/postgres/create-instance#rest-v1
    """

    name: str
    value: str


@dataclass(frozen=True)
class BackupConfiguration(CamelCaseJsonMixin):
    """
    enabled:  Whether backups are enabled, defaults to False

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#insert # noqa
    """

    enabled: bool = False


@dataclass(frozen=True)
class IpConfiguration(CamelCaseJsonMixin):
    """
    private_network: Name of VPC network from which the Cloud SQL instance is accessible for private IP,
                     defaults to 'default' VPC network created in the project
    authorized_networks: The list of external networks that are allowed to connect to the instance using the public IP,
                         defaults to None
    ipv4_enabled: Whether the instance should be assigned public IP address or not, defaults to True
    enable_private_path_for_google_cloud_services: Whether services are allowed to access Cloud SQL instance
                                                   over a private IP connection, defaults to True

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#insert # noqa
    """

    private_network: str = "default"
    authorized_networks: Optional[list[str]] = None
    ipv4_enabled: bool = True
    enable_private_path_for_google_cloud_services: bool = True


@dataclass(frozen=True)
class MaintenanceWindow(CamelCaseJsonMixin):
    """
    The maintenance window of SQL instance that specifies when the instance can be restarted for maintenance purpose.
    day: Day of week (1-7), starting on Monday.
    hour: Hour of day (0-23).
    update_track: Maintenance timing setting: canary (Earlier) or stable (Later).
    kind: This is always sql#maintenanceWindow.
    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#insert # noqa
    """

    day: int
    hour: int
    update_track: str = "stable"
    kind: str = "sql#maintenanceWindow"


@dataclass(frozen=True, kw_only=True)
class InstanceCreateSettings(CamelCaseJsonMixin):
    """
    tier: Machine type for this instance,
          it should be in a format 'db-custom-<CPUs>-<Memory_in_MB>', minimum is db-custom-1-3840, which is default
    edition: Cloud SQL edition: ENTERPRISE or ENTERPRISE_PLUS, defaults to ENTERPRISE
    availability_type: Availability type: ZONAL or REGIONAL, defaults to ZONAL
    data_disk_size_gb: The size of data disk, in GB, minimum is 10 GB, which is default
    enable_google_ml_integration: Whether the instances can connect to Vertex AI, defaults to False
    backup_configuration: See CloudSQLBackupConfiguration
    ip_configuration: See CloudSQLIpConfiguration

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#insert # noqa
    """

    tier: str = "db-custom-1-3840"
    edition: str = "ENTERPRISE"
    availability_type: str = "ZONAL"
    data_disk_size_gb: int = 10
    enable_google_ml_integration: bool = False
    database_flags: Optional[list[DatabaseFlag]] = None
    backup_configuration: BackupConfiguration
    ip_configuration: Optional[IpConfiguration] = None
    maintenance_window: Optional[MaintenanceWindow] = None


@dataclass(frozen=True, kw_only=True)
class CloudSQLCreateInstanceParams(CamelCaseJsonMixin):
    """
    name: Name of the Cloud SQL instance. This does not include the project ID
    region: GCP region where the instance will be created, defaults to europe-west3
    database_version: The database engine type and version, defaults to POSTGRES_15
    root_password: Initial root password
    settings: see CloudSQLInstanceSettings

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#insert # noqa
    """

    name: str
    region: str = GCP_REGION
    database_version: str = "POSTGRES_15"
    root_password: str
    settings: InstanceCreateSettings


@dataclass(frozen=True)
class IpAddress(CamelCaseJsonMixin):
    """
    ip_address: The IP address assigned
    type: The type of this IP address, can be PRIMARY, PRIVATE or OUTGOING:
          A PRIMARY address is a public address that can accept incoming connections.
          A PRIVATE address is a private address that can accept incoming connections.
          An OUTGOING address is the source address of connections originating from the instance, if supported

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#get # noqa
    """

    ip_address: str
    type: str


@dataclass(frozen=True)
class InstanceSettings(CamelCaseJsonMixin):
    """
    activation_policy: Specifies when the instance is activated;
                       it is applicable only when the instance state is RUNNABLE. Valid values:
                       # ALWAYS: The instance is on,
                       # NEVER: The instance is off;
    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#get # noqa
    """

    activation_policy: str


@dataclass(frozen=True)
class CloudSQLInstance(CamelCaseJsonMixin):
    """
    name: Name of the Cloud SQL instance. This does not include the project ID
    database_version: The database engine type and version
    connection_name: Connection name of the Cloud SQL instance used in connection strings
    ip_addresses: The assigned IP addresses for the instance, defaults to None
    settings: See

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#get # noqa
    """

    name: str
    connection_name: str
    database_version: str
    settings: InstanceSettings
    ip_addresses: Optional[list[IpAddress]] = None

    @cached_property
    def private_ip_address(self) -> Optional[str]:
        """Returns private IP address if defined"""
        if self.ip_addresses:
            return next(iter(address.ip_address for address in self.ip_addresses if address.type == "PRIVATE"), None)

        return None


class BackupType(StrEnum):
    """
    The type of backup run.

    ON_DEMAND: Backup created manually by user
    AUTOMATED: Backup created automatically by the system
    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#get # noqa
    """

    ON_DEMAND = "ON_DEMAND"
    AUTOMATED = "AUTOMATED"


@dataclass(frozen=True)
class Backup(CamelCaseJsonMixin):
    """
    id: The identifier of the backup. Unique only for a specific Cloud SQL instance
    instance: Name of the database instance
    start_time: The time the backup operation actually started in UTC timezone in RFC 3339 format, for example 2012-11-15T16:19:00.094Z
    end_time: The time the backup operation actually started in UTC timezone in RFC 3339 format, for example 2012-11-15T16:19:00.094Z
    type: The type of this run; can be either BackupType.AUTOMATED or BackupType.ON_DEMAND.
    status: The status of backup
    description: The description of backup, only applicable to on-demand backups.
    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#get # noqa
    """

    id: str
    instance: str
    start_time: datetime
    end_time: datetime
    type: BackupType
    status: str
    description: Optional[str] = None


@dataclass(frozen=True)
class RestoreBackupContext(CamelCaseJsonMixin):
    """
    instance_id: Name of the Cloud SQL instance of the backup. This does not include the project ID.
    project: The full project ID of the source instance.
    backup_run_id: The ID of the backup run to restore from

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#restoreBackup # noqa
    """

    instance_id: str
    backup_run_id: str
    project: str


@dataclass(frozen=True)
class CloudSQLRestoreBackupParams(CamelCaseJsonMixin):
    """
    restore_backup_context: Database instance restore from backup context

    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#restoreBackup # noqa
    """

    restore_backup_context: RestoreBackupContext
