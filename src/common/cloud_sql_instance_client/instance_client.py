import dataclasses
import time
from http import HTT<PERSON>tatus
from typing import Any, Optional

from googleapiclient import discovery
from googleapiclient.errors import HttpError
from tenacity import retry

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    BackupType,
    CloudSQLCreateInstanceParams,
    CloudSQLInstance,
    CloudSQLRestoreBackupParams,
    InstanceSettings,
)
from common.logger import Config, get_logger
from common.retry_config import RETRY_CONFIG
from common.timing import timing

logger = get_logger()


@dataclasses.dataclass(frozen=True)
class CloudSQLInstanceClientConfig(Config):
    """
    - sleep_in_seconds: number of seconds to sleep while waiting for SQL operation to complete
    """

    sleep_in_seconds: int = 10


class CloudSQLInstanceClientError(ValueError):
    """Cloud SQL Instance Client error"""


class CloudSQLInstanceClient:
    """
    A custom Cloud SQL Instance client to interact with the service
    Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/index.html
    """

    def __init__(self, config: CloudSQLInstanceClientConfig):
        self._config = config

        # Construct the service object for the interacting with the Cloud SQL Admin API.
        self._service = discovery.build("sqladmin", "v1beta4")

    @timing(logger)
    def create_instance(
        self, params: CloudSQLCreateInstanceParams, wait_for_completion: bool = True
    ) -> CloudSQLInstance:
        """
        Creates empty Cloud SQL instance
        :param params: params describing new instance
        :param wait_for_completion: whether to run synchronous
        Ref.
            https://cloud.google.com/sql/docs/postgres/create-instance#rest-v1beta4
            https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1beta4/operations
        :returns: an object describing the instance
        """
        logger.info(f"Creating Cloud SQL instance with following settings: {params.to_dict()}")
        request = self._service.instances().insert(project=self._config.project_id, body=params.to_dict())
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

        return self.describe_instance(name=params.name)

    @timing(logger)
    def drop_instance(self, name: str, wait_for_completion: bool = True) -> None:
        """
        Drops Cloud SQL instance
        :param name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#delete # noqa
        """

        logger.info(f"Dropping Cloud SQL '{name}' instance...")
        request = self._service.instances().delete(project=self._config.project_id, instance=name)
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

    @timing(logger)
    def restore_backup(
        self, instance_name: str, backup_params: CloudSQLRestoreBackupParams, wait_for_completion: bool = True
    ) -> None:
        """
        Restores Cloud SQL backup into given instance
        :param instance_name: Name of the Cloud SQL instance (excluding project ID) to which backup will be restored.
        :param backup_params: backup context, see CloudSQLBackupContext
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#restoreBackup # noqa
        """

        logger.info(
            f"Restoring '{backup_params.restore_backup_context.backup_run_id}' Cloud SQL backup "
            f"into '{instance_name}' instance..."
        )
        request = self._service.instances().restoreBackup(
            project=self._config.project_id, instance=instance_name, body=backup_params.to_dict()
        )
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

    def start_instance(self, instance_name: str, wait_for_completion: bool = True) -> CloudSQLInstance:
        """
        Starts Cloud SQL instance when stopped
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        :returns: an object describing the instance
        """

        return self._start_stop_instance(instance_name, wait_for_completion, True)

    def stop_instance(self, instance_name: str, wait_for_completion: bool = True) -> CloudSQLInstance:
        """
        Stops Cloud SQL instance when started
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        :returns: an object describing the instance
        """

        return self._start_stop_instance(instance_name, wait_for_completion, False)

    def get_instances(self) -> list[CloudSQLInstance]:
        """
        Lists instances under a given project in the alphabetical order of the instance name
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#list # noqa
        :returns: an object describing the instance
        """

        request = self._service.instances().list(project=self._config.project_id)
        response = request.execute()

        instances = response.get("items", [])
        if not instances:
            logger.warning(f"No Cloud SQL instance(s) found in the '{self._config.project_id}' project!")

        return [CloudSQLInstance.from_dict(instance) for instance in instances]

    def get_backups(self, instance_name: str, project_id: Optional[str] = None, max_results: int = 10) -> list[Backup]:
        """
        Get Cloud SQL backups in the reverse chronological order of the backup initiation time
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param project_id: GCP project ID, if not provided get from default config
        :param max_results: Maximum number of backup runs per response
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#list # noqa
        :returns: list of objects describing the backup
        """
        project_id = project_id if project_id else self._config.project_id

        logger.info(f"Getting Cloud SQL '{instance_name}' instance backups from '{project_id}' GCP project...")
        request = self._service.backupRuns().list(project=project_id, instance=instance_name, maxResults=max_results)
        response = request.execute()

        backups = response.get("items", [])
        if not backups:
            logger.warning(f"No backups found for the '{instance_name}' Cloud SQL instance!")

        return [Backup.from_dict(backup) for backup in backups]

    def get_latest_backup(self, instance_name: str, project_id: Optional[str] = None) -> Optional[Backup]:
        """
        Get Cloud SQL latest successful backup if present
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param project_id: GCP project ID, if not provided get from default config
        :returns: objects describing the backup or None
        """
        project_id = project_id if project_id else self._config.project_id
        latest_backup = None

        successful_backups = (
            backup for backup in self.get_backups(instance_name, project_id) if backup.status == "SUCCESSFUL"
        )
        if not successful_backups:
            logger.warning(f"No successful backups found for the '{instance_name}' Cloud SQL instance!")
        else:
            latest_backup = sorted(successful_backups, key=lambda x: x.end_time, reverse=True)[0]

        return latest_backup

    @timing(logger)
    def create_backup(
        self, instance_name: str, description: Optional[str] = None, wait_for_completion: bool = True
    ) -> Backup:
        """
        Creates a backup of a Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param description: The description of the backup
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#insert # noqa
        :returns: an object describing the backup
        :raises CloudSQLInstanceClientError: when cannot create the backup
        """
        logger.info(f"Creating backup for Cloud SQL instance '{instance_name}'...")

        body = {"description": description} if description else {}
        request = self._service.backupRuns().insert(project=self._config.project_id, instance=instance_name, body=body)

        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

        backup_id = response.get("backupContext", {}).get("backupId")
        if not backup_id:
            raise CloudSQLInstanceClientError("Backup ID not found in the response!")

        backup = self.describe_backup(instance_name, backup_id)
        if not backup:
            raise CloudSQLInstanceClientError(f"Cannot describe already created '{backup_id}' backup ID!")

        return backup

    @timing(logger)
    def delete_backup(self, instance_name: str, backup_id: str, wait_for_completion: bool = True) -> None:
        """
        Deletes a backup of a Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param backup_id: The ID of the backup to delete
        :param wait_for_completion: whether to run synchronous
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#delete # noqa
        """
        logger.info(f"Deleting backup '{backup_id}' for Cloud SQL instance '{instance_name}'...")

        try:
            request = self._service.backupRuns().delete(
                project=self._config.project_id, instance=instance_name, id=backup_id
            )
            response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.warning(f"Backup with ID '{backup_id}' not found!")
                return
            else:
                raise

        self._wait_for_completion(response, wait_for_completion)

        logger.info(f"Backup '{backup_id}' deleted!")

    @timing(logger)
    def delete_backups(self, instance_name: str, description: str, wait_for_completion: bool = True) -> int:
        """
        Deletes ON_DEMAND backups with the given description for a Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param description: The description of the backups to delete, cannot be empy string
        :param wait_for_completion: whether to run synchronous
        :returns: The number of backups deleted
        :raises CloudSQLInstanceClientError: when a description is an empty string
        """
        logger.info(
            f"Deleting '{BackupType.ON_DEMAND}' backups "
            f"with description '{description}' for Cloud SQL instance '{instance_name}'..."
        )

        if not description:
            raise CloudSQLInstanceClientError("Cannot specify empty description!")

        backups = self.get_backups(instance_name)
        matching_backups = [
            backup for backup in backups if backup.type == BackupType.ON_DEMAND and backup.description == description
        ]

        if not matching_backups:
            logger.warning(
                f"No '{BackupType.ON_DEMAND}' backups "
                f"with description '{description}' found for instance '{instance_name}'"
            )
            return 0

        logger.info(f"Found {len(matching_backups)} matching backup(s) to delete...")

        for backup in matching_backups:
            self.delete_backup(instance_name, backup.id, wait_for_completion)

        return len(matching_backups)

    def describe_backup(self, instance_name: str, backup_id: str) -> Optional[Backup]:
        """
        Retrieves a resource containing information about a Cloud SQL backup
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param backup_id: The ID of the backup to retrieve
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.backupRuns.html#get # noqa
        :returns: an object describing the backup, None if object does not exist
        """
        try:
            request = self._service.backupRuns().get(
                project=self._config.project_id, instance=instance_name, id=backup_id
            )
            backup_response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.warning(f"Backup with ID '{backup_id}' not found!")
                return None
            else:
                raise

        return Backup.from_dict(backup_response)

    def describe_instance(self, name: str) -> Optional[CloudSQLInstance]:
        """
        Retrieves a resource containing information about a Cloud SQL instance
        :param name: Name of the Cloud SQL instance. This does not include the project ID
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#get # noqa
        :returns: an object describing the instance or None if instance does not exist
        """
        logger.info(f"Describing '{name}' Cloud SQL instance...")

        request = self._service.instances().get(project=self._config.project_id, instance=name)
        try:
            response = request.execute()
        except HttpError as http_error:
            if http_error.status_code == HTTPStatus.NOT_FOUND:
                logger.info(f"The Cloud SQL instance '{name}' does not exist.")
                return None
            else:
                raise

        return CloudSQLInstance.from_dict(response)

    def _start_stop_instance(
        self, instance_name: str, wait_for_completion: bool = True, start: bool = True
    ) -> CloudSQLInstance:
        """
        Starts/Stops Cloud SQL instance
        :param instance_name: Name of the Cloud SQL instance. This does not include the project ID
        :param wait_for_completion: whether to run synchronous
        :param start: whether to start the instance
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.instances.html#patch # noqa
        :returns: an object describing the instance
        :raises CloudSQLInstanceClientError: when SQL instance does not exist
        """
        instance = self.describe_instance(name=instance_name)

        if not instance:
            raise CloudSQLInstanceClientError(f"The Cloud SQL instance '{instance_name}' does not exist.")

        expected_policy = "NEVER" if start else "ALWAYS"
        update_policy = "ALWAYS" if start else "NEVER"

        if instance.settings.activation_policy == expected_policy:
            settings = dataclasses.replace(instance, settings=InstanceSettings(activation_policy=update_policy))
        else:
            logger.warning(f"Cloud SQL '{instance_name}' instance is not {"stopped" if start else "started"}...")
            return instance

        logger.info(f"{"Starting" if start else "Stopping"} Cloud SQL '{instance_name}' instance...")
        request = self._service.instances().patch(
            project=self._config.project_id, instance=instance_name, body=settings.to_dict()
        )
        response = request.execute()

        self._wait_for_completion(response, wait_for_completion)

        return self.describe_instance(name=instance_name)

    def _wait_for_completion(self, response: dict[str, Any], wait_for_completion: bool) -> None:
        """
        Wait for completion if SQL operation found
        :param response: dictionary containing SQL operation name
        :param wait_for_completion: whether to run synchronous
        :raises CloudSQLInstanceClientError: when SQL operation not found
        """
        if wait_for_completion:
            operation_name = response.get("name")
            if not operation_name:
                raise CloudSQLInstanceClientError("No SQL operation name returned!")

            self._wait_for_operation_to_complete(operation_name)

    @retry(reraise=True, wait=RETRY_CONFIG)
    def _wait_for_operation_to_complete(self, operation: str) -> None:
        """
        Wait for SQL operation to complete
        :param operation: SQL operation name
        :raises CloudSQLInstanceClientError: when no SQL operation status found
        Ref.
            https://cloud.google.com/sql/docs/mysql/admin-api/rest/v1beta4/operations
            https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.operations.html # noqa
        """
        completed = False

        while not completed:
            status = self._get_operation(name=operation).get("status")
            if not status:
                raise CloudSQLInstanceClientError("No operation status returned!")

            if status.lower() not in ("pending", "running"):
                completed = True

            if completed:
                logger.info(f"{operation=} completed with status '{status}'...")
            else:
                logger.info(f"Waiting for {operation=} to complete, current status is '{status}'...")
                time.sleep(self._config.sleep_in_seconds)

    def _get_operation(self, name: str) -> dict[str, Any]:
        """
        Get SQL operation for given name
        Ref. https://developers.google.com/resources/api-libraries/documentation/sqladmin/v1beta4/python/latest/sqladmin_v1beta4.operations.html#get # noqa
        """

        request = self._service.operations().get(project=self._config.project_id, operation=name)
        response = request.execute()

        return response
