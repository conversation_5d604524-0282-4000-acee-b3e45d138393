import stat
from dataclasses import dataclass
from datetime import UTC, datetime
from pathlib import Path
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from paramiko import SSHClient
from paramiko.client import AutoAddPolicy
from paramiko.sftp_attr import SFTPAttributes
from paramiko.sftp_client import SFTPClient

from common.base import ClosingContextManager
from common.logger import get_logger
from common.typings import FileInfo

logger = get_logger()


class SftpError(ValueError):
    """SFTP client error"""


@dataclass(frozen=True)
class SshConfig(DataClassJsonMixin):
    """
    SSH server connection basic configuration.
    Can be extended with further properties if required.
    Reference: https://docs.paramiko.org/en/latest/api/client.html#connect
    """

    host: str
    port: int
    user: str
    password: str
    timeout_in_sec: int = 60  # connection timeout in seconds


class SftpClient(ClosingContextManager):
    """
    Abstraction over SFTP operations.
    """

    def __init__(self, ssh_config: SshConfig | str):
        """
        Creates an SSH client for communicating with an SFTP server for the transfer of files.
        Ref.: https://docs.paramiko.org/en/latest/api/client.html#paramiko.client.SSHClient

        :param ssh_config: Config as SshConfig or JSON string
        """
        self._config = ssh_config if isinstance(ssh_config, SshConfig) else SshConfig.from_json(ssh_config)
        self._ssh_client: SSHClient = SSHClient()
        self._ssh_client.load_system_host_keys()
        self._ssh_client.set_missing_host_key_policy(AutoAddPolicy())
        self._is_connected = False

    @property
    def is_connected(self) -> bool:
        """
        Check if the ssh session is active (open).
        We cannot use `transport.is_active()` because it is unreliable:
        - [Connection reset by peer (104)](https://github.com/paramiko/paramiko/issues/2293)

        ```python
        transport = self._ssh_client.get_transport() if self._ssh_client else None
        return transport is not None and transport.is_active()
        ```
        """
        return self._is_connected

    def close(self) -> None:
        """Closes ssh session upon exit of ClosingContextManager"""
        logger.debug(f"Closing connection to '{self._config.host}'...")
        if self._ssh_client is not None:
            self._ssh_client.close()
        self._is_connected = False
        logger.info(f"Closed connection to '{self._config.host}'.")

    def connect(self) -> None:
        """
        Connect or reconnect to an SSH server and authenticate to it:
        - Check `is_connected` to ensure the ssh socket is closed before re-creating the connection;
        - This is to avoid `Socket exception: Connection reset by peer (104)` error;

        References:
        - [SSHClient.connect](https://docs.paramiko.org/en/latest/api/client.html#connect)
        - [Paramiko : Error reading SSH protocol banner](https://stackoverflow.com/questions/25609153/paramiko-error-reading-ssh-protocol-banner) # noqa
        - [SSHException: key cannot be used for signing](https://github.com/paramiko/paramiko/issues/1574)
        """
        if self.is_connected:
            self.close()

        timeout = self._config.timeout_in_sec
        logger.debug(f"Connecting to '{self._config.host}' with {timeout=}...")
        self._ssh_client.connect(
            hostname=self._config.host,
            username=self._config.user,
            password=self._config.password,
            port=self._config.port,
            timeout=timeout,
            banner_timeout=timeout,
            auth_timeout=timeout,
            channel_timeout=timeout,
            # SSHException: key cannot be used for signing
            look_for_keys=False,
            allow_agent=False,
        )
        self._is_connected = True
        logger.info(f"Connected to '{self._config.host}'.")

    def download_files(
        self, local_path: Path, remote_folder: Optional[str] = None, file_suffix: Optional[str] = None
    ) -> dict[str, FileInfo]:
        """
        Download remote files from the SFTP server remote folder to the local path.
         - The remote folder is not traversed recursively!
         - file_suffix is optional, but recommended to limit the number of downloaded files

        Reference: https://docs.paramiko.org/en/latest/api/sftp.html#get

        :param local_path: local folder path (destination)
        :param remote_folder: optional remote folder in SFTP server (source)
        :param file_suffix: optional suffix to filter the files

        :returns: dictionary of [LOCAL_PATH, REMOTE_FILE_INFO]
        """
        result = {}
        self.connect()
        with self._ssh_client.open_sftp() as sftp:
            # Remote file names without a folder prefix
            remote_files = self._list_files(sftp, remote_folder, file_suffix)

            for remote_file in remote_files:
                local_file_path = str(local_path / remote_file.path.name)

                logger.info(f"Downloading '{remote_file.path}' into '{local_file_path}'...")
                sftp.get(remotepath=str(remote_file.path), localpath=local_file_path)
                result[local_file_path] = remote_file

        logger.info(f"Downloaded {len(result)} files into '{local_path}'.")

        return result

    def copy_file(self, local_file: Path, remote_folder: Optional[str] = None) -> str:
        """
        Copy a local file (source) to the SFTP server remote folder (destination).
        The file is copied under the same name into the remote folder.

        :param local_file: local file path (source)
        :param remote_folder: remote folder in SFTP server (destination)

        :returns: remote file path being copied
        """
        if not (local_file.is_file() and local_file.exists()):
            raise SftpError(f"'{str(local_file)}' is not a file or does not exist!")

        file_name = local_file.name
        remote_path = file_name if not remote_folder else f"{remote_folder}/{file_name}"

        self.connect()
        with self._ssh_client.open_sftp() as sftp:
            sftp.put(localpath=str(local_file), remotepath=remote_path)
            logger.info(f"Copied '{local_file}' into '{remote_path}'.")

            return remote_path

    def remove_files(self, remote_file_paths: list[str]) -> None:
        """
        Removes remote files at a given path:
        - this only works on files;
        - creates a single SFTP session for all remote files for performance reasons;
        - ignores `FileNotFoundError`

        :param remote_file_paths: remote file paths
        """
        self.connect()
        with self._ssh_client.open_sftp() as sftp:
            for file_path in remote_file_paths:
                try:
                    sftp.remove(path=file_path)
                    logger.info(f"'{file_path}' removed.")
                except FileNotFoundError:
                    logger.warning(f"'{file_path}' does not exists!")

    @staticmethod
    def _list_files(sftp: SFTPClient, remote_folder: Optional[str], file_suffix: Optional[str]) -> list[FileInfo]:
        """
        List remote SFTP folder files by filtering them with optional suffix:
         - remote folder is not recursively traversed!
         - `S_ISREG` returns True if SFTP attribute mode is from a regular file

        Reference: https://stackoverflow.com/questions/16412377/distinguish-between-a-file-or-a-directory-in-paramiko

        :param sftp: paramiko sftp client
        :param remote_folder: remote folder
        :param file_suffix: optional file suffix

        :returns: list of found files as `FileInfo`
        """
        folder = remote_folder or "."
        remote_files = [
            SftpClient._get_file_info(f, remote_folder)
            for f in sftp.listdir_attr(folder)
            if stat.S_ISREG(f.st_mode or 0)
        ]

        if file_suffix:
            remote_files = [f for f in remote_files if f.path.name.endswith(file_suffix)]

        logger.info(f"Found {len(remote_files)} files in '{folder}' folder with '{file_suffix}' suffix.")

        return remote_files

    @staticmethod
    def _get_file_info(file: SFTPAttributes, remote_folder: Optional[str]) -> FileInfo:
        """
        Gets file metadata from sFTP server.
        Note that paramiko provides only `st_mtime`, which is the last modified date/time!
        Time zone (using UTC) is required for later transformations into pandas types.

        :param file: SFTP file attributes
        :param remote_folder: remote folder

        :returns: `FileInfo` instance
        """
        filename = file.filename
        created = datetime.fromtimestamp(file.st_mtime or 0, tz=UTC)
        path = f"{remote_folder}/{filename}" if remote_folder else filename

        return FileInfo(path=Path(path), created=created, size=file.st_size)
