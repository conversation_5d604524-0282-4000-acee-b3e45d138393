import re
from dataclasses import dataclass, field
from datetime import datetime
from functools import cached_property
from typing import Any, Dict, List, Optional

import dataclasses_json
from dataclasses_json import DataClassJsonMixin, LetterCase, dataclass_json

from common.consts import GCP_CONSOLE_BASE_URL
from common.utils import format_seconds

dataclasses_json.cfg.global_config.encoders[datetime] = datetime.isoformat
dataclasses_json.cfg.global_config.decoders[datetime] = datetime.fromisoformat

RESOURCE_NAME_REGEX = re.compile(
    r"projects/(?P<project>.*)/locations/(?P<region>.*)/(?P<resource_type>jobs|functions|services)/(?P<short_name>.*)"
)
CLOUD_RUN_BASE_URL = f"{GCP_CONSOLE_BASE_URL}/run/detail"
JOB_BASE_URL = f"{GCP_CONSOLE_BASE_URL}/run/jobs/details"


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class JobExecutionCondition(DataClassJsonMixin):
    """
    Cloud Run job execution condition
    type: type is used to communicate the status of the reconciliation process.
    state: State of the condition.
    message: Human readable message indicating details about the current status.
    last_transition_time: Last time the condition transitioned from one status to another.
    execution_reason: Reason for the execution condition.
    """

    type: str
    state: str
    last_transition_time: Optional[str] = None
    message: Optional[str] = None
    execution_reason: Optional[str] = None


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class CloudRunJobExecution(DataClassJsonMixin):
    name: str
    create_time: datetime
    update_time: Optional[datetime] = None
    uid: Optional[str] = None
    log_uri: Optional[str] = None
    completion_status: Optional[str] = None
    completion_time: Optional[datetime] = None
    template: Optional[dict[str, Any]] = None
    conditions: Optional[list[JobExecutionCondition]] = None

    @cached_property
    def short_name(self) -> str:
        """Execution short name"""
        return self.name.split("/")[-1]

    @cached_property
    def is_completed(self) -> bool:
        """Whether execution is completed."""
        return self.completion_time is not None

    @cached_property
    def is_succeeded(self) -> bool:
        """Whether execution is succeeded."""

        return self.conditions is not None and not any(
            condition.execution_reason == "NON_ZERO_EXIT_CODE" for condition in self.conditions
        )

    @cached_property
    def is_in_progress(self) -> bool:
        """Whether execution is in progress."""
        return not self.is_completed

    @property
    def duration(self) -> str:
        """
        Returns the duration between create_time and completion_time (or now) as a string in "hh:mm:ss" format.
        """
        end_time = self.completion_time if self.is_completed and self.completion_time else datetime.now()
        delta = end_time - self.create_time

        return format_seconds(delta.total_seconds())

    @cached_property
    def env_variables(self) -> dict[str, str]:
        """
        Job environment variables.
        If no variables are defined, returns an empty dictionary.
        """
        return {
            env.get("name"): env.get("value")
            for env in ((self.template or {}).get("containers", [{}])[0].get("env", []))
            if env.get("name") and env.get("value")
        }


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class CloudRunResource(DataClassJsonMixin):
    name: str
    create_time: datetime
    update_time: datetime
    labels: dict[str, Any]
    uid: Optional[str] = None
    url: Optional[str] = None
    latest_created_execution: Optional[CloudRunJobExecution] = None
    project_name: str = field(init=False)
    region: str = field(init=False)
    resource_type: str = field(init=False)
    short_name: str = field(init=False)

    def __post_init__(self) -> None:
        names = re.search(RESOURCE_NAME_REGEX, self.name)

        if not names:
            raise ValueError(f"Resource name '{self.name}' does not match pattern '{RESOURCE_NAME_REGEX.pattern}'!")

        self.project_name = names.group("project")
        self.region = names.group("region")
        self.resource_type = names.group("resource_type")
        self.short_name = names.group("short_name")

    @cached_property
    def base_uri(self) -> str:
        """Base uri"""
        return JOB_BASE_URL if self.resource_type == "jobs" else CLOUD_RUN_BASE_URL

    @cached_property
    def uri(self) -> str:
        """
        Resource uri calculated from the name attribute, that must match following pattern:
         - for jobs: `projects/{project}/locations/{location}/jobs/{job_name}`
         - for functions: `projects/{project}/locations/{location}/functions/{function_name}`
         - for services: `projects/{project}/locations/{location}/services/{service_name}`
        """
        return f"{self.base_uri}/{self.region}/{self.short_name}?project_name={self.project_name}"

    @cached_property
    def log_uri(self) -> str:
        """
        Base log uri for all Cloud Run services (functions, jobs and services)
        """
        return f"{self.base_uri}/{self.region}/{self.short_name}/logs?project_name={self.project_name}"


@dataclass
class CloudRunJobRunRequest(DataClassJsonMixin):
    """
    Request body for running a Cloud Run job.
    Ref: https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects.locations.jobs.html#run
    """

    environment_variables: Dict[str, str]


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class CloudRunEnvironmentVariable:
    name: str
    value: str


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class CloudRunContainerOverride:
    env: List[CloudRunEnvironmentVariable]


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class CloudRunOverrides:
    container_overrides: List[CloudRunContainerOverride]


@dataclass_json(letter_case=LetterCase.CAMEL)  # noqa
@dataclass
class CloudRunJobRequest(DataClassJsonMixin):
    overrides: Optional[CloudRunOverrides] = None
