from abc import ABC, abstractmethod
from functools import cached_property
from typing import TYPE_CHECKING, Optional, TypeAlias, Union

from googleapiclient.errors import HttpError

from common.cloud_run_client.model import CloudRunResource
from common.logger import Config, get_logger

if TYPE_CHECKING:
    from googleapiclient._apis.cloudfunctions.v2 import CloudFunctionsResource
    from googleapiclient._apis.run.v2 import CloudRunResource as CloudRunResourceV2

    ApiFunctionsResource: TypeAlias = CloudFunctionsResource.ProjectsResource.LocationsResource.FunctionsResource
    ApiJobsResource: TypeAlias = CloudRunResourceV2.ProjectsResource.LocationsResource.JobsResource

logger = get_logger()


class CloudRunClient(ABC):
    """
    Custom Cloud Run abstract base client to interact with the jobs and Gen2 functions.
    """

    def __init__(self, config: Config, api_service: Union["ApiFunctionsResource", "ApiJobsResource"]):
        self._config = config
        self._service = api_service

    @cached_property
    @abstractmethod
    def resource_api_path(self) -> str:
        pass

    @abstractmethod
    def get_resources(self) -> Union["ApiFunctionsResource", "ApiJobsResource"]:
        pass

    def describe_resource(self, resource_name: str) -> Optional[CloudRunResource]:
        """
        Describes Cloud Run resource: job or function.
        Returns none if the resource does not exist.
        :param resource_name: name of the resource
        Ref.
            https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects.locations.jobs.html#get
            https://googleapis.github.io/google-api-python-client/docs/dyn/cloudfunctions_v2.projects.locations.functions.html#get
        :returns: an object describing the resource
        """
        logger.info(f"Describing '{resource_name}' Cloud Run resource...")
        request = self.get_resources().get(name=f"{self.resource_api_path}/{resource_name}")  # noqa

        try:
            response = request.execute()
        except HttpError as http_error:
            if resp := http_error.resp:
                if resp.status == 404:
                    logger.info(f"Cloud Run resource '{resource_name}' not found.")
                    return None
                else:
                    raise
            else:
                raise

        return CloudRunResource.from_dict(response)
