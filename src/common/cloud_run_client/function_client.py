from functools import cached_property
from typing import TYPE_CHECKING, TypeAlias

if TYPE_CHECKING:
    from googleapiclient._apis.cloudfunctions.v2 import CloudFunctionsResource

    FunctionsResource: TypeAlias = CloudFunctionsResource.ProjectsResource.LocationsResource.FunctionsResource

from googleapiclient import discovery

from common.cloud_run_client.base_client import CloudRunClient
from common.config import Config
from common.logger import get_logger

logger = get_logger()


class CloudRunFunctionClient(CloudRunClient):
    """
    Custom Cloud Run Function client to interact with the service
    Ref. https://googleapis.github.io/google-api-python-client/docs/dyn/cloudfunctions_v2.html
    """

    def __init__(self, config: Config):
        self._config = config

        # Construct the service object for the interacting with the Cloud Run API.
        service = discovery.build("cloudfunctions", "v2")
        super().__init__(config, service)
        self._config = config

    @cached_property
    def resource_api_path(self) -> str:
        return f"projects/{self._config.project_id}/locations/{self._config.location}/functions"

    def get_resources(self) -> "FunctionsResource":
        return self._service.projects().locations().functions()  # noqa
