from functools import cached_property
from typing import TYPE_CHECKING, TypeAlias

from googleapiclient import discovery

from common.cloud_run_client.base_client import CloudRunClient
from common.logger import Config, get_logger

if TYPE_CHECKING:
    from googleapiclient._apis.run.v2 import CloudRunResource

    JobsResource: TypeAlias = CloudRunResource.ProjectsResource.LocationsResource.JobsResource

logger = get_logger()


class CloudRunServiceClient(CloudRunClient):
    """
    Custom Cloud Run service client to interact with the service
    Ref. https://googleapis.github.io/google-api-python-client/docs/dyn/run_v2.projects
    """

    def __init__(self, config: Config):
        # Construct the service object for the interacting with the Cloud Run API.
        service = discovery.build("run", "v2")
        super().__init__(config, service)
        self._config = config

    @cached_property
    def resource_api_path(self) -> str:
        return f"projects/{self._config.project_id}/locations/{self._config.location}/services"

    def get_resources(self) -> "JobsResource":
        return self._service.projects().locations().services()  # noqa
