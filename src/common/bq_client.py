from dataclasses import dataclass
from functools import cached_property
from typing import Op<PERSON>

from dataclasses_json import DataClassJsonMixin
from google.cloud.bigquery import Client, QueryJobConfig
from google.cloud.bigquery.table import Row, RowIterator

from common.config import DEFAULT_CONFIG
from common.consts import GCP_MULTI_REGION
from common.logger import get_logger

logger = get_logger()


@dataclass(frozen=True)
class BigQueryConfig(DataClassJsonMixin):
    """
    BigQuery Client configuration:
    - https://github.com/googleapis/python-bigquery/blob/c2496a1014a7d99e805b3d0a66e4517165bd7e01/google/cloud/bigquery/client.py#L184 # noqa
    - https://github.com/googleapis/python-bigquery/issues/970#issuecomment-921934647
    - https://cloud.google.com/bigquery/docs/best-practices-costs#restrict-bytes-billed
    """

    project_id: str = DEFAULT_CONFIG.project_id
    location: Optional[str] = GCP_MULTI_REGION  # default location for jobs / datasets / tables
    timeout_in_sec: int = 60 * 5  # wait up to 5 minutes for the underlying HTTP transport before using `retry`
    maximum_bytes_billed: Optional[int] = 30 * (1024 * 1024 * 1024)  # 30 GB


@dataclass(frozen=True)
class QueryResult(DataClassJsonMixin):
    """
    Query Job Result:
    - job_result: BQ job result if any
    - total_rows: total number of rows returned or affected by DML query
    - total_bytes_billed: total bytes billed
    - total_bytes_processed: total bytes processed

    References:
    - https://cloud.google.com/bigquery/docs/reference/rest/v2/Job#JobStatistics2.FIELDS
    - https://github.com/googleapis/python-bigquery/blob/5251b5dbb254732ea730bab664ad319bd5be47e7/google/cloud/bigquery/job/query.py#L1187 # noqa
    """

    job_result: RowIterator
    total_rows: int
    total_bytes_billed: int
    total_bytes_processed: int

    @cached_property
    def rows(self) -> list[Row]:
        return list(self.job_result)

    @cached_property
    def log_totals(self) -> str:
        return (
            f"total rows={self.total_rows}; "
            f"bytes_billed={self.total_bytes_billed}; "
            f"bytes_processed={self.total_bytes_processed}"
        )


class BigQueryClient:
    """
    Abstraction over built-in BigQuery client
    """

    def __init__(self, bq_config: Optional[BigQueryConfig] = None):
        self._config = bq_config if bq_config is not None else BigQueryConfig()
        self._job_config = QueryJobConfig(maximum_bytes_billed=self._config.maximum_bytes_billed)
        self._client = Client(project=self._config.project_id, location=self._config.location)  # noqa

    @cached_property
    def config(self) -> BigQueryConfig:
        return self._config

    @cached_property
    def base_client(self) -> Client:
        return self._client

    def run_sql(self, sql: str, max_results: Optional[int] = None) -> QueryResult:
        """
        Run a SQL query using `QueryJob` and `jobs.insert` REST API method.

        References:
        - https://cloud.google.com/bigquery/docs/reference/rest/v2/Job#jobconfigurationquery
        - https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/insert

        :param sql: SQL query to be executed
        :param max_results: The maximum total number of rows returned

        :returns: :class:`QueryResult`
        """
        timeout = self._config.timeout_in_sec
        logger.info(f"Running {sql=} ...")
        query_job = self._client.query(sql, job_config=self._job_config, timeout=timeout)

        job_result = query_job.result(timeout=timeout, max_results=max_results)
        result = QueryResult(
            job_result=job_result,
            total_rows=job_result.total_rows or job_result.num_dml_affected_rows or 0,
            total_bytes_billed=query_job.total_bytes_billed or 0,
            total_bytes_processed=query_job.total_bytes_processed or 0,
        )

        logger.debug(f"Finished {sql=} with {result.log_totals}.")
        return result
