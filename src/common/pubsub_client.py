import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from functools import cached_property
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from google.api_core.exceptions import NotFound
from google.cloud.pubsub_v1 import PublisherClient, SubscriberClient
from google.protobuf import timestamp_pb2

from common.base import ClosingContextManager
from common.logger import Config, get_logger

logger = get_logger()


@dataclass(frozen=True)
class PubSubMessage(DataClassJsonMixin):
    id: str
    acknowledge_id: str
    value: str


@dataclass(frozen=True)
class PubSubPublisherConfig(DataClassJsonMixin):
    """
    PubSub publisher configuration
    :param topic_name: name of existing PubSub topic
    :param project_id: GCP project id, defaults to the staging project
    """

    topic_name: str
    project_id: str = Config.project_id


@dataclass(frozen=True, kw_only=True)
class PubSubSubscriberConfig(PubSubPublisherConfig):
    """
    PubSub subscriber configuration
     Ref.https://cloud.google.com/pubsub/docs/replay-overview#seek_eventual_consistency
    """

    subscription_name: str
    subscription_wait_sec_after_purge: int = 10


class PubSubClient(ABC):
    @property
    @abstractmethod
    def topic_name(self) -> str:
        pass

    @property
    @abstractmethod
    def topic_path(self) -> str:
        pass


class PubSubPublisher(PubSubClient):
    """
    A custom GCP PubSub publisher client to interact with the service.
    Allows sending messages to a given topic.

    Ref.
    https://cloud.google.com/pubsub/docs/publish-message-overview
    """

    def __init__(
        self,
        config: PubSubPublisherConfig | str,
        publisher_client: Optional[PublisherClient] = None,
    ) -> None:
        """
        :param config: PubSub publisher config or topic name
        :param publisher_client: GCP PublisherClient, if not provided initialized with default settings
        """
        if isinstance(config, PubSubPublisherConfig):
            self._config = config
        elif isinstance(config, str):
            self._config = PubSubPublisherConfig(topic_name=config)
        else:
            raise ValueError(f"PubSubPublisherConfig or topic name expected {config=}!")

        self._publisher_client = publisher_client if publisher_client else PublisherClient()
        self._topic_path = self._publisher_client.topic_path(self._config.project_id, topic=self.topic_name)

    @cached_property
    def topic_name(self) -> str:
        return self._config.topic_name

    @cached_property
    def topic_path(self) -> str:
        return self._topic_path

    def publish_message(self, message: str) -> str:
        """
        Publishes message
        :param message: string representation of a massage to be published
        :returns: id of successfully published message
        """
        logger.debug(f"Publishing message to a given topic '{self.topic_name}'...")
        message_id = self._publisher_client.publish(topic=self._topic_path, data=message.encode("utf-8")).result()

        return message_id


class PubSubSubscriber(ClosingContextManager):
    """
    A custom GCP PubSub subscriber client to interact with the service.

    It works in a PULL synchronous mode and can receive messages from a given topic.
    Subscription to a given topic is then initialized automatically.
    Due to internals of the GCP Pub/Sub SubscriberClient, implements the ClosingContextManager for a graceful shutdown.

    Ref.
    https://cloud.google.com/pubsub/docs/subscription-overview
    """

    def __init__(
        self,
        config: PubSubSubscriberConfig,
        subscriber_client: Optional[SubscriberClient] = None,
    ) -> None:
        """
        :param config: PubSub subscriber config
        :param subscriber_client: GCP SubscriberClient, if not provided initialized with default settings
        """
        self._config = config

        self._subscriber_client = subscriber_client if subscriber_client else SubscriberClient()
        self._topic_path = self._subscriber_client.topic_path(self._config.project_id, self.topic_name)
        self._subscription_path = self._subscriber_client.subscription_path(
            self._config.project_id, self._config.subscription_name
        )

    @cached_property
    def topic_name(self) -> str:
        return self._config.topic_name

    @cached_property
    def topic_path(self) -> str:
        return self._topic_path

    @cached_property
    def subscription_path(self) -> str:
        return self._subscription_path

    def receive_messages(
        self,
        max_messages: int = 10,
    ) -> list[PubSubMessage]:
        """
        Receives messages via synchronous PULL
        Ref.
            https://cloud.google.com/pubsub/docs/pull#synchronous_pull_mode
            https://cloud.google.com/pubsub/docs/samples/pubsub-subscriber-sync-pull

        :param max_messages: the maximum number of messages to return in one request
        :returns: received messages
        """
        logger.debug(f"Receiving messages from a topic '{self.topic_name}'...")
        response = self._subscriber_client.pull(subscription=self.subscription_path, max_messages=max_messages)

        messages = []
        for received_message in response.received_messages:
            message = PubSubMessage(
                id=received_message.message.message_id,
                acknowledge_id=received_message.ack_id,
                value=received_message.message.data.decode("utf-8"),
            )
            messages.append(message)

        return messages

    def acknowledge_messages(self, messages: list[PubSubMessage]) -> None:
        """Acknowledge PubSub messages

        :param messages: list of messages to acknowledge
        """
        # Low-level synchronous API requires explicit acknowledgment of received messages.
        # https://cloud.google.com/pubsub/docs/pull#low_client_library
        if messages:
            logger.debug(f"Acknowledging processed messages on a subscription '{self.subscription_path}'...")
            self._subscriber_client.acknowledge(
                subscription=self.subscription_path,
                ack_ids=[message.acknowledge_id for message in messages],
            )

    def subscribe(self) -> None:
        """
        Initializes subscription.
        Ref. https://cloud.google.com/pubsub/docs/subscriber
        """
        logger.debug(f"Subscribing to '{self.subscription_path}'...")
        try:
            self._subscriber_client.get_subscription(subscription=self.subscription_path)
        except NotFound:
            self._subscriber_client.create_subscription(name=self.subscription_path, topic=self._topic_path)

    def purge_subscription(self) -> None:
        """
        Purges existing subscription.
        There is no PURGE equivalent in Python SDK that is available on GCP web console.
        To achieve it, seek method is used.
        Ref.
            - https://cloud.google.com/pubsub/docs/replay-overview#seek_to_a_time
            - https://stackoverflow.com/a/71063834
        Pay special attention how to pass protobuf Timestamp: https://stackoverflow.com/a/74958327
        """

        timestamp = timestamp_pb2.Timestamp()
        # To purge all messages, you can seek to a time in the future.
        timestamp.FromDatetime(datetime.max)

        logger.debug(f"Purging '{self.subscription_path}' subscription for following topic `{self.topic_name}`...")
        self._subscriber_client.seek(request={"subscription": self.subscription_path, "time": timestamp})

        # Eventual consistency of seek operations:
        # https://cloud.google.com/pubsub/docs/replay-overview#seek_eventual_consistency
        # No better option than wait
        time.sleep(self._config.subscription_wait_sec_after_purge)

    def close(self) -> None:
        """
        Closes subscriber client to release socket resources.
        Ref. https://cloud.google.com/pubsub/docs/pull
        """
        if not self._subscriber_client.closed:
            logger.debug("Closing subscriber...")
            self._subscriber_client.close()
