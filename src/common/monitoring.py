import os
from time import perf_counter
from typing import Any, Callable

import psutil

from common.logger import get_logger
from common.timing import format_seconds

logger = get_logger()


def profile(func: Callable[[Any], Any]) -> Any:
    """
    Memory consumption decorator that logs the info to the console
    :param func: any callable

    :returns: return from callable
    """

    def process_memory() -> Any:
        process = psutil.Process(os.getpid())
        mem_info = process.memory_info()
        return mem_info.rss

    def wrapper(*args: Any, **kwargs: Any) -> Any:
        mem_before = process_memory()
        start = perf_counter()
        result = func(*args, **kwargs)
        elapsed_time = perf_counter() - start
        mem_after = process_memory()
        consumed = round((mem_after - mem_before) / (1024.0**2), 2)
        took = format_seconds(elapsed_time)
        logger.info(f"'{func.__name__}' took {took} and consumed {consumed} MB(s).")

        return result

    return wrapper
