from typing import Optional

from google.cloud.logging_v2 import Client

from common.cloud_logging_client.model import Log<PERSON><PERSON><PERSON>, LogFilter
from common.logger import Config, get_logger

logger = get_logger()


class CloudLoggingClient:
    """
    Custom Cloud Logging client to interact with the service
    Ref. https://cloud.google.com/python/docs/reference/logging/latest
    """

    def __init__(self, config: Config, client: Optional[Client] = None):
        self._config = config
        self._client = client if client else Client(project=self._config.project_id)

    def list_log_entries(self, log_filter: LogFilter) -> list[LogEntry]:
        """
        List Cloud Logging entries, results are sorted by timestamp, descending and limited by `log_filter.max_results`.
        Ref. https://cloud.google.com/python/docs/reference/logging/latest/entries
        :param log_filter: logs filter
        :returns: list of log entries
        """
        logger.info(f"Listing top {log_filter.max_results} log entries...")
        logger.debug(f"Log entries filer: '{log_filter.expression}'...")
        logs = self._client.list_entries(
            filter_=log_filter.expression, order_by="timestamp desc", max_results=log_filter.max_results
        )

        return [LogEntry(timestamp=log.timestamp, payload=log.payload) for log in logs]
