from dataclasses import dataclass
from datetime import datetime
from enum import Str<PERSON>num
from typing import Any, Optional

from dataclasses_json import DataClassJsonMixin

from common.consts import GCP_REGION


class CloudRunResourceType(StrEnum):
    JOB = "cloud_run_job"
    REVISION = "cloud_run_revision"


@dataclass(frozen=True)
class LogFilter(DataClassJsonMixin):
    resource_type: CloudRunResourceType
    region: str = GCP_REGION
    severity: str = "INFO"
    max_results: int = 100

    @property
    def expression(self) -> str:
        """
        Cloud Logging filter expression
        Ref. https://cloud.google.com/logging/docs/view/advanced_filters
        """
        return "\n".join(
            [
                f"resource.type={self.resource_type}",
                f"resource.labels.location={self.region}",
                f"severity={self.severity.upper()}",
            ]
        )


@dataclass(frozen=True, kw_only=True)
class CloudRunJobLogFilter(LogFilter):
    resource_type: CloudRunResourceType = CloudRunResourceType.JOB
    job_name: str
    job_execution_name: Optional[str] = None

    @property
    def expression(self) -> str:
        """
        Cloud Logging filter expression for Cloud Run Job
        Ref. https://cloud.google.com/logging/docs/view/advanced_filters
        """
        return "\n".join(
            [
                f"{super().expression}",
                f"resource.labels.job_name={self.job_name}",
                (
                    f'labels."run.googleapis.com/execution_name"={self.job_execution_name}'
                    if self.job_execution_name
                    else ""
                ),
            ]
        )


@dataclass(frozen=True, kw_only=True)
class CloudRunFunctionLogFilter(LogFilter):
    resource_type: CloudRunResourceType = CloudRunResourceType.REVISION
    function_name: str

    @property
    def expression(self) -> str:
        """
        Cloud Logging filter expression for Cloud Run Function
        Ref. https://cloud.google.com/logging/docs/view/advanced_filters
        """
        return "\n".join(
            [
                f"{super().expression}",
                f"resource.labels.service_name={self.function_name}",
            ]
        )


@dataclass(frozen=True)
class LogEntry(DataClassJsonMixin):
    timestamp: datetime
    payload: str | dict[str, Any]
