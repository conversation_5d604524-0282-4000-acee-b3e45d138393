from abc import ABC, abstractmethod
from types import TracebackType
from typing import Generic, Optional, Self, Type, TypeVar

from common.config import Config

TExtracted = TypeVar("TExtracted")
TTransformed = TypeVar("TTransformed")
TLoaded = TypeVar("TLoaded")


class BaseEtl(ABC, Generic[TExtracted, TTransformed, TLoaded]):
    """
    Base abstract ETL type to be implemented by each module
    """

    def __init__(self, config: Config):
        self._config = config

    @property
    def name(self) -> str:
        """
        Name of the instance used for saving results (can be overriden if needed).
        """
        return self.__class__.__name__

    def run(self) -> TLoaded:
        extracted: TExtracted = self.extract()
        transformed: TTransformed = self.transform(extracted)
        result: TLoaded = self.load(transformed)

        return result

    @abstractmethod
    def extract(self) -> TExtracted:
        """
        Extract data from the source.

        :return: TExtracted
        """

    @abstractmethod
    def transform(self, extracted: TExtracted) -> TTransformed:
        """
        Transform extracted data into target format.

        :param extracted: TExtracted
        :return: TTransformed
        """

    @abstractmethod
    def load(self, transformed: TTransformed) -> TLoaded:
        """
        Load transformed data into target database.

        :param transformed: TTransformed
        :return: TLoaded
        """


class Closable(ABC):
    @abstractmethod
    def close(self) -> None:
        """
        Closes resources upon exit
        https://docs.python.org/3/library/contextlib.html#contextlib.closing
        """


class ClosingContextManager(Closable, ABC):
    """
    Required for context managers
    Ref. https://book.pythontips.com/en/latest/context_managers.html#implementing-a-context-manager-as-a-class
    """

    def __enter__(self) -> Self:
        return self

    def __exit__(
        self,
        exception_type: Optional[Type[BaseException]],
        value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        """
        Types based on: https://stackoverflow.com/a/71171297
        """
        self.close()
