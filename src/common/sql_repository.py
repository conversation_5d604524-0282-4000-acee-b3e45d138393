import contextlib
from functools import cached_property
from pathlib import Path
from typing import TYPE_CHECKING, Any, Iterator, Optional

from google.cloud.sql.connector import Connector
from sqlalchemy import Connection, CursorResult, Engine, Row, text

from common.logger import get_logger
from common.sql_client import ConnectionPoolConfig, DatabaseConfig, get_database_engine
from common.sql_model import SqlFunction, SqlProcedure, SqlTable
from common.utils import escape_sql, format_file

logger = get_logger()

if TYPE_CHECKING:
    from pandas import DataFrame

    from common.pandas_utils import DataFrameSchema


class SqlRepository:
    """
    Abstraction over cloud sql statements.
    """

    def __init__(
        self,
        db_config: DatabaseConfig | str,
        connector: Optional[Connector] = None,
        pool_config: Optional[ConnectionPoolConfig] = None,
    ):
        """
        Initializes SqlRepository from the given database config with optional connector and pool config.

        :param db_config: Database configuration as DatabaseConfig or JSON string
        :param connector: Cloud SQL connector (mandatory for GCP hosted SQL)
        :param pool_config: Connection pool configuration
        """
        db_config = db_config if isinstance(db_config, DatabaseConfig) else DatabaseConfig.from_json(db_config)
        engine = get_database_engine(db_config=db_config, connector=connector, pool_config=pool_config)
        logger.info(f"Created {self.__class__.__name__} for '{db_config.host}'")

        self._db_config = db_config
        self._engine = engine

    @cached_property
    def db_config(self) -> DatabaseConfig:
        """Returns database configuration"""
        return self._db_config

    @cached_property
    def engine(self) -> Engine:
        """Returns sqlalchemy engine"""
        return self._engine

    @contextlib.contextmanager
    def begin_transaction(self, stream_results: bool = False, max_row_buffer: int = 1000) -> Iterator[Connection]:
        """
        Returns a context manager delivering a :class:`Connection` with a transaction established.

        The `Connection` class exposes additional methods:
            - commit(),
            - rollback()
        See more here: https://docs.sqlalchemy.org/en/20/core/connections.html#sqlalchemy.engine.RootTransaction

        :param stream_results: whether to use server side cursors, defaults to False
        :param max_row_buffer: maximum buffer size to use when the `stream_results` is used, defaults to 1000
        :returns: connection with started transaction
        """
        with self.engine.connect().execution_options(
            stream_results=stream_results, max_row_buffer=max_row_buffer
        ) as connection:
            with connection.begin():
                yield connection

    def call(
        self, procedure: SqlProcedure, arguments: Optional[str] = None, transaction: Optional[Connection] = None
    ) -> CursorResult:
        """
        Call a given `SqlProcedure` class with optional `transaction`.
        Note: parameter handling is not implemented yet!

        :param procedure: Sql procedure to run
        :param arguments: optional sql procedure arguments without `()`, i.e. `param_a=>'foo', param_b=>'bar'`
        :param transaction: optional transaction, if not provided new transaction is started

        :returns: Cursor result if any
        """
        pg_arguments = "()"
        if arguments:
            pg_arguments = f"({arguments})"

        call_sql = text(f"call {procedure.full_name}{pg_arguments};")
        return self._run_sql(call_sql, transaction)

    def insert(
        self,
        df: "DataFrame",
        table: SqlTable,
        truncate: bool = False,
        transaction: Optional[Connection] = None,
        dtype: Optional[dict[str, Any]] = None,
        use_batch: bool = False,
        batch_size: Optional[int] = None,
    ) -> int:
        """
        Insert given data frame into sql table. Optionally truncate the table before insert.

        References:
        - https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.DataFrame.to_sql.html
        - https://stackoverflow.com/questions/35202981/optimal-chunksize-parameter-in-pandas-dataframe-to-sql

        :param df: data frame to be inserted
        :param table: sql table definition
        :param truncate: True truncates the table before the insert; False otherwise
        :param transaction: optional transaction, if not provided new transaction is started
        :param dtype: optional dictionary with type conversion, by default empty, example: {"col": JSONB}
        :param use_batch: if True pass multiple values in a single `insert`, otherwise use one insert per row (default)
        :param batch_size: number of rows to be written at once, if `None` all rows are written at once (default)

        :returns: Number of rows affected
        """
        if transaction:
            return self._append_to_table(transaction, df, table, truncate, dtype, use_batch, batch_size)

        with self.begin_transaction() as transaction:
            return self._append_to_table(transaction, df, table, truncate, dtype, use_batch, batch_size)

    def select(
        self,
        table: SqlTable,
        transaction: Optional[Connection] = None,
        limit: Optional[int] = 100,
        columns: Optional[list[str]] = None,
    ) -> list[Row]:
        """
        Select all columns from the given :class:`SqlTable` using optional `limit`.

        :param table: table to select from
        :param transaction: optional transaction, if not provided new transaction is started
        :param limit: optional limit, default to 100
        :param columns: optional list of columns to select

        :returns: list of tuples
        """
        limit_sql = f" limit {limit}" if limit else ""
        columns_sql = ", ".join(columns) if columns else "*"
        select_sql = text(f"select {columns_sql} from {table.full_name}{limit_sql};")

        result = self._run_sql(select_sql, transaction)

        return list(result)

    def select_to_df(
        self, sql: text, schema: Optional["DataFrameSchema"] = None, transaction: Optional[Connection] = None
    ) -> "DataFrame":
        """
        Execute given `sql` and returns pandas DataFrame.

        :param sql: select sql or SqlTable to select from
        :param schema: optional DataFrame schema
        :param transaction: optional transaction, if not provided new transaction is started

        :returns: DataFrame
        """
        from pandas import read_sql_query

        if isinstance(sql, SqlTable):
            sql = sql.select_all_from

        datetime_columns = schema.datetime_columns if schema else None
        dtypes = schema.dtypes if schema else None

        logger.info(f"select_to_df(sql='{str(sql)}') ...")

        if transaction:
            return read_sql_query(sql=sql, con=transaction, parse_dates=datetime_columns, dtype=dtypes)
        else:
            with self.begin_transaction() as connection:
                return read_sql_query(sql=sql, con=connection, parse_dates=datetime_columns, dtype=dtypes)

    @staticmethod
    def select_in_chunks(
        sql: text,
        transaction: Connection,
        chunk_size: int = 1000,
        schema: Optional["DataFrameSchema"] = None,
    ) -> Iterator["DataFrame"]:
        """
        Execute given `sql` and returns Iterator of DataFrames.

        :param sql: select sql or SqlTable to select from
        :param transaction: Open transaction, use `stream_results=True` for server side chunking
        :param chunk_size: Number of rows in each chunk, defaults to 1000
        Ref.: https://pythonspeed.com/articles/pandas-sql-chunking/
        :param schema: optional DataFrame schema

        :returns: Iterator with results
        """
        from pandas import read_sql_query

        if isinstance(sql, SqlTable):
            sql = sql.select_all_from

        date_columns = schema.datetime_columns if schema else None
        dtypes = schema.dtypes if schema else None
        logger.debug(f"read_sql_query(sql='{str(sql)}', {chunk_size=}) ...")

        for df in read_sql_query(
            sql=sql, con=transaction, parse_dates=date_columns, dtype=dtypes, chunksize=chunk_size
        ):
            yield df

    def select_from_function(
        self, function: SqlFunction, args: Optional[list[str | int]] = None, transaction: Optional[Connection] = None
    ) -> CursorResult:
        """
        Call a given ` SqlFunction ` class with optional `transaction`.

        :param function: SQL function to run
        :param args: optional function arguments as a list of string values
        :param transaction: optional transaction, if not provided new transaction is started

        :returns: Cursor result if any
        """
        arg_values = "" if args is None else ",".join([f"'{escape_sql(arg)}'" for arg in args])
        select_sql = text(f"select * from {function.full_name}({arg_values});")
        return self._run_sql(select_sql, transaction)

    def select_from_query_file(self, query_file: Path, **kwargs: Any) -> CursorResult:
        """
        Select all rows from the given `query_file` template formatted with optional arguments.

        :param query_file: SQL query file with optional keyword arguments identified by braces ('{' and '}')
        :param kwargs: keyword arguments

        :returns: Cursor result if any
        """
        sql = format_file(query_file, **kwargs)
        return self._run_sql(text(sql))

    def truncate_table(
        self,
        table: SqlTable,
        restart_identity: bool = True,
        cascade: bool = True,
        transaction: Optional[Connection] = None,
    ) -> None:
        """
        Truncates given `table`:
        Ref.: https://www.postgresql.org/docs/12/sql-truncate.html

        :param table: table to be truncated
        :param restart_identity: if `True` (default) restarts sequences owned by columns of the truncated table(s)
        :param cascade: if `True` (default) truncates all tables that have foreign-key references
        :param transaction: optional transaction, if not provided new transaction is started
        """
        restart_sql = " restart identity" if restart_identity else ""
        cascade_sql = " cascade" if cascade else ""
        truncate_sql = text(f"truncate table {table.full_name}{restart_sql}{cascade_sql};")
        self._run_sql(truncate_sql, transaction)

    def create_database(self, name: str) -> None:
        """
        Creates database if not exists.
        Must run in `AUTOCOMMIT` isolation_level
        to prevent error 'CREATE DATABASE cannot run inside a transaction block'

        :param name: name of database to be created
        """
        exists = self._run_sql(text(f"select datname from pg_database where datname = '{name}';")).fetchone()

        if not exists:
            logger.info(f"Creating database '{name}'...")
            self._run_autocommit_sql(text(f"create database {name};"))

    def drop_database(self, name: str) -> None:
        """
        Drops database if exists.
        Must run in `AUTOCOMMIT` isolation_level
        to prevent error 'DROP DATABASE cannot run inside a transaction block'

        :param name: name of database to be dropped
        """
        logger.info(f"Dropping database '{name}'...")
        self._run_autocommit_sql(text(f"drop database if exists {name};"))

    def vacuum_database(self, do_analyze: bool = True) -> None:
        """
        Vacuums database.
        :param do_analyze: Whether to run analyze extra step.
        """
        command = "vacuum;"
        if do_analyze:
            command = command.replace(";", " analyze;")
        self._run_autocommit_sql(text(command))

    def drop_table(self, table: str, cascade: bool = False) -> None:
        """
        Drops given table.
        :param table: table to be dropped
        :param cascade: whether to drop all dependent objects
        """
        sql = f"drop table if exists {table};"
        if cascade:
            sql = sql.replace(";", " cascade;")

        logger.info(f"Dropping {table=}...")
        self._run_sql(text(sql))

    def create_schema(self, schema: str) -> None:
        """
        Creates database schema if not exists.
        """
        logger.info(f"Creating {schema=}...")
        self._run_sql(text(f"create schema if not exists {schema};"))

    def drop_schema(self, schema: str, cascade: bool = False) -> None:
        """
        Drops database schema if exists
        :param schema: schema to be dropped
        :param cascade: whether to drop all dependent objects
        """
        sql = f"drop schema if exists {schema};"
        if cascade:
            sql = sql.replace(";", " cascade;")

        logger.info(f"Dropping {schema=}...")
        self._run_sql(text(sql))

    def rename_schema(self, from_schema: str, to_schema: str) -> None:
        """
        Renames database schema
        :param from_schema: schema to be renamed
        :param to_schema: new schema name
        """
        logger.info(f"Renaming database schema '{from_schema}' to '{to_schema}'...")
        self._run_sql(text(f"alter schema {from_schema} rename to {to_schema};"))

    def _run_sql(self, sql: text, transaction: Optional[Connection] = None) -> CursorResult:
        logger.debug(sql)
        if transaction:
            return transaction.execute(sql)
        else:
            with self.begin_transaction() as connection:
                return connection.execute(sql)

    def _run_autocommit_sql(self, sql: text) -> None:
        """To prevent errors, 'cannot run inside a transaction block'"""
        logger.debug(sql)
        autocommit_engine = self.engine.execution_options(isolation_level="AUTOCOMMIT")
        with autocommit_engine.connect() as connection:
            connection.execute(sql)

    def _append_to_table(
        self,
        transaction: Connection,
        df: "DataFrame",
        table: SqlTable,
        truncate: bool,
        dtype: Optional[dict[str, Any]],
        use_batch: bool,
        batch_size: Optional[int],
    ) -> int:
        if truncate:
            self.truncate_table(table=table, transaction=transaction)

        method = "multi" if use_batch is True else None
        logger.debug(f"Inserting {len(df)} rows into '{table.full_name}' with {method=} and {batch_size=}...")
        result = (
            df.to_sql(
                name=table.table_name,
                schema=table.schema_name,
                if_exists="append",
                con=transaction,
                index=False,
                dtype=dtype,
                method=method,
                chunksize=batch_size,
            )
            or 0
        )
        logger.info(f"Inserted {result} rows into '{table.full_name}'.")

        return result
