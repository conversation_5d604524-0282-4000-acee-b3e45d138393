import logging
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
from functools import wraps
from time import perf_counter, sleep
from typing import Any, Callable, Optional

from common.utils import format_seconds


def timing(
    logger: logging.Logger,
    message: Optional[str] = None,
    level: int = logging.INFO,
) -> Callable[[Any], Any]:
    """
    Timing decorator
    Ref. https://dev.to/kcdchennai/python-decorator-to-measure-execution-time-54hk
    """

    def decorator(func: Callable[[Any], Any]) -> Callable[[Any], Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> None:
            def format_message(msg: str, elapsed: float) -> str:
                return f"'{msg}' took {format_seconds(elapsed)}"

            start = perf_counter()
            msg = message if message is not None else func.__name__
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                elapsed_time = perf_counter() - start
                text = format_message(msg, elapsed_time)
                logger.log(level, text)

        return wrapper

    return decorator


def background_task(
    logger: logging.Logger, name: Optional[str] = None, level: int = logging.INFO, sleep_seconds: int = 5
) -> Callable[[Any], Any]:
    """
    Background task decorator
    """

    def decorator(func: Callable[[Any], Any]) -> Callable[[Any], Any]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> None:
            msg = name if name else func.__name__

            start = perf_counter()

            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(func, *args, **kwargs)
                logger.log(level, f"{msg} started...")

                while future.running():
                    elapsed_time = perf_counter() - start
                    text = f"{msg} is running {format_seconds(elapsed_time)}..."
                    logger.log(level, text)
                    sleep(sleep_seconds)

                elapsed_time = perf_counter() - start
                logger.log(level, f"{msg} finished in {format_seconds(elapsed_time)}.")

                return future.result()

        return wrapper

    return decorator


@dataclass(frozen=True)
class ExecutionResult[T]:
    """
    Function execution results:
    - timing: execution duration in seconds
    - result: return value from the measured function
    """

    timing: float
    result: T


def measure_execution[T](func: Callable[..., T], *args: Any, **kwargs: Any) -> ExecutionResult[T]:
    """
    Measures function execution time and captures its result.
    :param func: function to measure
    :param args: positional arguments for the function
    :param kwargs: keyword arguments for the function
    :returns: timing result containing execution duration and function's return value
    """
    start = perf_counter()
    func_result = func(*args, **kwargs)
    elapsed_time = round(perf_counter() - start, 2)

    return ExecutionResult(timing=elapsed_time, result=func_result)
