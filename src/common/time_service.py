from datetime import UTC, date, datetime, timedelta
from functools import cached_property
from typing import Iterator, Optional

from common.typings import Timestamp, TimestampInSec


class TimeService:
    """
    Abstraction over time that eases unit testing.
    """

    def __init__(self, now: Optional[datetime] = None):
        """
        Initialize new TimeService with the given current date/time (if provided).

        Do not use default for now!
        https://stackoverflow.com/questions/11050714/python-default-parameter-value-using-datetime

        :param now: given date/time, default to now if not provided
        """
        self._now = now if now else datetime.now(UTC)

    @cached_property
    def now(self) -> datetime:
        """
        Gets now datetime
        """
        return self._now

    @cached_property
    def last_hour(self) -> datetime:
        """
        Last full hour e.g., 11:39 -> 11:00
        """
        return self.now.replace(
            minute=0,
            second=0,
            microsecond=0,
        )

    @cached_property
    def today(self) -> date:
        """
        Gets today date
        """
        return self.now.date()

    @cached_property
    def yesterday(self) -> date:
        """
        Gets yesterday date
        """
        return self.days_ago(days=1)

    @cached_property
    def timestamp(self) -> Timestamp:
        """
        Gets timestamp from current date/time as float
        """
        return self.now.timestamp()

    @cached_property
    def timestamp_in_sec(self) -> TimestampInSec:
        """
        Gets timestamp from current date/time as float
        """
        return int(self.timestamp)

    @cached_property
    def day_of_week(self) -> str:
        """
        Gets the day of the week as a string (e.g., 'Monday', 'Tuesday', etc.)
        """
        return self.now.strftime("%A")

    @cached_property
    def day_of_week_number(self) -> int:
        """
        Gets the day of the week as a number (0-6, where 0 is Monday in ISO format)
        """
        return self.now.weekday()  # Monday is 0, Sunday is 6 (ISO)

    def days_ago(self, days: int) -> date:
        """
        Gets date from given days ago.

        :param days: number of days from today
        :returns: date
        """
        delta = timedelta(days=days)
        return self.today - delta

    def last_days(self, days: int, start: int = 0) -> Iterator[date]:
        """
        Gets last `days` from `start` until `days` ago.

        :param days: number of days from today
        :param start: start date; default to 0 (today)
        :returns: iterator of dates
        """
        for day in range(start, days):
            yield self.days_ago(day)

    @staticmethod
    def days_range(start: date, end: date, step: int = 1) -> Iterator[date]:
        """
        Gets all days from `start` to `end` (inclusive) with the given `step` is days.

        :param start: start date
        :param end: end date
        :param step: step in days
        :returns: iterator of dates
        """
        current = start
        while current <= end:
            yield current
            current += timedelta(days=step)
