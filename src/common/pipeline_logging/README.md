# Pipeline monitoring

## Logging ETL execution with `log_pipeline_execution`

The `log_pipeline_execution` decorator allows you to automatically log the execution time,
status, and errors of your ETL pipeline methods into the `log.pipeline_execution` and `log.pipeline_execution_step` tables.

This is useful for monitoring, auditing, and reporting on your data pipelines steps.

### What gets logged
- Start and end times for each decorated method
- Success/failure status
- Error stack trace if an exception occurs
- All data is stored in the `log.pipeline_execution` and `log.pipeline_execution_step` tables

### How to use it inside in-process component (like a single cloud run job)

1. **Implement `SupportsPipelineExecution`**
   Your pipeline class should inherit from `SupportsPipelineExecution` and provide:
   - a `run_id` property (unique for each run)
   - a `pipeline_execution_repository` property (an instance of `SqlRepository`)

2. **Decorate your methods**
- Use `@log_pipeline_execution()` on any method you want to log.
- For the main entrypoint use `@log_pipeline_execution(start=True, end=True)` to mark the start and end of the pipeline.

See `src/analytics_db_refresh/refresh.py` for a real-world usage example.

3. **Report on pipeline execution**

The decorator itself does not send messages, but the logged data can be used to generate reports.
See `PipelineReporter` usage how to get execution summary and/or send it to a Mattermost channel.

### How to use it for distributed processing within a workflow

See `platform_export.yaml` for reference.

1. **Use workflow-starter function to start logging**
- Use the `workflow-starter` in your `yaml` file.
- It starts the pipeline execution and returns the `pipeline_run` object.

2. **Implement `SupportsPipelineExecution` in each ETL class of the workflow**
- See `PlatformExportDispatcher` for cloud run function implementation.
- See `PlatformExtractorEtl` for cloud run job implementation.
- Remember to override `get_step_name` to avoid conflicts with other instances of the same ETL.
- Use `@log_pipeline_execution()` only on one of the methods of your ETL.

3. **workflow-finalizer will automatically finish logging**
- The `workflow-finalizer` will automatically finish the logging and send MM report.
- Id of the `log.pipeline_execution` will be returned in the `workflow-starter`:

    ```json
     "workflow-starter": {
      "log_id": 2,
      "pipeline_name": "platform-export",
      "run_id": "20250605_102848094"
     }
    ```
