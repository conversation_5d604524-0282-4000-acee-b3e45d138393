import traceback
from sys import exc_info
from typing import Any, Callable, Optional

from common.pipeline_logging.pipeline_model import (
    PipelineExecutionError,
    SupportsPipelineExecution,
)
from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository


class PipelineExecutionLogger:
    """Class for logging pipeline execution details to SQL log tables."""

    def __init__(self, instance: SupportsPipelineExecution) -> None:
        self._run_id = instance.run_id
        self._repository = instance.pipeline_execution_repository

        if not isinstance(self._repository, SqlRepository):
            raise PipelineExecutionError("Invalid SqlRepository instance!")

        if not self._run_id:
            raise PipelineExecutionError("No run_id for the pipeline!")

    def start_processing(self, pipeline_name: str) -> int:
        """
        Inserts the initial parent log entry to the pipeline_execution table.
        :param pipeline_name: Name of the pipeline
        :returns: Execution id of the started pipeline
        """
        execution_id = self._get_execution_id("start_processing", pipeline_name, self._run_id)

        return execution_id

    def start_processing_step(self, pipeline_name: str, step_name: str) -> int:
        """
        Inserts the initial child log entry to the pipeline_execution_step table.
        :param pipeline_name: Name of the pipeline
        :param step_name: Name of the step
        :returns: Execution id of the started step
        """
        execution_id = self._get_execution_id("get_pipeline_execution_id", pipeline_name, self._run_id)
        step_execution_id = self._get_execution_id("start_processing_step", execution_id, step_name)

        return step_execution_id

    def finish_processing_step(self, pipeline_name: str, step_name: str, error: Optional[str] = None) -> None:
        """
        Closes the child log entry of pipeline_execution_step.
        :param pipeline_name: Name of the pipeline
        :param step_name: Name of the step
        :param error: Error if present, otherwise None
        """
        execution_id = self._get_execution_id("get_pipeline_step_execution_id", pipeline_name, self._run_id, step_name)

        self._repository.select_from_function(
            function=SqlFunction(schema_name="log", function_name="finish_processing_step"),
            args=[execution_id, error] if error else [execution_id],
        )

    def finish_processing(self, pipeline_name: str, error: Optional[str] = None) -> int:
        """
        Closes the parent log entry of pipeline_execution.
        :param pipeline_name: Name of the pipeline
        :param error: Error if present, otherwise None
        :returns: Execution id of the finished pipeline
        """
        execution_id = self._get_execution_id("get_pipeline_execution_id", pipeline_name, self._run_id)

        self._repository.select_from_function(
            function=SqlFunction(schema_name="log", function_name="finish_processing"),
            args=[execution_id, error] if error else [execution_id],
        )

        return execution_id

    def _get_execution_id(self, function_name: str, *args: Any) -> int:
        """Gets execution id for pipeline or step"""
        execution_id = next(
            iter(
                self._repository.select_from_function(
                    function=SqlFunction(schema_name="log", function_name=function_name),
                    args=list(args),
                )
            )
        )[0]

        if not execution_id:
            raise PipelineExecutionError("Cannot find execution_id for the pipeline!")

        return execution_id


def log_pipeline_execution(  # noqa: C901
    pipeline_name: Optional[str] = None, step_name: Optional[str] = None, start: bool = False, end: bool = False
) -> Callable[[Any], Any]:
    """
    Pipeline SQL execution decorator.
    Saves pipeline execution details to SQL log tables.
    :param pipeline_name: Pipeline name, defaults to the class name
    :param step_name: Step name, defaults to the function name
    :param start: Whether to start pipeline logging
    :param end: Whether to end pipeline logging
    """

    def decorator(function: Callable[[Any], Any]) -> Callable[[Any], Any]:
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Note that args[0] is self
            self: SupportsPipelineExecution = args[0]
            pipeline = pipeline_name if pipeline_name else self.pipeline_name
            step = step_name if step_name else self.get_step_name(function.__name__)

            pipeline_logger = PipelineExecutionLogger(self)

            def start_pipeline() -> None:
                """Inner function to start pipeline/step log entry"""
                if start:
                    pipeline_logger.start_processing(pipeline)
                elif not end:
                    pipeline_logger.start_processing_step(pipeline, step)

            def finish_pipeline(error: Optional[str] = None) -> None:
                """Inner function to close pipeline/step log entry"""
                if end:
                    pipeline_logger.finish_processing(pipeline, error)
                elif not start:
                    pipeline_logger.finish_processing_step(pipeline, step, error)

            start_pipeline()
            try:
                function_return = function(*args, **kwargs)
            finally:
                exc_type, _, _ = exc_info()
                stack_trace = traceback.format_exc() if exc_type else None
                finish_pipeline(error=stack_trace)

            return function_return

        return wrapper

    return decorator
