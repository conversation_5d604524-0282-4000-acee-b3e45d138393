from dataclasses import dataclass
from functools import cached_property
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from tabulate2 import tabulate

from common.config import Config
from common.mattermost_client import MattermostClient, MattermostMessage
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution
from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository


@dataclass(frozen=True, slots=True)
class ExecutionSummary(DataClassJsonMixin):
    HEADERS = ["Metric name", "Metric value"]

    metric_name: str
    metric_value: Optional[str]


class PipelineReporter:
    """
    Reports on pipeline execution.
    """

    def __init__(
        self,
        pipeline_execution: SupportsPipelineExecution,
        mattermost_client: MattermostClient,
        config: Config,
    ) -> None:
        self._pipeline_execution = pipeline_execution
        self._mattermost_client = mattermost_client
        self._config = config

    @cached_property
    def pipeline_name(self) -> str:
        """Name of the pipeline."""
        return self._pipeline_execution.pipeline_name

    @cached_property
    def run_id(self) -> str:
        """Unique ID of the run."""
        return self._pipeline_execution.run_id

    @cached_property
    def env(self) -> str:
        return self._config.env.upper()

    @cached_property
    def repository(self) -> SqlRepository:
        """SQL repository used by the pipeline execution."""
        return self._pipeline_execution.pipeline_execution_repository

    def get_execution_summary(self) -> str:
        """
        Gets an execution summary Markdown for the pipeline execution.
        """
        summary = self._get_execution_summary(self.run_id)
        markdown = tabulate(
            summary, headers=ExecutionSummary.HEADERS, tablefmt="simple_outline", colalign=("left", "right")
        )

        return f"```\n{markdown}\n```"

    def send_report(self, channel: Optional[str] = None) -> None:
        """
        Sends a summary report of the pipeline execution to the specified Mattermost channel.
        :param channel: (optional) mattermost channel to send the report to
        """
        markdown = self.get_execution_summary()
        message_text = f"**[{self.env}] {self.pipeline_name} summary:**\n\n{markdown}"
        self._mattermost_client.send_message(message=MattermostMessage(text=message_text, channel=channel))

    def _get_execution_summary(self, run_id: str) -> list[ExecutionSummary]:
        """
        Gets pipeline execution summary
        :param run_id: pipeline unique run id
        :returns: summary
        """

        return [
            ExecutionSummary.from_dict(rows._mapping)  # noqa
            for rows in self.repository.select_from_function(
                function=SqlFunction(schema_name="log", function_name="get_execution_summary_details"),
                args=[run_id],
            )
        ]
