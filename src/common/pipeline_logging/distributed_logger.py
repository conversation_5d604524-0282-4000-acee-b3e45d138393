from typing import Optional

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
    SupportsPipelineRun,
)
from common.sql_repository import SqlRepository

logger = get_logger()


class DistributedPipelineLogger(SupportsPipelineExecution):
    """Distributed pipeline logger used to start and finish pipeline execution logging."""

    def __init__(self, pipeline_run: SupportsPipelineRun, repository: SqlRepository):
        self.supports_pipeline_logging(pipeline_run, raise_error=True)
        self._pipeline_run = pipeline_run
        self._repository = repository
        self._pipeline_logger = PipelineExecutionLogger(self)

    @property
    def pipeline_name(self) -> str:
        """Name of the pipeline."""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """Unique ID of the run."""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SQL repository instance used for logging pipeline execution."""
        return self._repository

    def start_logging(self) -> PipelineRun:
        """Starts pipeline execution logging."""
        log_id = self._pipeline_logger.start_processing(self.pipeline_name)
        run = PipelineRun(pipeline_name=self.pipeline_name, run_id=self.run_id, log_id=log_id)
        logger.info(f"Started pipeline logging for {run.to_dict()}.")
        return run

    def finish_logging(self, error: Optional[str] = None) -> PipelineRun:
        """Finishes pipeline execution logging."""
        log_id = self._pipeline_logger.finish_processing(self.pipeline_name, error)
        run = PipelineRun(pipeline_name=self.pipeline_name, run_id=self.run_id, log_id=log_id)
        logger.info(f"Finished pipeline logging for {run.to_dict()}.")
        return run
