from dataclasses import dataclass
from pathlib import Path
from typing import Iterator, Optional

from dataclasses_json import DataClassJsonMixin
from google.cloud import storage
from google.cloud.storage import Blob, Bucket, Client, transfer_manager

from common.logger import Config, get_logger

logger = get_logger()


class CloudStorageClientError(ValueError):
    """Cloud Storage client error"""


@dataclass(frozen=True)
class CloudStorageClientConfig(DataClassJsonMixin):
    """
    CloudStorageClient configuration
    :param bucket_name: name of existing Cloud Storage bucket
    :param project_id: GCP project id, defaults to the staging project
    :param timeout_seconds: The amount of time, in seconds, to wait for the server response
    """

    bucket_name: str
    project_id: str = Config.project_id
    timeout_seconds: int = 3600


class CloudStorageClient:
    """
    Bucket specific custom GCP cloud storage client to interact with the bucket objects.
    """

    def __init__(
        self,
        config: CloudStorageClientConfig,
        gcp_storage_client: Optional[Client] = None,
    ) -> None:
        self._config = config
        gcp_storage_client = (
            gcp_storage_client if gcp_storage_client else storage.Client(project=self._config.project_id)
        )
        self._bucket = gcp_storage_client.bucket(self._config.bucket_name)

    @property
    def bucket(self) -> Bucket:
        return self._bucket

    @property
    def bucket_name(self) -> str:
        return self._config.bucket_name

    def upload_file(self, file_path: Path, bucket_directory: Path) -> Path:
        """
        Uploads a file to the bucket
        If file exists in the bucket folder, it wil be overwritten
        Reference: https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.blob.Blob#google_cloud_storage_blob_Blob_upload_from_filename # noqa

        :param file_path: path to the local file
        :param bucket_directory: a directory in the bucket where the file will be placed, e.g. /path/to/my/file/

        :returns: file location on the bucket
        :raises CloudStorageClientError: when file does not exist in the local filesystem
        """
        if not file_path.exists():
            raise CloudStorageClientError(f"The file '{file_path}' does not exist!")

        logger.debug(
            f"Uploading the file '{file_path}' to the '{self.bucket_name}' bucket '{bucket_directory}' location..."
        )

        bucket_path = Path(bucket_directory, file_path.name)
        blob = self._bucket.blob(str(bucket_path))
        blob.upload_from_filename(filename=str(file_path), timeout=self._config.timeout_seconds)

        logger.debug(f"File '{file_path}' uploaded to the '{self.bucket_name}' bucket '{bucket_directory}' location.")

        return bucket_path

    def upload_directory(
        self, local_directory: Path, bucket_directory: str, include_subdirectories: bool = True, workers: int = 8
    ) -> list[Path]:
        """
        Uploads non-empty directory to the bucket
        Uses GCS Transfer Manager to upload all the files in a directory with concurrency
        If files with exact name exist in the bucket folder, they will be overwritten
        Ref.:
        - https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.transfer_manager#google_cloud_storage_transfer_manager_upload_many_from_filenames # noqa
        - https://cloud.google.com/storage/docs/samples/storage-transfer-manager-upload-directory

        :param local_directory: local directory path
        :param bucket_directory: a directory in the bucket where the files will be placed, e.g. /path/to/my/file/
        :param include_subdirectories: whether to get all subdirectories of local_directory
        :param workers: the maximum number of processes to use for the operation. The performance
                        impact of this value depends on the use case, but smaller files usually
                        benefit from a higher number of processes. Each additional process occupies
                        some CPU and memory resources until finished.

        :returns: list of files location on the bucket
        :raises CloudStorageClientError: when the directory does not exist, or it's empty in the local filesystem
        """
        if bucket_directory and not bucket_directory.endswith("/"):
            bucket_directory += "/"

        if not local_directory.exists():
            raise CloudStorageClientError(f"The directory '{local_directory}' does not exist!")

        if not any(local_directory.iterdir()):
            logger.warning(f"The directory '{local_directory}' is empty!")

        logger.debug(
            f"Uploading the directory '{local_directory}' to the '{self.bucket_name}' "
            + f"bucket '{bucket_directory}' location..."
        )
        glob = local_directory.rglob("*") if include_subdirectories else local_directory.glob("*")
        file_paths = [str(path.relative_to(local_directory)) for path in glob if path.is_file()]

        results = transfer_manager.upload_many_from_filenames(
            bucket=self._bucket,
            filenames=file_paths,
            source_directory=str(local_directory),
            blob_name_prefix=bucket_directory,
            max_workers=workers,
            upload_kwargs={"timeout": self._config.timeout_seconds},
        )

        bucket_paths = []
        for name, result in zip(file_paths, results):
            if isinstance(result, Exception):
                raise CloudStorageClientError(
                    f"Failed to upload the '{name}' file to the '{self.bucket_name}' bucket"
                    + f" due to exception: {result}"
                )
            else:
                logger.debug(
                    f"File '{name}' uploaded to the '{self.bucket_name}' bucket '{bucket_directory}' location."
                )
                bucket_paths.append(Path(bucket_directory, name))

        return bucket_paths

    def download_file(self, bucket_path: Path, local_folder: Path) -> Path:
        """
        Downloads a file from a bucket to the local folder.
        Creates a local directory if not exists.
        If the file exists in the local folder, it will be overwritten
        Reference: https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.blob.Blob#google_cloud_storage_blob_Blob_download_to_filename # noqa

        :param bucket_path: a full path to the bucket file, e.g., /my/path/to/the/file.txt
        :param local_folder: local folder where the file will be saved

        :returns: local file path
        :raises CloudStorageClientError: when a file does not exist on the bucket in a given folder
        """
        self._raise_non_exiting_file_error(bucket_path)

        if not local_folder.exists():
            local_folder.mkdir(parents=True)

        local_file_path = Path(local_folder, bucket_path.name)

        logger.debug(
            f"Downloading the '{bucket_path}' file from the '{self.bucket_name}' bucket to the '{local_file_path}'..."
        )

        blob = self._bucket.blob(str(bucket_path))
        blob.download_to_filename(filename=str(local_file_path), timeout=self._config.timeout_seconds)

        logger.debug(f"File downloaded to the '{local_file_path}' location.")

        return local_file_path

    def download_directory(
        self,
        bucket_directory: str,
        local_directory: Path,
        recursive: bool = True,
        include_subdirectories: bool = True,
        workers: int = 8,
    ) -> list[Path]:
        """
        Downloads files from a bucket directory to a local directory preserving bucket's directory structure.
        Uses transfer_manager.download_many_to_path to download all the files in parallel.
        If files with the exact name exist in the local folder, they will be overwritten.
        Ref. https://cloud.google.com/storage/docs/samples/storage-transfer-manager-download-bucket

        :param bucket_directory: bucket directory from where the files will be downloaded, e.g. /path/to/my/file/
        :param local_directory: local directory path where files will be saved, created if not exists
        :param recursive: whether to download all subdirectories of bucket_directory
        :param include_subdirectories: whether to reflect bucket directory structure in the local directory
        :param workers: the maximum number of processes to use for the operation. The performance
                        impact of this value depends on the use case, but smaller files usually
                        benefit from a higher number of processes. Each additional process occupies
                        some CPU and memory resources until finished.

        :returns: list of local file paths where files were downloaded
        """

        logger.debug(
            f"Downloading files from the '{self.bucket_name}' bucket '{bucket_directory}' location "
            + f"to the local directory '{local_directory}'..."
        )

        if bucket_directory and not bucket_directory.endswith("/"):
            bucket_directory += "/"

        files = list(self.list_files(bucket_directory=bucket_directory, recursive=recursive))

        if not files:
            logger.warning(f"No files found in the '{bucket_directory}' directory of the '{self.bucket_name}' bucket!")
            return []

        if not include_subdirectories:
            file_names = [str(Path(file.name).relative_to(bucket_directory)) for file in files]
            file_name_prefix = bucket_directory
        else:
            file_names = [blob.name for blob in files]
            file_name_prefix = ""

        results = transfer_manager.download_many_to_path(
            bucket=self._bucket,
            blob_names=file_names,
            destination_directory=str(local_directory),
            blob_name_prefix=file_name_prefix,
            max_workers=workers,
            create_directories=True,
            download_kwargs={"timeout": self._config.timeout_seconds},
        )

        downloaded_file_paths = []
        for file_name, result in zip(file_names, results):
            if isinstance(result, Exception):
                raise CloudStorageClientError(f"Failed to download '{file_name}' due to exception: {result}")
            else:
                logger.debug(f"Downloaded '{file_name}' to '{local_directory}'.")
                downloaded_file_paths.append(Path(local_directory, file_name))

        return downloaded_file_paths

    def delete_file(self, bucket_path: Path) -> None:
        """
        Deletes a file on a bucket
        Reference: https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.blob.Blob#google_cloud_storage_blob_Blob_delete # noqa

        :param bucket_path: a full path to the bucket file, e.g. /my/path/to/the/file.txt
        :raises CloudStorageClientError: when file does not exist on the bucket in given folder
        """
        self._raise_non_exiting_file_error(bucket_path)

        logger.info(f"Deleting the '{bucket_path}' file in the '{self.bucket_name}' bucket...")

        blob = self._bucket.blob(str(bucket_path))
        blob.delete()

        logger.info(f"File '{bucket_path}' deleted.")

    def delete_files(self, bucket_directory: str) -> None:
        """
        Delete files in a bucket directory
        Reference:
        https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.blob.Blob#google_cloud_storage_blob_Blob_delete # noqa

        :param bucket_directory: a directory in the bucket
        """

        logger.debug(f"Deleting all files from '{bucket_directory}' in the '{self.bucket_name}' bucket'...")

        files = self.list_files(bucket_directory)
        for file in files:
            self.delete_file(Path(file.name))

    def list_files(self, bucket_directory: str, recursive: bool = True) -> Iterator[Blob]:
        """
        List files in a bucket directory
        Reference:
        https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.bucket.Bucket#google_cloud_storage_bucket_Bucket_list_blobs # noqa

        :param bucket_directory: a directory in the bucket
        :param recursive: whether to list files in all subdirectories of bucket_directory
        :returns: list of Blob objects
        """

        delimiter = None if recursive else "/"

        logger.debug(f"Listing all files from '{bucket_directory}' in the '{self.bucket_name}' bucket'...")

        return self._bucket.list_blobs(prefix=bucket_directory, delimiter=delimiter)

    def file_exist(self, bucket_path: Path) -> bool:
        """
        Checks if bucket file exists
        Reference: https://cloud.google.com/python/docs/reference/storage/latest/google.cloud.storage.blob.Blob#google_cloud_storage_blob_Blob_exists # noqa

        :param bucket_path: a full path of the bucket file/object, e.g. /my/path/to/the/file.txt
        """
        logger.debug(f"Checking if the '{bucket_path}' file exists in the '{self.bucket_name}' bucket...")

        blob = self._bucket.blob(str(bucket_path))

        return blob.exists()

    def _raise_non_exiting_file_error(self, bucket_path: Path) -> None:
        """
        Raises custom error when bucket file does not exist.

        :param bucket_path: a full path to the bucket file, e.g. /my/path/to/the/file.txt
        :raises CloudStorageClientError: when file does not exist on the bucket in given folder
        """
        if not self.file_exist(bucket_path):
            raise CloudStorageClientError(
                f"The file does not exist in the '{bucket_path}' " + f"path of the '{self.bucket_name}' bucket!"
            )
