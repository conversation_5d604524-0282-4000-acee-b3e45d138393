from dataclasses import dataclass
from typing import Optional

import requests
from dataclasses_json import DataClassJsonMixin

from common.logger import get_logger

logger = get_logger()


@dataclass(frozen=True)
class MattermostMessage(DataClassJsonMixin):
    """
    Mattermost message as described here: https://developers.mattermost.com/integrate/webhooks/incoming/#parameters

    - text: Markdown-formatted message to display in the post.

    - channel: Overrides the webhook configured channel the message posts in.
      Use the channel’s name and not the display name!
      The channel name can be found at the end of the url of a channel, example:

      - url: https://ozwiena.refurbed.io/refurbed/channels/customer-happiness--analytics
      - channel name: 'customer-happiness--analytics'
      If you are using the app, you simply right-click on a channel and press copy link to get the url!

    - username: Overrides the username the message posts as.
      Defaults to the username set during webhook creation; if no username was set during creation, webhook is used.

    - icon_emoji: Overrides the profile picture and icon_url parameter.
      Defaults to none and is not set during webhook creation.
    """

    text: str
    channel: Optional[str] = None
    username: Optional[str] = None
    icon_emoji: Optional[str] = None


class MattermostClientError(ValueError):
    """
    Mattermost exception class
    """


class MattermostClient:
    HEADERS = {"Content-Type": "application/json"}

    def __init__(self, webhook_url: str):
        self._webhook_url = webhook_url

    def send_message(self, message: MattermostMessage) -> None:
        """
        Sends a message to the Mattermost channel via incoming webhook.
        See more here: https://developers.mattermost.com/integrate/webhooks/incoming/

        :param message: Mattermost message to send.
        :raises MattermostClientError: If Mattermost request fails.
        """
        response = requests.post(self._webhook_url, data=message.to_json(), headers=self.HEADERS)

        channel = message.channel if message.channel is not None else "webhook configured channel"
        if response.status_code == 200:
            logger.debug(f"Message posted successfully to {channel=}.")
        else:
            raise MattermostClientError(f"Error posting message: {response.status_code=}, {response.text=}.")
