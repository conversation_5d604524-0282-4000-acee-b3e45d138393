import decimal
from abc import ABC
from collections import defaultdict
from dataclasses import dataclass, field
from datetime import UTC
from functools import cached_property

from dataclasses_json import DataClassJsonMixin
from db_dtypes import DateDtype
from pandas import DataFrame, DatetimeTZDtype, StringDtype
from pandas.core.dtypes.base import ExtensionDtype


@dataclass(frozen=True)
class DataFrameSchema(DataClassJsonMixin):
    """
    Pandas DataFrame schema used by pandas `read_csv` / `read_sql` methods:
     - https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_sql_query.html
     - https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html

    **Notes:**
     - for date only (no time zone) columns use `DATE_TYPE`
     - `datetime_columns` inherit `DATETIME_TYPE`
     - `DATETIME_TYPE` is using microsecond precision, default for Postgres timestamp
     - Overwrite `DATETIME_TYPE` if you need different type or precision for `date_columns`
    """

    DATE_TYPE = DateDtype()
    DATETIME_TYPE = DatetimeTZDtype(unit="us", tz=UTC)

    date_columns: list[str] = field(default_factory=list)
    datetime_columns: list[str] = field(default_factory=list)
    data_types: dict[str, ExtensionDtype] = field(default_factory=lambda: defaultdict(StringDtype))

    @cached_property
    def columns(self) -> list[str]:
        """All column names"""

        return list(dict.fromkeys(list(self.data_types.keys()) + self.date_columns + self.datetime_columns))

    @cached_property
    def dtypes(self) -> dict[str, ExtensionDtype]:
        """All data frame data types"""
        date_types: dict[str, ExtensionDtype] = {col: self.DATE_TYPE for col in self.date_columns}
        datetime_types: dict[str, ExtensionDtype] = {col: self.DATETIME_TYPE for col in self.datetime_columns}

        return self.data_types | date_types | datetime_types


class DataFrameWithSchema:
    """
    A wrapper around a Pandas DataFrame that enforces schema constraints.

    This class ensures that the DataFrame adheres to a specified schema by applying
    data types to columns and validating the DataFrame structure.

    Attributes:
        dataframe: A Pandas DataFrame to be validated against the schema.
        schema: A DataFrameSchema object that defines the expected structure and data types.
    """

    def __init__(self, dataframe: DataFrame, schema: DataFrameSchema):
        """
        Initialize the DataFrameWithSchema object.

        :param dataframe: The Pandas DataFrame to validate.
        :param schema: The DataFrameSchema object that defines the expected structure and data types.
        """
        self.dataframe = dataframe
        self.schema = schema

    @cached_property
    def dataframe_with_schema(self) -> DataFrame:
        if self.dataframe.empty:
            return DataFrame(columns=self.schema.columns)
        else:
            return self.dataframe.astype(self.schema.dtypes)


def to_decimal_type(data_frame: DataFrame, column_name: str, decimal_precision: int = 12) -> DataFrame:
    """
    Changes float column type to Decimal with given precision.
    Ref. https://stackoverflow.com/a/71774352/27543394
    :param data_frame: pandas dataframe
    :param column_name : column name to be converted to Decimal
    :param decimal_precision: numeric precision for Decimal type, defaults to 12 which means 1_000_000_000.00
    :returns: BigQuery types compatible dataframe
    """

    context = decimal.Context(prec=decimal_precision)
    data_frame[column_name] = data_frame[column_name].apply(context.create_decimal_from_float)

    return data_frame


class BaseDataFrameSchema(ABC):
    SCHEMA: DataFrameSchema = NotImplemented
