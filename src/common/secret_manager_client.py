from google.cloud import secretmanager


def get_secret(secret_id: str, project_id: str, version: str = "latest") -> str:
    """
    Retrieves a secret from the Google Secret Manager.
    Secret id can be passed as:
     - relative name: secret_id
     - fully qualified name: projects/{{project}}/secrets/{{secret_id}}

    Reference: https://cloud.google.com/secret-manager/docs/access-secret-version

    :param secret_id: secret id
    :param project_id: google project id
    :param version: optional secret version number or alias, defaults to `latest`

    :returns: latest version of the secret
    """
    version_path = f"/versions/{version}"
    project_path = f"projects/{project_id}/secrets/"
    if project_path in secret_id:
        name = f"{secret_id}{version_path}"
    else:
        name = f"{project_path}{secret_id}{version_path}"

    client = secretmanager.SecretManagerServiceClient()
    response = client.access_secret_version(name=name)

    return response.payload.data.decode("UTF-8")
