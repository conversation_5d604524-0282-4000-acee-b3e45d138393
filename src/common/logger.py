import logging
import re
from logging import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from typing import Any, Optional

from google.cloud.logging_v2.handlers import StructuredLogHandler
from google.cloud.logging_v2.handlers.transports import SyncTransport

from common.config import DEFAULT_CONFIG, Config

# Same as in refbetl library
LOCAL_LOG_FORMAT = "%(asctime)s |%(levelname)s| (%(module)s):%(lineno)s >> %(message)s"

# Cloud logs does not need time nor level as it's already embedded
CLOUD_LOG_FORMAT = "(%(module)s):%(lineno)s >> %(message)s"


def setup_logger(config: Config, logging_filter: Optional[Filter] = None) -> Logger:
    """
    Setup custom logger with the name taken from the config.
    Should be called only once, module level loggers will inherit its configuration.

    :param config: project wise configuration
    :param logging_filter: logging filter
    :returns: pre-configured logger
    """

    logger = logging.getLogger(name=config.logger_name)
    logger.setLevel(config.log_level)

    if config.is_cloud:
        cloud_handler = _setup_cloud_logging(logger, config)
        _setup_cloud_log_format(config, logger, logging_filter, cloud_handler)
        logger.debug("Cloud logging was enabled.")
    else:
        setup_local_logging(logger, logging_filter)
        logger.debug("Color logging was enabled.")

    return logger


def get_logger(name: Optional[str] = None) -> Logger:
    """
    Get analytics-pipelines logger with the name taken from the default config.

    :param name: optional child logger name of the analytics-pipelines logger
    :returns: analytics-pipelines logger
    """
    logger_name = f"{DEFAULT_CONFIG.logger_name}.{name}" if name else DEFAULT_CONFIG.logger_name

    return logging.getLogger(name=logger_name)


def _setup_cloud_logging(logger: Logger, config: Config) -> StructuredLogHandler:
    """
    Remove all existing handlers to avoid duplicate logs.
    Attach the StructuredLogHandler to the Python root logger.
    Use synchronous transport to avoid gRPC channel issues in Cloud Run.
    Ref. https://cloud.google.com/python/docs/reference/logging/latest/google.cloud.logging_v2.handlers.structured_log.StructuredLogHandler # noqa

    :param logger: Python root logger
    :param config: project wise configuration
    :returns: structured log handler
    """

    logger.handlers.clear()

    if parent := logger.parent:
        parent.handlers.clear()

    handler = StructuredLogHandler(project_id=config.project_id, transport=SyncTransport)
    logger.addHandler(handler)

    return handler


class ColorFormatter(Formatter):
    """
    Colorful logging formatter, with no additional packages required.
    https://stackoverflow.com/questions/384076/how-can-i-color-python-logging-output
    """

    RESET = "\x1b[0m"
    GREY = "\x1b[37;20m"
    YELLOW = "\x1b[33;20m"
    RED = "\x1b[31;20m"
    BOLD_RED = "\x1b[31;1m"

    FORMATS = {
        logging.DEBUG: GREY + LOCAL_LOG_FORMAT + RESET,
        # Use default theme color for INFO, so it works for both Darcula and Light scheme!
        logging.INFO: LOCAL_LOG_FORMAT,
        logging.WARNING: YELLOW + LOCAL_LOG_FORMAT + RESET,
        logging.ERROR: RED + LOCAL_LOG_FORMAT + RESET,
        logging.CRITICAL: BOLD_RED + LOCAL_LOG_FORMAT + RESET,
    }

    def format(self, record: LogRecord) -> str:
        log_fmt = self.FORMATS.get(record.levelno)
        formatter = Formatter(log_fmt)
        return formatter.format(record)


def _setup_cloud_log_format(config: Config, logger: Logger, logging_filter: Optional[Filter], handler: Handler) -> None:
    """
    Setup log format for cloud stream handler (if present).
    For cloud run job add execution in front of the log entry.

    :param config: project wise configuration
    :param logger: root logger
    :param logging_filter: optional logging filter
    :param handler: cloud logging handler
    """
    logging_format = (
        f"{config.cloud_run_execution} >> {CLOUD_LOG_FORMAT}" if config.cloud_run_execution else CLOUD_LOG_FORMAT
    )
    handler.setFormatter(Formatter(logging_format))
    _add_logging_filter(handler, logger, logging_filter)


def setup_local_logging(logger: Logger, logging_filter: Optional[Filter]) -> None:
    """
    Setup local console logging with color formatting and optional filter.
    Add color logging for the local stream handler.

    :param logger: logger instance
    :param logging_filter: optional logging filter
    """
    # Clear any existing handlers
    logger.handlers.clear()

    # Create console handler
    handler = StreamHandler()
    handler.setLevel(logger.level)
    handler.setFormatter(ColorFormatter())

    # Add the filter if provided
    _add_logging_filter(handler, logger, logging_filter)

    # Add handler to logger
    logger.addHandler(handler)


def _add_logging_filter(handler: Handler, logger: Logger, logging_filter: Optional[Filter]) -> None:
    if logging_filter:
        handler.addFilter(logging_filter)
        logger.debug(f"Logging filter '{logging_filter.__class__.__name__}' was added.")


class PasswordMasker(Filter):
    """
    Masks passwords with asterisks
    https://dev.to/camillehe1992/mask-sensitive-data-using-python-built-in-logging-module-45fa
    """

    passwords_keys = ("pwd", "pass", "password")
    password_mask = "*****"

    def filter(self, record: LogRecord) -> bool:
        if isinstance(record.msg, str):
            record.msg = self.mask_password(record.msg)
        elif isinstance(record.msg, dict):
            self.mask_password_in_dict(record.msg)
        return True

    def mask_password(self, message: str) -> str:
        """
        Replaces any word that is preceded by one of the `passwords_keys` and one non-alphanumeric character.
        Handles various password formats including:
        - password='value'
        - password="value"
        - password=value
        - {'password': 'value'}
        - {'rootPassword': 'value'}
        - '-password=value' (command-line arguments)
        - '-password=value' (quoted command-line arguments)
        - Special characters in passwords
        :param message: message to mask
        :return: masked message
        """
        for key in self.passwords_keys:
            # Pattern 1: password='value' or password="value" (with quotes, preserving quote style)
            message = re.sub(rf"(?i)({key}=)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message)

            # Pattern 2: password=value (without quotes, stopping at word boundary, comma, space, or closing paren)
            # Add single quotes around the masked password for consistency
            message = re.sub(rf"(?i)({key}=)([^'\"\s,\)]+)(?=[\s,\)]|$)", rf"\1'{self.password_mask}'", message)

            # Pattern 3: password: 'value' or password: "value" (colon with quotes)
            message = re.sub(rf"(?i)({key}['][:][\s]*)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message)

            # Pattern 4: JSON-like format {'password': 'value'} or {"password": "value"}
            message = re.sub(
                rf"(?i)(['\"].*{key}['\"][\s]*:[\s]*)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message
            )

            # Pattern 5: JSON-like format with compound keys like 'rootPassword'
            message = re.sub(
                rf"(?i)(['\"][^'\"]*{key}[^'\"]*['\"][\s]*:[\s]*)(['\"])([^'\"]*)\2",
                rf"\1\2{self.password_mask}\2",
                message,
            )

            # Pattern 6: Command-line argument format '-password=value' or '--password=value'
            message = re.sub(rf"(?i)(-+{key}=)(['\"])([^'\"]*)\2", rf"\1\2{self.password_mask}\2", message)

            # Pattern 7: Quoted command-line argument format like '-password=value'
            message = re.sub(rf"(?i)(['\"])(-+{key}=)([^'\"]*)\1", rf"\1\2{self.password_mask}\1", message)

            # Pattern 8: Unclosed quotes - password='value (missing closing quote)
            # This handles cases where a quote is started but not properly closed
            message = re.sub(rf"(?i)({key}=)(['\"])([^'\"]*?)(?=\s|$)", rf"\1\2{self.password_mask}\2", message)

        return message

    def mask_password_in_dict(self, data: dict[str, Any]) -> None:
        """
        Masks password values in dictionary objects (for structured logging).
        Recursively processes nested dictionaries.
        Modifies the dictionary in-place.
        :param data: dictionary to mask
        """

        for key, value in data.items():
            if isinstance(value, dict):
                self.mask_password_in_dict(value)
            elif isinstance(value, str):
                # Check if this key contains any password-related substring
                if any(pwd_key in key.lower() for pwd_key in self.passwords_keys):
                    data[key] = self.password_mask


PASSWORD_MASKER = PasswordMasker()

if __name__ == "__main__":
    test_config = Config(log_level=logging.getLevelName(logging.DEBUG))
    test_logger = setup_logger(test_config)

    # test me!
    test_logger.debug("This is DEBUG message")
    test_logger.info("This is INFO message")
    test_logger.warning("This is WARNING message")
    test_logger.error("This is ERROR message")
    test_logger.critical("This is CRITICAL message")

    # Setup logger with password masking filter
    test_logger_with_filter = setup_logger(test_config, PASSWORD_MASKER)

    # Test cases for password masking
    test_logger_with_filter.info("This is INFO message with password=qwerty123")
    test_logger_with_filter.info("This is INFO message with password='qwerty123'")
    test_logger_with_filter.warning("This is WARNING message with password='@*&&#$%@#/")
    test_logger_with_filter.error("This is ERROR message with serialized JSON: {'password': 'foo'}")
    test_logger_with_filter.critical(
        "This is CRITICAL message with serialized JSON: {'rootPassword': 'foo', 'bar': 'baz'}"
    )

    # Additional test cases
    test_logger_with_filter.info(
        "FlywayParams(host='localhost', password='postgres', url='*****************************************')"
    )
    test_logger_with_filter.info('Database config: {"password": "secret123", "host": "localhost"}')
    test_logger_with_filter.info("Multiple passwords: password=first pwd='second' pass=\"third\"")
