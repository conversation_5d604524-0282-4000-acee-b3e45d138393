"""
GCP cloud events utility functions
"""

import base64
import binascii
from typing import Any, Optional

from cloudevents.abstract import CloudEvent
from cloudevents.http import CloudEvent as HttpEvent

from common.typings import HttpAttributes

DEFAULT_HTTP_ATTRIBUTES: HttpAttributes = {"type": "google.cloud.pubsub.topic.v1.messagePublished", "source": "test"}


def create_http_event(data: Any, attributes: Optional[HttpAttributes] = None) -> HttpEvent:
    """
    Creates Http CloudEvent.

    :param data: any data
    :param attributes: optional attributes

    :returns: Instance of Http CloudEvent
    """
    if not attributes:
        attributes = DEFAULT_HTTP_ATTRIBUTES

    return HttpEvent.create(attributes, data)


def get_event_message(event: CloudEvent) -> Optional[Any]:
    """
    Extracts and decodes event message from abstract cloud event, i.e. pub/sub or http event.

    Ref.:
    - https://cloud.google.com/eventarc/docs/workflows/cloudevents
    - https://cloud.google.com/functions/docs/tutorials/pubsub

    :param event: cloud event

    :returns: event message
    """
    data = event.get_data()

    if isinstance(data, dict):
        message = data.get("message", "")
        return get_message_data(message)

    return data


def get_message_data(message: Any) -> Optional[Any]:
    """
    Gets message data from the message content.

    :param message: event message

    :returns: decoded message
    """
    if isinstance(message, dict):
        data = message.get("data", "")

        if data:
            try:
                return base64.b64decode(data).decode()
            except (binascii.Error, UnicodeDecodeError):
                return data

    return message
