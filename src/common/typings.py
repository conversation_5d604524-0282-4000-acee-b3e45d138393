"""
# HTTP Cloud Functions types:
- https://cloud.google.com/functions/docs/writing/write-http-functions#http_frameworks

# Cloud events formats
- https://cloud.google.com/eventarc/docs/cloudevents
"""

from dataclasses import dataclass
from datetime import datetime
from http import HTTPStatus
from pathlib import Path
from typing import Any, NamedTuple, TypeAlias

from dataclasses_json import DataClassJsonMixin

HttpAttributes: TypeAlias = dict[str, str]

Timestamp: TypeAlias = float
TimestampInSec: TypeAlias = int

APPLICATION_JSON_HEADER: dict[str, str] = {"Content-Type": "application/json"}


class HttpResponse(NamedTuple):
    """
    The response text, or any set of values that can be turned into a Response object using
    `make_response` <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    body: str = "OK"
    status: int = HTTPStatus.OK
    headers: dict[str, Any] = {}


@dataclass(frozen=True)
class FileInfo(DataClassJsonMixin):
    """
    Wrapper over some `os.stat` attributes: https://docs.python.org/3/library/os.html#os.stat
    """

    path: Path  # full path to the file
    created: datetime  # created or modified time depending on OS support with time zone specified!
    size: int  # size in bytes

    def __post_init__(self) -> None:
        if self.created.tzname() is None:
            raise TypeError("Field 'created' must contain time zone!")

        if self.size < 0:
            raise TypeError("Field 'size' must be greater or equal to 0!")


@dataclass(frozen=True)
class DataLakePartition(DataClassJsonMixin):
    """
    Generic schema for data lake Hive style partition
    Ref.
    - https://cloud.google.com/bigquery/docs/hive-partitioned-queries#supported_data_layouts
    - https://cloud.google.com/bigquery/docs/create-cloud-storage-table-biglake#create-biglake-partitioned-data
    """

    base_path: str

    @property
    def path(self) -> Path:
        return Path(f"{self.base_path}/")


@dataclass(frozen=True)
class DataLakeTimePartition(DataLakePartition):
    """
    Data lake time based partition
    """

    year: int
    month: int
    day: int

    @property
    def path(self) -> Path:
        return Path(f"{self.base_path}/year={self.year}/month={self.month}/day={self.day}/")
