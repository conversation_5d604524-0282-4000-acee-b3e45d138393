from dataclasses import dataclass
from functools import cached_property
from typing import Any

import gspread
import pandas as pd
from google.auth import default
from google.auth.transport.requests import Request
from gspread.exceptions import SpreadsheetNotFound, WorksheetNotFound

from common.logger import get_logger

logger = get_logger()


@dataclass(frozen=True)
class GoogleSheetConfig:
    """
    spreadsheet_id: The ID of the Google Spreadsheet (can be found in the URL).
    sheet_name: The name of the worksheet within the spreadsheet.
    starting_cell: The cell number of the first cell in the spreadsheet to be accessed
    """

    spreadsheet_id: str
    sheet_name: str
    starting_cell: str


class GoogleSheetsClient:
    def __init__(self, gs_config: GoogleSheetConfig) -> None:
        """
        Initializes the GoogleSheetsClient by performing authorization with the Google Sheets API.
        gs_config (GoogleSheetConfig): The Google Sheets config object,
                                including Spreadsheet ID and Worksheet ID.
        """
        # Use application default credentials provided by Google Cloud Function
        credentials, _ = default(
            scopes=[
                "https://www.googleapis.com/auth/spreadsheets",
                "https://www.googleapis.com/auth/drive.file",
                "https://www.googleapis.com/auth/drive",
            ]
        )

        # Refresh the credentials if needed
        credentials.refresh(Request())

        # Authenticate with Google Sheets API using the credentials
        self.client = gspread.authorize(credentials)

        # Target specific Google Sheet
        self._gs_config = gs_config

    @cached_property
    def spreadsheet_url(self) -> str:
        """Returns the URL of the Google Spreadsheet."""

        return f"https://docs.google.com/spreadsheets/d/{self._gs_config.spreadsheet_id}"

    def reload_google_sheet(self, df: pd.DataFrame) -> None:
        """
        Reload a Google Sheet with the content of a Pandas DataFrame, replacing existing data.

        Args:
            df (pd.DataFrame): The DataFrame containing the data to insert into the Google Sheet.
                               The DataFrame's columns will be used as the header row in the Google Sheet.

        Returns:
            None: The function performs the action of updating the Google Sheet but does not return anything.
        """
        try:
            # Open the Google Sheet by its spreadsheet ID and the specific worksheet
            sheet = self.client.open_by_key(self._gs_config.spreadsheet_id).worksheet(self._gs_config.sheet_name)

            # Clear existing data in the worksheet
            sheet.clear()

            # Convert the DataFrame to a list of lists (including the header row)
            data: list[list[Any]] = [df.columns.values.tolist()] + df.values.tolist()

            # Update the sheet starting from the defined starting cell, with the new data
            sheet.update(range_name=self._gs_config.starting_cell, values=data)

            logger.info(
                f"Successfully refreshed '{self._gs_config.sheet_name}' sheet in the "
                f"'{self.spreadsheet_url}' Google Spreadsheet "
                f"with {len(df)} rows."
            )

        except SpreadsheetNotFound as e:
            logger.error(f"Spreadsheet not found: {self._gs_config.spreadsheet_id}. Error: {e}")
            raise

        except WorksheetNotFound as e:
            logger.error(
                f"Worksheet not found: {self._gs_config.sheet_name} in spreadsheet {self._gs_config.spreadsheet_id}."
                f" Error: {e}"
            )
            raise
