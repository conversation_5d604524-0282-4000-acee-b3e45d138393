from abc import ABC
from pathlib import Path
from typing import Any, Optional

import duckdb
from dataclasses_json import DataClassJsonMixin
from duckdb import DuckDBPyConnection

from common.config import Config


class DuckDbRepository(ABC):
    """
    DuckDB abstraction over sql statements.
    """

    def __init__(self, connection: Optional[DuckDBPyConnection] = None, config: Optional[Config] = None):
        self._config = config if config else Config()
        self._connection = connection if connection else self._create_connection()

    def query_to_table(self, query: str, table_name: str) -> None:
        """
        Runs CTAS (Create Table as Select) query by persisting the results to the table.

        :param query: sql query to run
        :param table_name: name of the table where query will be persisted
        """
        self.drop_table(table_name)
        duckdb.query(
            query=query,
            connection=self._connection,
        ).to_table(table_name)

    def get_table_results[T: DataClassJsonMixin](self, table_name: str, entity: type[T]) -> list[T]:
        """
        Gets all the rows from the given table and map the results to the type of passed data class.
        Data class at least has to have one field with the same name as table column.

        :param table_name: name of the table
        :param entity: type of data class to which the results will be mapped
        :returns: table rows mapped to list of data classes
        """

        results = self._table_to_dict(table_name)
        entities = [entity.from_dict(result) for result in results]

        return entities

    def table_exists(self, table_name: str) -> bool:
        """
        Check if table exists

        :param table_name: name of the table
        :returns: true or false
        """

        tables = duckdb.query("show tables;", connection=self._connection).fetchall()

        return any([table for table_tuple in tables for table in table_tuple if table == table_name])

    def _table_to_dict(self, table_name: str) -> list[dict[str, Any]]:
        """
        Gets all the rows from the given table and map the results to the list of dict type.

        :param table_name: name of the table
        :returns: table rows mapped to the list of dict type
        """

        table = duckdb.table(table_name, connection=self._connection)
        results = [dict(zip(table.columns, item)) for item in table.fetchall()]

        return results

    def drop_table(self, table_name: str) -> None:
        """
        Drops given table if exists.

        :param table_name: name of the table
        """

        duckdb.query(f"drop table if exists {table_name};", connection=self._connection)

    def _create_connection(self) -> DuckDBPyConnection:
        """Create DuckDB connection"""

        base_path = Path(self._config.root_dir, ".temp")
        base_path.mkdir(parents=True, exist_ok=True)
        db_path = Path(base_path, f"{self.__class__.__name__}.db")

        return duckdb.connect(database=str(db_path))
