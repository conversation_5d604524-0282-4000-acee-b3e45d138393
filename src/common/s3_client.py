from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Union

from boto3 import Session
from botocore.config import Config


class S3Client:
    """
    Bucket specific custom AWS S3 client to interact with the bucket objects.
    """

    def __init__(
        self,
        bucket_name: str,
        session: Optional[Session] = None,
        config: Optional[Config] = None,
    ) -> None:
        self._bucket_name = bucket_name
        _config = config if config else Config()
        _session = session if session else Session()
        self._s3_client = _session.client(service_name="s3", config=_config)

    @property
    def bucket_name(self) -> str:
        return self._bucket_name

    def upload_file(self, file_path: Path, s3_prefix: str, include_full_local_path: bool = False) -> str:
        """
        Uploads a file to an S3 bucket.

        :param file_path: full path to the local file to upload
        :param s3_prefix: s3 prefix where to upload the file, e.g. "/foo/bar/"
        :param include_full_local_path: whether to include full local path in s3 prefix,
         example: s3_prefix = "/foo/bar/"
                  file_path = "/baz/file.txt"
                  full s3 path when set to True = "/foo/bar/baz/file.txt"
                  full s3 path when set to False = "/foo/bar/file.txt"
        :returns: full s3 path to the successfully uploaded file
        """
        s3_prefix = str(
            Path(s3_prefix, file_path.relative_to("/")) if include_full_local_path else Path(s3_prefix, file_path.name)
        )

        self._s3_client.upload_file(Filename=str(file_path), Key=s3_prefix, Bucket=self._bucket_name)

        return s3_prefix

    def download_files(
        self,
        s3_prefix: str,
        local_path: Path,
        include_prefix_in_local_path: bool = True,
        max_workers: Optional[int] = None,
        max_items: int = 1000,
    ) -> List[str]:
        """
        Downloads all files from the given s3 location `s3_prefix` by:
        - listing the objects with s3 client list_objects_v2 using `max_items` with NextContinuationToken
        - downloading the key into the `local_path`, ensuring all folders from the local path are created

        :param s3_prefix: pattern to match in s3
        :param local_path: local path to folder in which to place files, will be created when does not exist
        :param include_prefix_in_local_path: whether to have a prefix in the local path,
         example: local_path = "/foo/bar/"
                  s3_prefix = "/baz"
                  local full path when set to True= "/foo/bar/baz/file.txt"
                  local full path when set to False= "/foo/bar/file.txt"
        :param max_workers: optional number of downloading threads, otherwise default for `ThreadPoolExecutor`
        :param max_items: optional maximum number of items returned by the s3 client `list_objects_v2`, defaults to 1000
        :returns: Local paths to the downloaded files

        Ref.:
        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html#S3.Client.list_objects_v2
        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html#S3.Client.download_file
        """
        s3_keys = self.list_objects(s3_prefix, max_items)

        if s3_keys:
            local_path.mkdir(parents=True, exist_ok=True)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {
                executor.submit(self._download_key, s3_prefix, s3_key, local_path, include_prefix_in_local_path)
                for s3_key in s3_keys
            }
            downloaded_file_paths = [future.result() for future in as_completed(futures)]

        return downloaded_file_paths

    def list_objects(self, s3_prefix: str = "", max_items: int = 1000) -> Iterator[str]:
        """
        Gets all s3 keys recursively from the given `s3_prefix`.

        :param s3_prefix: s3 prefix to the given bucket, be default empty means list all objects in the bucket
        :param max_items: sets the maximum number of items returned by the s3 client `list_objects_v2`, defaults to 1000
        :returns: S3 paths of listed objects
        """
        next_token: Optional[str] = ""
        while next_token is not None:
            results = self._list_objects(prefix=s3_prefix, max_items=max_items, next_token=next_token)
            contents = results.get("Contents")

            if contents:
                for content in contents:
                    yield content.get("Key")

            next_token = results.get("NextContinuationToken")

    def _download_key(self, s3_prefix: str, s3_key: str, local_path: Path, include_prefix_in_local_path: bool) -> str:
        """
        Downloads given `s3_key` to the `local_path`.

        :param s3_prefix: pattern to match in s3
        :param s3_key: The name of the key to download from
        :param local_path: The local path (folder) to the file to download to
        :returns: The full path to the downloaded file
        """
        full_path = (
            Path(local_path, Path(s3_key).parent)
            if include_prefix_in_local_path
            else Path(local_path, Path(s3_key).relative_to(s3_prefix).parent)
        )
        full_path.mkdir(parents=True, exist_ok=True)
        downloaded_file = str(Path(full_path, Path(s3_key).name))
        self._s3_client.download_file(Bucket=self._bucket_name, Key=s3_key, Filename=downloaded_file)

        return downloaded_file

    def _list_objects(
        self,
        prefix: str,
        max_items: int = 1000,
        delimiter: Optional[str] = None,
        next_token: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Lists s3 keys from the given prefix.

        :param prefix: s3 prefix to the given bucket
        :param max_items: sets the maximum number of items returned by the s3 client `list_objects_v2`, defaults to 1000
        :param delimiter: a character to group keys
        :param next_token: boto3 ContinuationToken
        :returns: s3 keys

        Ref.:
        https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html#S3.Client.list_objects_v2
        """

        objects_list_args = ListObjectsArgs(
            bucket_name=self._bucket_name,
            prefix=prefix,
            max_keys=max_items,
            delimiter=delimiter,
            token=next_token,
        )
        return self._s3_client.list_objects_v2(**objects_list_args.args)


class ListObjectsArgs:
    args: Dict[str, Union[str, int]]

    def __init__(
        self,
        bucket_name: str,
        prefix: str,
        max_keys: int,
        delimiter: Optional[str] = None,
        token: Optional[str] = None,
    ) -> None:
        self.args = {
            "Bucket": bucket_name,
            "Prefix": prefix,
            "MaxKeys": max_keys,
        }
        if delimiter:
            self.args.update({"Delimiter": delimiter})

        if token:
            self.args.update({"ContinuationToken": token})
