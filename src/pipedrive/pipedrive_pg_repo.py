from pipedrive_model import <PERSON><PERSON><PERSON><PERSON>ransformed
from sqlalchemy import <PERSON><PERSON><PERSON>, Date, DateTime, Integer, Numeric, String
from sqlalchemy.dialects.postgresql import ARRAY, JSONB

from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository


class PipedrivePgRepository(SqlRepository):
    """
    Pipedrive PostgreSQL Analytics DB repo
    """

    TABLE = SqlTable(schema_name="analytics", table_name="pipedrive")
    TABLE_DTYPES = {
        "new_sales_manager": String,
        "new_sales_manager_email": String,
        "merchant_id": Integer,
        "merchant_name": String,
        "primary_email": String,
        "all_emails": JSONB,
        "account_manager": String,
        "account_manager_email": String,
        "account_name": String,
        "first_name": String,
        "last_name": String,
        "contact_roles": <PERSON><PERSON><PERSON>(String),
        "has_performance_role": <PERSON>olean,
        "live_selling": DateTime,
        "date_entered": Date,
        "probability_of_default": Numeric,
        "vat_number": String,
        "credit_score_on_monitor": String,
        "credit_score_when_live": String,
    }

    def reload_table(self, transformed: DataFrameWithSchema) -> int:
        """
        Reloads pipedrive Analytics DB table by transformed dataframe
        Converts Python dictionary type do Postgres json
        :param transformed: transformed dataset that matches `TABLE` schema
        :returns: Number of rows affected
        """

        return self.insert(transformed.dataframe_with_schema, self.TABLE, truncate=True, dtype=self.TABLE_DTYPES)

    def select_table(self) -> list[PipedriveTransformed]:
        """
        Select all rows from the pipedrive table.
        Converts Decimal to float for probability_of_default column.
        :returns: list of `PipedriveTransformed`
        """
        rows = self.select(self.TABLE)

        return [
            PipedriveTransformed(
                new_sales_manager=row.new_sales_manager,
                new_sales_manager_email=row.new_sales_manager_email,
                merchant_id=row.merchant_id,
                merchant_name=row.merchant_name,
                primary_email=row.primary_email,
                all_emails=row.all_emails,
                account_manager=row.account_manager,
                account_manager_email=row.account_manager_email,
                account_name=row.account_name,
                first_name=row.first_name,
                last_name=row.last_name,
                contact_roles=row.contact_roles,
                has_performance_role=row.has_performance_role,
                live_selling=row.live_selling,
                date_entered=row.date_entered,
                date_closed=row.date_closed,
                probability_of_default=float(row.probability_of_default) if row.probability_of_default else None,
                vat_number=row.vat_number,
                credit_score_on_monitor=row.credit_score_on_monitor,
                credit_score_when_live=row.credit_score_when_live,
            )
            for row in rows
        ]
