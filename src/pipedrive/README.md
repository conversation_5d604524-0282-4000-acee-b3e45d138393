# Pipedrive ETL
Pipedrive is a CRM tool used by supply team to store important information about suppliers.
This data can be extracted, transformed, and loaded (ETL) through their REST API.


The ETL process retrieves Pipedrive data, saves it to a data lake, and then loads it into a data warehouse and Analytics DB for further analysis.
It runs daily, at 7 am UTC.

## High level diagram

![Pipedrive ETL diagram](resources/pipedrive_diagram.png)

## Pipedrive API
Reference: https://developers.pipedrive.com/docs/api/v1

See the details in `PipedriveApiClient` in the code.

## Available tables
### BigQuery `analytics_raw.pipedrive_*`
The tables with following prefix contain data appended for each ETL run. Therefore, they contain the full history of all ETL runs.

To retrieve data for a specific ETL run, use the following queries, where `run_id` is an identifier of a given ETL run.
If you do not know the `run_id`, you can find it in the Cloud Functions logs for that ETL.

```sql
select * from analytics_raw.pipedrive_organizations where run_id = '20240802_070730886' limit 100;
select * from analytics_raw.pipedrive_organization_fields where run_id = '20240802_070730886' limit 100;
select * from analytics_raw.pipedrive_persons where run_id = '20240802_070730886' limit 100;
select * from analytics_raw.pipedrive_person_fields where run_id = '20240802_070730886' limit 100;
select * from analytics_raw.pipedrive_deals where run_id = '20240802_070730886' limit 100;
select * from analytics_raw.pipedrive_deal_fields where run_id = '20240802_070730886' limit 100;
```
⚠️ WARNING: These tables are for debugging and experimental purposes only.
Do not use them for regular reporting.
The DE team cannot guarantee that the schema of these tables will not change in the future.
The tables are also not optimized from a performance perspective.
See the next section for information on accessing tables for regular reporting.

### BigQuery `analytics_transformed.pipedrive`
This table displays only the data from the most recent ETL run.
For example, if `20240802_070730886` represents the latest ETL run, you'll find the corresponding data here.
```sql
select * from analytics_transformed.pipedrive limit 100;
```
### PostgreSQL Analytics DB `analytics.pipedrive`
This table displays only the data from the most recent ETL run and contains exactly the same data as BigQuery `analytics_transformed.pipedrive`.
```sql
select * from analytics.pipedrive limit 100;
```
✅ BigQuery transformed and PostgreSQL Analytics DB tables can be used for regular reporting.

💡 If new fields from raw tables has to be added to the transformed tables, contact DE team at `Data Engineering - Analytics` MM channel.


## Stakeholders
@felix.schoenleitner
