import calendar
from dataclasses import dataclass
from datetime import date
from typing import List, Optional

from circuit_breaker_repository import CircuitBreakersPgRepository
from circuit_breakers_model import (
    CircuitBreakerColumns,
    ConditionRatePayload,
    CsatScorePayload,
    DefectRatePayload,
    MerchantTicketPayload,
    ResponseTimePayload,
)
from pandas import DataFrame
from zendesk_client import (
    NoZendeskUserForEmail,
    TicketPayload,
    ZendeskClient,
    ZendeskConfig,
)

from common.base import BaseEtl
from common.logger import get_logger
from common.pandas_utils import DataFrameWithSchema

logger = get_logger()


@dataclass(frozen=True, kw_only=True)
class Extracted:
    """
    Dataclass object for raw data extracted from different circuit breaker sources.

    This dataclass holds the raw DataFrameWithSchema objects for each type of circuit breaker:
    condition, defect, urgent, response time, and CSAT.

    Attributes:
        condition_raw: Raw data for condition rate circuit breakers
        defect_raw: Raw data for defect rate circuit breakers
        urgent_raw: Raw data for urgent score circuit breakers
        response_raw: Raw data for response time circuit breakers
        csat_raw: Raw data for CSAT score circuit breakers
    """

    condition_raw: DataFrameWithSchema
    defect_raw: DataFrameWithSchema
    urgent_raw: DataFrameWithSchema
    response_raw: DataFrameWithSchema
    csat_raw: DataFrameWithSchema


@dataclass(frozen=True)
class IsRunDay:
    condition_defect_response: Optional[bool] = None
    urgent: Optional[bool] = None
    csat: Optional[bool] = None


def create_run_day(current_date: Optional[date] = None) -> IsRunDay:
    """
    Which alert to run for today?

    - Find all Tuesdays (index 1) in the current month
    - Get the first and second Tuesday
    """
    today = current_date if current_date else date.today()

    # List of lists: List of weeks in the month, each containing 0 if day not in month, day of month otherwise
    # e.g. March 2025 > [[0, 0, 0, 0, 0, 1, 2], [3, 4, 5, 6, 7, 8, 9], [10, 11, 12, 13, 14, 15, 16] ...]
    month_calendar = calendar.monthcalendar(today.year, today.month)
    # Get the second item of the inside lists, if the item is not 0, i.e. not belonging to this month.
    tuesdays = [week[1] for week in month_calendar if week[1] != 0]

    # Convert day numbers to actual date objects for comparison
    tuesday_dates = [date(today.year, today.month, day) for day in tuesdays]

    return IsRunDay(
        # Any Tuesday of the month
        condition_defect_response=today in tuesday_dates,
        # First Tuesday of the month
        urgent=today == tuesday_dates[0],
        # Second Tuesday of the month
        csat=today == tuesday_dates[1],
    )


class CircuitBreakersEtl(BaseEtl):
    """
    ETL process for circuit breakers data.

    This class handles the extraction of circuit breaker data from the database,
    transformation into Zendesk ticket payloads, and loading (creating tickets)
    in the Zendesk system.
    """

    _config: ZendeskConfig

    def __init__(
        self,
        config: ZendeskConfig,
        zendesk_client: ZendeskClient,
        pg_repository: CircuitBreakersPgRepository,
        run_day: IsRunDay,
    ) -> None:
        """
        Initialize the CircuitBreakersEtl.

        :param config: ZenDesk's configuration settings.
        :param zendesk_client: Client for interacting with Zendesk API.
        :param pg_repository: Repository for accessing circuit breaker data from Postgres.
        :param run_day: Deciding which alert to run today
        """
        super().__init__(config)
        self.zendesk_client = zendesk_client
        self.pg_repository = pg_repository
        self.run_day = run_day

    def extract(self) -> Extracted:
        """
        Extract circuit breaker data from the database.

        Executes queries for all circuit breaker types (condition, defect, urgent,
        response time, and CSAT) and returns the results in an Extracted container.

        :returns: Container with extracted data for all circuit breaker types.
        """
        logger.info("EXTRACTING circuit breaker data...")

        condition_df = self.pg_repository.select_circuit_breaker_data(CircuitBreakersPgRepository.condition)
        logger.info(f"Condition data shape: {condition_df.dataframe.shape}")

        defect_df = self.pg_repository.select_circuit_breaker_data(CircuitBreakersPgRepository.defect)
        logger.info(f"Defect data shape: {defect_df.dataframe.shape}")

        urgent_df = self.pg_repository.select_circuit_breaker_data(CircuitBreakersPgRepository.urgent)
        logger.info(f"Urgent data shape: {urgent_df.dataframe.shape}")

        response_df = self.pg_repository.select_circuit_breaker_data(CircuitBreakersPgRepository.response)
        logger.info(f"Response data shape: {response_df.dataframe.shape}")

        csat_df = self.pg_repository.select_circuit_breaker_data(CircuitBreakersPgRepository.csat)
        logger.info(f"Csat data shape: {csat_df.dataframe.shape}")

        return Extracted(
            condition_raw=condition_df,
            defect_raw=defect_df,
            urgent_raw=urgent_df,
            response_raw=response_df,
            csat_raw=csat_df,
        )

    def transform(self, extracted: Extracted) -> List[TicketPayload]:
        """
        Transform extracted data into Zendesk ticket payloads.

        Creates appropriate ticket payloads for each circuit breaker type based on
        the extracted data.

        :param extracted: Container with extracted circuit breaker data.
        :returns: List of ticket payloads for all circuit breaker types.
        """
        logger.info("TRANSFORMING circuit breakers data into Zendesk payloads")

        # Gather the Zendesk payloads
        payloads = []

        if self.run_day.condition_defect_response:
            # Condition Rate
            condition = self._construct_payload(
                df=extracted.condition_raw.dataframe, payload_class=ConditionRatePayload
            )
            logger.info(f"\tWill send out {len(condition)} Zendesk Tickets for Condition Rate Alerts")
            payloads.extend(condition)

            # Defect Rate
            defect = self._construct_payload(df=extracted.defect_raw.dataframe, payload_class=DefectRatePayload)
            logger.info(f"\tWill send out {len(defect)} Zendesk Tickets for Defect Rate Alerts")
            payloads.extend(defect)

            # Response Time
            response = self._construct_payload(df=extracted.response_raw.dataframe, payload_class=ResponseTimePayload)
            logger.info(f"\tWill send out {len(response)} Zendesk Tickets for Response Time Alerts")
            payloads.extend(response)

        # CSAT Score
        if self.run_day.csat:
            csat = self._construct_payload(df=extracted.csat_raw.dataframe, payload_class=CsatScorePayload)
            logger.info(f"\tWill send out {len(csat)} Zendesk Tickets for CSAT Score Alerts")
            payloads.extend(csat)

        return payloads

    def load(self, transformed: List[TicketPayload]) -> None:
        """
        Load transformed data by creating tickets in Zendesk.

        Creates tickets in Zendesk for each payload. In production, tickets are created
        permanently. In non-production environments, tickets are created and then deleted in the Sandbox.

        :param transformed: List of ticket payloads to create in Zendesk.
        """
        logger.info(f"LOADING - Will send {len(transformed)} Zendesk Tickets")
        for payload in transformed:
            if self._config.is_production:
                self.zendesk_client.create_ticket(payload=payload, delete_after_creation=False)
            else:
                self.zendesk_client.create_ticket(payload=payload, delete_after_creation=True)

    def _construct_payload(self, df: DataFrame, payload_class: type[MerchantTicketPayload]) -> list[TicketPayload]:
        """
        Construct ticket payloads from a dataframe of merchant data.

        Groups merchants by ID and name, collects their email addresses, and creates
        appropriate ticket payloads for each merchant.
        Removes any rows with null/None/NaN emails as they cannot be used to create tickets.

        :param df: DataFrame containing merchant data.
        :param payload_class: The specific payload class to instantiate (e.g., ConditionRatePayload).
        :returns: List of ticket payloads for the specified circuit breaker type.
        """
        if df.empty:
            return []

        payloads = []

        # Remove rows with null/None/NaN emails
        df = df[df[CircuitBreakerColumns.PRIMARY_EMAIL].notnull()]

        # Group by merchant id and get list of emails
        df = (
            df.groupby([CircuitBreakerColumns.MERCHANT_ID, CircuitBreakerColumns.MERCHANT_NAME])[
                CircuitBreakerColumns.PRIMARY_EMAIL
            ]
            .apply(list)
            .reset_index()
        )

        for _, row in df.iterrows():
            # Find the relevant Zendesk user for the first email and add the others to CCs
            try:
                requester_id = self.zendesk_client.find_user_id_via_email(
                    email=row[CircuitBreakerColumns.PRIMARY_EMAIL][0]
                )
            except NoZendeskUserForEmail:
                logger.error(f"\t\tCan't find the relevant Zendesk User: {row[CircuitBreakerColumns.PRIMARY_EMAIL][0]}")
                continue

            email_ccs = (
                row[CircuitBreakerColumns.PRIMARY_EMAIL][1:]
                if len(row[CircuitBreakerColumns.PRIMARY_EMAIL]) > 1
                else []
            )

            payloads.append(
                payload_class(
                    merchant_name=row[CircuitBreakerColumns.MERCHANT_NAME],
                    merchant_id=row[CircuitBreakerColumns.MERCHANT_ID],
                    requester_id=requester_id,
                    email_ccs=email_ccs,
                )
            )

        return payloads
