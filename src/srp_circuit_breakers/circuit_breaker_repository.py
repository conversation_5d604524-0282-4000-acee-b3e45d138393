from typing import Optional

from circuit_breakers_model import CircuitBreakerSchema
from pandas import DataFrame
from sqlalchemy import Connection

from common.pandas_utils import DataFrameWithSchema
from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository


class CircuitBreakersPgRepository(SqlRepository):
    """
    Repository for accessing Analytics Postgres DB views for circuit breakers.

    This class provides a method to retrieve merchant data for all different circuit breakers
    from Analytics DB.
    """

    condition = SqlFunction(function_name="select_circuit_breakers_condition_score", schema_name="analytics")
    defect = SqlFunction(function_name="select_circuit_breakers_defect_score", schema_name="analytics")
    urgent = SqlFunction(function_name="select_circuit_breakers_urgent_score", schema_name="analytics")
    response = SqlFunction(function_name="select_circuit_breakers_response_time", schema_name="analytics")
    csat = SqlFunction(function_name="select_circuit_breakers_csat_score", schema_name="analytics")

    def select_circuit_breaker_data(
        self,
        circuit_breaker_function: SqlFunction,
        transaction: Optional[Connection] = None,
        args: Optional[list[str]] = None,
    ) -> DataFrameWithSchema:
        """
        Select merchant circuit breaker data from a view in Analytics DB.

        This method selects merchant data (id, name, primary_email) from views to retrieve circuit breaker data,
        returning it as a DataFrame with an associated schema.

        :param circuit_breaker_function: Circuit breaker table to be queried.
        :param transaction: Optional transaction connection.
        :param args: Optional parameters to pass to the SQL function:
            - condition_score: p_n_order_items, p_first_order_date, p_count_condition, p_condition_points_per_grading,
                               p_date, p_country
            - defect_score: sda_merchant_ids, date_join, p_date, p_n_order_items, p_count_defect, p_country,
                            p_defect_points, p_merchant_actual_defect_rate
            - urgent_score: p_n_order_items, p_date, p_country, p_mid_term_urgents_points
            - response_time: p_ten_h, p_n_order_items, p_country, p_date, messages_num
            - csat_score: p_date, p_n_order_items, p_csat_points, p_country
        :returns: DataFrameWithSchema containing merchant circuit breaker data.
        """
        result = self.select_from_function(function=circuit_breaker_function, transaction=transaction, args=args)
        return DataFrameWithSchema(dataframe=DataFrame(result), schema=CircuitBreakerSchema)
