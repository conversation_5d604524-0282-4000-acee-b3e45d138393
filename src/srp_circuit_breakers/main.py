from datetime import date

import functions_framework
from circuit_breaker_etl import CircuitBreakersEtl, create_run_day
from circuit_breaker_repository import CircuitBreakersPgRepository
from cloudevents.abstract import CloudEvent
from google.cloud.sql.connector import Connector
from zendesk_client import ZendeskClient, ZendeskConfig

from common.cloud_events import create_http_event
from common.consts import ANALYTICS_CLOUD_SQL_CF_READER_SECRET_ID
from common.logger import setup_logger
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.timing import timing
from common.typings import HttpResponse

zendesk_secret = get_secret(secret_id=ZendeskConfig.ZENDESK_SECRET, project_id=ZendeskConfig.project_id)
zendesk_config = ZendeskConfig.from_json(zendesk_secret)
zendesk_client = ZendeskClient(zendesk_config)
logger = setup_logger(zendesk_config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_CF_READER_SECRET_ID, zendesk_config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)

is_run_day = create_run_day()


@timing(logger, "srp_circuit_breakers")
@functions_framework.http
def run(_: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    Execute the Circuit Breakers ETL process.

    This function is the entry point for the circuit breakers pipeline.
    It sets up the database connection, initializes the repository and ETL components,
    and runs the complete ETL process to create Zendesk tickets for merchants with circuit breaker alerts.
    """
    logger.info("Starting Circuit Breakers ETL...")
    with Connector() as connector:
        pg_repo = CircuitBreakersPgRepository(db_config=pg_config, connector=connector)
        etl = CircuitBreakersEtl(
            config=zendesk_config,
            zendesk_client=zendesk_client,
            pg_repository=pg_repo,
            run_day=is_run_day,
        )
        etl.run()

    return HttpResponse()


if __name__ == "__main__":
    # Local DB
    pg_config = DatabaseConfig()

    # Override run day (4 is first Tuesday of March 2025, 11 is the second, 18 is the third)
    is_run_day = create_run_day(current_date=date(year=2025, month=3, day=18))

    # Will send (and delete) messages to Zendesk Sandbox refurbedhelp1714995320, if any.
    run(create_http_event("Running locally..."))
