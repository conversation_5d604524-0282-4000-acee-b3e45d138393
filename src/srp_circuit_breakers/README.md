# Circuit breakers
GitLab:
- https://gitlab.com/groups/refurbed/analytics-and-ds/-/epics/26

Notion project + Supplier Happiness page about it:
- Specifications: https://www.notion.so/refurbed/New-Zendesk-Circuit-Breakers-specifications-18bb6a8983ab8071b29ee65aa13413ea?pvs=4
- https://www.notion.so/refurbed/Circuit-Breakers-cd1d91b241b24dd794a0ceb88afe6c98
- https://www.notion.so/refurbed/Workflow-Circuit-Breaker-7c65cbd2bdb64580890e391842d271af

# Alerts
- Every Tuesday
  - Condition rate
  - Defect rate
  - Response time
- Every first Tuesday of the month
  - Urgent score
- Every second Tuesday of the month
  - CSAT score

# Relevant tests and how to test locally-sandbox
- Relevant tests
  - `tests/unit/test_srp_circuit_breakers`
  - `tests/unit/zendesk_client`
  - `tests/integration/local_sql/test_srp_circuit_breakers` (requires `make pg-reboot`)
  - `tests/integration/zendesk_client`
- How to test locally
  - Run tests/integration/local_sql/test_srp_circuit_breakers (requires `make pg-reboot`)
    - with `transaction.rollback()` commented out, so that the last test data remains (currently is condition test data)
  - Run src/srp_circuit_breakers/main.py
    - it should retrieve the test data for a Condition alert and create + delete a Zendesk ticket in the sandbox

# How to controllably send out alerts from local run
- Use-case Wednesday 26 March 2025
  - In the initial phase of this project we set out to send only condition rate alerts ad-hoc
  - Plan was to send only 1-2 seller alerts to merchants so that Seller Performance can review and then send the rest.
    - MM thread in Seller Performance channel: https://ozwiena.refurbed.io/refurbed/pl/bg1p7coiwpnetdttjc34b8nbdo
- Execution (temporary changes which were then rolled-back)
  - Change the targeted secrets from `main.py`
    - `zendesk_secret = get_secret(secret_id=ZendeskConfig.ZENDESK_SECRET, project_id="refb-analytics")`
    - `pg_secret = get_secret(ANALYTICS_CLOUD_SQL_CF_READER_SECRET_ID, "refb-analytics")`
  - Comment out the local DB in `__main__` of `main.py` and set desired day (Tuesday)
    - `# pg_config = DatabaseConfig()`
    - `is_run_day = create_run_day(current_date=date(year=2025, month=3, day=25))`
  - Pretend that this is production in `src/common/config.py`
    - ` def is_production(self) -> bool: return self.env.lower() == DEFAULT_ENV`
  - Comment out the `Defect Rate` and `Response Time` transformations in `circuit_breakers_etl.py` > `trasnform()`
    - Didn't have to comment out `Urgent Score` and `CSAT Score` since it wasn't first or second Tuesday of month
  - Modify the `circuit_breakers_etl.py` > `load()` function as needed for the testing
    - for example added `logger.info(f"Loading Zendesk Ticket: {payload.subject}")` and commented out the `self.zendesk_client.create_ticket` part to see what would be sent out
    - afterwards, activated the `create_ticket` and added `break` after the first alert send-out to limit test to 1 alert send-out
    - then added condition to not resend the same alerts like
      - `if "Marycom" in payload.subject or "Click&Carry S.R.L" in payload.subject: continue`

# Brand IDs
- Adding here since only way I found was to programmatically retrieve them
- Code in `tests/integration/zendesk_client`
- Production
```text
[
    {
        "id": 24502339749661,
        "name": "Commission"
    },
    {
        "id": 20089463411229,
        "name": "Finance"
    },
    {
        "id": 20089409107101,
        "name": "Listing"
    },
    {
        "id": 20089364155421,
        "name": "Merchant Relations"
    },
    {
        "id": 19285405048349,
        "name": "Merchant Support"
    },
    {
        "id": 8036713936925,
        "name": "Refurbed"
    },
    {
        "id": 16700572973213,
        "name": "Refurbed AT"
    },
    {
        "id": 16860556969885,
        "name": "Refurbed BE"
    },
    {
        "id": 16860576948893,
        "name": "Refurbed CH"
    },
    {
        "id": 16860558422173,
        "name": "Refurbed CZ"
    },
    {
        "id": 16790613050141,
        "name": "Refurbed DE"
    },
    {
        "id": 16700414824349,
        "name": "Refurbed DK"
    },
    {
        "id": 16700454294813,
        "name": "Refurbed ES"
    },
    {
        "id": 17124952166941,
        "name": "Refurbed FI"
    },
    {
        "id": 16700457942173,
        "name": "Refurbed FR"
    },
    {
        "id": 16474424372509,
        "name": "Refurbed IE"
    },
    {
        "id": 16700435248413,
        "name": "Refurbed IT"
    },
    {
        "id": 16700417462813,
        "name": "Refurbed NL"
    },
    {
        "id": 16700482805021,
        "name": "Refurbed PL"
    },
    {
        "id": 16860575148701,
        "name": "Refurbed PT"
    },
    {
        "id": 16700437756445,
        "name": "Refurbed SE"
    },
    {
        "id": 16700512615965,
        "name": "Refurbed SI"
    },
    {
        "id": 16700497824925,
        "name": "Refurbed SK"
    },
    {
        "id": 20089507262749,
        "name": "Supplier performance"
    },
    {
        "id": 20089429949085,
        "name": "Supplier Services"
    }
]
```
- Sandbox
```text
[
    {
        "id": 24396848440477,
        "name": "Commission"
    },
    {
        "id": 20089884381597,
        "name": "Finance"
    },
    {
        "id": 20089918575133,
        "name": "Listing"
    },
    {
        "id": 20089980079389,
        "name": "Merchant Relations"
    },
    {
        "id": 19285375932573,
        "name": "Merchant Support"
    },
    {
        "id": 18669456211357,
        "name": "Refurbed AT"
    },
    {
        "id": 18669440508317,
        "name": "Refurbed BE"
    },
    {
        "id": 18669472955293,
        "name": "Refurbed CH"
    },
    {
        "id": 18669461522205,
        "name": "Refurbed CZ"
    },
    {
        "id": 18669501646109,
        "name": "Refurbed DE"
    },
    {
        "id": 18669488393117,
        "name": "Refurbed DK"
    },
    {
        "id": 18669440886685,
        "name": "Refurbed ES"
    },
    {
        "id": 18669488495517,
        "name": "Refurbed FI"
    },
    {
        "id": 18669501869469,
        "name": "Refurbed FR"
    },
    {
        "id": 18669473352989,
        "name": "Refurbed IE"
    },
    {
        "id": 18669441147805,
        "name": "Refurbed IT"
    },
    {
        "id": 18669462051485,
        "name": "Refurbed NL"
    },
    {
        "id": 18669441277085,
        "name": "Refurbed PL"
    },
    {
        "id": 18669502162205,
        "name": "Refurbed PT"
    },
    {
        "id": 18669457046685,
        "name": "Refurbed SE"
    },
    {
        "id": 18669502276509,
        "name": "Refurbed SI"
    },
    {
        "id": 18669489045917,
        "name": "Refurbed SK"
    },
    {
        "id": 18669468996253,
        "name": "sandbox-refurbed"
    },
    {
        "id": 20089850658973,
        "name": "Supplier performance"
    },
    {
        "id": 20089900698525,
        "name": "Supplier Services"
    }
]
```
