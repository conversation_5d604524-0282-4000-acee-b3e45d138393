from dataclasses import dataclass, field
from functools import cached_property
from http import HTTPStatus
from typing import Any, Optional

import requests

from common.logger import Config, get_logger

logger = get_logger()
base_config = Config()


@dataclass(frozen=True, kw_only=True)
class ZendeskConfig(Config):
    """
    Zendesk client configuration:
    - email: The email address used for authentication with the Zendesk API.
    - subdomain: The subdomain for the Zendesk account (e.g., sandbox or production).
    - token: The API token used for authentication with the Zendesk API.
    - headers: A dictionary containing HTTP headers for API requests, defaulting to JSON content type.
    """

    ZENDESK_SECRET: str = "zendesk"

    email: str
    subdomain: str
    token: str
    headers: dict[str, str] = field(default_factory=lambda: {"Content-Type": "application/json"})

    @cached_property
    def user(self) -> str:
        """Used in authentication, returns the user identifier for the API."""
        return self.email + "/token"

    @cached_property
    def base_url(self) -> str:
        """Constructs the base URL for the Zendesk API."""
        return f"https://{self.subdomain}.zendesk.com/api/v2"

    @cached_property
    def create_ticket_url(self) -> str:
        """Constructs the URL for creating tickets in the Zendesk API."""
        return f"{self.base_url}/tickets.json"

    @cached_property
    def user_url(self) -> str:
        """Constructs the URL for finding users in the Zendesk API."""
        return f"{self.base_url}/users/search.json"

    @cached_property
    def ticket_url(self) -> str:
        """Constructs the URL for tickets in the Zendesk API."""
        return f"{self.base_url}/tickets/"


class MissingTicketField(ValueError):
    """Custom exception raised when required ticket fields are missing."""


class NoZendeskUserForEmail(ValueError):
    """Custom exception raised when no Zendesk user found for email."""


class TicketCreationError(Exception):
    """Custom exception raised when ticket creation fails."""


class TicketDeletionError(Exception):
    """Custom exception raised when ticket deletion fails."""


class TicketPayload:
    """
    Represents the payload for creating a Zendesk ticket.

    Attributes:
        brand_id (int): The ID of the brand the ticket belongs to
        ticket_form_id (int): The ID of the ticket form to use
        subject (str): The subject of the ticket
        requester_id (int): The ID of the user requesting the ticket
        submitter_id (int): The ID of the user submitting the ticket
        email_ccs (list[str]): List of email addresses to CC on the ticket
        body (str): The body content of the ticket

        -- Ticket fields
        primary_supplier_services_topic_field_id (int): The field id of the ticket field
        primary_supplier_services_topic (str): The topic category for supplier services
        supplier_id_field_id (int): The field id of the ticket field
        supplier_id (str): The ID of the supplier in the format "supplier_<id>"
    """

    def __init__(
        self,
        brand_id: int,
        ticket_form_id: int,
        subject: str,
        requester_id: int,
        submitter_id: int,
        body: str,
        email_ccs: Optional[list[str]] = None,
        primary_supplier_services_topic_field_id: Optional[int] = None,
        primary_supplier_services_topic: Optional[str] = None,
        supplier_id_field_id: Optional[int] = None,
        supplier_id: Optional[str] = None,
    ):
        """Initialize a new ticket payload."""
        self.brand_id = brand_id
        self.ticket_form_id = ticket_form_id
        self.subject = subject
        self.requester_id = requester_id
        self.submitter_id = submitter_id
        self.email_ccs = email_ccs if email_ccs is not None else []
        self.body = body
        self.primary_supplier_services_topic_field_id = primary_supplier_services_topic_field_id
        self.primary_supplier_services_topic = primary_supplier_services_topic
        self.supplier_id_field_id = supplier_id_field_id
        self.supplier_id = supplier_id

    def to_dict(
        self,
    ) -> dict[str, Any]:
        """
        Converts the TicketPayload instance into a dictionary format
        suitable for the Zendesk API ticket creation request.

        :returns: A dictionary formatted for the Zendesk API.
        """
        payload = {
            "ticket": {
                "brand_id": self.brand_id,
                "ticket_form_id": self.ticket_form_id,
                "subject": self.subject,
                "requester_id": self.requester_id,
                "submitter_id": self.submitter_id,
                "description": self.body,
                "fields": {
                    self.primary_supplier_services_topic_field_id: self.primary_supplier_services_topic,
                    self.supplier_id_field_id: self.supplier_id,
                },
                # The Zendesk API requires email_ccs to be a list of user_email objects (not just strings)
                "email_ccs": [{"user_email": cc_email} for cc_email in self.email_ccs],
            }
        }
        return payload


class ZendeskClient:
    def __init__(self, config: ZendeskConfig):
        """
        Initialize the ZendeskClient with the given configuration.

        :param config: Configuration object containing Zendesk credentials and settings.
        """
        self._config = config
        self.auth = (self._config.user, self._config.token)

    @staticmethod
    def _validate_ticket_payload(payload: TicketPayload) -> None:
        """
        Validates that all required fields are present in the ticket payload.

        :param payload: The ticket payload to validate
        :raises MissingTicketField: If any required fields are missing
        """
        missing_fields = []
        if not payload.brand_id:
            missing_fields.append("brand_id")
        if not payload.ticket_form_id:
            missing_fields.append("ticket_form_id")
        if not payload.requester_id:
            missing_fields.append("requester_id")
        if not payload.submitter_id:
            missing_fields.append("submitter_id")
        if not payload.subject:
            missing_fields.append("subject")
        if not payload.body:
            missing_fields.append("body")

        if missing_fields:
            raise MissingTicketField(f"Not all requirements are filled in: {', '.join(missing_fields)} are required.")

    def create_ticket(self, payload: TicketPayload, delete_after_creation: bool = False) -> int | None:
        """
        Create a Zendesk ticket.

        :param payload: An instance of TicketPayload containing ticket details.
        :param delete_after_creation: Flag indicating whether to delete the ticket after creation.
        :returns: Ticket ID or None if the ticket gets deleted.
        """
        # Validate the payload
        self._validate_ticket_payload(payload)

        # Try to create the ticket
        try:
            response = requests.post(
                self._config.create_ticket_url, json=payload.to_dict(), headers=self._config.headers, auth=self.auth
            )
        except Exception as e:
            raise TicketCreationError(
                f"Failed to create ticket in '{self._config.subdomain}' due to a requests error: {e}"
            ) from e

        # Handle the response
        if response.status_code != HTTPStatus.CREATED:
            raise TicketCreationError(
                f"Failed to create ticket in '{self._config.subdomain}': {response.status_code} - {response.text}"
            )

        # Process successful response
        ticket_id = response.json()["ticket"]["id"]
        logger.info(
            f"Ticket https://{self._config.subdomain}.zendesk.com/agent/tickets/{ticket_id} "
            f"created successfully in '{self._config.subdomain}'."
        )

        if delete_after_creation:
            self.delete_ticket(ticket_id)
            return None

        return ticket_id

    def find_user_id_via_email(self, email: str) -> int:
        """
        Find a Zendesk user ID via email address.

        :param email: The email address of the Zendesk user.
        :returns: The Zendesk user ID.
        """
        # Lower given email
        email = email.lower()

        response = requests.get(
            self._config.user_url,
            params={"query": email},
            headers=self._config.headers,
            auth=self.auth,
        )

        if response.status_code != HTTPStatus.OK:
            raise NoZendeskUserForEmail(
                f"Failed to search for user with email {email}: {response.status_code} - {response.text}"
            )

        users = response.json().get("users", [])
        if not users:
            raise NoZendeskUserForEmail(f"No user found with email {email}")

        # Find the exact match for the email
        for user in users:
            if user.get("email") == email:
                return user["id"]

        raise NoZendeskUserForEmail(f"No user found with exact email match for {email}")

    def delete_ticket(self, ticket_id: int) -> None:
        """
        Delete a Zendesk ticket.

        Sends a DELETE request to the Zendesk API to remove the specified ticket.
        Raises TicketDeletionError if the deletion fails.

        :param ticket_id: The ID of the ticket to delete.
        """
        response = requests.delete(
            f"{self._config.ticket_url}{str(ticket_id)}.json",
            headers=self._config.headers,
            auth=self.auth,
        )

        if response.status_code == HTTPStatus.NO_CONTENT:
            logger.info(f"Ticket {ticket_id} deleted successfully.")
        else:
            raise TicketDeletionError(f"Failed to delete ticket {ticket_id}: {response.status_code} - {response.text}")

    def get_ticket(self, ticket_id: int) -> dict[str, Any]:
        """
        Retrieve a Zendesk ticket by its ID.

        Sends a GET request to the Zendesk API to fetch the specified ticket's details.

        :param ticket_id: The ID of the ticket to retrieve.
        :returns: Dictionary containing the ticket data from Zendesk.
        :raises: ValueError if the ticket retrieval fails.
        """
        response = requests.get(
            f"{self._config.ticket_url}{str(ticket_id)}.json",
            headers=self._config.headers,
            auth=self.auth,
        )

        if response.status_code != HTTPStatus.OK:
            raise ValueError(f"Failed to get ticket {ticket_id}: {response.status_code} - {response.text}")

        if ticket_data := response.json().get("ticket"):
            logger.info(f"Successfully retrieved ticket {ticket_id}.")
            return ticket_data
        else:
            raise ValueError(f"No ticket found with ID {ticket_id}")
