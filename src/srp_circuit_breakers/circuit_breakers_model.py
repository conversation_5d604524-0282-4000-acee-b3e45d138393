from abc import ABC, abstractmethod
from functools import cached_property

from pandas import Int32Dtype, StringDtype
from zendesk_client import TicketPayload

from common.config import Config
from common.pandas_utils import DataFrameSchema


class CircuitBreakersZendeskConfig(Config):
    """CONSTANTS -- Production vs Sandbox refurbedhelp1714995320

    Alert type identifiers for different circuit breaker categories
    These values are the same for both production and sandbox environments
    https://refurbedhelp1714995320.zendesk.com/admin/objects-rules/tickets/ticket-fields/20135351518237
    """

    condition: str = "supplier_performance__performance_alerts__condition_alert_"
    defect: str = "supplier_performance__performance_alerts__defect_alert_"
    response_time: str = "supplier_performance__performance_alerts__response_time_alert"
    urgent: str = "supplier_performance__performance_alerts__urgent_alert"
    csat: str = "supplier_performance__performance_alerts__csat_alert"

    @cached_property
    def brand_id_supplier_performance(self) -> int:
        """Brand ID for Supplier Performance in production and sandbox environments (check README)"""
        return 20089507262749 if self.is_production else 20089850658973

    @cached_property
    def submitter_id(self) -> int:
        """
        Submitter agent ID for Tanja Abdulrahman in production and sandbox environments
        Production: https://refurbedhelp.zendesk.com/admin/people/team/members/12584503437085#product-roles
        Sandbox: https://refurbedhelp1714995320.zendesk.com/admin/people/team/members/20062477064733#product-roles
        """
        return 12584503437085 if self.is_production else 20062477064733

    @cached_property
    def ticket_form_id_supplier_services(self) -> int:
        """
        Ticket form ID for Supplier Services in production and sandbox environments
        Production: https://refurbedhelp.zendesk.com/admin/objects-rules/tickets/ticket-forms/edit/20135455546653
        Sandbox: https://refurbedhelp1714995320.zendesk.com/admin/objects-rules/tickets/ticket-forms/edit/20135416808477
        """
        return 20135455546653 if self.is_production else 20135416808477

    @cached_property
    def primary_supplier_services_topic_field_id(self) -> int:
        """
        Field IDs for ticket properties in production and sandbox environments
        Production: https://refurbedhelp.zendesk.com/admin/objects-rules/tickets/ticket-fields
        Sandbox: https://refurbedhelp1714995320.zendesk.com/admin/objects-rules/tickets/ticket-fields
        """
        return 20135367167261 if self.is_production else 20135351518237

    @cached_property
    def supplier_field_id(self) -> int:
        return 21661886823709 if self.is_production else 23843349874589


class CircuitBreakerColumns:
    """
    Column definition for all circuit breaker view data.

    This class defines the standard column names used across all circuit breaker views.
    All views return the same columns: merchant_id, merchant_name, and primary_email.
    """

    MERCHANT_ID: str = "merchant_id"
    MERCHANT_NAME: str = "merchant_name"
    PRIMARY_EMAIL: str = "primary_email"


# Defines the expected schema for the circuit breaker data, including the data types for each column.
CircuitBreakerSchema = DataFrameSchema(
    data_types={
        CircuitBreakerColumns.MERCHANT_ID: Int32Dtype(),
        CircuitBreakerColumns.MERCHANT_NAME: StringDtype(),
        CircuitBreakerColumns.PRIMARY_EMAIL: StringDtype(),
    }
)


class MerchantTicketPayload(TicketPayload, CircuitBreakersZendeskConfig, ABC):
    """
    Abstract base class for merchant-specific ticket payloads.

    This class extends CircuitBreakersZendeskConstants and TicketPayload
    to include merchant-specific parameters and functionality.
    It provides a common structure for all merchant-related ticket types (condition rate,
    defect rate, response time, urgent score, and CSAT score) while allowing each subclass
    to define its specific content.

    Subclasses must implement the subject method to provide a specific subject line
    for each ticket type.
    """

    def __init__(self, merchant_name: str, merchant_id: int, requester_id: int, email_ccs: list[str]):
        """
        Initialize a merchant-specific ticket.

        This constructor sets up common fields for all merchant tickets and calls the parent
        TicketPayload constructor with appropriate values.

        :param merchant_name: Name of the merchant for the ticket.
        :param merchant_id: ID of the merchant.
        :param requester_id: ID of the person requesting the ticket in Zendesk.
        :param email_ccs: List of email addresses to CC on the ticket.
        """
        brand_id: int = self.brand_id_supplier_performance
        ticket_form_id: int = self.ticket_form_id_supplier_services
        submitter_id: int = self.submitter_id
        subject: str = self.get_subject(merchant_name)

        # Ticket fields
        primary_supplier_services_topic_field_id: int = self.primary_supplier_services_topic_field_id
        primary_supplier_services_topic: str = self.primary_supplier_services_topic
        supplier_field_id: int = self.supplier_field_id
        supplier_id: str = f"supplier_{merchant_id}"

        super().__init__(
            brand_id=brand_id,
            ticket_form_id=ticket_form_id,
            subject=subject,
            requester_id=requester_id,
            submitter_id=submitter_id,
            email_ccs=email_ccs,
            body=self.body,
            primary_supplier_services_topic_field_id=primary_supplier_services_topic_field_id,
            primary_supplier_services_topic=primary_supplier_services_topic,
            supplier_id_field_id=supplier_field_id,
            supplier_id=supplier_id,
        )

    @property
    @abstractmethod
    def primary_supplier_services_topic(self) -> str:
        """Abstract to get the primary_supplier_services_topic for the ticket."""

    @abstractmethod
    def get_subject(self, merchant_name: str) -> str:
        """Abstract to get the subject line for the ticket."""


class ConditionRatePayload(MerchantTicketPayload):
    """
    A specific ticket payload for condition rate notifications.
    Predefines several fields and provides a template for condition rate related tickets.
    """

    body: str = (
        "Dear Partner,\n\n"
        "Unfortunately, we noticed an underperformance in your product quality "
        "reflected in decreased condition score.\n\n"
        "Please note that your current score for this metric doesn’t meet our refurbed quality standards.\n\n"
        "We kindly ask you to review the affected orders, which are available in your Performance section in "
        "the Seller interface, in order to analyze and improve this issue. In case of no improvement, "
        "further action steps might follow.\n\n"
        "You can find more information in the [FAQ page]"
        "(https://www.notion.so/refurbed/Seller-Performance-related-FAQs-4add7925ad684485b77e27e452335ae7).\n\n"
        "If you have more questions, please reach out.\n\n"
    )

    @cached_property
    def primary_supplier_services_topic(self) -> str:
        return self.condition

    def get_subject(self, merchant_name: str) -> str:
        """
        Generate the subject line for condition rate notification tickets.

        :param merchant_name: Name of the merchant.
        :returns: Formatted subject line for the ticket.
        """
        return f"{merchant_name} - Condition Score Notification refurbed™"


class DefectRatePayload(MerchantTicketPayload):
    """
    A specific ticket payload for defect rate notifications.
    Predefines several fields and provides a template for defect rate related tickets.
    """

    body: str = (
        "Dear Partner,\n\n"
        "Unfortunately, we noticed an underperformance in your product quality reflected in decreased defect score.\n\n"
        "Please note that your current score for this metric doesn’t meet our refurbed quality standards. \n\n"
        "We kindly ask you to review the affected orders, which are available in your Performance section in "
        "the Seller interface, in order to analyze and improve this issue. In case of no improvement, "
        "further action steps might follow.\n\n"
        "You can find more information in the "
        "[FAQ page]"
        "(https://www.notion.so/refurbed/Seller-Performance-related-FAQs-4add7925ad684485b77e27e452335ae7).\n\n"
        "If you have more questions, please reach out.\n\n"
    )

    @cached_property
    def primary_supplier_services_topic(self) -> str:
        return self.defect

    def get_subject(self, merchant_name: str) -> str:
        """
        Generate the subject line for defect rate notification tickets.

        :param merchant_name: Name of the merchant.
        :returns: Formatted subject line for the ticket.
        """
        return f"{merchant_name} - Defect Score Notification refurbed™"


class ResponseTimePayload(MerchantTicketPayload):
    """
    A specific ticket payload for response time notifications.
    Predefines several fields and provides a template for response time related tickets.
    """

    body: str = (
        "Dear Partner,\n\n"
        "Unfortunately, we noticed that your response time score is below our refurbed standards and does not meet "
        "our customer service SLA.\n\n"
        "Please find in your Performance Report located in the Interface a list of your affected orders, "
        "which did not meet our SLA. We recommend analyzing these orders to identify areas for improvement and "
        "effectively address recurring issues.\n\n"
        "You can find more information in the "
        "[FAQ page]"
        "(https://www.notion.so/refurbed/Seller-Performance-related-FAQs-4add7925ad684485b77e27e452335ae7).\n\n"
    )

    @cached_property
    def primary_supplier_services_topic(self) -> str:
        return self.response_time

    def get_subject(self, merchant_name: str) -> str:
        """
        Generate the subject line for response time notification tickets.

        :param merchant_name: Name of the merchant.
        :returns: Formatted subject line for the ticket.
        """
        return f"{merchant_name} - Response Time Notification refurbed™"


class UrgentScorePayload(MerchantTicketPayload):
    """
    A specific ticket payload for urgent score notifications.
    Predefines several fields and provides a template for urgent score related tickets.
    """

    body: str = (
        "Dear Partner,\n\n"
        "Unfortunately, we noticed that your urgent score is below our marketplace threshold "
        "and does not meet our customer service standards.\n\n"
        "Please find a list of orders that are negatively impacting your urgent score in your Performance Report. "
        "We recommend analyzing these orders to identify areas for improvement and effectively "
        "address recurring issues.\n\n"
        "You can find more information in the "
        "[FAQ page](https://www.notion.so/Seller-Performance-related-FAQs-4add7925ad684485b77e27e452335ae7?pvs=21).\n\n"
        "If you have more questions, please reach out.\n\n"
        "Best Regards,\n"
        "Seller Performance Team"
    )

    @cached_property
    def primary_supplier_services_topic(self) -> str:
        return self.urgent

    def get_subject(self, merchant_name: str) -> str:
        """
        Generate the subject line for urgent score notification tickets.

        :param merchant_name: Name of the merchant.
        :returns: Formatted subject line for the ticket.
        """
        return f"{merchant_name} - Urgent Score Notification refurbed™"


class CsatScorePayload(MerchantTicketPayload):
    """
    A specific ticket payload for CSAT score notifications.
    Predefines several fields and provides a template for CSAT score related tickets.
    """

    body: str = (
        "Dear Partner,\n\n"
        "Unfortunately, we noticed that your CSAT score is below our marketplace standards. "
        "You can find a full list of the orders impacting this rating in the "
        "CSAT Score section of your Performance Report.\n\n"
        "For a detailed analysis of the factors contributing to this result, please check the CSAT section in your "
        "Service Insights Report. For further information on the CSAT score, please visit our FAQ pages.\n\n"
        "Performance Report: https://merchant.refurbed.com/performance\n\n"
        "Service Insights Report: https://merchant.refurbed.com/performance-insights/service-insights\n\n"
    )

    @cached_property
    def primary_supplier_services_topic(self) -> str:
        return self.csat

    def get_subject(self, merchant_name: str) -> str:
        """
        Generate the subject line for CSAT score notification tickets.

        :param merchant_name: Name of the merchant.
        :returns: Formatted subject line for the ticket.
        """
        return f"{merchant_name} - CSAT Score Notification refurbed™"
