/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipNrmExternalTables}" = "false" then -- noqa
        -- RAW: revenue_countries
        drop external table if exists analytics_raw.revenue_countries;
        create external table analytics_raw.revenue_countries
        with partition columns (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/crystal_ball/revenue_countries/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/crystal_ball/revenue_countries",
            require_hive_partition_filter = true
        );

        -- test: analytics-pipelines-staging-raw/crystal_ball/revenue_countries/year=2024/month=8
        select * from analytics_raw.revenue_countries
        where
            year = 2024 and month = 8 and day > 0
        order by paid_at, country
        limit 10;

        -- RAW: marketing_countries
        drop external table if exists analytics_raw.marketing_countries;
        create external table analytics_raw.marketing_countries
        with partition columns (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/crystal_ball/marketing_countries/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/crystal_ball/marketing_countries",
            require_hive_partition_filter = true
        );

        -- test: analytics-pipelines-staging-raw/crystal_ball/marketing_countries/year=2024/month=8
        select * from analytics_raw.marketing_countries
        where
            year = 2024 and month = 8 and day > 0
        order by date, country
        limit 10;

        -- TRANSFORMED: nrm
        drop external table if exists analytics_transformed.nrm;
        create external table analytics_transformed.nrm
        with partition columns (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-transformed/crystal_ball/nrm/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-transformed/crystal_ball/nrm",
            require_hive_partition_filter = true
        );

        -- test: analytics-pipelines-staging-transformed/crystal_ball/nrm/year=2024/month=8
        select * from analytics_transformed.nrm
        where
            year = 2024 and month = 8 and day = 31
        order by ds, unique_id
        limit 10;
    end if;
end;
