/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipShippingBillingExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_transformed.shipping_billing_charges;
        create external table analytics_transformed.shipping_billing_charges
        with partition columns (
            country string,
            provider string,
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-transformed/shipping_billing_charges/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-transformed/shipping_billing_charges",
            require_hive_partition_filter = true
        );

        -- test
        select * from analytics_transformed.shipping_billing_charges
        where
            country = "ES" and provider = "DHL" and year = 2024 and month = 4 and day = 29
        limit 10;
    end if;
end;
