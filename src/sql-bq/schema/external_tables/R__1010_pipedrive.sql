/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipPipedriveExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.pipedrive_organizations;
        create external table analytics_raw.pipedrive_organizations
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/pipedrive/*/organizations.parquet"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/pipedrive",
            require_hive_partition_filter = true
        );

        drop external table if exists analytics_raw.pipedrive_organization_fields;
        create external table analytics_raw.pipedrive_organization_fields
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/pipedrive/*/organization_fields.parquet"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/pipedrive",
            require_hive_partition_filter = true
        );

        drop external table if exists analytics_raw.pipedrive_persons;
        create external table analytics_raw.pipedrive_persons
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/pipedrive/*/persons.parquet"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/pipedrive",
            require_hive_partition_filter = true
        );

        drop external table if exists analytics_raw.pipedrive_person_fields;
        create external table analytics_raw.pipedrive_person_fields
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/pipedrive/*/person_fields.parquet"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/pipedrive",
            require_hive_partition_filter = true
        );
        drop external table if exists analytics_raw.pipedrive_deals;
        create external table analytics_raw.pipedrive_deals
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/pipedrive/*/deals.parquet"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/pipedrive",
            require_hive_partition_filter = true
        );

        drop external table if exists analytics_raw.pipedrive_deal_fields;
        create external table analytics_raw.pipedrive_deal_fields
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/pipedrive/*/deal_fields.parquet"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/pipedrive",
            require_hive_partition_filter = true
        );
    end if;
end;
