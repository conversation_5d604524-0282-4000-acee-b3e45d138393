/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/

begin
    if "${skipBackmarketExternalTables}" = "false" then -- noqa
        drop external table if exists analytics_transformed.backmarket_listing_products;
        create external table if not exists analytics_transformed.backmarket_listing_products
        with partition columns
        (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-transformed/backmarket/listing/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-transformed/backmarket/listing"
        );
    end if;
end;
