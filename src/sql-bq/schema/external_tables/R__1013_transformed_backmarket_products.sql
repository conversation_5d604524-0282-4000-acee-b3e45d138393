begin
    if "${skipBackmarketExternalTables}" = "false" then -- noqa
        drop external table if exists analytics_transformed.backmarket_products;
        create external table if not exists analytics_transformed.backmarket_products
        (
            product_name string,
            instance_features string,
            price float64,
            grade string,
            url string,
            country string,
            scrapped_at timestamp,
            extra_features string,
            title string,
            id1 string,
            id2 string,
            currency string,
            merchant_id int64
        )
        with partition columns
        (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-transformed/backmarket/products/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-transformed/backmarket/products"
        );

    end if;
end;
