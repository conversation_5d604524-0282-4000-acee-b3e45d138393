/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipAdtribaExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.adtriba_tcm;
        create external table analytics_raw.adtriba_tcm
        with partition columns (
            run_id string,
            `date` string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/adtriba/tcm*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/adtriba/tcm",
            require_hive_partition_filter = false
        );
    end if;
end;
