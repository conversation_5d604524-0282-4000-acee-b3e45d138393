begin
    if "${skipCategorySlugsMappingExternalTables}" = "false" then -- noqa
        -- RAW: category_slugs_mapping
        drop external table if exists analytics_raw.category_slugs_mapping;
        create external table analytics_raw.category_slugs_mapping
        with partition columns (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/ranking/category_slugs_mapping/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/ranking/category_slugs_mapping",
            require_hive_partition_filter = true
        );

        -- RAW: countries (different instance than platform-export)
        drop external table if exists analytics_raw.ranking_countries;
        create external table analytics_raw.ranking_countries
        with partition columns (
            year int64,
            month int64,
            day int64
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/ranking/countries/*"],
            hive_partition_uri_prefix
            = "gs://analytics-pipelines-${flyway:environment}-raw/ranking/countries",
            require_hive_partition_filter = true
        );

        -- test: analytics-pipelines-staging-raw/ranking/category_slugs_mapping/year=2025/month=5/day=2
        select *
        from analytics_raw.category_slugs_mapping
        where
            year = 2025 and month = 5 and day = 2
        limit 5;

        -- test: analytics-pipelines-staging-raw/ranking/countries/year=2025/month=5/day=2
        select *
        from analytics_raw.ranking_countries
        where
            year = 2025 and month = 5 and day = 2
        limit 5;

        -- KEY: category_id, language
        select
            category_id,
            language,
            if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
        from analytics_raw.category_slugs_mapping
        where
            year = 2025 and month = 5 and day = 2
        group by
            category_id,
            language
        having count(*) > 1;
    end if;
end;
