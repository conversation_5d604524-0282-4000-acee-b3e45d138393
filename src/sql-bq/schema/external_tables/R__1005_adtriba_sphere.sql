/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipAdtribaExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.adtriba_sphere;
        create external table analytics_raw.adtriba_sphere
        with partition columns (
            run_id string,
            brand string,
            country string,
            conversion_event string,
            device_type string
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/adtriba/sphere/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/adtriba/sphere",
            require_hive_partition_filter = false
        );

        -- test
        select *
        from analytics_raw.adtriba_sphere
        where
            run_id = "20240712_130530170"
            and brand = "refurbed"
            and country = "at"
            and conversion_event = "transaction"
            and device_type = "default"
        limit 10;
    end if;
end;
