begin
    if "${skipGoogleAdsTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.google_ads_costs;
        create external table analytics_raw.google_ads_costs
        with partition columns (
            run_id string,
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/google_ads/costs/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/google_ads/costs",
            require_hive_partition_filter = false,
            description = "External table mapped to parquet files and triggered from the google-ads-etl-job pipeline.\n"
            || "owner: data_engineering"
        );
    end if;
end;
