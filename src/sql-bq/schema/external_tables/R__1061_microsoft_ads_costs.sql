begin
    if "${skipMicrosoftAdsTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.microsoft_ads_costs;
        create external table analytics_raw.microsoft_ads_costs (
            campaign_name string not null,
            hour_of_day int not null,
            updated_at date not null,
            impressions int not null,
            spend float64 not null
        )
        with partition columns (
            run_id string
        )
        options (
            format = "PARQUET",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/microsoft_ads/costs/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/microsoft_ads/costs",
            require_hive_partition_filter = true,
            description
            = "External table mapped to parquet files and triggered from the microsoft-ads-etl-job pipeline.\n"
            || "owner: data_engineering"
        );

        -- test: analytics_raw.microsoft_ads_costs
        select * from analytics_raw.microsoft_ads_costs
        where run_id != "test-run"
        limit 5;
    end if;
end;
