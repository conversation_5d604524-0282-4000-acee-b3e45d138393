/**
# External tables
- https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement
*/
begin
    if "${skipShippingBillingExternalTables}" = "false" then -- noqa
        -- Drop is safe for external table!
        drop external table if exists analytics_raw.shipping_billing;
        create external table if not exists analytics_raw.shipping_billing
        with partition columns (
            country string,
            provider string,
            year int64,
            month int64,
            day int64
        )
        options (
            format = "CSV",
            field_delimiter = ",",
            uris = ["gs://analytics-pipelines-${flyway:environment}-raw/shipping_billing/*"],
            hive_partition_uri_prefix = "gs://analytics-pipelines-${flyway:environment}-raw/shipping_billing",
            require_hive_partition_filter = true,
            skip_leading_rows = 1,
            max_bad_records = 10000
        );

        -- test
        select * from analytics_raw.shipping_billing
        where
            country = "ES" and provider = "DHL" and year = 2024 and month = 4 and day = 29
        limit 10;
    end if;
end;
