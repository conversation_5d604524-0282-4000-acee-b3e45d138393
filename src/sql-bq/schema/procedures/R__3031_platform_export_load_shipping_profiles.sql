-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_shipping_profiles;
create or replace procedure analytics_transformed.load_platform_shipping_profiles (
) options (
    description = """Loads `analytics_transformed.shipping_profiles` target table
from the `platform_export.shipping_profiles` staging table."""
)
begin
    merge into analytics_transformed.shipping_profiles as t
    using (
        with staging as (
            select
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from platform_export.shipping_profiles as t
        )

        select
            id,
            -- Change tracking attributes
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Business fields
            merchant_id,
            name,
            ships_from
        from staging
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            merchant_id = s.merchant_id,
            name = s.name,
            ships_from = s.ships_from;
end;

drop procedure if exists analytics_transformed.check_shipping_profiles;
create or replace procedure analytics_transformed.check_shipping_profiles ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        valid_from,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.shipping_profiles
    group by id, valid_from
    having count(*) > 1;
end;

/*
select * from platform_export.shipping_profiles limit 10;
truncate table analytics_transformed.shipping_profiles;
select * from analytics_transformed.shipping_profiles limit 10;

call analytics_transformed.load_platform_shipping_profiles ();
call analytics_transformed.check_shipping_profiles ();
*/
