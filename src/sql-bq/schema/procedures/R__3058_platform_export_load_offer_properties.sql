-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_offer_properties;
create or replace procedure analytics_transformed.load_platform_offer_properties (
)
options (
    description = """Loads `analytics_transformed.offer_properties` target table
from the `analytics_raw.offer_properties` staging table."""
)
begin
    merge into analytics_transformed.offer_properties as t
    using (
        select *
        from platform_export.offer_properties
    ) as s
    on t.offer_id = s.offer_id
    when not matched by target then
        insert row
    when matched and (
        t.offer_valid_from != s.offer_valid_from
        or t.created_at != s.created_at
        or t.battery_condition != s.battery_condition
        or t.is_new_battery != s.is_new_battery
    ) then
        update set
            offer_valid_from = s.offer_valid_from,
            created_at = s.created_at,
            battery_condition = s.battery_condition,
            is_new_battery = s.is_new_battery
    ;
end;

drop procedure if exists analytics_transformed.check_offer_properties;
create or replace procedure analytics_transformed.check_offer_properties ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        offer_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.offer_properties
    group by offer_id
    having count(*) > 1;
end;

/*
select * from platform_export.offer_properties limit 10;
truncate table analytics_transformed.offer_properties;
select * from analytics_transformed.offer_properties limit 10;

call analytics_transformed.load_platform_offer_properties ();
call analytics_transformed.check_offer_properties ();
*/
