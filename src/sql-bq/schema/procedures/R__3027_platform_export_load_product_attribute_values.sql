-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_product_attribute_values;
create or replace procedure analytics_transformed.load_platform_product_attribute_values (
) options (
    description = """Loads `analytics_transformed.load_product_attribute_values` target table
from the `platform_export.product_attribute_values` staging table."""
)
begin
    merge into analytics_transformed.product_attribute_values as t
    using (
        select
            product_id,
            attribute_id,
            attribute_value_id
        from platform_export.product_attribute_values
    ) as s
    on t.product_id = s.product_id and t.attribute_value_id = s.attribute_value_id
    when not matched by target then
        insert row
    when matched and t.attribute_id != s.attribute_id then
        update set
            attribute_id = s.attribute_id
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_product_attribute_values;
create or replace procedure analytics_transformed.check_product_attribute_values ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        product_id,
        attribute_value_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.product_attribute_values
    group by
        product_id,
        attribute_value_id
    having count(*) > 1;
end;

/*
select * from platform_export.product_attribute_values limit 10;
truncate table analytics_transformed.product_attribute_values;
select * from analytics_transformed.product_attribute_values limit 10;

call analytics_transformed.load_platform_product_attribute_values ();
call analytics_transformed.check_product_attribute_values ();
*/
