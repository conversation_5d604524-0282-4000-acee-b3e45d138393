-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_product_categories;
create or replace procedure analytics_transformed.load_platform_product_categories (
) options (
    description = """Loads `analytics_transformed.product_categories` target table
from the `platform_export.product_categories` staging table."""
)
begin
    merge into analytics_transformed.product_categories as t
    using (
        select
            product_id,
            main_category_id,
            show_category_id
        from platform_export.product_categories
    ) as s
    on t.product_id = s.product_id and t.show_category_id = s.show_category_id
    when not matched by target then
        insert row
    when matched and t.main_category_id != s.main_category_id then
        update set main_category_id = s.main_category_id
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_product_categories;
create or replace procedure analytics_transformed.check_product_categories ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        product_id,
        show_category_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.product_categories
    group by
        product_id,
        show_category_id
    having count(*) > 1;
end;

/*
select * from platform_export.product_categories limit 10;
truncate table analytics_transformed.product_categories;
select * from analytics_transformed.product_categories limit 10;
call analytics_transformed.load_platform_product_categories ();
call analytics_transformed.check_product_categories ();
*/
