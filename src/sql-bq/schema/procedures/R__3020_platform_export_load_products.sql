-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_products;
create or replace procedure analytics_transformed.load_platform_products (
) options (
    description = """Loads `analytics_transformed.products` target table
from the `platform_export.products` staging table."""
)
begin
    merge into analytics_transformed.products as t
    using (
        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            category_id,
            listing_mode,
            `name`,
            name_en,
            slug
        from platform_export.products
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            category_id = s.category_id,
            listing_mode = s.listing_mode,
            name = s.name,
            name_en = s.name_en,
            slug = s.slug
    ;
end;

drop procedure if exists analytics_transformed.check_products;
create or replace procedure analytics_transformed.check_products ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.products
    group by id
    having count(*) > 1;
end;

/*
        select * from platform_export.products limit 10;
        truncate table analytics_transformed.products;
        select * from analytics_transformed.products limit 10;

        call analytics_transformed.load_platform_products ();
        call analytics_transformed.check_products ();
        */
