-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_product_rankings;
create or replace procedure analytics_transformed.load_platform_product_rankings (
)
options (
    description = """Loads `analytics_transformed.product_rankings` target table
from the `analytics_raw.product_rankings` staging table using MERGE statement."""
)
begin
    merge into analytics_transformed.product_rankings as t
    using (
        select
            -- PK
            product_id,
            category_id,
            market_country,
            -- Change tracking attributes
            updated_at,
            -- Rank fields
            rank,
            rank_b
        from platform_export.product_rankings
    ) as s
    on t.product_id = s.product_id
        and t.category_id = s.category_id
        and t.market_country = s.market_country
    when not matched by target then
        insert row
    when matched and s.updated_at > t.updated_at then
        update set
            rank = s.rank,
            rank_b = s.rank_b,
            updated_at = s.updated_at
    ;
end;

drop procedure if exists analytics_transformed.check_product_rankings;
create or replace procedure analytics_transformed.check_product_rankings ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        product_id,
        category_id,
        market_country,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.product_rankings
    group by product_id, category_id, market_country
    having count(*) > 1;
end;

/*
select * from platform_export.product_rankings limit 10;
truncate table analytics_transformed.product_rankings;
select * from analytics_transformed.product_rankings limit 10;

call analytics_transformed.load_platform_product_rankings ();
call analytics_transformed.check_product_rankings ();
*/
