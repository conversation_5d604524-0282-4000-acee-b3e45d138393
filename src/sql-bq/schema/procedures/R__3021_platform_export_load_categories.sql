-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_categories;
create or replace procedure analytics_transformed.load_platform_categories (
) options (
    description = """Loads `analytics_transformed.categories` target table
from the `platform_export.categories` staging table."""
)
begin
    merge into analytics_transformed.categories as t
    using (
        select
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            category_name,
            brand,
            category_type,
            product_category
        from platform_export.categories
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            -- Change tracking attributes
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            -- Supportive fields that ease merge
            modified_at = s.modified_at,
            is_active = s.is_active,
            -- Business fields
            category_name = s.category_name,
            brand = s.brand,
            category_type = s.category_type,
            product_category = s.product_category
    ;
end;

drop procedure if exists analytics_transformed.check_categories;
create or replace procedure analytics_transformed.check_categories ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.categories
    group by id
    having count(*) > 1;
end;

/*
select * from platform_export.categories limit 10;
truncate table analytics_transformed.categories;
select * from analytics_transformed.categories limit 10;

call analytics_transformed.load_platform_categories ();
call analytics_transformed.check_categories ();
*/
