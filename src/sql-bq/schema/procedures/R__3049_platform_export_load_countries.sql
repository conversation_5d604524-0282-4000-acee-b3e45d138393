-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_countries;
create or replace procedure analytics_transformed.load_platform_countries (
) options (
    description = """Loads `analytics_transformed.countries` target table
from the `platform_export.countries` staging table."""
)
begin
    merge into analytics_transformed.countries as t
    using (
        select
            t.code,
            t.name,
            t.main_language,
            t.currency,
            t.is_site
        from platform_export.countries as t
    ) as s
    on t.code = s.code
    when not matched by target then
        insert row
    when matched and (
        t.name != s.name
        or t.main_language != s.main_language
        or t.currency != s.currency
        or t.is_site != s.is_site
    ) then
        update set
            name = s.name,
            main_language = s.main_language,
            currency = s.currency,
            is_site = s.is_site;
end;

drop procedure if exists analytics_transformed.check_countries;
create or replace procedure analytics_transformed.check_countries ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        code,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.countries
    group by code
    having count(*) > 1;
end;

/*
select * from platform_export.countries limit 10;
truncate table analytics_transformed.countries;
select * from analytics_transformed.countries limit 10;

call analytics_transformed.load_platform_countries ();
call analytics_transformed.check_countries ();
*/
