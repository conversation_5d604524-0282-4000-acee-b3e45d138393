-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_orders;
create or replace procedure analytics_transformed.load_platform_orders (
) options (
    description = """Loads `analytics_transformed.orders` target table
from the `platform_export.orders` staging table."""
)
begin
    merge into analytics_transformed.orders as t
    using (
        select
            id,

            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,

            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,

            -- Business fields
            paid_at,
            country,
            state,
            is_released,
            user_id,
            partner_id,
            invoice_addr_id,
            invoice_addr_valid_from,
            shipping_addr_id,
            shipping_addr_valid_from,

            presentment_currency,
            settlement_currency,
            cast(presentment_to_settlement_exchange_rate as numeric)
                as presentment_to_settlement_exchange_rate,

            payment_provider,
            payment_info,
            payment_handle,

            payment_provider_category,
            parse_json(payment_provider_info) as payment_provider_info
        from platform_export.orders
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,

            paid_at = s.paid_at,
            country = s.country,
            state = s.state,
            is_released = s.is_released,

            user_id = s.user_id,
            partner_id = s.partner_id,
            invoice_addr_id = s.invoice_addr_id,
            invoice_addr_valid_from = s.invoice_addr_valid_from,
            shipping_addr_id = s.shipping_addr_id,
            shipping_addr_valid_from = s.shipping_addr_valid_from,

            presentment_currency = s.presentment_currency,
            settlement_currency = s.settlement_currency,
            presentment_to_settlement_exchange_rate = s.presentment_to_settlement_exchange_rate,

            payment_provider = s.payment_provider,
            payment_info = s.payment_info,
            payment_handle = s.payment_handle,

            payment_provider_category = s.payment_provider_category,
            payment_provider_info = s.payment_provider_info;
end;

drop procedure if exists analytics_transformed.check_orders;
create or replace procedure analytics_transformed.check_orders ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.orders
    group by id
    having count(*) > 1;
end;

/*
select * from platform_export.orders limit 10;
truncate table analytics_transformed.orders;
select * from analytics_transformed.orders limit 10;

call analytics_transformed.load_platform_orders ();
call analytics_transformed.check_orders ();
*/
