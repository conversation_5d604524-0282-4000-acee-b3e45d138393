-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_order_item_exchange_rate;
create or replace procedure analytics_transformed.load_platform_order_item_exchange_rate (
) options (
    description = """Loads `analytics_transformed.order_item_exchange_rate` target table
from the `platform_export.order_item_exchange_rate` staging table using MERGE statement.
Updates only when exchange_rate has changed."""
)
begin
    merge into analytics_transformed.order_item_exchange_rate as t
    using (
        select
            order_item_id,
            cast(exchange_rate as numeric) as exchange_rate
        from platform_export.order_item_exchange_rate
    ) as s
    on t.order_item_id = s.order_item_id
    when not matched by target then
        insert row
    when matched and t.exchange_rate is distinct from s.exchange_rate then
        update set
            exchange_rate = s.exchange_rate
    ;
end;

drop procedure if exists analytics_transformed.check_order_item_exchange_rate;
create or replace procedure analytics_transformed.check_order_item_exchange_rate ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        order_item_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_item_exchange_rate
    group by
        order_item_id
    having count(*) > 1;
end;

/*
select * from platform_export.order_item_exchange_rate limit 10;
truncate table analytics_transformed.order_item_exchange_rate;
select * from analytics_transformed.order_item_exchange_rate limit 10;

call analytics_transformed.load_platform_order_item_exchange_rate ();
call analytics_transformed.check_order_item_exchange_rate ();
*/
