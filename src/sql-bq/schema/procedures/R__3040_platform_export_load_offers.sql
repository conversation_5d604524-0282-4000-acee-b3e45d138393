-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_offers;
create or replace procedure analytics_transformed.load_platform_offers (
) options (
    description = """Loads `analytics_transformed.offers` target table
from the `platform_export.offers` staging table."""
)
begin
    merge into analytics_transformed.offers as t
    using (
        with staging as (
            select
                t.*,
                analytics_transformed.is_infinity(t.valid_to) as is_active
            from platform_export.offers as t
        )

        select
            id,
            -- Change tracking attributes
            created_at,
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Business fields
            state,
            instance_id,
            merchant_id,
            warranty,
            stock,
            grading,
            hidden,
            tax_difference,
            shipping_profile_id,
            shipping_profile_valid_from,
            reference_currency_code,
            cast(reference_price as numeric) as reference_price,
            cast(reference_min_flex_price as numeric) as reference_min_flex_price,
            sku
        from staging
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            state = s.state,
            instance_id = s.instance_id,
            merchant_id = s.merchant_id,
            warranty = s.warranty,
            stock = s.stock,
            grading = s.grading,
            hidden = s.hidden,
            tax_difference = s.tax_difference,
            shipping_profile_id = s.shipping_profile_id,
            shipping_profile_valid_from = s.shipping_profile_valid_from,
            reference_currency_code = s.reference_currency_code,
            reference_price = s.reference_price,
            reference_min_flex_price = s.reference_min_flex_price,
            sku = s.sku
    ;
end;

drop procedure if exists analytics_transformed.check_offers;
create or replace procedure analytics_transformed.check_offers ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        valid_from,
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.offers
    group by valid_from, id
    having count(*) > 1;
end;

/*
select * from platform_export.offers limit 10;
truncate table analytics_transformed.offers;
select * from analytics_transformed.offers limit 10;

call analytics_transformed.load_platform_offers ();
call analytics_transformed.check_offers ();
*/
