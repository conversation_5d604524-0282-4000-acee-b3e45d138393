-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_category_slugs_mapping;
create or replace procedure analytics_transformed.load_category_slugs_mapping (
    p_year int64,
    p_month int64,
    p_day int64
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = """Loads `analytics_transformed.category_slugs_mapping` target table
from the source `analytics_raw.category_slugs_mapping` and `analytics_raw.ranking_countries`
for the given partition columns using `MERGE` statement.
Uses `distinct` as there could be duplicate files for the same timestamp."""
)
begin
    merge into analytics_transformed.category_slugs_mapping as t
    using (
        with countries as (
            select distinct *
            from
                analytics_raw.ranking_countries
            where
                `year` = p_year and `month` = p_month and `day` = p_day
        ),

        category_slugs_mapping as (
            select distinct *
            from analytics_raw.category_slugs_mapping
            where
                `year` = p_year and `month` = p_month and `day` = p_day
        )

        select distinct
            csm.category_id,

            c.code as market,
            c.main_language,
            csm.category_slug,

            csm.created_at,
            csm.updated_at,
            cast(null as timestamp) as deleted_at
        from
            countries as c
        inner join
            category_slugs_mapping as csm
        -- Note `de` is not present as a `slug_de` column,
        -- so use `slug` (without country code) as a default value for `de` markets.
        on c.main_language = case when csm.language = "" then "de" else csm.language end
    ) as s
    on t.category_id = s.category_id and t.market = s.market
    when not matched by target then
        insert row
    when not matched by source then
        update set
            deleted_at = cast(datetime(p_year, p_month, p_day, 0, 0, 0) as timestamp)
    when matched and (s.updated_at > t.updated_at or s.created_at > t.created_at) then
        update set
            language = s.main_language,
            category_slug = s.category_slug,
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at
    ;
end;

drop procedure if exists analytics_transformed.check_category_slugs_mapping;
create or replace procedure analytics_transformed.check_category_slugs_mapping (
    p_year int64,
    p_month int64,
    p_day int64
)
options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description = "Throws error if data anomalies are found."
)
begin
    -- Assert PK is enforced
    select
        category_id,
        market,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.category_slugs_mapping
    group by
        category_id,
        market
    having count(*) > 1;

    -- Assert each market is covered with the correct language
    select
        c.code,
        c.main_language,
        csm.market,
        csm.language,
        error("Duplicate rows found!") as duplicate_rows
    from
        analytics_raw.ranking_countries as c
    left join
        analytics_transformed.category_slugs_mapping as csm
    on c.code = csm.market
    where
        c.year = p_year and c.month = p_month and c.day = p_day -- noqa
        and (csm.market is null or c.main_language != csm.language);
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-raw/ranking/category_slugs_mapping/year=2025/month=5/day=2
        -- select * from analytics_transformed.category_slugs_mapping limit 5;
        truncate table analytics_transformed.category_slugs_mapping;

        call analytics_transformed.load_category_slugs_mapping (2025, 5, 8);
        call analytics_transformed.check_category_slugs_mapping (2025, 5, 8);
    end if;
end;
