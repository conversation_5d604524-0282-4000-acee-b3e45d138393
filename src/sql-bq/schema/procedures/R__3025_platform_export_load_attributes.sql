-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_attributes;
create or replace procedure analytics_transformed.load_platform_attributes (
) options (
    description = """Loads `analytics_transformed.attributes` target table
from the `platform_export.attributes` staging table using MERGE statement.
* Uses `farm_fingerprint` to generate a row_hash for change tracking.
* Does not delete rows not present in source to keep historical data."""
)
begin
    merge into analytics_transformed.attributes as t
    using (
        select
            -- PK
            attribute_id,
            attribute_value_id,
            -- Business fields
            attribute_name,
            attribute_value,
            `type`,
            filterable,
            `precision`,
            unit,
            value_bool,
            cast(value_numeric as numeric) as value_numeric,
            value_enum_id,
            parse_json(`value`) as `value`,
            -- Change tracking field
            farm_fingerprint(
                concat(
                    coalesce(attribute_name, ""),
                    coalesce(attribute_value, ""),
                    coalesce(`type`, ""),
                    coalesce(cast(filterable as string), ""),
                    coalesce(cast(`precision` as string), ""),
                    coalesce(unit, ""),
                    coalesce(cast(value_bool as string), ""),
                    coalesce(cast(value_numeric as string), ""),
                    coalesce(cast(value_enum_id as string), ""),
                    coalesce(`value`, "")
                )
            ) as row_hash
        from platform_export.attributes
    ) as s
    on t.attribute_id = s.attribute_id and t.attribute_value_id = s.attribute_value_id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            attribute_name = s.attribute_name,
            attribute_value = s.attribute_value,
            `type` = s.`type`,
            filterable = s.filterable,
            `precision` = s.`precision`,
            unit = s.unit,
            value_bool = s.value_bool,
            value_numeric = s.value_numeric,
            value_enum_id = s.value_enum_id,
            `value` = s.value,
            row_hash = s.row_hash
    ;
end;

drop procedure if exists analytics_transformed.check_attributes;
create or replace procedure analytics_transformed.check_attributes ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        attribute_id,
        attribute_value_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.attributes
    group by
        attribute_id,
        attribute_value_id
    having count(*) > 1;
end;

/*
select * from platform_export.attributes limit 10;
truncate table analytics_transformed.attributes;
select * from analytics_transformed.attributes limit 10;

call analytics_transformed.load_platform_attributes ();
call analytics_transformed.check_attributes ();
*/
