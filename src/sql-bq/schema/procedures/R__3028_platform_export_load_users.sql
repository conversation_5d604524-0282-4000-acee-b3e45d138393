-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_users;
create or replace procedure analytics_transformed.load_platform_users (
) options (
    description = """Loads `analytics_transformed.users` target table
from the `platform_export.users` staging table."""
)
begin
    merge into analytics_transformed.users as t
    using (
        select
            -- PK
            id,
            -- Change tracking attributes
            created_at,
            updated_at,
            deleted_at,
            -- Supportive fields that ease merge
            greatest(updated_at, coalesce(deleted_at, created_at)) as modified_at,
            if(deleted_at is null, true, false) as is_active,
            -- Business fields
            email,
            first_name,
            family_name,
            `type`,
            language,
            merchant_id
        from platform_export.users
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.modified_at > t.modified_at then
        update set
            created_at = s.created_at,
            updated_at = s.updated_at,
            deleted_at = s.deleted_at,
            modified_at = s.modified_at,
            is_active = s.is_active,
            email = s.email,
            first_name = s.first_name,
            family_name = s.family_name,
            `type` = s.`type`,
            language = s.language,
            merchant_id = s.merchant_id
    ;
end;

drop procedure if exists analytics_transformed.check_users;
create or replace procedure analytics_transformed.check_users ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.users
    group by id
    having count(*) > 1;
end;

/*
select * from platform_export.users limit 10;
truncate table analytics_transformed.users;
select * from analytics_transformed.users limit 10;

call analytics_transformed.load_platform_users ();
call analytics_transformed.check_users ();
*/
