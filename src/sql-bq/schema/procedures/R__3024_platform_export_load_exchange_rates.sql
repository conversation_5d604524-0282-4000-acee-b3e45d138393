-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_exchange_rates;
create or replace procedure analytics_transformed.load_platform_exchange_rates (
) options (
    description = """Loads `analytics_transformed.exchange_rates` target table
from the `platform_export.exchange_rates` staging table."""
)
begin
    merge into analytics_transformed.exchange_rates as t
    using (
        with staging as (
            select
                t.id,
                t.valid_from,
                t.valid_to,
                analytics_transformed.is_infinity(t.valid_to) as is_active,
                t.base,
                t.target,
                cast(t.rate as numeric) as rate
            from platform_export.exchange_rates as t
        )

        select
            id,
            -- Change tracking attributes
            valid_from,
            valid_to,
            -- Supportive fields that ease merge
            if(is_active, valid_from, greatest(valid_from, valid_to)) as modified_at,
            is_active,
            -- Business fields
            base,
            target,
            rate
        from staging
    ) as s
    on t.id = s.id and t.valid_from = s.valid_from
    when not matched by target then
        insert row
    when matched and s.valid_to != t.valid_to then
        update set
            valid_to = s.valid_to,
            modified_at = s.modified_at,
            is_active = s.is_active,
            base = s.base,
            target = s.target,
            rate = s.rate
    ;
end;

drop procedure if exists analytics_transformed.check_exchange_rates;
create or replace procedure analytics_transformed.check_exchange_rates ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        valid_from,
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.exchange_rates
    group by all
    having count(*) > 1;
end;

/*
select * from platform_export.exchange_rates limit 10;
truncate table analytics_transformed.exchange_rates;
select * from analytics_transformed.exchange_rates limit 10;

call analytics_transformed.load_platform_exchange_rates ();
call analytics_transformed.check_exchange_rates ();
*/
