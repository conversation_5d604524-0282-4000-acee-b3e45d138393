-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_order_item_offers;
create or replace procedure analytics_transformed.load_platform_order_item_offers (
) options (
    description = """Loads `analytics_transformed.order_item_offers` target table
from the `platform_export.order_item_offers` staging table using MERGE statement.
* Use `farm_fingerprint` to generate a row_hash for change tracking.
* Delete rows not present in the source index table."""
)
begin
    merge into analytics_transformed.order_item_offers as t
    using (
        select
            id, -- PK
            created_at,
            updated_at,
            order_id,
            offer_id,
            offer_valid_from,
            type,
            paid_at,
            state,
            country,
            presentment_currency,
            payment_provider,
            valid_to,
            instance_id,
            merchant_id,
            grading,
            warranty,
            battery_condition,
            is_new_battery,
            farm_fingerprint(
                concat(
                    coalesce(cast(created_at as string), ""),
                    coalesce(cast(updated_at as string), ""),
                    coalesce(cast(order_id as string), ""),
                    coalesce(cast(offer_id as string), ""),
                    coalesce(cast(offer_valid_from as string), ""),
                    coalesce(type, ""),
                    coalesce(cast(paid_at as string), ""),
                    coalesce(state, ""),
                    coalesce(country, ""),
                    coalesce(presentment_currency, ""),
                    coalesce(payment_provider, ""),
                    coalesce(cast(valid_to as string), ""),
                    coalesce(cast(instance_id as string), ""),
                    coalesce(cast(merchant_id as string), ""),
                    coalesce(grading, ""),
                    coalesce(cast(warranty as string), ""),
                    coalesce(battery_condition, ""),
                    coalesce(cast(is_new_battery as string), "")
                )
            ) as row_hash
        from platform_export.order_item_offers
    ) as s
    on t.id = s.id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            t.created_at = s.created_at,
            t.updated_at = s.updated_at,
            t.order_id = s.order_id,
            t.offer_id = s.offer_id,
            t.offer_valid_from = s.offer_valid_from,
            t.type = s.type,
            t.paid_at = s.paid_at,
            t.state = s.state,
            t.country = s.country,
            t.presentment_currency = s.presentment_currency,
            t.payment_provider = s.payment_provider,
            t.valid_to = s.valid_to,
            t.instance_id = s.instance_id,
            t.merchant_id = s.merchant_id,
            t.grading = s.grading,
            t.warranty = s.warranty,
            t.battery_condition = s.battery_condition,
            t.is_new_battery = s.is_new_battery,
            t.row_hash = s.row_hash
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_order_item_offers;
create or replace procedure analytics_transformed.check_order_item_offers ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_item_offers
    group by
        id
    having count(*) > 1;
end;

/*
select * from platform_export.order_item_offers limit 10;
truncate table analytics_transformed.order_item_offers;
select * from analytics_transformed.order_item_offers limit 10;

call analytics_transformed.load_platform_order_item_offers ();
call analytics_transformed.check_order_item_offers ();
*/
