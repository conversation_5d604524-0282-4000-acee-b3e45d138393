-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_instance_attribute_values;
create or replace procedure analytics_transformed.load_platform_instance_attribute_values (
) options (
    description = """Loads `analytics_transformed.instance_attribute_values` target table
from the `platform_export.instance_attribute_values` staging table."""
)
begin
    merge into analytics_transformed.instance_attribute_values as t
    using (
        select
            instance_id,
            attribute_id,
            attribute_value_id
        from platform_export.instance_attribute_values
    ) as s
    on t.instance_id = s.instance_id and t.attribute_value_id = s.attribute_value_id
    when not matched by target then
        insert row
    when matched and t.attribute_id != s.attribute_id then
        update set
            attribute_id = s.attribute_id
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_instance_attribute_values;
create or replace procedure analytics_transformed.check_instance_attribute_values ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        instance_id,
        attribute_value_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.instance_attribute_values
    group by
        instance_id,
        attribute_value_id
    having count(*) > 1;
end;

/*
select * from platform_export.instance_attribute_values limit 10;
truncate table analytics_transformed.instance_attribute_values;
select * from analytics_transformed.instance_attribute_values limit 10;

call analytics_transformed.load_platform_instance_attribute_values ();
call analytics_transformed.check_instance_attribute_values ();
*/
