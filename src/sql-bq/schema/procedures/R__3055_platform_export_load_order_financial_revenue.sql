-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_order_financial_revenue;
create or replace procedure analytics_transformed.load_platform_order_financial_revenue (
)
options (
    description = """Loads `analytics_transformed.order_financial_revenue` target table
from the `platform_export.order_financial_revenue` staging table using MERGE statement.
* Uses `farm_fingerprint` to generate a row_hash for change tracking.
* Deletes rows that are not present in the source table."""
)
begin
    merge into analytics_transformed.order_financial_revenue as t
    using (
        select
            -- PK
            order_item_id,
            -- Business fields
            order_id,
            cast(revenue as numeric) as revenue,
            cast(addon_revenue as numeric) as addon_revenue,
            cast(gmv as numeric) as gmv,
            cast(discount_eur as numeric) as discount_eur,
            cast(service_fee_revenue_eur as numeric) as service_fee_revenue_eur,
            -- Change tracking field
            farm_fingerprint(
                concat(
                    coalesce(cast(order_id as string), ""),
                    coalesce(cast(revenue as string), ""),
                    coalesce(cast(addon_revenue as string), ""),
                    coalesce(cast(gmv as string), ""),
                    coalesce(cast(discount_eur as string), ""),
                    coalesce(cast(service_fee_revenue_eur as string), "")
                )
            ) as row_hash
        from platform_export.order_financial_revenue
    ) as s
    on t.order_item_id = s.order_item_id
    when not matched by target then
        insert row
    when matched and s.row_hash is distinct from t.row_hash then
        update set
            t.order_id = s.order_id,
            t.revenue = s.revenue,
            t.addon_revenue = s.addon_revenue,
            t.gmv = s.gmv,
            t.discount_eur = s.discount_eur,
            t.service_fee_revenue_eur = s.service_fee_revenue_eur,
            t.row_hash = s.row_hash
    when not matched by source then
        delete
    ;
end;

drop procedure if exists analytics_transformed.check_order_financial_revenue;
create or replace procedure analytics_transformed.check_order_financial_revenue ()
options (
    description = "Throws error if duplicate rows or missing references are found."
)
begin
    select
        order_item_id,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.order_financial_revenue
    group by order_item_id
    having count(*) > 1;
end;

/*
select * from platform_export.order_financial_revenue limit 10;
truncate table platform_export.order_financial_revenue;
select * from analytics_transformed.order_financial_revenue limit 10;

call analytics_transformed.load_platform_order_financial_revenue();
call analytics_transformed.check_order_financial_revenue();
*/
