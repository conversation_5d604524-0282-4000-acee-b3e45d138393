-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_google_ads_costs;
create procedure analytics_transformed.load_google_ads_costs (
    p_run_id string
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description
    = """Loads `analytics_transformed.google_ads_costs` target table from the source `analytics_raw.google_ads_costs`
for the given run using `MERGE` statement. Uses `distinct` to avoid duplicates."""
)
begin
    merge into analytics_transformed.google_ads_costs as t
    using (
        with raw as (
            select distinct t.*
            from analytics_raw.google_ads_costs as t
            where
                run_id = p_run_id
                and cost_date is not null
                and cost_hour is not null
                and client_account is not null
                and campaign_name is not null
                and costs is not null
        )

        select
            date(cost_date) as cost_date,
            cost_hour,
            client_account,
            campaign_name,
            costs,
            updated_at
        from raw
    ) as s
    on t.cost_date = s.cost_date
        and t.cost_hour = s.cost_hour
        and t.client_account = s.client_account
        and t.campaign_name = s.campaign_name
    when not matched by target then
        insert row
    when matched and s.costs <> t.costs then
        update set
            costs = s.costs,
            updated_at = s.updated_at
    ;
end;

drop procedure if exists analytics_transformed.check_google_ads_costs;
create procedure analytics_transformed.check_google_ads_costs ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        cost_date,
        cost_hour,
        client_account,
        campaign_name,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.google_ads_costs
    group by
        cost_date,
        cost_hour,
        client_account,
        campaign_name
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        truncate table analytics_transformed.google_ads_costs;
        call analytics_transformed.load_google_ads_costs ("20250718_070125802");
        call analytics_transformed.check_google_ads_costs ();
    end if;
end;
