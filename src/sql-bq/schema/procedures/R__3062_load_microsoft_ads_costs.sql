-- <PERSON>way will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.load_microsoft_ads_costs;
create procedure analytics_transformed.load_microsoft_ads_costs (
    p_run_id string
) options (
    strict_mode = false,  -- Required to avoid `Cannot query over table without a filter` error.
    description
    = """Loads `analytics_transformed.microsoft_ads_costs` target table from the source
`analytics_raw.microsoft_ads_costs` for the given run using `MERGE` statement. Uses `distinct` to avoid duplicates."""
)
begin
    merge into analytics_transformed.microsoft_ads_costs as t
    using (
        with raw as (
            select distinct t.*
            from analytics_raw.microsoft_ads_costs as t
            where
                run_id = p_run_id
                and campaign_name is not null
                and impressions is not null
                and spend is not null
                and hour_of_day is not null
        )

        select
            campaign_name,
            hour_of_day,
            updated_at,
            impressions,
            spend
        from raw
    ) as s
    on t.campaign_name = s.campaign_name
        and t.hour_of_day = s.hour_of_day
        and t.updated_at = s.updated_at
    when not matched by target then
        insert row
    when matched and (s.spend != t.spend or s.impressions != t.impressions) then
        update set
            spend = s.spend,
            impressions = s.impressions
    ;
end;

drop procedure if exists analytics_transformed.check_microsoft_ads_costs;
create procedure analytics_transformed.check_microsoft_ads_costs ()
options (
    description = "Throws error if duplicate rows are found."
)
begin
    select
        campaign_name,
        hour_of_day,
        updated_at,
        if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
    from analytics_transformed.microsoft_ads_costs
    group by
        campaign_name,
        hour_of_day,
        updated_at
    having count(*) > 1;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        truncate table analytics_transformed.microsoft_ads_costs;
        call analytics_transformed.load_microsoft_ads_costs ("20250718_070125802");
        call analytics_transformed.check_microsoft_ads_costs ();
    end if;
end;
