-- Flyway will re-run this script if ${changeReason} is updated
drop procedure if exists analytics_transformed.count_nrm_rows;
create or replace procedure analytics_transformed.count_nrm_rows (
    p_year int64,
    p_month int64,
    p_day int64
) options (
    -- Required to avoid `Cannot query over table 'xyz' without a filter` error.
    strict_mode = false
)
/*
    Count rows in analytics_transformed.nrm table for ETL purposes.
*/
begin
    select count(*) as row_count
    from analytics_transformed.nrm
    where
        -- `year` = 2024 and `month` = 6 and `day` > 0;
        `year` = p_year and `month` = p_month and `day` = p_day;
end;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: analytics-pipelines-staging-transformed/crystal_ball/nrm/year=2024/month=10/day=1
        call analytics_transformed.count_nrm_rows (2024, 10, 1);
    end if;
end;
