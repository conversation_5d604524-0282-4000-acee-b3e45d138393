-- Test script for Flyway upgrade FROM flyway/flyway:11.8.1
-- Flyway will re-run this script if ${changeReason} is updated
create schema if not exists test;
drop table if exists test.test_table;
create table if not exists test.test_table (id int64, one bool);
insert into test.test_table (id, one) values
(1, true),
(2, false),
(3, false),
(4, false),
(5, false);

drop procedure if exists test.merge_with_if_statement;
create procedure test.merge_with_if_statement ()
begin
    merge into test.test_table as t
    using (
        select
            id,
            if(id = 1, true, false) as one
        from
            test.test_table
    ) as s
    on t.id = s.id
    when matched then
        update set
            t.one = s.one
    ;
end;

call test.merge_with_if_statement ();
select * from test.test_table;
