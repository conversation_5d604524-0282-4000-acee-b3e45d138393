-- Flyway will re-run this script if ${changeReason} is updated
drop table function if exists google_analytics.select_category_view_item_clicks;
create table function google_analytics.select_category_view_item_clicks (p_event_date date) -- noqa
options (
    description = "Category view item list and item clicks function.\n"
    || "subject: Ranking,\n"
    || "notion: https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4,\n" -- noqa
    || "owner: data_engineering"
)
as (
    with ga_events as (
        select
            -- table partition
            _table_suffix as table_suffix,
            date(parse_date("%Y%m%d", _table_suffix)) as table_date,

            -- event_ fields
            date(timestamp_micros(event_timestamp)) as event_date,
            timestamp_micros(event_timestamp) as event_timestamp,
            event_name,
            dense_rank() over (
                partition by (
                    date(timestamp_micros(event_timestamp))
                    || (
                        select value.int_value
                        from unnest(events.event_params)
                        where key = "ga_session_id") || (
                        select value.int_value
                        from unnest(events.event_params)
                        where key = "ga_session_number"
                    )
                    || user_pseudo_id
                )
                order by
                    timestamp_micros(event_timestamp
                    )
            ) as event_rank,

            -- user / items
            user_pseudo_id,
            regexp_extract(
                (
                    select value.string_value from unnest(event_params)
                    where key = "page_location"
                ),
                r"(?:https?://)?(?:www\.)?[^/]+\.([a-z]+)"
            ) as market,
            (
                select value.string_value
                from unnest(event_params)
                where key = "page_location"
            ) as page_location,

            -- items
            safe_cast(items.item_id as int) as product_id,
            items.item_name as product_name,
            items.item_list_name,
            items.price,
            regexp_extract(
                (
                    select value.string_value from unnest(event_params)
                    where key = "page_location"
                ),
                r"/(?:c|sc|kc)/([^/?\s]+)"
            ) as category_slug,
            date(timestamp_micros(event_timestamp))
            || (
                select value.int_value from unnest(events.event_params)
                where key = "ga_session_id"
            )
            || (
                select value.int_value from unnest(events.event_params)
                where key = "ga_session_number"
            )
            || user_pseudo_id as sl_key
        from analytics_304326635.`events_fresh_*` as events,
            unnest(events.items) as items
        where
            -- technical date for partition pruning
                _table_suffix = format_date("%Y%m%d", p_event_date)
            -- event date of the actual view event
            and date(timestamp_micros(event_timestamp)) = p_event_date
            and privacy_info.analytics_storage = "Yes"
            and event_name in ("view_item_list", "view_item")
            and safe_cast(items.item_id as int) > 0
            and
            regexp_extract(
                (
                    select value.string_value from unnest(event_params)
                    where key = "page_location"
                ),
                r"/([a-z]|[a-z][a-z])/"
            ) in ("c", "sc", "kc", "p", "sp", "kp")
            and ((
                select coalesce(cast(value.int_value as string), value.string_value)
                from unnest(items.item_params)
                where key = "instance_id"
            ) is not null)
    )

    select
        ga_events.*,
        csm.category_id
    from ga_events
    left join
        analytics_transformed.category_slugs_mapping as csm
    on ga_events.market = csm.market
        and ga_events.category_slug = csm.category_slug
);

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: google_analytics.select_category_view_item_clicks;
        select *
        from google_analytics.select_category_view_item_clicks('2024-05-17')
        limit 5;
    end if;
end;
