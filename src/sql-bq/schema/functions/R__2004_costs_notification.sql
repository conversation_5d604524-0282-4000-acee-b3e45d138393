-- Flyway will re-run this script if ${changeReason} is updated
-- https://www.percentage-change-calculator.com/
drop function if exists maintenance.calculate_kpi;
create function maintenance.calculate_kpi(numerator float64, denominator float64) returns float64
as
(
    if(denominator > 0, round((numerator - denominator) / denominator * 100, 2), 0)
);

begin
    if ('${runTest}' = 'true') then -- noqa
        select maintenance.calculate_kpi(3.4, 0);
        select maintenance.calculate_kpi(3.4, 5);
        select maintenance.calculate_kpi(7.2, 5.7);
        select maintenance.calculate_kpi(1.3, 50.7);
        select maintenance.calculate_kpi(130.3, 0.7);
    end if;
end;


drop function if exists maintenance.calculate_kpi_indicator;
create function maintenance.calculate_kpi_indicator(numerator float64, denominator float64) returns string
as
(
    (
        select
            case
                when (t.kpi) = 0 then concat('■ ', t.kpi_str, '%')
                when t.kpi > 0 then concat('▲ ', t.kpi_str, '%')
                else concat('▼ ', t.kpi_str, '%')
            end
        from
            (
                select
                    maintenance.calculate_kpi(numerator, denominator) as kpi,
                    lpad(cast(maintenance.calculate_kpi(numerator, denominator) as string), 7) as kpi_str
            ) as t
    )
);

begin
    if ("${runTest}" = "true") then -- noqa
        select maintenance.calculate_kpi_indicator(3.4, 0);
        select maintenance.calculate_kpi_indicator(3.4, 5);
        select maintenance.calculate_kpi_indicator(7.2, 5.7);
        select maintenance.calculate_kpi_indicator(1.3, 50.7);
        select maintenance.calculate_kpi_indicator(130.3, 0.7);
    end if;
end;
