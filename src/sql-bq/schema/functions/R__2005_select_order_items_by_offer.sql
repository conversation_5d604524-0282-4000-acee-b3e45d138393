-- Flyway will re-run this script if ${changeReason} is updated
drop table function if exists analytics_reporting.select_order_items_by_offer;
create table function analytics_reporting.select_order_items_by_offer (start_date date, end_date date) -- noqa
options (
    description = "Counts released order items by offer for a given date range.\n"
    || "subject: Smart Pricing,\n"
    || "notion: https://www.notion.so/refurbed/Update-purchases_and_clicks-table-1dfb6a8983ab800d9d0fd253dff01e25,\n" -- noqa
    || "owner: data_engineering"
)
as (
select
    -- dimensions
    order_item_offers.order_id as transaction_id,
    instances.product_id as product_id,
    order_item_offers.offer_id as offer_id,
    order_item_offers.instance_id as instance_id,

    -- aggregates
    max(order_item_offers.warranty) as warranty,
    max(ships_to.delivery_max_days) as delivery_max_days,
    min(ships_to.delivery_min_days) as delivery_min_days,
    count(order_item_offers.id) as nbr_order_items
from
    analytics_transformed.order_item_offers as order_item_offers -- main steering query
inner join
    analytics_transformed.order_items as order_items -- to get shipping_profile_destination_id
on order_item_offers.id = order_items.id
left join
    analytics_transformed.instances as instances -- to get product_id
on order_item_offers.instance_id = instances.id
left join
    analytics_transformed.shipping_profile_destinations as ships_to
on  order_items.shipping_profile_destination_id = ships_to.id
and order_items.shipping_profile_destination_valid_from = ships_to.valid_from
where
    order_item_offers.type = 'offer'
    and order_item_offers.state in ('released', 'released.failed')
    and date(order_item_offers.created_at)
        between coalesce(start_date, current_date()-1) and coalesce(end_date, current_date())
group by
    all
);

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: select_order_items_by_offer
        select
            current_date() - 1 as report_date,
            t.*
        from analytics_reporting.select_order_items_by_offer(null, null) t
        limit 5;
    end if;
end;
