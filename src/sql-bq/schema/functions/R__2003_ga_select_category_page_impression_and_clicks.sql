-- Flyway will re-run this script if ${changeReason} is updated
drop table function if exists google_analytics.select_category_page_impression_and_clicks;
create table function google_analytics.select_category_page_impression_and_clicks (p_event_date date) -- noqa
options (
    description = "Provides aggregation in category to get impression and clicks of each product in market by day.\n"
    || "subject: Ranking,\n"
    || "notion: https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4,\n" -- noqa
    || "owner: data_engineering"
)
as (
    with item_click as (
        select
            user_pseudo_id,
            sl_key,
            product_id,
            event_timestamp,
            page_location,
            market,
            event_rank
        from google_analytics.category_view_item_clicks
        where
            event_date = p_event_date
        and event_name = 'view_item'
        group by all
    ),

    category_page_events as (
        select
            view_item_list.event_date,
            view_item_list.user_pseudo_id,
            view_item_list.sl_key,
            view_item_list.event_timestamp AS view_item_list_timestamp,
            view_item_list.category_id,
            view_item_list.category_slug,
            view_item_list.market,
            view_item_list.product_id,
            view_item_list.price,
            case
                when
                    (
                        (
                            item_click.page_location like '%/p/%'
                            or item_click.page_location like '%/sp/%'
                            or item_click.page_location like '%/kp/%'
                        )
                        and item_click.product_id = view_item_list.product_id
                        and item_click.event_rank - view_item_list.event_rank = 1
                    )
                    then 1
                else 0
            end as immediate_click
        from
            google_analytics.category_view_item_clicks as view_item_list
        left join
            item_click
        on
                view_item_list.user_pseudo_id = item_click.user_pseudo_id
            and view_item_list.sl_key = item_click.sl_key
            and view_item_list.market = item_click.market
            and view_item_list.product_id = item_click.product_id
            and item_click.event_timestamp > view_item_list.event_timestamp
            and item_click.event_rank - view_item_list.event_rank = 1
        where
                view_item_list.event_date = p_event_date
            and view_item_list.event_name = 'view_item_list'
            and view_item_list.item_list_name = 'category-results'
        group by all
    )

    select
        event_date,
        market,
        category_id,
        category_slug,
        product_id,
        count(*) as impressions,
        round(avg(coalesce(price, 0)), 2) as avg_price,
        sum(immediate_click) as c_page_item_clicks
    from
        category_page_events
    group by all
);

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: google_analytics.category_page_impression_and_clicks
        select *
        from google_analytics.select_category_page_impression_and_clicks(current_date()-1)
        limit 10;
    end if;
end;
