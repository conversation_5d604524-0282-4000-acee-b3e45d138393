-- Flyway will re-run this script if ${changeReason} is updated
drop table function if exists analytics_reporting.select_purchases_and_clicks;
create table function analytics_reporting.select_purchases_and_clicks (start_date date, end_date date) -- noqa
options (
    description = "Selects purchases and clicks enriched with platform orders for a given date range.\n"
    || "subject: Smart Pricing;\n"
    || "sources: google_analytics.purchases_and_clicks, analytics_reporting.select_order_items_by_offer; \n"
    || "notion: https://www.notion.so/refurbed/Update-purchases_and_clicks-table-1dfb6a8983ab800d9d0fd253dff01e25;\n" -- noqa
    || "owner: data_engineering"
)
as (
select
    -- purchases_and_clicks
    pc.*
    except(
        transaction_id,
        warranty,
        offer_id,
        delivery_max_days,
        delivery_min_days,
        battery_condition
    ),
    safe_cast(pc.transaction_id as int64) as transaction_id,

    -- purchases_and_clicks enriched with order_items
    coalesce(pc.warranty, order_items.warranty) as warranty,
    coalesce(pc.delivery_max_days, order_items.delivery_max_days) as delivery_max_days,
    coalesce(pc.delivery_min_days, order_items.delivery_min_days) as delivery_min_days,
    coalesce(pc.offer_id, order_items.offer_id) as offer_id,
    order_items.nbr_order_items,

    -- op.battery_condition supersedes GA4 data, which is often `unspecified`
    coalesce(op.battery_condition, pc.battery_condition) as battery_condition
from
    google_analytics.purchases_and_clicks as pc
left join
    analytics_reporting.select_order_items_by_offer(start_date, end_date) as order_items
on  safe_cast(pc.transaction_id as int64) = order_items.transaction_id
and pc.instance_id = order_items.instance_id
and pc.product_id = order_items.product_id
and pc.event_name = 'purchase'
left join
    analytics_transformed.offer_properties as op
on  coalesce(pc.offer_id, order_items.offer_id) = op.offer_id
where
    pc.table_date between coalesce(start_date, current_date()-1) and coalesce(end_date, current_date())
    -- filters out purchases that don't exist in platform orders
    and (pc.event_name != 'purchase' or order_items.transaction_id is not null)
);

begin
    if ("${runTest}" = "true") then -- noqa
        select
            current_date() - 1 as report_date,
            t.*
        from analytics_reporting.select_purchases_and_clicks(null, null) t
        where
            t.battery_condition is not null
        and t.battery_condition != 'unspecified'
        limit 5;
    end if;
end;
