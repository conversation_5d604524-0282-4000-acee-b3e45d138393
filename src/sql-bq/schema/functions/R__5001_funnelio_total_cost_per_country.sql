-- Flyway will re-run this script if ${changeReason} is updated
drop table function if exists funnelio.total_cost_per_country;
create table function funnelio.total_cost_per_country (start_date date, end_date date) -- noqa
options (
    description = "Selects total cost online per country and date for the given time period (defaults to last 7 days).\n"
    || "subject: Performance marketing,\n"
    || "source: funnelio.funnel_data,\n"
    || "notion: https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab80f2bd8cd1f82df21e67,\n" -- noqa
    || "owner: data_engineering"
)
as
(
select
    current_date() as report_date,
    t.funnel_date,
    t.country,
    coalesce(sum(t.cost_online), 0) - coalesce(sum(t.voucher_amount), 0) as total_cost_online
from
    funnelio.funnel_data as t
where
    t.funnel_date between coalesce(start_date, current_date()-7) and coalesce(end_date, current_date())
group by
    t.funnel_date,
    t.country
);

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: funnelio.total_cost_per_country
        select
            t.*
        from funnelio.total_cost_per_country(null, null) t
        limit 5;
    end if;
end;
