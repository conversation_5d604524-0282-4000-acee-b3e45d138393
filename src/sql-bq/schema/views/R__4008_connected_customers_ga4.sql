-- Flyway will re-run this script if ${changeReason} is updated
-- View to get GA4 data used in the connected customers pipeline
-- Originals: GA in Looker: https://refurbedgcp.cloud.looker.com/looks/2715,
-- GA4: https://refurbedgcp.cloud.looker.com/looks/2894
-- raw static query and other notes: https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/528
create or replace view google_analytics.connected_customers as
-- GA data (static data - inserted one-off using `src/sql-bq/queries/ga_360_static.sql`)
select
    visitor_id,
    transaction_id,
    visit_date
from google_analytics.sessions_360_static

union all

-- GA4 data
select
    user_pseudo_id as visitor_id,
    ecommerce.transaction_id,
    date(timestamp(parse_date('%Y%m%d', regexp_extract(_table_suffix, r'^\d{8}')), 'Europe/Vienna')) as visit_date
from
    `analytics_304326635.events_fresh_*`
where
    -- Date range filter using table suffix
    -- We join deprecated GA 360 with new GA4 for connected customers,
    -- hence the hardcoded date filter
    _table_suffix >= '20240601'
    -- Only include rows with transactions
    and ecommerce.transaction_id is not null
group by
    visitor_id,
    transaction_id,
    visit_date;
