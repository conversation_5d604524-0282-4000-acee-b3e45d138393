-- <PERSON>way will re-run this script if ${changeReason} is updated
/*
View to get statuses for all shards in `analytics_304326635.events_fresh_` table:
- Returns same column names like `google_analytics.purchases_and_clicks_partitions`
- Usage is free! Bytes billed: 0 MB
- Use `__TABLES__` as it does not incur any cost as opposed to `INFORMATION_SCHEMA.TABLE_STORAGE`

References:
- https://stackoverflow.com/questions/43457651/bigquery-select-tables-from-all-tables-within-project
- https://cloud.google.com/bigquery/docs/information-schema-table-storage
*/
create or replace view google_analytics.events_fresh_partitions
as
select
    dataset_id as table_schema,
    "events_fresh" as table_name,
    parse_date("%Y%m%d", right(table_id, 8)) as table_partition,
    right(table_id, 8) as partition_id,
    timestamp_millis(last_modified_time) as last_modified,
    row_count as total_rows,
    size_bytes
from
    analytics_304326635.`__TABLES__`
where
    dataset_id = "analytics_304326635"
    and table_id like "events_fresh_%";

begin
    if ("${runTest}" = "true") then -- noqa
        -- test
        select * from google_analytics.events_fresh_partitions limit 10;
    end if;
end;
