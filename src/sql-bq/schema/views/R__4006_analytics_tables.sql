-- Flyway will re-run this script if ${changeReason} is updated
-- List of all analytics BQ native tables managed by DE
drop view if exists maintenance.analytics_tables;
create view maintenance.analytics_tables
as
select concat(table_schema, ".", table_name) as table_path
from
    analytics_raw.INFORMATION_SCHEMA.TABLES as t -- noqa
where
    t.table_type = "BASE TABLE"
union all
select concat(table_schema, ".", table_name) as table_path
from
    analytics_transformed.INFORMATION_SCHEMA.TABLES as t -- noqa
where
    t.table_type = "BASE TABLE"
union all
select concat(table_schema, ".", table_name) as table_path
from
    google_analytics.INFORMATION_SCHEMA.TABLES as t -- noqa
where
    t.table_type = "BASE TABLE";
