-- Flyway will re-run this script if ${changeReason} is updated
-- View to get sales data for the connected customers pipeline
-- Original in Looker: https://refurbedgcp.cloud.looker.com/looks/3564
-- raw static query and other notes: https://gitlab.com/refurbed/analytics-and-ds/analytics-pipelines/-/issues/528
create or replace view analytics_reporting.sales_data_connected_customers as
with full_order_item_refunds_dt as (
    select
        order_item_id,
        sum(refunded_eur) as total_refunded_eur,
        sum(refunded) as total_refunded,
        count(distinct order_item_refund_id) as n_refunds
    from analytics_transformed.full_order_item_refunds
    group by order_item_id
)

select
    users.id as users_id,
    users.email as users_email,
    orders.id as orders_id,
    date(orders.paid_at, 'Europe/Vienna') as orders_paid_date,
    shipping_addresses.company as shipping_addresses_company,
    lower(shipping_addresses.country) as shipping_addresses_country,
    shipping_addresses.gender as shipping_addresses_gender,
    shipping_addresses.is_company as shipping_addresses_is_company,
    shipping_addresses.street_name || ' ' || shipping_addresses.house_no as shipping_addresses_address1,
    shipping_addresses.supplement as shipping_addresses_supplement,
    shipping_addresses.house_no as shipping_addresses_house_no,
    shipping_addresses.street_name
    || ' '
    || shipping_addresses.house_no
    || ' '
    || coalesce(shipping_addresses.supplement, '') as shipping_addresses_street,
    shipping_addresses.post_code as shipping_addresses_post_code,
    shipping_addresses.town as shipping_addresses_town,
    shipping_addresses.phone as shipping_addresses_phone,
    shipping_addresses.first_name || ' ' || shipping_addresses.family_name as shipping_addresses_name,
    shipping_addresses.first_name as shipping_addresses_first_name,
    shipping_addresses.family_name as shipping_addresses_family_name,
    users.first_name as users_first_name,
    users.family_name as users_family_name,
    case
        when
            coalesce(
                safe_divide(
                    full_order_item_refunds_dt.total_refunded,
                    (order_items.charged + order_items.discount)
                ),
                0.00
            ) >= 0.98
            then 'yes'
        else 'no'
    end as order_item_refunds_new_is_fully_refunded,
    sum(order_financial_revenue.gmv) as order_items_total_gmv_eur

from analytics_transformed.order_item_offers as order_item_offers
left join analytics_transformed.order_items as order_items on order_item_offers.id = order_items.id
left join analytics_transformed.order_financial_revenue as order_financial_revenue on
    order_items.id = order_financial_revenue.order_item_id
left join analytics_transformed.orders as orders on order_items.order_id = orders.id
left join analytics_transformed.addresses as shipping_addresses on
    orders.shipping_addr_id = shipping_addresses.id and orders.shipping_addr_valid_from = shipping_addresses.valid_from
left join analytics_transformed.users as users on orders.user_id = users.id
left join full_order_item_refunds_dt on order_items.id = full_order_item_refunds_dt.order_item_id
where order_item_offers.state in ('released', 'released.failed')
group by all;
