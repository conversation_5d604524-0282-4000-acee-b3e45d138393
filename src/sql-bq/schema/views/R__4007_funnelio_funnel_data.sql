-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists funnelio.funnel_data;
create view funnelio.funnel_data
/*
Performance Marketing Input:
- Reads source data from `refb-analytics-funnelio.Funnel_export_refurbed.all_funnel_data_view`
- Provides source data parsed to human-readable columns without aggregation or filtering

Details:
- Deployed as [authorized view](https://cloud.google.com/bigquery/docs/authorized-views)
- Source: https://www.notion.so/refurbed/Performance-marketing-inputs-estimation-22db6a8983ab809d9d14e03442accb86?source=copy_link#22db6a8983ab80f2bd8cd1f82df21e67 -- noqa
*/
as
select
    -- Public fields exposed on the report
    funnel_data.date as funnel_date,
    case when funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy" then "at"
        else funnel_data.dim_1e81sjq0ist0a_country_custom
    end as country,
    cast(case when (upper((case when funnel_data.campaign like "%WKZ%" then "Supplier Financing"
            when
                funnel_data.campaign like "%BAE%"
                or funnel_data.campaign like "%ORG |%"
                or funnel_data.campaign like "%| Reach%"
                or funnel_data.campaign like "%Video View%"
                or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TV"
                then "Brand Marketing"
            when funnel_data.campaign like "%| 050%" or funnel_data.campaign like "%Buyback%" then "Trade-in"
            when funnel_data.campaign like "%| 070%" then "App Marketing"
            when funnel_data.traffic_source = "Apple Search Ads" then "App Marketing"
            when funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TVM4E" then "Media for equity"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in ("Events", "Market Research", "Tools")
                then "Marketing Tools"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end) = "Google Sheets - Offline Media" or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Brand Marketing" then "Brand Marketing"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in (
                    "Influencer", "Online Audio", "Press", "Rebranding", "Content"
                )
                then "Brand Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) in ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1")
            or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "Online Collaborations"
            or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Sales Marketing" then "Sales Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Administrative" then "Administrative"
            when (funnel_data.dim_1e81r9nnpnlc8_stages in ("Stage 1", "Stage 2", "Stage 0", "No Stage", "No stage"))
                and (
                    (
                        case when
                                funnel_data.traffic_source in (
                                    "Prisjagt",
                                    "Prisjakt",
                                    "Shopping24",
                                    "Kelkoo",
                                    "Idealo",
                                    "Marktplaats",
                                    "Oskenskyen",
                                    "Billiger.de",
                                    "PriceRunner"
                                )
                                then "Price Comparison"
                            when funnel_data.traffic_source in (
                                    "Google"
                                )
                                then (case
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Display%"
                                        then "Google-gdn"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%CSS%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%YouTube%"
                                        then "Youtube"
                                    when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                                    when
                                        funnel_data.sourcetypename like "%Campaign Manager 360%"
                                        then "Programmatic display"
                                    else funnel_data.sourcetypename
                                end)
                            when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                            when
                                funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network")
                                then "Affiliate"
                            when
                                funnel_data.sourcetypename in ("Google Sheets")
                                then concat(
                                        "Google Sheets - ",
                                        (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        )
                                    )
                            else funnel_data.traffic_source
                        end) = "Paid Social" or (case
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Display%"
                            then "Display"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                            then "Search - Brand"
                        when
                            (funnel_data.sourcetypename = "Google Ads")
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            then "Search - Non Brand"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%CSS%"
                            then "Shopping"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%YouTube%"
                            then "Youtube"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%- Brand%"
                            then "Search - Brand"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%Shopping%"
                            then "Shopping"
                        when funnel_data.sourcetypename = "Microsoft Advertising" then "Search - Non Brand"
                        when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                        else funnel_data.sourcetypename
                    end) = "Youtube"
                ) then "Upper Funnel Performance Marketing"
            else "Lower Funnel Performance Marketing"
        end)) = upper("Lower Funnel Performance Marketing")
        or upper((case when funnel_data.campaign like "%WKZ%" then "Supplier Financing"
            when
                funnel_data.campaign like "%BAE%"
                or funnel_data.campaign like "%ORG |%"
                or funnel_data.campaign like "%| Reach%"
                or funnel_data.campaign like "%Video View%"
                or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TV"
                then "Brand Marketing"
            when funnel_data.campaign like "%| 050%" or funnel_data.campaign like "%Buyback%" then "Trade-in"
            when funnel_data.campaign like "%| 070%" then "App Marketing"
            when funnel_data.traffic_source = "Apple Search Ads" then "App Marketing"
            when funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TVM4E" then "Media for equity"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in ("Events", "Market Research", "Tools")
                then "Marketing Tools"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end) = "Google Sheets - Offline Media" or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Brand Marketing" then "Brand Marketing"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in (
                    "Influencer", "Online Audio", "Press", "Rebranding", "Content"
                )
                then "Brand Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) in ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1")
            or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "Online Collaborations"
            or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Sales Marketing" then "Sales Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Administrative" then "Administrative"
            when (funnel_data.dim_1e81r9nnpnlc8_stages in ("Stage 1", "Stage 2", "Stage 0", "No Stage", "No stage"))
                and (
                    (
                        case when
                                funnel_data.traffic_source in (
                                    "Prisjagt",
                                    "Prisjakt",
                                    "Shopping24",
                                    "Kelkoo",
                                    "Idealo",
                                    "Marktplaats",
                                    "Oskenskyen",
                                    "Billiger.de",
                                    "PriceRunner"
                                )
                                then "Price Comparison"
                            when funnel_data.traffic_source in (
                                    "Google"
                                )
                                then (case
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Display%"
                                        then "Google-gdn"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%CSS%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%YouTube%"
                                        then "Youtube"
                                    when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                                    when
                                        funnel_data.sourcetypename like "%Campaign Manager 360%"
                                        then "Programmatic display"
                                    else funnel_data.sourcetypename
                                end)
                            when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                            when
                                funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network")
                                then "Affiliate"
                            when
                                funnel_data.sourcetypename in ("Google Sheets")
                                then concat(
                                        "Google Sheets - ",
                                        (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        )
                                    )
                            else funnel_data.traffic_source
                        end) = "Paid Social" or (case
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Display%"
                            then "Display"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                            then "Search - Brand"
                        when
                            (funnel_data.sourcetypename = "Google Ads")
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            then "Search - Non Brand"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%CSS%"
                            then "Shopping"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%YouTube%"
                            then "Youtube"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%- Brand%"
                            then "Search - Brand"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%Shopping%"
                            then "Shopping"
                        when funnel_data.sourcetypename = "Microsoft Advertising" then "Search - Non Brand"
                        when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                        else funnel_data.sourcetypename
                    end) = "Youtube"
                ) then "Upper Funnel Performance Marketing"
            else "Lower Funnel Performance Marketing"
        end)) = upper("Upper Funnel Performance Marketing")
        or upper((case when funnel_data.campaign like "%WKZ%" then "Supplier Financing"
            when
                funnel_data.campaign like "%BAE%"
                or funnel_data.campaign like "%ORG |%"
                or funnel_data.campaign like "%| Reach%"
                or funnel_data.campaign like "%Video View%"
                or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TV"
                then "Brand Marketing"
            when funnel_data.campaign like "%| 050%" or funnel_data.campaign like "%Buyback%" then "Trade-in"
            when funnel_data.campaign like "%| 070%" then "App Marketing"
            when funnel_data.traffic_source = "Apple Search Ads" then "App Marketing"
            when funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TVM4E" then "Media for equity"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in ("Events", "Market Research", "Tools")
                then "Marketing Tools"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end) = "Google Sheets - Offline Media" or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Brand Marketing" then "Brand Marketing"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in (
                    "Influencer", "Online Audio", "Press", "Rebranding", "Content"
                )
                then "Brand Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) in ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1")
            or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "Online Collaborations"
            or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Sales Marketing" then "Sales Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Administrative" then "Administrative"
            when (funnel_data.dim_1e81r9nnpnlc8_stages in ("Stage 1", "Stage 2", "Stage 0", "No Stage", "No stage"))
                and (
                    (
                        case when
                                funnel_data.traffic_source in (
                                    "Prisjagt",
                                    "Prisjakt",
                                    "Shopping24",
                                    "Kelkoo",
                                    "Idealo",
                                    "Marktplaats",
                                    "Oskenskyen",
                                    "Billiger.de",
                                    "PriceRunner"
                                )
                                then "Price Comparison"
                            when funnel_data.traffic_source in (
                                    "Google"
                                )
                                then (case
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Display%"
                                        then "Google-gdn"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%CSS%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%YouTube%"
                                        then "Youtube"
                                    when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                                    when
                                        funnel_data.sourcetypename like "%Campaign Manager 360%"
                                        then "Programmatic display"
                                    else funnel_data.sourcetypename
                                end)
                            when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                            when
                                funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network")
                                then "Affiliate"
                            when
                                funnel_data.sourcetypename in ("Google Sheets")
                                then concat(
                                        "Google Sheets - ",
                                        (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        )
                                    )
                            else funnel_data.traffic_source
                        end) = "Paid Social" or (case
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Display%"
                            then "Display"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                            then "Search - Brand"
                        when
                            (funnel_data.sourcetypename = "Google Ads")
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            then "Search - Non Brand"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%CSS%"
                            then "Shopping"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%YouTube%"
                            then "Youtube"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%- Brand%"
                            then "Search - Brand"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%Shopping%"
                            then "Shopping"
                        when funnel_data.sourcetypename = "Microsoft Advertising" then "Search - Non Brand"
                        when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                        else funnel_data.sourcetypename
                    end) = "Youtube"
                ) then "Upper Funnel Performance Marketing"
            else "Lower Funnel Performance Marketing"
        end)) = upper("Sales Marketing") or upper((case when funnel_data.campaign like "%WKZ%" then "Supplier Financing"
            when
                funnel_data.campaign like "%BAE%"
                or funnel_data.campaign like "%ORG |%"
                or funnel_data.campaign like "%| Reach%"
                or funnel_data.campaign like "%Video View%"
                or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TV"
                then "Brand Marketing"
            when funnel_data.campaign like "%| 050%" or funnel_data.campaign like "%Buyback%" then "Trade-in"
            when funnel_data.campaign like "%| 070%" then "App Marketing"
            when funnel_data.traffic_source = "Apple Search Ads" then "App Marketing"
            when funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "TVM4E" then "Media for equity"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in ("Events", "Market Research", "Tools")
                then "Marketing Tools"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end) = "Google Sheets - Offline Media" or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Brand Marketing" then "Brand Marketing"
            when
                funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ in (
                    "Influencer", "Online Audio", "Press", "Rebranding", "Content"
                )
                then "Brand Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) in ("Affiliate", "Price Comparison", "Google Sheets - Marketing Spend Online Looker - Sheet1")
            or funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ = "Online Collaborations"
            or (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Sales Marketing" then "Sales Marketing"
            when (
                case when
                        funnel_data.traffic_source in (
                            "Prisjagt",
                            "Prisjakt",
                            "Shopping24",
                            "Kelkoo",
                            "Idealo",
                            "Marktplaats",
                            "Oskenskyen",
                            "Billiger.de",
                            "PriceRunner"
                        )
                        then "Price Comparison"
                    when funnel_data.traffic_source in (
                            "Google"
                        )
                        then (case
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Display%"
                                then "Google-gdn"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%Search%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%CSS%"
                                then "Google-cpc"
                            when
                                funnel_data.sourcetypename = "Google Ads"
                                and (
                                    replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                                ) like "%YouTube%"
                                then "Youtube"
                            when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                            when funnel_data.sourcetypename like "%Campaign Manager 360%" then "Programmatic display"
                            else funnel_data.sourcetypename
                        end)
                    when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                    when funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network") then "Affiliate"
                    when
                        funnel_data.sourcetypename in ("Google Sheets")
                        then concat(
                                "Google Sheets - ",
                                (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            )
                    else funnel_data.traffic_source
                end
            ) = "Google Sheets - Administrative" then "Administrative"
            when (funnel_data.dim_1e81r9nnpnlc8_stages in ("Stage 1", "Stage 2", "Stage 0", "No Stage", "No stage"))
                and (
                    (
                        case when
                                funnel_data.traffic_source in (
                                    "Prisjagt",
                                    "Prisjakt",
                                    "Shopping24",
                                    "Kelkoo",
                                    "Idealo",
                                    "Marktplaats",
                                    "Oskenskyen",
                                    "Billiger.de",
                                    "PriceRunner"
                                )
                                then "Price Comparison"
                            when funnel_data.traffic_source in (
                                    "Google"
                                )
                                then (case
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Display%"
                                        then "Google-gdn"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%Search%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%CSS%"
                                        then "Google-cpc"
                                    when
                                        funnel_data.sourcetypename = "Google Ads"
                                        and (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        ) like "%YouTube%"
                                        then "Youtube"
                                    when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                                    when
                                        funnel_data.sourcetypename like "%Campaign Manager 360%"
                                        then "Programmatic display"
                                    else funnel_data.sourcetypename
                                end)
                            when funnel_data.traffic_source in ("TikTok", "Facebook", "Twitter") then "Paid Social"
                            when
                                funnel_data.traffic_source in ("Adtraction", "Rakuten Affiliate Network")
                                then "Affiliate"
                            when
                                funnel_data.sourcetypename in ("Google Sheets")
                                then concat(
                                        "Google Sheets - ",
                                        (
                                            replace(
                                                replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""
                                            )
                                        )
                                    )
                            else funnel_data.traffic_source
                        end) = "Paid Social" or (case
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Display%"
                            then "Display"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            and upper(funnel_data.campaign) like "%SEARCH - BRAND%"
                            then "Search - Brand"
                        when
                            (funnel_data.sourcetypename = "Google Ads")
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%Search%"
                            then "Search - Non Brand"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%CSS%"
                            then "Shopping"
                        when
                            funnel_data.sourcetypename = "Google Ads"
                            and (
                                replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", "")
                            ) like "%YouTube%"
                            then "Youtube"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%- Brand%"
                            then "Search - Brand"
                        when
                            funnel_data.sourcetypename = "Microsoft Advertising"
                            and funnel_data.campaign like "%Shopping%"
                            then "Shopping"
                        when funnel_data.sourcetypename = "Microsoft Advertising" then "Search - Non Brand"
                        when funnel_data.sourcetypename = "Facebook Ads" then "Facebook"
                        else funnel_data.sourcetypename
                    end) = "Youtube"
                ) then "Upper Funnel Performance Marketing"
            else "Lower Funnel Performance Marketing"
        end)) = upper("Trade-in"))
            then (
                case when
                        (
                            (replace(replace(funnel_data.sourcename, " - Sheet1", ""), " - Funnel Input", ""))
                            <> "Offline Media"
                        )
                        = true
                        then (case
                            when (case when funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy" then "at"
                                else funnel_data.dim_1e81sjq0ist0a_country_custom
                            end) = "at"
                            and funnel_data.date >= date(2020, 11, 1)
                            and (funnel_data.sourcetypename = "AdWords" or funnel_data.sourcetypename = "Google Ads")
                                then 1.05 * funnel_data.cost
                            when (case when funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy" then "at"
                                else funnel_data.dim_1e81sjq0ist0a_country_custom
                            end) = "it"
                            and funnel_data.date >= date(2024, 07, 1)
                            and (funnel_data.sourcetypename = "AdWords" or funnel_data.sourcetypename = "Google Ads")
                                then 1.025 * funnel_data.cost
                            when (case when funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy" then "at"
                                else funnel_data.dim_1e81sjq0ist0a_country_custom
                            end) = "it"
                            and funnel_data.date >= date(2021, 10, 1)
                            and (funnel_data.sourcetypename = "AdWords" or funnel_data.sourcetypename = "Google Ads")
                                then 1.02 * funnel_data.cost
                            else funnel_data.cost
                        end)
                    else 0
                end
            )
    end as numeric) as cost_online,
    cast(funnel_data.cf1ftljmnkd6oub_voucher_amount as numeric) as voucher_amount,

    -- Internal data columns
    funnel_data.dim_1e81sjq0ist0a_country_custom as country_custom,
    funnel_data.cost,
    funnel_data.campaign,
    funnel_data.sourcename as source_name,
    funnel_data.sourcetypename as source_type,
    funnel_data.traffic_source,
    funnel_data.dim_1i85je69h69r3_channel_2_equivalent_ as channel
from
    `refb-analytics-funnelio.Funnel_export_refurbed.all_funnel_data_view` as funnel_data;

begin
    if ("${runTest}" = "true") then -- noqa
        -- test: funnel_data
        select *
        from funnelio.funnel_data
        where funnel_date = current_date()
        limit 5;
    end if;
end;
