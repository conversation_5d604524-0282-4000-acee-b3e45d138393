-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists analytics_reporting.product_release;
create view analytics_reporting.product_release
as
select
    pav.product_id,
    cast(max(av.value_numeric) as int64) as release_year
from
    analytics_transformed.products as p
inner join
    analytics_transformed.product_attribute_values as pav
on p.id = pav.product_id
inner join
    analytics_transformed.attributes as av
on pav.attribute_id = av.attribute_id
    and pav.attribute_value_id = av.attribute_value_id
where
    -- Active products with listing
    p.deleted_at is null
    and p.listing_mode <> 'hidden'
    -- Erscheinungsjahr = Release Year
    and pav.attribute_id = 183
group by
    pav.product_id;

-- test: select * from analytics_reporting.product_release limit 5;
select if(count(*) > 1, error('Invalid release year found!'), 0) as invalid_rows
from
    analytics_reporting.product_release
where
    release_year < 2000
    or release_year > extract(year from current_date());
