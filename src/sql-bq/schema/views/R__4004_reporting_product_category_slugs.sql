-- Flyway will re-run this script if ${changeReason} is updated
drop view if exists analytics_reporting.product_category_slugs;
create view analytics_reporting.product_category_slugs
as
select
    pc.product_id,
    pc.show_category_id as category_id,
    cs.market,
    cs.language,
    cs.category_slug
from
    analytics_transformed.product_categories as pc
inner join
    analytics_transformed.category_slugs_mapping as cs
on pc.show_category_id = cs.category_id;

-- test: analytics_reporting.product_category_slugs
-- select * from analytics_reporting.product_category_slugs limit 5;
select
    product_id,
    category_id,
    market,
    language,
    category_slug,
    if(count(*) > 1, error("Duplicate rows found!"), 0) as duplicate_rows
from
    analytics_reporting.product_category_slugs
group by
    product_id,
    category_id,
    market,
    language,
    category_slug
having count(*) > 1;
