-- This prints 5 rows of results in staging (or production if you modify) before any actions done.
-- It should print 0 rows after we remove qa data from ga data!
-- 1 rows for 304... events for 22nd
-- 1 rows for 304... events_fresh for 22nd
-- 1 rows for 304... events_intraday for 6th
-- 2 rows for 214 ga_sessions


-- Need to add location in EU specifically through more > query setting > location when running in UI
begin
    -- Declare the table patterns and corresponding datasets
    declare tables array<struct<
        dataset string,
        table_pattern string,
        qa_condition string,
        date_start string,
        date_end string
    >> default [
        (
            'analytics_304326635_qa',
            'events_fresh',
            'device.operating_system = "Linux" ' || -- noqa
            'and device.web_info.browser = "Chrome" ' || -- noqa
            'and device.web_info.browser_version = "116.0.5845.187"',
            '20240822',
            '20240823'
        ),
        (
            'analytics_304326635_qa',
            'events',
            'device.operating_system = "Linux" ' || -- noqa
            'and device.web_info.browser = "Chrome" ' || -- noqa
            'and device.web_info.browser_version = "116.0.5845.187"',
            '20240822',
            '20240823'
        ),
        (
            'analytics_304326635_qa',
            'events_intraday',
            'device.operating_system = "Linux" ' || -- noqa
            'and device.web_info.browser = "Chrome" ' || -- noqa
            'and device.web_info.browser_version = "116.0.5845.187"',
            '20240806',
            '20241008'
        ),
        (
            '214239644_qa',
            'ga_sessions',
            'device.operatingSystem = "Linux" ' || -- noqa
            'and device.browser = "Chrome" and device.browserVersion = "116.0.0.0"',
            '20240501',
            '20240502'
        )
    ];

    -- Declare variables for constructing the query
    declare sql_query string default '';
    declare first_loop bool default true;

    -- Start constructing the query
    set sql_query = '';

    -- Loop over each table
    for table_info in (select * from unnest(tables)) do

    -- Add union all between queries after the first one
        if not first_loop then
            set sql_query = concat(sql_query, ' union all ');
        else
            set first_loop = false;
        end if;

        -- Append the query for the current table
        set sql_query = concat(
            sql_query,
            'select "', table_info.dataset, '" as dataset_name, "',
            table_info.table_pattern, '" as table_name, ',
            '_TABLE_SUFFIX as partition_date, ',
            'count(*) as qa_count ',
            'from `refb-analytics-staging.', table_info.dataset, '.', table_info.table_pattern, '_*` ',
            'where ', table_info.qa_condition, ' ',
            'and _TABLE_SUFFIX BETWEEN "', table_info.date_start, '" and "', table_info.date_end, '" ',
            'group by partition_date '
        );

    end for;

    set sql_query = concat(sql_query, 'order by partition_date');

    -- Debug: Print the generated SQL query
    select sql_query;

    -- Execute the combined query
    execute immediate sql_query;
end;
