-- This is a "disaster" control query
-- It allows us to revert the state of any ga table (defined in the variables)
-- back in specified timed (defined in the variables)
-- for all it's partitions or specified ones (defined, you guessed it, in the variables)

-- Set the processing location to EU when running in BigQuery UI
-- Based on https://stackoverflow.com/questions/65329311/timestamp-literals-in-for-system-time-as-of-dont-default-to-utc
set @@time_zone = 'UTC'; -- noqa

begin
    -- Declare variables
    declare project_id string default 'refb-analytics-staging';  -- Replace with your project ID
    declare dataset_id string default 'analytics_304326635_qa';  -- Replace with your dataset ID
    declare table_pattern string default '';  -- Replace with your table base name
    declare time_travel_minutes int64 default 15;  -- Number of minutes to go back in time
    declare restore_all_partitions bool default true;  -- Set to TRUE to restore all partitions
    -- Specify partitions if restore_all_partitions is FALSE
    declare specific_partitions array<string> default ['20240822', '20240823'];

    -- Calculate the timestamp for time travel
    declare time_travel_timestamp timestamp default timestamp_sub(
        current_timestamp(), interval time_travel_minutes minute
    );

    -- Declare variables for processing
    declare partition_suffixes array<string>;
    declare full_dataset_name string;
    declare table_exists bool default false;

    -- Construct the full dataset name
    set full_dataset_name = concat('`', project_id, '.', dataset_id, '`');

    -- Retrieve all date-suffixed tables matching the table pattern
    if restore_all_partitions then
        execute immediate format(
            '''
            select array_agg(right(table_id, 8))
            from `%s.%s.__TABLES__`
            where table_id like '%s%%' and length(table_id) = length('%s') + 9
            ''',
            project_id,
            dataset_id,
            table_pattern,
            table_pattern
        )
        into partition_suffixes;
    else
        set partition_suffixes = specific_partitions;
    end if;

    -- Loop over each partition suffix (date)
    for partition_suffix_struct in (select * from unnest(partition_suffixes)) do
        begin
            -- Declare variables at the start of the block
            declare partition_suffix string;
            declare table_name string;
            declare table_id string;

            -- Extract the string value from the STRUCT
            set partition_suffix = partition_suffix_struct.f0_;

            -- Construct table names
            set table_name = concat(table_pattern, '_', partition_suffix);
            set table_id = concat('`', project_id, '.', dataset_id, '.', table_name, '`');

            -- Overwrite the original table with data from time travel
            execute immediate format(
                '''
                create or replace table %s as
                select * from %s for system_time as of timestamp '%s';
                ''',
                table_id,
                table_id,
                format_timestamp('%F %H:%M:%E*S', time_travel_timestamp)
            );

        end;
    end for;

end;
