-- This query will copy the defined partitions (explicitly from `date_start` to `date_end`)
-- from the defined tables in `tables` array below
-- from production (refb-analytics) to staging (refb-analytics-staging)
-- if they don't exist.

-- Set the processing location to EU when running in BigQuery UI

-- Declare the table patterns and corresponding datasets
declare tables array<struct<
    src_dataset string,
    dest_dataset string,
    table_name string,
    date_start string,
    date_end string
>> default [
    ('analytics_304326635', 'analytics_304326635_qa', 'events_fresh', '20240822', '20240823'),
    ('analytics_304326635', 'analytics_304326635_qa', 'events', '20240822', '20240823'),
    ('analytics_304326635', 'analytics_304326635_qa', 'events_intraday', '20240806', '20241008'),
    ('214239644', '214239644_qa', 'ga_sessions', '20240501', '20240502')
];

-- Create staging datasets in the EU region
create schema if not exists `refb-analytics-staging.analytics_304326635_qa`
options (location = 'EU', default_table_expiration_days = 7);
create schema if not exists `refb-analytics-staging.214239644_qa`
options (location = 'EU', default_table_expiration_days = 7);

begin
    -- Loop over each table
    for table_info in (select * from unnest(tables)) do
        begin
            -- Declare variables at the start of the block
            declare date_list array<string>;

            -- Create date_list as an array containing date_start and date_end
            if table_info.date_start = table_info.date_end then
                set date_list = [table_info.date_start];
            else
                set date_list = [table_info.date_start, table_info.date_end];
            end if;

            -- Loop over each date
            for partition_suffix_struct in (select * from unnest(date_list)) do
                begin
                    -- Declare variables at the start of the block
                    declare partition_suffix string;
                    declare src_table string;
                    declare dest_table string;
                    declare dest_table_exists bool default false;

                    -- Extract the string value from the struct
                    set partition_suffix = partition_suffix_struct.f0_;

                    -- Construct source and destination table names
                    set src_table
                    = concat(
                        '`refb-analytics.',
                        table_info.src_dataset,
                        '.',
                        table_info.table_name,
                        '_',
                        partition_suffix,
                        '`'
                    );
                    set dest_table
                    = concat(
                        '`refb-analytics-staging.',
                        table_info.dest_dataset,
                        '.',
                        table_info.table_name,
                        '_',
                        partition_suffix,
                        '`'
                    );

                    -- Check if the destination table exists
                    begin
                        -- Try to query the destination table
                        execute immediate format('select 1 from %s limit 1', dest_table);
                        -- If no error occurs, the table exists
                        set dest_table_exists = true;
                    exception when error then
                        -- If an error occurs, the table does not exist
                        set dest_table_exists = false;
                    end;

                    -- If the destination table does not exist, proceed to copy
                    if not dest_table_exists then
                        -- Copy the data
                        execute immediate format(
                            '''
                              create table %s AS
                              select * from %s;
                            ''',
                            dest_table,
                            src_table
                        );
                    end if;

                end;
            end for;

        end;
    end for;
end;
