/*
    Test Ranking functions and procedures before running them from Workflows and Cloud Functions.
    Relevant functions, views and procedures were already deployed to `refb-analytics` project in BQ.
*/
begin
    declare p_event_date date;
    set p_event_date = current_date() - 1;

    -- 1. Load category_view_item_clicks
    call google_analytics.load_category_view_item_clicks (p_event_date);

    -- category_view_item_clicks
    select *
    from google_analytics.category_view_item_clicks
    where event_date = p_event_date
    limit 10;

    -- 2. Load category_page_impression_and_clicks
    call google_analytics.load_category_page_impression_and_clicks (p_event_date);

    -- category_page_impression_and_clicks
    select *
    from google_analytics.category_page_impression_and_clicks
    where event_date = p_event_date
    order by c_page_item_clicks desc
    limit 10;

    -- 3. Load target `google_analytics.category_view_item_clicks_purchases` table
    call google_analytics.load_category_view_item_clicks_purchases (p_event_date);

    -- category_view_item_clicks_purchases
    select *
    from google_analytics.category_view_item_clicks_purchases
    where event_date = p_event_date
    order by p_page_item_clicks desc
    limit 10;
end;
