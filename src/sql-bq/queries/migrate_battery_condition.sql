-- 0. Check whether we need to update:
-- 2025-05-17: 268 rows to update
select count(*)
from
    google_analytics.purchases_and_clicks as pc
inner join
    analytics_transformed.offer_properties as op
on
    pc.table_date = date(op.offer_valid_from)
    and pc.offer_id = op.offer_id
    and pc.table_date between "2023-08-09" and current_date()
where
    coalesce(pc.battery_condition, "pc") != coalesce(op.battery_condition, "op");

-- 1. Check update date range
-- 2023-08-09 : 2025-05-18
select
    min(offer_valid_from) as min_date,
    max(offer_valid_from) as max_date
from analytics_transformed.offer_properties;

-- 2: Update purchases_and_clicks
update google_analytics.purchases_and_clicks pc
set battery_condition = t.battery_condition
from
    analytics_transformed.offer_properties as t
where
    pc.table_date = date(t.offer_valid_from)
    and pc.offer_id = t.offer_id
    and pc.table_date between "2023-08-09" and current_date();

-- 3. Ensure now rows need update
select
    pc.offer_id,
    pc.battery_condition,
    op.battery_condition as new_battery_condition
from
    google_analytics.purchases_and_clicks as pc
inner join
    analytics_transformed.offer_properties as op
on
    pc.table_date = date(op.offer_valid_from)
    and pc.offer_id = op.offer_id
    and pc.table_date between "2023-08-09" and current_date()
where
    coalesce(pc.battery_condition, "pc") != coalesce(op.battery_condition, "op")
limit 10;
