-- This query will find all the partitions on the defined tables
-- (defined through `project_id` and `tables` array)
-- create a backup table for every partition with `qa_traffic_` prefix on the same dataset,
-- based on qa defined condition on the `tables` array
-- and delete the qa data from the original tables, defined in `tables` array.

-- Set the processing location to EU when running in BigQuery UI

begin
    -- Declare the project ID
    declare project_id string default 'refb-analytics-staging';

    -- Declare the table patterns and corresponding datasets
    declare tables array<struct<
        dataset string,
        table_pattern string,
        qa_condition string
    >> default [
        (
            'analytics_304326635_qa',
            'events_fresh',
            'device.operating_system = "Linux" and device.web_info.browser = "Chrome" ' || -- noqa
            'and device.web_info.browser_version = "116.0.5845.187"'
        ),
        (
            'analytics_304326635_qa',
            'events',
            'device.operating_system = "Linux" and device.web_info.browser = "Chrome" ' || -- noqa
            'and device.web_info.browser_version = "116.0.5845.187"'
        ),
        (
            'analytics_304326635_qa',
            'events_intraday',
            'device.operating_system = "Linux" and device.web_info.browser = "Chrome" ' || -- noqa
            'and device.web_info.browser_version = "116.0.5845.187"'
        ),
        (
            '214239644_qa',
            'ga_sessions',
            'device.operatingSystem = "Linux" and device.browser = "Chrome" and device.browserVersion = "116.0.0.0"'
        )
    ];

    -- Loop over each table pattern
    for table_info in (select * from unnest(tables)) do
        begin
            -- Declare variables at the start of the block
            declare partition_suffixes array<string>;

            -- Construct the full dataset name with project ID
            declare full_dataset_name string;
            set full_dataset_name = concat('`', project_id, '.', table_info.dataset, '`');

            -- Retrieve all date-suffixed tables matching the table pattern
            execute immediate format(
                '''
                select array_agg(right(table_id, 8))
                from `%s.%s.__TABLES__`
                where table_id like '%s%%' and length(table_id) = length('%s') + 9
                ''',
                project_id,
                table_info.dataset,
                table_info.table_pattern,
                table_info.table_pattern
            )
            into partition_suffixes;

            -- Loop over each partition suffix (date)
            for partition_suffix_struct in (select * from unnest(partition_suffixes)) do
                begin
                    -- Declare variables at the start of the block
                    declare partition_suffix string;
                    declare original_table string;
                    declare backup_table string;
                    declare qa_data_exists bool default false;

                    -- Extract the string value from the STRUCT
                    set partition_suffix = partition_suffix_struct.f0_;

                    -- Construct original table name
                    set original_table = concat(
                        '`',
                        project_id,
                        '.',
                        table_info.dataset,
                        '.',
                        table_info.table_pattern,
                        '_',
                        partition_suffix,
                        '`'
                    );

                    -- Construct backup table name
                    set backup_table = concat(
                        '`',
                        project_id,
                        '.',
                        table_info.dataset,
                        '.',
                        'qa_traffic_',
                        table_info.table_pattern,
                        '_',
                        partition_suffix,
                        '`'
                    );

                    -- Check if the table contains QA data
                    execute immediate format(
                        '''
                        select count(*) > 0
                        from %s
                        where %s
                        ''',
                        original_table,
                        table_info.qa_condition
                    ) into qa_data_exists;

                    if qa_data_exists then
                        -- Proceed with backup and deletion
                        -- Make sure the backup table exists and it's empty
                        execute immediate format(
                            '''
                            create or replace table %s
                            like %s
                            ''',
                            backup_table,
                            original_table
                        );

                        -- begin transaction
                        begin transaction;
                        begin
                            -- insert qa data into the backup table
                            execute immediate format(
                                '''
                                insert into %s
                                select * from %s
                                where %s
                                ''',
                                backup_table,
                                original_table,
                                table_info.qa_condition
                            );

                            -- delete qa data from the original table
                            execute immediate format(
                                '''
                                delete from %s
                                where %s
                                ''',
                                original_table,
                                table_info.qa_condition
                            );

                            commit transaction;
                        exception when error then
                            select @@error.message;
                            rollback transaction;
                        end;

                    else
                        -- Skip this partition as it contains no qa data
                        continue;
                    end if;

                end;
            end for;

        end;
    end for;

end;
