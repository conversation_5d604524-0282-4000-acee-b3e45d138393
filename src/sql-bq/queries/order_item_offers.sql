------------------------------------------------------------------
-- Re-create order_item_offers table logic with a view
------------------------------------------------------------------
drop view if exists analytics_reporting.order_item_offers;
create view analytics_reporting.order_item_offers
as
select
    -- order_items
    order_items.id,
    order_items.created_at,
    order_items.updated_at,
    order_items.order_id,
    order_items.offer_id,
    order_items.offer_valid_from,
    order_items.type,

    -- orders
    orders.paid_at,
    orders.state,
    orders.country,
    orders.presentment_currency,
    orders.payment_provider,

    -- offers: nullable
    offers.valid_to,
    offers.instance_id,
    offers.merchant_id,
    offers.grading,
    offers.warranty,

    -- order_financial_revenue: nullable
    order_financial_revenue.revenue as revenue_eur,
    order_financial_revenue.addon_revenue as addon_revenue_eur,
    order_financial_revenue.gmv as gmv_eur,
    order_financial_revenue.discount_eur,
    order_financial_revenue.service_fee_revenue_eur,

    -- offer_properties: nullable
    offer_properties.battery_condition
from
    analytics_transformed.order_items as order_items
inner join
    analytics_transformed.orders as orders
on order_items.order_id = orders.id
left join
    analytics_transformed.order_financial_revenue as order_financial_revenue
on order_items.id = order_financial_revenue.order_item_id
left join
    analytics_transformed.offers as offers
on order_items.offer_id = offers.id
    and order_items.offer_valid_from = offers.valid_from
left join
    analytics_transformed.offer_properties as offer_properties
on order_items.offer_id = offer_properties.offer_id;

-- 2. Compare analytics_transformed.order_item_offers with the view
-- 17,643,855 vs 17,643,855
select count(*) from analytics_transformed.order_item_offers;
select count(*) from analytics_reporting.order_item_offers;

-- 3. Rows in table, but not in a view: 0
select count(*)
from (select
    id,
    order_id,
    paid_at,
    offer_id
    --round(revenue_eur, 2) as revenue_eur,
    --battery_condition
from analytics_transformed.order_item_offers

except
distinct

select
    id,
    order_id,
    paid_at,
    offer_id
from analytics_reporting.order_item_offers) as t;

-- 3. Rows in table, but not in a view: 12,831,526
-- Missing instance_id in order_item_offers view to missing offers?
create table test.missing_order_item_offers
as
select
    id,
    order_id,
    paid_at,
    offer_id,
    instance_id
from analytics_transformed.order_item_offers

except
distinct

select
    id,
    order_id,
    paid_at,
    offer_id,
    instance_id
from analytics_reporting.order_item_offers;

select count(*) from test.missing_order_item_offers;
select * from test.missing_order_item_offers limit 5;

-- 4. Investigate the difference: instance_id present in a table, but not in a view?
select *
from analytics_transformed.order_item_offers
where id = 366029;

select *
from analytics_reporting.order_item_offers
where id = 366029;

-- missing offer :(
select * from analytics_transformed.offers
where
    id = 59
    and valid_from between cast("2020-01-28 01:13:26" as timestamp) and cast("2020-01-28 01:13:28" as timestamp);

------------------------------------------------------------------
-- Ensure incremental works as expected
------------------------------------------------------------------

-- 1. Save incremental under new name
drop table if exists test.order_item_offers_incremental;
create table if not exists test.order_item_offers_incremental
as
select * from analytics_transformed.order_item_offers;

drop table if exists test.order_items_incremental;
create table if not exists test.order_items_incremental
as
select * from analytics_transformed.order_items;


-- 2. Reload via truncate and insert
truncate table analytics_transformed.order_item_offers;
truncate table analytics_transformed.order_items;

-- Full data dump
-- analytics-pipelines-staging-raw/platform/v_order_item_offers/year=2025/month=5/day=29/timestamp=1748503442
call analytics_transformed.load_order_item_offers (2025, 5, 29, 1748503442);

-- 3. Compare the incremental table with the new view
-- 17,758,549 vs 17,758,549
select count(*) from test.order_item_offers_incremental;
select count(*) from analytics_transformed.order_item_offers;

-- 17,758,549 vs 17,758,549
select count(*) from test.order_items_incremental;
select count(*) from analytics_transformed.order_items;

drop table if exists test.order_item_offers_not_in_full;
create table if not exists test.order_item_offers_not_in_full
as
select * from test.order_item_offers_incremental
except distinct
select * from analytics_transformed.order_item_offers;

drop table if exists test.order_items_not_in_full;
create table if not exists test.order_items_not_in_full
as
select * except (addon_details) from test.order_items_incremental
except distinct
select * except (addon_details) from analytics_transformed.order_items;

----------------------------------------------------------------------
-- order_items disprepancies
-- 0 differences - no discrepancies!!!
----------------------------------------------------------------------
select count(*) from test.order_items_not_in_full;

----------------------------------------------------------------------
-- order_item_offers disprepancies
-- 42 differences ???
----------------------------------------------------------------------
select count(*) from test.order_item_offers_not_in_full;
select * from test.order_item_offers_not_in_full;

-- Incremental: valid_to = 2099-12-31 23:59:59
select * from test.order_item_offers_incremental
where
    id = 17745767;

-- Full: valid_to = 2025-05-27 15:40:44.640423
select * from analytics_transformed.order_item_offers
where
    id = 17745767;

-- Incremental data dump
-- analytics-pipelines-staging-raw/platform/v_order_item_offers/year=2025/month=5/day=29/timestamp=1748501414
-- No rows, it means that the incremental did not detect changes in offers.valid_to
select * from analytics_raw.order_item_offers
where
    year = 2025 and month = 5 and day = 29 and timestamp = 1748501414
limit 10;
