/*
    Reloads GA4 purchases_and_clicks table for the given period.
    - set `dry_run` = false to actually copy the data
    - note that existing `analytics-google-etl` reloads x3 a day for the last 3 days
    - run in BigQuery console to avoid timeout issues
*/
begin
    declare dry_run bool default true;
    declare start_date date default cast('2025-05-01' as date);
    declare end_date date default cast('2025-07-27' as date);

    declare date_suffix string;

    -- Loop through each date from start_date to end_date
    for date_value in (
        select dt from unnest(generate_date_array(start_date, end_date)) as dt
    ) do
        -- Create a formatted date string for table suffix (YYYYMMDD)
        set date_suffix = format_date('%Y%m%d', date_value.dt);

        if dry_run then
            select date_suffix;
        else
            call google_analytics.load_purchases_and_clicks (date_suffix);
        end if;
    end for;
end;
