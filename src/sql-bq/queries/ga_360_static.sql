-- One-off insert of GA 360 static data used in Connected Customers pipeline

insert into google_analytics.sessions_360_static
(visitor_id, transaction_id, visit_date)
select
    concat(ga_sessions.fullvisitorid, '007007') as visitor_id,
    hits.transaction.transactionid as transaction_id,
    date(timestamp_seconds(ga_sessions.visitstarttime), 'Europe/Vienna') as visit_date
-- Hardcoded prod dataset on purpose, since 214239644 doesn't exist on staging
from `refb-analytics.214239644.ga_sessions_*` as ga_sessions
cross join unnest(ga_sessions.hits) as hits
where
    -- It's static data from deprecated GA 360 we use in union with new GA4 for connected customers,
    -- hence the hardcoded date filters.
    -- For staging (fewer data - 1 month)
    _table_suffix >= '20240501'
    -- For production (full data)
    --_table_suffix >= '20180101'
    and _table_suffix < '20240601'
    and hits.transaction.transactionid is not null
group by visitor_id, transaction_id, visit_date;
