drop table if exists analytics_transformed.product_rankings;
create table if not exists analytics_transformed.product_rankings
( -- noqa
    -- PK
    product_id int64 not null,
    category_id int64 not null,
    market_country string not null,
    primary key (product_id, category_id, market_country) not enforced,

    -- Change tracking attributes
    updated_at timestamp default (current_timestamp()) not null,

    -- Rank fields
    rank int64 not null,
    rank_b int64 not null
)
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_product_rankings,\n"
    || "source: analytics_raw.product_rankings,\n"
    || "owner: data_engineering",
    enable_fine_grained_mutations = true
);
