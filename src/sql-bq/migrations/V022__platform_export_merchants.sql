drop table if exists analytics_transformed.merchants;
create table if not exists analytics_transformed.merchants
( -- noqa
    id int64 primary key not enforced,

    -- Change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    country string not null,
    state string not null,
    is_addon_support bool not null,
    name string not null, -- noqa
    email string not null,
    public_id string not null,

    handicap numeric(5, 4),
    payment_commission_pc numeric(5, 4),
    payout_commission_pc numeric(5, 4)
)
cluster by
id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_merchants,\n"
    || "source: analytics_raw.merchants,\n"
    || "owner: data_engineering"
);
