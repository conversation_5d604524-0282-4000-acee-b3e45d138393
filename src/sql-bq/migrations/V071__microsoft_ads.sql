drop table if exists analytics_transformed.microsoft_ads_costs;
create table if not exists analytics_transformed.microsoft_ads_costs
(
    -- PK columns
    campaign_name string not null,
    hour_of_day int not null,
    updated_at date not null,

    -- Attributes
    impressions int not null,
    spend float64 not null,

    primary key (campaign_name, hour_of_day, updated_at) not enforced
)
cluster by
campaign_name, hour_of_day, updated_at
options (
    description = "Loaded by the SQL procedure and triggered from its own schedule.\n"
    || "procedure: analytics_transformed.load_microsoft_ads_costs,\n"
    || "source: analytics_raw.microsoft_ads_costs,\n"
    || "owner: data_engineering",
    enable_fine_grained_mutations = true
);
