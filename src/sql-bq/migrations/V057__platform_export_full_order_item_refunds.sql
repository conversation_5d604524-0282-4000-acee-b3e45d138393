/*
FK dependency on:
- analytics_transformed.order_item_offers
- analytics_transformed.order_items
*/
drop table if exists analytics_transformed.full_order_item_refunds;
create table if not exists analytics_transformed.full_order_item_refunds
(
    order_item_refund_id int64 not null primary key not enforced,
    order_item_id int64 not null,
    refunded_at timestamp,
    updated_at timestamp,
    refunded numeric,
    refunded_charge numeric,
    refunded_eur numeric,
    refunded_charge_eur numeric,
    min_reversed_commission_date date,
    held_base_commission numeric,
    kept_base_commission numeric,
    executed_base_commission numeric,
    held_payment_commission numeric,
    kept_payment_commission numeric,
    executed_payment_commission numeric,
    held_base_commission_eur numeric,
    kept_base_commission_eur numeric,
    executed_base_commission_eur numeric,
    held_payment_commission_eur numeric,
    kept_payment_commission_eur numeric,
    executed_payment_commission_eur numeric,

    foreign key (order_item_id) references analytics_transformed.order_items (id) not enforced,
    foreign key (order_item_id) references analytics_transformed.order_item_offers (id) not enforced
)
cluster by order_item_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_full_order_item_refunds,\n"
    || "source: analytics_raw.full_order_item_refunds,\n"
    || "owner: data_engineering"
);
