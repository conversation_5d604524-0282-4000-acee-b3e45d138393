drop table if exists analytics_transformed.orders;
create table if not exists analytics_transformed.orders
( -- noqa
    id int64 not null primary key not enforced,

    -- change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- orders
    paid_at timestamp,
    country string not null,
    state string not null,
    is_released bool not null,

    user_id int64 not null,
    partner_id int64,
    invoice_addr_id int64 not null,
    invoice_addr_valid_from timestamp not null,
    shipping_addr_id int64 not null,
    shipping_addr_valid_from timestamp not null,

    presentment_currency string not null,
    settlement_currency string not null,
    presentment_to_settlement_exchange_rate numeric(24, 9) not null,

    payment_provider string not null,
    payment_info string,
    payment_handle string,

    payment_provider_category string not null,
    payment_provider_info json,

    foreign key
    (invoice_addr_id, invoice_addr_valid_from)
    references analytics_transformed.addresses
    (id, valid_from) not enforced,

    foreign key
    (shipping_addr_id, shipping_addr_valid_from)
    references analytics_transformed.addresses
    (id, valid_from) not enforced

    /*
    !!! Users and partners are not loaded yet
    foreign key (user_id) references analytics_transformed.users (id) not enforced,
    foreign key (partner_id) references analytics_transformed.partners (id) not enforced,
    */
)
cluster by
id,
country,
state,
paid_at
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_orders,\n"
    || "source: analytics_raw.orders,\n"
    || "owner: data_engineering"
);
