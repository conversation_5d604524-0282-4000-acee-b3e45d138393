drop table if exists analytics_transformed.addresses;
create table if not exists analytics_transformed.addresses
( -- noqa
    -- PK / time travel
    id int64 not null,
    valid_from timestamp not null,
    valid_to timestamp not null,
    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,
    -- Mandatory address fields
    owner_id integer not null,
    first_name string not null,
    family_name string not null,
    country string not null,
    post_code string not null,
    town string not null,
    street_name string not null,
    house_no string not null,
    phone string not null,
    -- Optional address fields
    male bool,
    address_type string,
    supplement string,
    company string,
    vatin string,
    personal_vatin string,
    -- Calculated fields
    is_company bool not null,
    gender string not null,
    -- Constraints
    primary key (id, valid_from) not enforced,
    foreign key (owner_id) references analytics_transformed.users (id) not enforced
)
cluster by
id,
valid_from
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_addresses,\n"
    || "source: analytics_raw.addresses,\n"
    || "owner: data_engineering",
    enable_fine_grained_mutations = true
);
