drop table if exists analytics_transformed.exchange_rates;
create table if not exists analytics_transformed.exchange_rates
( -- noqa
    id int64 not null,

    -- Change tracking attributes
    valid_from timestamp not null,
    valid_to timestamp not null,
    primary key (valid_from, id) not enforced,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    `base` string not null,
    `target` string not null,
    rate numeric(24, 9) not null
)
cluster by
valid_from,
valid_to,
base,
target
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_exchange_rates,\n"
    || "source: analytics_raw.exchange_rates,\n"
    || "owner: data_engineering"
);
