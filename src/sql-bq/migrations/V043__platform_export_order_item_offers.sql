drop table if exists analytics_transformed.order_item_offers;
create table if not exists analytics_transformed.order_item_offers
( -- noqa
    -- change tracking attributes
    id int64 not null primary key not enforced,
    created_at timestamp not null,
    updated_at timestamp not null,

    -- order_items
    order_id integer not null,
    offer_id integer,
    offer_valid_from timestamp,
    type string not null, -- noqa

    -- orders
    paid_at timestamp,
    state string not null,
    country string not null,
    presentment_currency string not null,
    payment_provider string not null,

    -- offers: nullable
    valid_to timestamp,
    instance_id integer,
    merchant_id integer,
    grading string,
    warranty smallint,

    -- offer_properties: nullable
    battery_condition string,
    is_new_battery boolean,

    foreign key (id) references analytics_transformed.order_items (id) not enforced,
    foreign key (order_id) references analytics_transformed.orders (id) not enforced,
    foreign key (instance_id) references analytics_transformed.instances (id) not enforced,
    foreign key (merchant_id) references analytics_transformed.merchants (id) not enforced,
    foreign key
    (offer_id, offer_valid_from)
    references analytics_transformed.offers
    (id, valid_from) not enforced

)
cluster by
state,
type -- noqa
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_order_item_offers,\n"
    || "source: analytics_raw.order_item_offers,\n"
    || "owner: data_engineering"
);
