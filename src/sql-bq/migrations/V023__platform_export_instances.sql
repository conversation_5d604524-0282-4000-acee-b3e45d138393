drop table if exists analytics_transformed.instances;
create table if not exists analytics_transformed.instances
( -- noqa
    id int64 primary key not enforced,

    -- Change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    published bool not null,
    product_id int64 not null references analytics_transformed.products (id) not enforced,
    name string not null, -- noqa
    name_en string,
    srp numeric(14, 4)
)
cluster by
id,
product_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_instances,\n"
    || "source: analytics_raw.instances,\n"
    || "owner: data_engineering"
);
