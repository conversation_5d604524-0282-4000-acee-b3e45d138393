drop table if exists analytics_transformed.order_items;
create table if not exists analytics_transformed.order_items
( -- noqa
    id int64 not null primary key not enforced,

    -- change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- order_items
    order_id int64 not null,
    offer_id int64,
    offer_valid_from timestamp,
    offer_price_id int64,
    offer_price_valid_from timestamp,

    `type` string not null,
    is_addon bool not null,
    charged numeric(14, 4) not null,
    discount numeric(14, 4) not null,

    net bool not null,
    vat numeric(14, 4) not null,
    vat_country string not null,
    price numeric(14, 4) not null,
    flex_price numeric(14, 4) not null,

    addon_id int64,
    addon_valid_from timestamp,
    addon_assigned_order_item_id int64,
    addon_details json,

    shipping_profile_destination_id int64,
    shipping_profile_destination_valid_from timestamp,
    shipping_costs numeric(14, 4) not null,
    is_pre_shipping_versioning bool not null,

    presentment_price_gross numeric(14, 4) not null,
    presentment_flex_price_gross numeric(14, 4) not null,
    presentment_shipping_costs_gross numeric(14, 4) not null,

    presentment_base_commission numeric(14, 4) not null,
    presentment_payment_commission numeric(14, 4) not null,
    presentment_payout_commission numeric(14, 4) not null,
    presentment_target_price_commission numeric(14, 4) not null,
    presentment_commissions numeric(14, 4) not null,
    presentment_exchange_rate_modifier numeric(24, 9) not null,
    presentment_to_settlement_exchange_rate numeric(24, 9) not null,

    settlement_price_gross numeric(14, 4) not null,
    settlement_flex_price_gross numeric(14, 4) not null,
    settlement_shipping_costs_gross numeric(14, 4) not null,
    settlement_currency string not null,

    settlement_base_commission numeric(14, 4) not null,
    settlement_payout_commission numeric(14, 4) not null,
    settlement_payment_commission numeric(14, 4) not null,
    settlement_target_price_commission numeric(14, 4) not null,
    settlement_commissions numeric(14, 4) not null,

    commission_pc numeric(5, 4) not null,
    payment_commission_pc numeric(5, 4) not null,
    payout_commission_pc numeric(5, 4) not null,
    target_price_commission_pc numeric(5, 4) not null,

    foreign key (order_id) references analytics_transformed.orders (id) not enforced,

    foreign key
    (offer_id, offer_valid_from)
    references analytics_transformed.offers
    (id, valid_from) not enforced
)
cluster by
id,
order_id,
type
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_order_items,\n"
    || "source: analytics_raw.order_items,\n"
    || "owner: data_engineering"
);
