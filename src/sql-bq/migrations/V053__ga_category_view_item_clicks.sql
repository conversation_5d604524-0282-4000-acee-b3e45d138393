drop table if exists google_analytics.category_view_item_clicks;
create table google_analytics.category_view_item_clicks (
    event_date date,
    event_timestamp timestamp,
    event_name string,
    event_rank int64,
    user_pseudo_id string,
    market string,
    page_location string,
    product_id int64,
    product_name string,
    item_list_name string,
    price float64,
    category_slug string,
    sl_key string,
    category_id int64
)
partition by
event_date
cluster by
event_name,
item_list_name
options (
    partition_expiration_days = 366,
    require_partition_filter = true,
    description = "Category view item list and item clicks table.\n"
    || "subject: Ranking,\n"
    || "notion: https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4,\n" -- noqa
    || "owner: data_engineering"
);

drop table if exists google_analytics.category_page_impression_and_clicks;
create table google_analytics.category_page_impression_and_clicks (
    event_date date,
    market string,
    category_id int64,
    category_slug string,
    product_id int64,
    impressions int64,
    avg_price float64,
    c_page_item_clicks int64
)
partition by
event_date
cluster by
market,
product_id,
category_id
options (
    partition_expiration_days = 366,
    require_partition_filter = true,
    description = "Category page impression and clicks table.\n"
    || "subject: Ranking,\n"
    || "notion: https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4,\n" -- noqa
    || "owner: data_engineering"
);

drop table if exists google_analytics.category_view_item_clicks_purchases;
create table google_analytics.category_view_item_clicks_purchases (
    event_date date,
    category_id int64,
    category_name string,
    market string,
    product_id int64,
    impressions int64,
    avg_price float64,
    c_page_item_clicks int64,
    p_page_item_clicks int64,
    transactions int64,
    trx_revenue_total numeric,
    trx_product_revenue_total numeric,
    trx_addon_revenue numeric
)
partition by
event_date
options (
    partition_expiration_days = 366,
    require_partition_filter = true,
    description = "Category view item clicks and purchases table.\n"
    || "subject: Ranking,\n"
    || "notion: https://www.notion.so/refurbed/Develop-ETL-For-Ranking-159b6a8983ab8065ae09e70a7272c2f4,\n" -- noqa
    || "owner: data_engineering"
);
