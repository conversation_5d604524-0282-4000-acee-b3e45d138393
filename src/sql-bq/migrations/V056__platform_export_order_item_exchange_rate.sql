drop table if exists analytics_transformed.order_item_exchange_rate;
create table if not exists analytics_transformed.order_item_exchange_rate
(
    order_item_id int64 not null primary key not enforced,
    exchange_rate numeric
)
cluster by order_item_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_order_item_exchange_rate,\n"
    || "source: analytics_raw.order_item_exchange_rate,\n"
    || "owner: data_engineering"
);
