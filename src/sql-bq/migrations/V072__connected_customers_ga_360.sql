drop table if exists google_analytics.sessions_360_static;
create table if not exists google_analytics.sessions_360_static
(
    -- PK columns
    visitor_id string not null,
    transaction_id string not null,
    visit_date date not null,

    -- For possible merge operations
    primary key (visitor_id, transaction_id, visit_date) not enforced
)

options (
    description = "Static GA360 transactions deduplicated by visitor_id, transaction_id, visit_date. "
    || "Used in union with GA4 data for connected customers pipeline. "
    || "Loaded one-off manually.\n"
    || "procedure: queries/xxx,\n"
    || "source: refb-analytics.214239644.ga_sessions_*,\n"
    || "owner: data_engineering",
    enable_fine_grained_mutations = true
);
