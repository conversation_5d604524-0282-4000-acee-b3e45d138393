drop table if exists analytics_transformed.product_categories;
create table if not exists analytics_transformed.product_categories
( -- noqa
    product_id int64 not null,
    main_category_id int64 not null,
    show_category_id int64 not null,

    primary key (product_id, show_category_id) not enforced
)
cluster by
product_id,
show_category_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_product_categories,\n"
    || "source: analytics_raw.product_categories,\n"
    || "owner: data_engineering"
);
