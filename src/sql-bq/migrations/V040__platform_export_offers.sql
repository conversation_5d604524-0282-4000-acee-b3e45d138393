drop table if exists analytics_transformed.offers;
create table if not exists analytics_transformed.offers
( -- noqa
    id int64 not null,

    -- change tracking attributes
    created_at timestamp not null,
    valid_from timestamp not null,
    valid_to timestamp not null,

    -- supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- offers
    state string not null,
    instance_id int64,
    merchant_id int64 not null,
    warranty int64 not null,
    stock int64 not null,
    grading string not null,
    hidden bool not null,
    tax_difference bool not null,

    shipping_profile_id int64 not null,
    shipping_profile_valid_from timestamp not null,
    reference_currency_code string not null,
    reference_price numeric(14, 4) not null,
    reference_min_flex_price numeric(14, 4) not null,
    sku string,

    primary key (valid_from, id) not enforced,
    foreign key (instance_id) references analytics_transformed.instances (id) not enforced,
    foreign key (merchant_id) references analytics_transformed.merchants (id) not enforced,
    foreign key (shipping_profile_id, shipping_profile_valid_from)
    references analytics_transformed.shipping_profiles (id, valid_from) not enforced
)
partition by
timestamp_trunc(valid_from, year)
cluster by
id,
valid_to,
merchant_id,
instance_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_offers,\n"
    || "source: analytics_raw.offers,\n"
    || "owner: data_engineering"
);
