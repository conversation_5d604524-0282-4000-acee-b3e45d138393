drop table if exists analytics_transformed.users;
create table if not exists analytics_transformed.users
( -- noqa
    -- PK
    id int64 primary key not enforced,

    -- Change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    email string not null,
    first_name string not null,
    family_name string not null,
    `type` string not null,
    language string not null, -- noqa
    merchant_id int64 references analytics_transformed.merchants (id) not enforced
)
cluster by
id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_users,\n"
    || "source: analytics_raw.users,\n"
    || "owner: data_engineering",
    enable_fine_grained_mutations = true
);
