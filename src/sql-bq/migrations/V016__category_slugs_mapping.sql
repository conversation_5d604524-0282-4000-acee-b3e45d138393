drop table if exists analytics_transformed.category_slugs_mapping;
create table if not exists analytics_transformed.category_slugs_mapping
(
    category_id integer not null,

    -- slug per country
    market string not null,
    language string not null, # noqa
    category_slug string not null,

    -- Change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    primary key (category_id, market) not enforced
)
cluster by category_id;
