drop external table if exists analytics_raw.users;
drop table if exists analytics_transformed.users;
drop procedure if exists analytics_transformed.load_users;

drop external table if exists analytics_raw.partners;
drop table if exists analytics_transformed.partners;
drop procedure if exists analytics_transformed.load_partners;

drop external table if exists analytics_raw.addresses;
drop table if exists analytics_transformed.addresses;
drop procedure if exists analytics_transformed.load_addresses;

drop external table if exists analytics_raw.order_item_details;
drop table if exists analytics_transformed.order_item_details;
drop procedure if exists analytics_transformed.load_order_item_details;

drop external table if exists analytics_raw.order_refunds;
drop table if exists analytics_transformed.order_refunds;
drop procedure if exists analytics_transformed.load_order_refunds;

drop external table if exists analytics_raw.order_item_refunds;
drop table if exists analytics_transformed.order_item_refunds;
drop procedure if exists analytics_transformed.load_order_item_refunds;

drop external table if exists analytics_raw.order_documents;
drop table if exists analytics_transformed.order_documents;
drop procedure if exists analytics_transformed.load_order_documents;

drop external table if exists analytics_raw.merchant_orders;
drop table if exists analytics_transformed.merchant_orders;
drop procedure if exists analytics_transformed.load_merchant_orders;
