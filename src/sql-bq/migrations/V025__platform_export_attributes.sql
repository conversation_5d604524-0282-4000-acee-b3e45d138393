drop table if exists analytics_transformed.attributes;
create table if not exists analytics_transformed.attributes
( -- noqa
    attribute_id int64 not null,
    attribute_value_id int64 not null,
    primary key (attribute_id, attribute_value_id) not enforced,

    -- Business fields
    attribute_name string not null,
    attribute_value string not null,
    `type` string not null,
    filterable bool not null,
    `precision` int64 not null,
    unit string,
    value_bool bool,
    value_numeric numeric,
    value_enum_id int64,
    `value` json
)
cluster by
attribute_id,
attribute_value_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_attributes,\n"
    || "source: analytics_raw.attributes,\n"
    || "owner: data_engineering"
);
