drop table if exists analytics_transformed.shipping_profiles;
create table if not exists analytics_transformed.shipping_profiles
( -- noqa
    id int64 not null,

    -- change tracking attributes
    valid_from timestamp not null,
    valid_to timestamp not null,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    merchant_id int64 not null,
    `name` string not null,
    ships_from string not null,

    primary key (valid_from, id) not enforced,
    foreign key (merchant_id) references analytics_transformed.merchants (id) not enforced
)
cluster by
valid_from,
id,
valid_to
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_shipping_profiles,\n"
    || "source: analytics_raw.shipping_profiles,\n"
    || "owner: data_engineering"
);
