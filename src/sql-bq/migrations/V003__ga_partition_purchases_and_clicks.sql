-- Drop view is required should any columns will change its data type
drop view if exists google_analytics.purchases_and_clicks_view;

create or replace view google_analytics.purchases_and_clicks_view
/*
View over Google Analytics table: analytics_304326635.events_fresh_:
- Use `view` suffix to differentiate from the physical table with the same name!
- It is the source for the `purchases_and_clicks` partitioned table thus inside a migration script
- DATE functions: If no time zone is specified with timestamp_expression, the default time zone, UTC, is used.
*/
as
select distinct
    -- table partition
    _table_suffix as table_suffix,
    date(parse_date('%Y%m%d', _table_suffix)) as table_date,

    -- logical partitions
    date(timestamp_micros(event_timestamp)) as event_date,
    timestamp_micros(event_timestamp) as event_timestamp,
    event_name,
    split(events.device.web_info.hostname, 'www.refurbed.')[safe_offset(1)] as page_country,

    -- purchases
    (
        select value.int_value from unnest(event_params)
        where key = 'ga_session_id'
    ) as session_id,
    user_pseudo_id,
    traffic_source.medium as channel,
    ecommerce.transaction_id,
    case when events.device.category = 'mobile' then 1 else 0 end as is_mobile,

    -- clicks
    device.category as device_category,
    traffic_source.name as traffic_source_name,
    geo.city as geo_city,
    geo.country as geo_country,
    device.web_info.browser,
    safe_cast(item_id as int64) as product_id,
    item_name as product_name,

    -- As purchase event doesn't have item_params defined, we need to get the grade from item_variant
    coalesce(
        (
            select value.string_value from unnest(item_params)
            where key = 'grade'
        ),
        (case
            when regexp_extract(item_variant, r'Grade\s([A-Z])') is not null
                then regexp_extract(item_variant, r'Grade\s([A-Z])')
            when regexp_extract(item_variant, r'Grade\s(\d+)') = '1'
                then 'A'
            when regexp_extract(item_variant, r'Grade\s(\d+)') = '2'
                then 'B'
            when regexp_extract(item_variant, r'Grade\s(\d+)') = '3'
                then 'C'
            else 'Unknown'
        end)
    ) as grade,

    (
        select value.int_value from unnest(item_params)
        where key = 'offer_id'
    ) as offer_id,
    (
        select safe_cast(coalesce(value.int_value, value.double_value, value.float_value) as numeric)
        from unnest(item_params)
        where key = 'price2'
    ) as price,
    safe_cast(
        split((
            select value.string_value from unnest(item_params)
            where key = 'dynamic_pricing_model'
        ), '|')[
            safe_offset(0)
        ] as int64
    ) as dynamic_pricing_model,

    -- As purchase event has item_category set as product_category, whereas view_item has item_category3
    (case
        when item_category3 = '(notset)' then item_category
        else item_category3
    end) as product_category,

    (
        select value.string_value from unnest(item_params)
        where key = 'dynamic_pricing_model'
    ) as target_price_label,

    safe_cast(
        split((
            select value.string_value from unnest(item_params)
            where key = 'delivery_time'
        ), '-')[safe_offset(0
        )]
        as int64
    ) as delivery_min_days,

    safe_cast(
        split((
            select value.string_value from unnest(item_params)
            where key = 'delivery_time'
        ), '-')[safe_offset(1
        )]
        as int64
    ) as delivery_max_days,

    -- instance_id under item_params in "purchase" event is missing so we use item_varant.
    -- We cannot use item_variant on "view_item" event for this purpose becuase the instance id
    -- part on item_variant is empty most of the time for sports items.
    -- So, we use coalesce, where we use instance_id in view_item, but item_varint in purchase
    safe_cast(
        regexp_extract(
            coalesce(
                (
                    select value.string_value from unnest(item_params)
                    where key = 'instance_id'
                ),
                item_variant
            ),
            r'\((\d+)\)$'
        ) as int64
    ) as instance_id,
    (
        select value.string_value from unnest(item_params)
        where key = 'all_devices_warranty_options')
        as warranty_options,
    safe_cast(
        regexp_extract(
            (
                select value.string_value from unnest(item_params)
                where key = 'all_devices_warranty_options'
            ),
            r'(\d+) M [+-]€?\s?0(?:[,.]0{1,2})?\s?€?'
        ) as int64
    ) as warranty
from
    analytics_304326635.`events_fresh_*` as events, unnest(items) as items
where
    privacy_info.analytics_storage = 'Yes'
    and event_name in ('view_item', 'purchase');

-- WARNING: Do not accidentally drop big partitioned table should it already exists in PROD:
-- drop table if exists google_analytics.purchases_and_clicks;

/*
Create partitioned `purchases_and_clicks` table from view:
- partition by source table `table_date` (analytics_304326635.`events_fresh_*`)
- cluster by `event_name` as this is mainly used in all filters
- expire each partition after 360 days

References:
- https://cloud.google.com/bigquery/docs/clustered-tables#combinine-clustered-partitioned-tables
*/
create table if not exists google_analytics.purchases_and_clicks (
    table_suffix string,
    table_date date,
    event_date date,
    event_timestamp timestamp,
    event_name string,
    page_country string,
    session_id int64,
    user_pseudo_id string,
    channel string,
    transaction_id string,
    is_mobile int64,
    device_category string,
    traffic_source_name string,
    geo_city string,
    geo_country string,
    browser string,
    product_id int64,
    product_name string,
    grade string,
    offer_id int64,
    price numeric,
    dynamic_pricing_model int64,
    product_category string,
    target_price_label string,
    delivery_min_days int64,
    delivery_max_days int64,
    instance_id int64,
    warranty_options string,
    warranty int64
)
partition by
table_date
cluster by
event_name
options (
    partition_expiration_days = 360,
    require_partition_filter = true,
    description = 'GA4 smart pricing optimization: materialized google_analytics.purchases_and_clicks_view'
)
as (
    select
        table_suffix,
        table_date,
        event_date,
        event_timestamp,
        event_name,
        page_country,
        session_id,
        user_pseudo_id,
        channel,
        transaction_id,
        is_mobile,
        device_category,
        traffic_source_name,
        geo_city,
        geo_country,
        browser,
        product_id,
        product_name,
        grade,
        offer_id,
        price,
        dynamic_pricing_model,
        product_category,
        target_price_label,
        delivery_min_days,
        delivery_max_days,
        instance_id,
        warranty_options,
        warranty
    from
        google_analytics.purchases_and_clicks_view
);
