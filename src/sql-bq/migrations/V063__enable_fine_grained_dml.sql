/*
    Enable fine-grained DML in BigQuery:
    https://cloud.google.com/bigquery/docs/data-manipulation-language#enable_fine-grained_dml

    Need to use `while` instead of `for` loop as the latter is not supported by the SIMBA driver in Flyway.
*/
begin
    declare options_str string default " set options(enable_fine_grained_mutations = true)";
    declare alter_sql string;
    declare all_table_paths array<string>;
    declare i int64 default 0;
    declare current_table_path string;

    -- Load all table paths into an array
    set all_table_paths = (select array_agg(table_path) from maintenance.analytics_tables);

    while i < array_length(all_table_paths) do
        set current_table_path = all_table_paths[offset(i)];
        set alter_sql = concat("alter table ", current_table_path, options_str);
        execute immediate alter_sql;
        set i = i + 1;
    end while;
end;
