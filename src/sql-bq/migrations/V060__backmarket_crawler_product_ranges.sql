drop table if exists analytics_transformed.backmarket_product_ranges;
create table if not exists analytics_transformed.backmarket_product_ranges
( -- noqa
    product_name string,
    instance_features string,
    price float64,
    grade string,
    url string not null,
    country string,
    extra_features string,
    title string,
    id1 string,
    id2 string,
    currency string,
    start_date timestamp not null,
    end_date timestamp not null,

    primary key (url, start_date, end_date) not enforced
)
cluster by
start_date,
url,
end_date
options (
    description = "Loaded by the SQL procedure and triggered from the crawler workflow.\n"
    || "procedure: analytics_transformed.refresh_backmarket_product_ranges,\n"
    || "source: analytics_transformed.backmarket_products,\n"
    || "owner: data_engineering"
);
