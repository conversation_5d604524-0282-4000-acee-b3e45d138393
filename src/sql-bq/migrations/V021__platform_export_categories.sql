drop table if exists analytics_transformed.categories;
create table if not exists analytics_transformed.categories
( -- noqa
    id int64 primary key not enforced,

    -- Change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    category_name string not null,
    brand string not null,
    category_type string not null,
    product_category string
)
cluster by
id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_categories,\n"
    || "source: analytics_raw.categories,\n"
    || "owner: data_engineering"
);
