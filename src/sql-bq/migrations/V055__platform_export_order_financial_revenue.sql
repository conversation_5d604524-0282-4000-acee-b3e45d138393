/*
FK dependency on:
- analytics_transformed.order_item_offers
- analytics_transformed.order_items
- analytics_transformed.orders
*/
drop table if exists analytics_transformed.order_financial_revenue;
create table if not exists analytics_transformed.order_financial_revenue
(
    order_item_id int64 not null primary key not enforced,
    order_id int64 not null,
    revenue numeric,
    addon_revenue numeric,
    gmv numeric,
    discount_eur numeric,
    service_fee_revenue_eur numeric,

    foreign key (order_item_id) references analytics_transformed.order_item_offers (id) not enforced,
    foreign key (order_item_id) references analytics_transformed.order_items (id) not enforced,
    foreign key (order_id) references analytics_transformed.orders (id) not enforced
)
cluster by order_item_id, order_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_order_financial_revenue,\n"
    || "source: analytics_raw.order_financial_revenue,\n"
    || "owner: data_engineering"
);
