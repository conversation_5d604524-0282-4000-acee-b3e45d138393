drop table if exists analytics_transformed.instance_attribute_values;
create table if not exists analytics_transformed.instance_attribute_values
( -- noqa
    instance_id int64 not null,
    attribute_id int64 not null,
    attribute_value_id int64 not null,

    primary key (instance_id, attribute_value_id) not enforced,
    foreign key (instance_id)
    references analytics_transformed.instances (id) not enforced,
    foreign key (attribute_id, attribute_value_id)
    references analytics_transformed.attributes (attribute_id, attribute_value_id) not enforced
)
cluster by
instance_id,
attribute_value_id,
attribute_id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_instance_attribute_values,\n"
    || "source: analytics_raw.instance_attribute_values,\n"
    || "owner: data_engineering"
);
