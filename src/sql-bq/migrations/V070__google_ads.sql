drop table if exists analytics_transformed.google_ads_costs;
create table if not exists analytics_transformed.google_ads_costs
(
    cost_date date not null,
    cost_hour int not null,
    client_account string not null,
    campaign_name string not null,
    costs float64 not null,
    updated_at timestamp not null,

    primary key (cost_date, cost_hour, client_account, campaign_name) not enforced
)
cluster by
cost_date, cost_hour, client_account, campaign_name
options (
    description = "Loaded by the SQL procedure and triggered from the own schedule.\n"
    || "procedure: analytics_transformed.load_google_ads_costs,\n"
    || "source: analytics_raw.google_ads_costs,\n"
    || "owner: data_engineering",
    enable_fine_grained_mutations = true
);
