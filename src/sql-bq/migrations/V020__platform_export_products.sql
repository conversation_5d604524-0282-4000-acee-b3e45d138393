drop table if exists analytics_transformed.products;
create table if not exists analytics_transformed.products
( -- noqa
    id int64 primary key not enforced,

    -- Change tracking attributes
    created_at timestamp not null,
    updated_at timestamp not null,
    deleted_at timestamp,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    category_id integer not null,
    listing_mode string not null,
    name string not null, -- noqa
    name_en string,
    slug string
)
cluster by
id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_products,\n"
    || "source: analytics_raw.products,\n"
    || "owner: data_engineering"
);
