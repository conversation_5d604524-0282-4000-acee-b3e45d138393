drop table if exists analytics_transformed.offer_properties;
create table if not exists analytics_transformed.offer_properties
(
    offer_id int64 not null primary key not enforced,

    -- dates
    offer_valid_from timestamp not null,
    created_at timestamp not null,

    -- properties
    battery_condition string not null,
    is_new_battery bool not null,

    -- FK constraints
    foreign key (offer_id, offer_valid_from) references analytics_transformed.offers (id, valid_from) not enforced
)
cluster by
offer_id,
offer_valid_from
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_offer_properties,\n"
    || "source: analytics_raw.offer_properties,\n"
    || "owner: data_engineering"
);
