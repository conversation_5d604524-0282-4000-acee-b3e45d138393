-- PRODUCTS
drop table if exists platform_export.products;
create table platform_export.products
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    category_id int64,
    listing_mode string,
    `name` string,
    name_en string,
    slug string
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_products,\n"
    || "owner: data_engineering"
);

-- CATEGORIES
drop table if exists platform_export.categories;
create table platform_export.categories
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    category_name string,
    brand string,
    category_type string,
    product_category string
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_categories,\n"
    || "owner: data_engineering"
);

-- MERCHANTS
drop table if exists platform_export.merchants;
create table platform_export.merchants
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    country string,
    state string,
    is_addon_support bool,
    `name` string,
    email string,
    public_id string,
    handicap numeric,
    payment_commission_pc numeric,
    payout_commission_pc numeric
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_merchants,\n"
    || "owner: data_engineering"
);

-- INSTANCES
drop table if exists platform_export.instances;
create table platform_export.instances
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    published bool,
    product_id int64,
    `name` string,
    name_en string,
    srp numeric
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_instances,\n"
    || "owner: data_engineering"
);

-- EXCHANGE_RATES
drop table if exists platform_export.exchange_rates;
create table platform_export.exchange_rates
(
    id int64 not null,
    valid_from timestamp not null,
    valid_to timestamp,
    `base` string,
    `target` string,
    rate numeric,
    primary key (valid_from, id) not enforced
)
cluster by
valid_from,
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_exchange_rates,\n"
    || "owner: data_engineering"
);

-- ATTRIBUTES
drop table if exists platform_export.attributes;
create table platform_export.attributes
(
    attribute_id int64 not null,
    attribute_value_id int64 not null,
    attribute_name string,
    attribute_value string,
    `type` string,
    filterable bool,
    `precision` int64,
    unit string,
    value_bool bool,
    value_numeric numeric,
    value_enum_id int64,
    `value` string,
    primary key (attribute_id, attribute_value_id) not enforced
)
cluster by
attribute_id,
attribute_value_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_attributes,\n"
    || "owner: data_engineering"
);

-- INSTANCE_ATTRIBUTE_VALUES
drop table if exists platform_export.instance_attribute_values;
create table platform_export.instance_attribute_values
(
    instance_id int64 not null,
    attribute_id int64,
    attribute_value_id int64 not null,
    primary key (instance_id, attribute_value_id) not enforced
)
cluster by
instance_id,
attribute_value_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_instance_attribute_values,\n"
    || "owner: data_engineering"
);

-- PRODUCT_ATTRIBUTE_VALUES
drop table if exists platform_export.product_attribute_values;
create table platform_export.product_attribute_values
(
    product_id int64 not null,
    attribute_id int64,
    attribute_value_id int64 not null,
    primary key (product_id, attribute_value_id) not enforced
)
cluster by
product_id,
attribute_value_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_product_attribute_values,\n"
    || "owner: data_engineering"
);

-- USERS
drop table if exists platform_export.users;
create table platform_export.users
(
    id int64 primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    email string,
    first_name string,
    family_name string,
    `type` string,
    `language` string,
    merchant_id int64
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_users,\n"
    || "owner: data_engineering"
);

-- ADDRESSES
drop table if exists platform_export.addresses;
create table platform_export.addresses
(
    id int64 not null,
    valid_from timestamp not null,
    valid_to timestamp,
    owner_id int64,
    first_name string,
    family_name string,
    country string,
    post_code string,
    town string,
    street_name string,
    house_no string,
    phone string,
    male bool,
    address_type string,
    supplement string,
    company string,
    vatin string,
    personal_vatin string,
    is_company bool,
    gender string,
    primary key (id, valid_from) not enforced
)
cluster by
id,
valid_from
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_addresses,\n"
    || "owner: data_engineering"
);

-- SHIPPING_PROFILES
drop table if exists platform_export.shipping_profiles;
create table platform_export.shipping_profiles
(
    id int64 not null,
    valid_from timestamp not null,
    valid_to timestamp,
    merchant_id int64,
    `name` string,
    ships_from string,
    primary key (valid_from, id) not enforced
)
cluster by
valid_from,
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_shipping_profiles,\n"
    || "owner: data_engineering"
);

-- SHIPPING_PROFILE_DESTINATIONS
drop table if exists platform_export.shipping_profile_destinations;
create table platform_export.shipping_profile_destinations
(
    id int64 not null,
    valid_from timestamp not null,
    valid_to timestamp,
    shipping_profile_id int64,
    shipping_profile_valid_to timestamp,
    country string,
    shipping_costs numeric,
    delivery_min_days int64,
    delivery_max_days int64,
    delivery_time_cond int64,
    primary key (valid_from, id) not enforced
)
cluster by
valid_from,
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_shipping_profile_destinations,\n"
    || "owner: data_engineering"
);

-- OFFERS
drop table if exists platform_export.offers;
create table platform_export.offers
(
    id int64 not null,
    created_at timestamp,
    valid_from timestamp not null,
    valid_to timestamp,
    state string,
    instance_id int64,
    merchant_id int64,
    warranty int64,
    stock int64,
    grading string,
    hidden bool,
    tax_difference bool,
    shipping_profile_id int64,
    shipping_profile_valid_from timestamp,
    reference_currency_code string,
    reference_price numeric,
    reference_min_flex_price numeric,
    sku string,
    primary key (valid_from, id) not enforced
)
cluster by
valid_from,
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_offers,\n"
    || "owner: data_engineering"
);

-- ORDERS
drop table if exists platform_export.orders;
create table platform_export.orders
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    paid_at timestamp,
    country string,
    state string,
    is_released bool,
    user_id int64,
    partner_id int64,
    invoice_addr_id int64,
    invoice_addr_valid_from timestamp,
    shipping_addr_id int64,
    shipping_addr_valid_from timestamp,
    presentment_currency string,
    settlement_currency string,
    presentment_to_settlement_exchange_rate numeric,
    payment_provider string,
    payment_info string,
    payment_handle string,
    payment_provider_category string,
    payment_provider_info string
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_orders,\n"
    || "owner: data_engineering"
);

-- ORDER_ITEMS
drop table if exists platform_export.order_items;
create table platform_export.order_items
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    deleted_at timestamp,
    order_id int64,
    offer_id int64,
    offer_valid_from timestamp,
    offer_price_id int64,
    offer_price_valid_from timestamp,
    `type` string,
    is_addon bool,
    charged numeric,
    discount numeric,
    net bool,
    vat numeric,
    vat_country string,
    price numeric,
    flex_price numeric,
    addon_id int64,
    addon_valid_from timestamp,
    addon_assigned_order_item_id int64,
    addon_details string,
    shipping_profile_destination_id int64,
    shipping_profile_destination_valid_from timestamp,
    shipping_costs numeric,
    is_pre_shipping_versioning bool,
    presentment_price_gross numeric,
    presentment_flex_price_gross numeric,
    presentment_shipping_costs_gross numeric,
    presentment_base_commission numeric,
    presentment_payment_commission numeric,
    presentment_payout_commission numeric,
    presentment_target_price_commission numeric,
    presentment_commissions numeric,
    presentment_exchange_rate_modifier numeric,
    presentment_to_settlement_exchange_rate numeric,
    settlement_price_gross numeric,
    settlement_flex_price_gross numeric,
    settlement_shipping_costs_gross numeric,
    settlement_currency string,
    settlement_base_commission numeric,
    settlement_payout_commission numeric,
    settlement_payment_commission numeric,
    settlement_target_price_commission numeric,
    settlement_commissions numeric,
    commission_pc numeric,
    payment_commission_pc numeric,
    payout_commission_pc numeric,
    target_price_commission_pc numeric
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_order_items,\n"
    || "owner: data_engineering"
);

-- ORDER_ITEM_OFFERS
drop table if exists platform_export.order_item_offers;
create table platform_export.order_item_offers
(
    id int64 not null primary key not enforced,
    created_at timestamp,
    updated_at timestamp,
    order_id int64,
    offer_id int64,
    offer_valid_from timestamp,
    `type` string,
    paid_at timestamp,
    `state` string,
    country string,
    presentment_currency string,
    payment_provider string,
    valid_to timestamp,
    instance_id int64,
    merchant_id int64,
    grading string,
    warranty int64,
    battery_condition string,
    is_new_battery bool
)
cluster by
id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_order_item_offers,\n"
    || "owner: data_engineering"
);

-- COUNTRIES
drop table if exists platform_export.countries;
create table platform_export.countries
(
    code string not null primary key not enforced,
    `name` string,
    main_language string,
    currency string,
    is_site bool
)
cluster by
code
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_countries,\n"
    || "owner: data_engineering"
);

-- PRODUCT_CATEGORIES
drop table if exists platform_export.product_categories;
create table platform_export.product_categories
(
    product_id int64 not null,
    main_category_id int64,
    show_category_id int64 not null,
    primary key (product_id, show_category_id) not enforced
)
cluster by
product_id,
show_category_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_product_categories,\n"
    || "owner: data_engineering"
);

-- ORDER_FINANCIAL_REVENUE
drop table if exists platform_export.order_financial_revenue;
create table platform_export.order_financial_revenue
(
    order_item_id int64 not null primary key not enforced,
    order_id int64,
    revenue numeric,
    addon_revenue numeric,
    gmv numeric,
    discount_eur numeric,
    service_fee_revenue_eur numeric
)
cluster by
order_item_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: analytics.order_financial_revenue,\n"
    || "owner: data_engineering"
);

-- ORDER_ITEM_EXCHANGE_RATE
drop table if exists platform_export.order_item_exchange_rate;
create table platform_export.order_item_exchange_rate
(
    order_item_id int64 not null primary key not enforced,
    exchange_rate float64
)
cluster by
order_item_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: analytics.order_item_exchange_rate,\n"
    || "owner: data_engineering"
);

-- FULL_ORDER_ITEM_REFUNDS
drop table if exists platform_export.full_order_item_refunds;
create table platform_export.full_order_item_refunds
(
    order_item_refund_id int64 not null primary key not enforced,
    order_item_id int64,
    refunded_at timestamp,
    updated_at timestamp,
    refunded numeric,
    refunded_charge numeric,
    refunded_eur numeric,
    refunded_charge_eur numeric,
    min_reversed_commission_date date,
    held_base_commission numeric,
    kept_base_commission numeric,
    executed_base_commission numeric,
    held_payment_commission numeric,
    kept_payment_commission numeric,
    executed_payment_commission numeric,
    held_base_commission_eur numeric,
    kept_base_commission_eur numeric,
    executed_base_commission_eur numeric,
    held_payment_commission_eur numeric,
    kept_payment_commission_eur numeric,
    executed_payment_commission_eur numeric
)
cluster by
order_item_refund_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: analytics.full_order_item_refunds,\n"
    || "owner: data_engineering"
);

-- OFFER_PROPERTIES
drop table if exists platform_export.offer_properties;
create table platform_export.offer_properties
(
    offer_id int64 not null primary key not enforced,
    offer_valid_from timestamp,
    created_at timestamp,
    battery_condition string,
    is_new_battery bool
)
cluster by
offer_id
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: analytics.offer_properties,\n"
    || "owner: data_engineering"
);

-- PRODUCT_RANKINGS
drop table if exists platform_export.product_rankings;
create table platform_export.product_rankings
(
    product_id int64 not null,
    category_id int64 not null,
    market_country string not null,
    updated_at timestamp,
    rank int64,
    rank_b int64
)
cluster by
product_id,
category_id,
market_country
options (
    description = "Staging table populated by the platform-export.\n"
    || "source: export_etl.v_product_rankings,\n"
    || "owner: data_engineering"
);
