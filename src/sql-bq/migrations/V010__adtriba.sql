drop table if exists analytics_transformed.adtriba;
create table if not exists analytics_transformed.adtriba
(
    brand string,
    country string,
    conversion_event string,
    device_type string,
    `date` date,
    sphere_source string,
    attributed_conversions float64,
    attributed_revenue float64,
    `type` string,
    spend float64,
    roas float64,
    cpa float64,
    optimization_target string
)
cluster by
country, conversion_event, device_type, `date`;
