drop table if exists analytics_transformed.shipping_profile_destinations;
create table if not exists analytics_transformed.shipping_profile_destinations
( -- noqa
    id int64 not null,

    -- Change tracking attributes
    valid_from timestamp not null,
    valid_to timestamp not null,

    -- Supportive fields that ease merge
    modified_at timestamp not null,
    is_active bool not null,

    -- Business fields
    shipping_profile_id int64 not null,
    shipping_profile_valid_to timestamp not null,
    country string not null,
    shipping_costs numeric not null,
    delivery_min_days int64,
    delivery_max_days int64,
    delivery_time_cond int64,

    primary key (valid_from, id) not enforced
    /*
    ERROR: Need for 2nd primary key on shipping_profiles (id, valid_to)

    foreign key (shipping_profile_id, shipping_profile_valid_to)
    references analytics_transformed.shipping_profiles (id, valid_to) not enforced
    */
)
cluster by
valid_from,
id
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_shipping_profile_destinations,\n"
    || "source: analytics_raw.shipping_profile_destinations,\n"
    || "owner: data_engineering"
);
