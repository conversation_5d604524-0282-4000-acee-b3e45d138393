-- cannot run it in one transaction
-- DDL statements are not supported in a transaction
begin
    create temp table pipedrive_bk as
    select * from analytics_transformed.pipedrive;

    drop table if exists analytics_transformed.pipedrive;
    create table if not exists analytics_transformed.pipedrive
    (
        new_sales_manager string,
        new_sales_manager_email string,
        merchant_id integer,
        merchant_name string,
        primary_email string,
        all_emails array<struct<label string, primary bool, value string>>,
        account_manager string,
        account_manager_email string,
        account_name string,
        first_name string,
        last_name string,
        contact_roles array<string>,
        has_performance_role bool,
        live_selling timestamp,
        date_entered date,
        date_closed date
    );

    insert into analytics_transformed.pipedrive
    (
        new_sales_manager,
        merchant_id,
        merchant_name,
        primary_email,
        all_emails,
        account_manager,
        account_name,
        first_name,
        last_name,
        live_selling,
        date_entered,
        date_closed
    )
    select
        new_sales_manager,
        merchant_id,
        merchant_name,
        primary_email,
        all_emails,
        account_manager,
        account_name,
        first_name,
        last_name,
        live_selling,
        date_entered,
        date_closed
    from
        pipedrive_bk;
end;
