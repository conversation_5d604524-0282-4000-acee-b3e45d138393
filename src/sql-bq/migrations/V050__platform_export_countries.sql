drop table if exists analytics_transformed.countries;
create table if not exists analytics_transformed.countries
( -- noqa
    code string not null primary key not enforced,
    name string not null, -- noqa
    main_language string not null,
    currency string not null,
    is_site bool not null
)
cluster by
code
options (
    description = "Loaded by the SQL procedure and triggered from the platform-export workflow.\n"
    || "procedure: analytics_transformed.load_countries,\n"
    || "source: analytics_raw.countries,\n"
    || "owner: data_engineering"
);
