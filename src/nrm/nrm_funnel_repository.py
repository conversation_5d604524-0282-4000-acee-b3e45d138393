from datetime import date
from pathlib import Path
from typing import Optional

from nrm_model import MARKETING_COUNTRIES_SCHEMA
from pandas import DataFrame

from common.bq_client import BigQueryClient, BigQueryConfig
from common.bq_repository import BigQueryRepository
from common.logger import get_logger

logger = get_logger()

CURRENT_DIR = Path(__file__).resolve().parent


class NrmBqRepositoryError(ValueError):
    """NRM specific BQ repository error"""


class NrmFunnelRepository(BigQueryRepository):
    """NRM Funnelio read-only repository for reading the source data"""

    DATASET = "Funnel_export_refurbed"
    TABLE = "all_funnel_data_view"

    QUERY_FILE = CURRENT_DIR / "marketing_countries.sql"

    @classmethod
    def create_bq_client(cls) -> BigQueryClient:
        """Creates BQ client for the funnelio project source"""
        bq_config = BigQueryConfig(project_id="refb-analytics-funnelio", location="US")
        return BigQueryClient(bq_config=bq_config)

    def __init__(self, bq_client: Optional[BigQueryClient] = None) -> None:
        bq_client = bq_client or self.create_bq_client()
        super().__init__(bq_client)

    def select_marketing_countries(self, start_date: date, end_date: Optional[date] = None) -> DataFrame:
        """
        Selects marketing countries for the given start and end dates.

        :param start_date: start of `all_funnel_data_view.date`
        :param end_date: optional end of `all_funnel_data_view.date`, defaults to start_date

        :returns: DataFrame with the following columns [BQ type]:
                  - date [date]
                  - country [string]
                  - cost [float64]
                  - cost_online [float64]
        """
        end_date = end_date or start_date

        if start_date > end_date:
            raise NrmBqRepositoryError(f"Start date must be equal or before end date: {start_date=}, {end_date=}!")

        result = self.select_from_query_file(
            self.QUERY_FILE, start_date=start_date.isoformat(), end_date=end_date.isoformat()
        )
        logger.info(f"{self.QUERY_FILE.name}: {result.log_totals}")

        return result.job_result.to_dataframe(dtypes=MARKETING_COUNTRIES_SCHEMA.dtypes)
