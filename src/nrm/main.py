from datetime import datetime

import functions_framework
from cloudevents.abstract import CloudEvent
from etl import NrmEtl
from google.cloud.sql.connector import Connector
from nrm_etl_dispatcher import NrmEtlDispatcher
from nrm_funnel_repository import NrmFunnelRepository
from nrm_model import NrmConfig
from nrm_pg_repository import NrmPgRepository

from common.cloud_events import create_http_event, get_event_message
from common.config import Config
from common.consts import ANALYTICS_CLOUD_SQL_SECRET_ID
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.pipeline_logging.pipeline_model import PipelineRun
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig
from common.time_service import TimeService
from common.timing import timing
from common.typings import HttpResponse

config = Config()
logger = setup_logger(config)

pg_secret = get_secret(ANALYTICS_CLOUD_SQL_SECRET_ID, config.project_id)
pg_config = DatabaseConfig.from_json(pg_secret)

funnel_repository = NrmFunnelRepository()
raw_repository = DataLakeRepository(config.raw_bucket)
transformed_repository = DataLakeRepository(config.transformed_bucket)


@timing(logger, "nrm-etl")
@functions_framework.http
def run(request: CloudEvent) -> HttpResponse:
    """
    Http function entry point, called from analytics-workflow.
    """
    if config.is_production:
        time_service = TimeService()
    else:
        # Use datetime available on local/staging database instance
        time_service = TimeService(datetime(2024, 9, 1))

    message = get_event_message(request)
    pipeline_run = PipelineRun.from_json(message)
    msg = f"run_id={pipeline_run.run_id}, load_date={time_service.today.isoformat()}"
    logger.info(f"Starting load for {msg}...")

    with Connector() as connector:
        pg_repository = NrmPgRepository(db_config=pg_config, connector=connector)
        dispatcher = NrmEtlDispatcher(pipeline_run=pipeline_run, pg_repository=pg_repository, time_service=time_service)

        days_to_load = dispatcher.get_days_to_load()
        logger.info(f"Found {len(days_to_load)} day(s) to load {days_to_load=}.")

        for day in days_to_load:
            nrm_config = NrmConfig(load_date=day)
            logger.info(f"{nrm_config.description} started...")
            etl = NrmEtl(
                config=nrm_config,
                pipeline_run=pipeline_run,
                pg_repository=pg_repository,
                bq_repository=funnel_repository,
                raw_data_lake=raw_repository,
                transformed_data_lake=transformed_repository,
            )
            etl.run()
            logger.info(f"{nrm_config.description} finished!")

    logger.info(f"Finished load for {msg}.")
    return HttpResponse()


if __name__ == "__main__":
    # Overwrite db_config with local docker db to test your SQL before releasing to staging
    pg_config = DatabaseConfig()
    test_pipeline = PipelineRun()
    run(create_http_event(test_pipeline.to_json()))
