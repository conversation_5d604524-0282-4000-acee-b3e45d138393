"""
Prepare input data for `neuralforecast`:
- https://nixtlaverse.nixtla.io/neuralforecast/docs/getting-started/introduction.html
- https://github.com/Nixtla/neuralforecast/blob/main/neuralforecast/utils.py#L250
"""

from nrm_model import (
    MC_COLUMNS,
    NEURAL_FORECAST_SCHEMA,
    RC_COLUMNS,
    NeuralForecastColumns,
)
from pandas import DataFrame, concat


def transform_revenue_countries(df: DataFrame) -> DataFrame:
    """
    Transform revenue countries into NRM format.

    :param df: input dataframe as returned by `NrmPgRepository`
    :returns: dataframe transformed into NRM format
    """
    return _transform_into_nrm(df, RC_COLUMNS.DATE, RC_COLUMNS.COUNTRY)


def transform_marketing_countries(df: DataFrame) -> DataFrame:
    """
    Transform marketing countries into NRM format.

    :param df: input dataframe as returned by `NrmFunnelRepository`
    :returns: dataframe transformed into NRM format
    """
    df[MC_COLUMNS.COST_OFFLINE] = df[MC_COLUMNS.COST] - df[MC_COLUMNS.COST_ONLINE]
    return _transform_into_nrm(df, MC_COLUMNS.DATE, MC_COLUMNS.COUNTRY)


def concat_transformed(revenue_countries: DataFrame, marketing_countries: DataFrame) -> DataFrame:
    """
    Concatenate transformed results into single DataFrame.

    **Note:** we need to check whether concatenated DataFrame is not empty to avoid concat warning:
    `In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes`

    :param revenue_countries: revenue countries data frame
    :param marketing_countries: marketing countries data frame
    :returns: concatenated dataframe
    """
    if revenue_countries.empty:
        return marketing_countries

    if marketing_countries.empty:
        return revenue_countries

    return concat([revenue_countries, marketing_countries], ignore_index=True)


def _transform_into_nrm(df: DataFrame, date_column: str, country_column: str) -> DataFrame:
    """
    1. Unpivot dataframe to:
    - date
    - country
    - unique_id => column name + country
    - y => column value

    2. Transform un-pivoted result into:
    - ds => date
    - unique_id => unique_id + "_" + country
    - y => y

    :param df: input dataframe
    :param date_column: date column name
    :param country_column: country column name

    :returns: dataframe transformed into NRM format
    """
    if df.empty:
        return DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    return (
        # Unpivot columns into column=`unique_id` and value=`y`
        df.melt(
            id_vars=[date_column, country_column],
            var_name=NeuralForecastColumns.UNIQUE_ID,
            value_name=NeuralForecastColumns.Y,
        )
        # Concatenate `unique_id` with `country`
        .assign(
            **{NeuralForecastColumns.UNIQUE_ID: lambda x: x[NeuralForecastColumns.UNIQUE_ID] + "_" + x[country_column]}
        )
        # Drop `country` and rename `date` -> `ds`
        .drop(columns=[country_column])
        .rename(columns={date_column: NeuralForecastColumns.DS})
        .astype(dtype=NEURAL_FORECAST_SCHEMA.dtypes)
    )
