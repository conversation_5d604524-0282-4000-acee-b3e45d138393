from datetime import date

from common.bq_repository import BigQueryRepository, BQProcedure


class NrmTransformedRepository(BigQueryRepository):
    """NRM Analytics BQ transformed repository for checking loaded data"""

    DATASET = "analytics_transformed"
    TABLE = "nrm"

    COUNT_ROWS_PROCEDURE = BQProcedure(DATASET, "count_nrm_rows")

    def count_rows(self, load_date: date) -> int:
        """
        Count rows in the `nrm` table for given date (year, month, day) partition.

        :param load_date: load date / partition
        :returns: number of rows
        """
        result = self.call_procedure(self.COUNT_ROWS_PROCEDURE, [load_date.year, load_date.month, load_date.day])

        if (rows := result.rows) and len(rows) == 1:
            row = rows[0]

            return int(row.row_count)
        else:
            return 0
