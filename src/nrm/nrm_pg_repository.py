from datetime import date, timedelta
from typing import Optional

from nrm_model import REVE<PERSON>E_COUNTRIES_SCHEMA
from pandas import <PERSON>Frame
from sqlalchemy import Connection

from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository


class NrmPgRepositoryError(ValueError):
    """NRM specific PG repository error"""


class NrmPgRepository(SqlRepository):
    """NRM Analytics PG read-only repository for reading the source data"""

    SELECT_REVENUE_COUNTRIES = SqlFunction(schema_name="analytics", function_name="select_revenue_countries")

    def select_revenue_countries(
        self, start_date: date, end_date: Optional[date] = None, transaction: Optional[Connection] = None
    ) -> DataFrame:
        """
        Selects revenue countries for the given start and end dates.
        Note that to select a single day you need to query for end_date = start_date + 1 day!

        :param start_date: start of `order_item_offers.paid_at`
        :param end_date: optional end of `order_item_offers.paid_at`, defaults to start_date + 1 day
        :param transaction: optional transaction, if not provided new transaction is started

        :returns: DataFrame with the following columns [PG type]:
                  - paid_at [timestamp]
                  - country [text]
                  - revenue [numeric]
                  - gmv [numeric]
        """
        end_date = end_date or (start_date + timedelta(days=1))

        if start_date > end_date:
            raise NrmPgRepositoryError(f"Start date must be equal or before end date: {start_date=}, {end_date=}!")

        args = [start_date.isoformat(), end_date.isoformat()]
        result = self.select_from_function(function=self.SELECT_REVENUE_COUNTRIES, args=args, transaction=transaction)

        df = DataFrame(result)
        if df.empty:
            return DataFrame(columns=REVENUE_COUNTRIES_SCHEMA.columns)
        else:
            return df.astype(REVENUE_COUNTRIES_SCHEMA.dtypes)
