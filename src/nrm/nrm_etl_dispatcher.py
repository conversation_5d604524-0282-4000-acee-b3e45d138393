from datetime import date
from os import environ
from typing import Optional

from nrm_transformed_repository import NrmTransformedRepository

from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_repository import SqlRepository
from common.time_service import TimeService

logger = get_logger()


class NrmEtlDispatcher(SupportsPipelineExecution):
    """NRM etl dispatcher responsible for finding dates to be loaded"""

    DEFAULT_RELOAD_DAYS = 5

    def __init__(
        self,
        pipeline_run: PipelineRun,
        pg_repository: SqlRepository,
        time_service: Optional[TimeService] = None,
        bq_repository: Optional[NrmTransformedRepository] = None,
    ):
        self._pipeline_run = pipeline_run
        self._pg_repository = pg_repository
        self._time_service = time_service if time_service is not None else TimeService()
        self._bq_repository = bq_repository if bq_repository is not None else NrmTransformedRepository()

    @property
    def reload_days(self) -> int:
        """Number of days to check for reload, default is 5"""
        return int(environ.get("RELOAD_DAYS", self.DEFAULT_RELOAD_DAYS))

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._pg_repository

    @log_pipeline_execution(step_name="nrm:get days to load")
    def get_days_to_load(self, reload_days: Optional[int] = None) -> list[date]:
        """
        Checks NRM data for missing load dates, exclude today as we expect data up to yesterday!

        :param reload_days: maximum number of historical days to check and load.
        :returns: list of dates to be loaded
        """
        result = []
        reload_days = reload_days or self.reload_days
        days = list(self._time_service.last_days(reload_days + 1, start=1))

        for day in days:
            row_count = self._bq_repository.count_rows(day)
            if row_count > 0:
                logger.info(f"{day.isoformat()} was already loaded with {row_count=}.")
            else:
                logger.info(f"{day.isoformat()} has not been loaded and will be scheduled for load!")
                result.append(day)

        return result
