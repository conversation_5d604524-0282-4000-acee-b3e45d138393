from dataclasses import dataclass

from nrm_funnel_repository import NrmFunnelRepository
from nrm_model import NrmConfig, TableName
from nrm_pg_repository import NrmPgRepository
from nrm_transformer import (
    concat_transformed,
    transform_marketing_countries,
    transform_revenue_countries,
)
from pandas import DataFrame

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_repository import SqlRepository

logger = get_logger()


@dataclass(frozen=True)
class Extracted:
    """
    Extracted result used internally by the `NrmEtl` only.
    """

    revenue_countries: DataFrame
    marketing_countries: DataFrame


@dataclass(frozen=True)
class Transformed:
    """
    Transformed result used internally by the `NrmEtl` only.
    """

    extracted: Extracted
    nrm: DataFrame


class NrmEtl(BaseEtl, SupportsPipelineExecution):
    _config: NrmConfig

    def __init__(
        self,
        config: NrmConfig,
        pipeline_run: PipelineRun,
        pg_repository: NrmPgRepository,
        bq_repository: NrmFunnelRepository,
        raw_data_lake: DataLakeRepository,
        transformed_data_lake: DataLakeRepository,
    ) -> None:
        super().__init__(config)
        self._pipeline_run = pipeline_run
        self._pg_repository = pg_repository
        self._bq_repository = bq_repository
        self._raw_data_lake = raw_data_lake
        self._transformed_data_lake = transformed_data_lake

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._pg_repository

    def get_step_name(self, function_name: str) -> str:
        """SupportsPipelineExecution interface"""
        return f"nrm:{function_name} day {self._config.load_date.day}"

    @log_pipeline_execution()
    def extract(self) -> Extracted:
        """
        Extracts data from the following sources:
        - PG: `revenue_countries`
        - BQ: `marketing_countries`

        :returns: Extracted data
        """
        revenue_countries = self._pg_repository.select_revenue_countries(self._config.load_date)
        logger.info(f"Extracted {revenue_countries.shape=} from PG.")

        marketing_countries = self._bq_repository.select_marketing_countries(self._config.load_date)
        logger.info(f"Extracted {marketing_countries.shape=} from BQ.")

        return Extracted(revenue_countries, marketing_countries)

    @log_pipeline_execution()
    def transform(self, extracted: Extracted) -> Transformed:
        """
        Transforms extracted data into concatenated, `neuralforecast` compatible data frame.

        :param extracted: Extracted data
        :returns: Transformed data
        """
        revenue_countries = transform_revenue_countries(extracted.revenue_countries)
        marketing_countries = transform_marketing_countries(extracted.marketing_countries)
        nrm = concat_transformed(revenue_countries, marketing_countries)
        logger.info(f"Transformed and concatenated into {nrm.shape=}.")

        return Transformed(extracted, nrm)

    @log_pipeline_execution()
    def load(self, transformed: Transformed) -> int:
        self._load_into_raw(transformed.extracted.revenue_countries, "revenue_countries")
        self._load_into_raw(transformed.extracted.marketing_countries, "marketing_countries")
        self._load_into_transformed(transformed.nrm)

        return len(transformed.nrm)

    def _load_into_raw(self, df: DataFrame, table_name: TableName) -> None:
        bucket_name = self._raw_data_lake.bucket_name
        path = self._raw_data_lake.upload_table_partition(
            data_frame=df,
            table_name=table_name,
            load_date=self._config.load_date,
            root_folder=self._config.root_folder,
        )
        if path:
            logger.info(f"Loaded extracted {table_name=} into {bucket_name=} under '{str(path)}'.")
        else:
            logger.error(f"No data in {table_name=}!")

    def _load_into_transformed(self, df: DataFrame) -> None:
        bucket_name = self._transformed_data_lake.bucket_name
        path = self._transformed_data_lake.upload_table_partition(
            data_frame=df,
            table_name="nrm",
            load_date=self._config.load_date,
            root_folder=self._config.root_folder,
        )
        if path:
            logger.info(f"Loaded NRM into {bucket_name=} under '{str(path)}'.")
        else:
            logger.error("No NRM data to upload!")
