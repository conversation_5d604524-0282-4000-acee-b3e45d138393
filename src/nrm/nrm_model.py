from dataclasses import dataclass
from datetime import date
from functools import cached_property
from typing import Literal

# https://googleapis.dev/python/db-dtypes/latest/usage.html
from db_dtypes import date_dtype_name
from pandas import Float32Dtype, Int32Dtype, StringDtype

from common.config import Config
from common.pandas_utils import DataFrameSchema

TableName = Literal["revenue_countries", "marketing_countries", "nrm"]


@dataclass(frozen=True)
class NrmConfig(Config):
    """
    NRM configuration:
    - root_folder: root folder in a GCS bucket where NRM data is stored
    - load_date: date to be loaded (YYYY-MM-DD)
    - description: common prefix for log messages
    """

    root_folder = "crystal_ball"
    load_date: date = date.today()

    @cached_property
    def description(self) -> str:
        """Config as log friendly description"""
        return f"NRM pipeline for '{self.load_date.isoformat()}'"


class RevenueCountriesColumns:
    DATE = "paid_at"
    COUNTRY = "country"
    REVENUE = "revenue"
    GMV = "gmv"
    N_ORDERS = "n_orders"
    N_ORDER_ITEMS = "n_order_items"


RC_COLUMNS = RevenueCountriesColumns
REVENUE_COUNTRIES_SCHEMA = DataFrameSchema(
    # Need to use same data_types as in MARKETING_COUNTRIES_SCHEMA for concatenation to work!
    data_types={
        RC_COLUMNS.DATE: date_dtype_name,
        RC_COLUMNS.COUNTRY: StringDtype(),
        RC_COLUMNS.REVENUE: Float32Dtype(),
        RC_COLUMNS.GMV: Float32Dtype(),
        RC_COLUMNS.N_ORDERS: Int32Dtype(),
        RC_COLUMNS.N_ORDER_ITEMS: Int32Dtype(),
    },
)


class MarketingCountriesColumns:
    DATE = "date"
    COUNTRY = "country"
    COST = "cost"
    COST_ONLINE = "cost_online"

    # Calculated column:
    COST_OFFLINE = "cost_offline"


MC_COLUMNS = MarketingCountriesColumns
MARKETING_COUNTRIES_SCHEMA = DataFrameSchema(
    # Need to use same data_types as in REVENUE_COUNTRIES_SCHEMA for concatenation to work!
    data_types={
        MarketingCountriesColumns.DATE: date_dtype_name,
        MarketingCountriesColumns.COUNTRY: StringDtype(),
        MarketingCountriesColumns.COST: Float32Dtype(),
        MarketingCountriesColumns.COST_ONLINE: Float32Dtype(),
    },
)


class NeuralForecastColumns:
    """
    `neuralforecast` columns used by the `nrm_transformer`:
    - ds: date [date_dtype_name]
    - unique_id: revenue / marketing column + country [StringDtype]
    - y: revenue / marketing value [Float32Dtype]
    """

    DS = "ds"
    UNIQUE_ID = "unique_id"
    Y = "y"


NEURAL_FORECAST_SCHEMA = DataFrameSchema(
    data_types={
        NeuralForecastColumns.DS: date_dtype_name,
        NeuralForecastColumns.UNIQUE_ID: StringDtype(),
        NeuralForecastColumns.Y: Float32Dtype(),
    }
)
