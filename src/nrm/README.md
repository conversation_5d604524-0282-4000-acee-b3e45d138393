# NRM forecast supporting ETL (crystal ball)

## Specification
See description in notion [page](https://www.notion.so/refurbed/Build-ETLs-for-NRM-52cc38688a4a4e0894fe06c20fd44aaa).
Contact person: <PERSON>

## Prerequisites
1. Access to BigQuery project `refb-analytics-funnelio` and `Funnel_export_refurbed` dataset.
2. Access to `refurbed_machine_learning_production` bucket in `refb-analytics` project.

## Design Assumptions and Constraints

### BigQuery in `refb-analytics-funnelio` project
- DE has no ownership over it and no `flyway` automation;
- Queries are coded as SQL files and executed in runtime (as oppposed to having views or procedures);
- CF service account access would need to be configured manually via GCP console;

### GCS bucket `refurbed_machine_learning_production`
- DE has no ownership over it and no `terraform` automation;
- CF service account access would need to be configured manually via GCP console;

## Implemented features
1. Extraction of PG source: revenue countries.
2. Extraction of BQ source: marketing countries.
3. Transformation of extracted sources into `neuralforecast` format.
4. Load of extracted sources into RAW bucket, and transformed result into TRANSFORMED bucket.
5. Map loaded data sources into following BQ `dataset.table`:
   - `analytics_raw.revenue_countries` - PG revenue countries
   - `analytics_raw.marketing_countries` - BQ marketing countries
   - `analytics_transformed.nrm` - transformed NRM data
6. Infrastructure in terraform supporting Cloud Run Function runtime
7. Dispatcher module triggering ETL for missing (not loaded) days
8. Run from within `analytics-workflow`
