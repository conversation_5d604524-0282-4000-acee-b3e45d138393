/**
Query that targets `refb-analytics-funnelio` BQ project we lack CI/CD, thus hard-coded query.

Original source:
https://gitlab.com/refurbed/ds/crystal-ball/-/blob/feat/hierarchy/src/queries/queries_sql/marketing_countries_split.sql -- noqa
*/
select
    funnel_data.date,
    if(
        funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy",
        "at",
        funnel_data.dim_1e81sjq0ist0a_country_custom
    ) as country,
    coalesce(sum(
        if(
            if(
                funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy",
                "at",
                funnel_data.dim_1e81sjq0ist0a_country_custom
            ) = "at"
            and funnel_data.date >= date(2020, 11, 1)
            and (funnel_data.sourcetypename = "AdWords" or funnel_data.sourcetypename = "Google Ads"),
            1.05 * funnel_data.cost,
            funnel_data.cost
        )
    ), 0) as cost,
    coalesce(
        sum(
            if(
                (funnel_data.sourcename <> "Offline Media") = true,
                if(
                    if(
                        funnel_data.dim_1e81sjq0ist0a_country_custom = "at - copy",
                        "at",
                        funnel_data.dim_1e81sjq0ist0a_country_custom
                    ) = "at"
                    and funnel_data.date >= date(2020, 11, 1)
                    and (funnel_data.sourcetypename = "AdWords" or funnel_data.sourcetypename = "Google Ads"),
                    1.05 * funnel_data.cost,
                    funnel_data.cost
                ),
                0
            )
        ),
        0
    )
    - coalesce(coalesce(sum(funnel_data.cf1ftljmnkd6oub_voucher_amount), 0), 0) as cost_online
from
    `refb-analytics-funnelio.Funnel_export_refurbed.all_funnel_data_view` as funnel_data
where
    -- funnel_data.date between "2024-06-20" and "2024-06-20"
    funnel_data.date between "{start_date}" and "{end_date}"
group by
    funnel_data.date,
    country;
