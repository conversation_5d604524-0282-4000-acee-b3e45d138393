from dataclasses import fields
from datetime import datetime
from math import ceil

import bm_worker_transformer as transformer
from bm_shared.api import (
    AlgoliaClient,
    AlgoliaHit,
    ApiParams,
    ApiPayload,
    ApiPayloadParams,
)
from bm_shared.bm_base_etl import ListingBaseEtl
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import AlgoliaSecret, ListingConfig, ListingMessage
from bm_shared.bm_schema import (
    LISTING_OUTPUT_COLUMNS,
    BMMerchantColumns,
    BMProductColumns,
    ListColumns,
)
from bm_shared.bm_utils import url_to_filename
from bm_shared.listing_worker_model import ListingWorkerData, MerchantPage
from bm_shared.oxy_http_client import OxyHTTPClient
from google.cloud.pubsub_v1 import PublisherClient
from pandas import NA, DataFrame, concat

from common.logger import get_logger

logger = get_logger()

MERCHANT_PAGE_PAYLOAD: ApiPayload = {
    "query": "",
    "distinct": 1,
    "clickAnalytics": True,
    ApiPayloadParams.FILTERS: "",
    "facets": [
        "price",
        "page",
        "q",
        "sort",
        "brand",
        "model",
        "backbox_grade",
        "storage",
        "color",
        "year_date_release",
        "shipping_delay",
        "payment_methods",
        "warranty_with_unit",
        "keyboard_type_language",
        "price_ranges.sm-1",
        "price_ranges.sm-2",
        "price_ranges.md-1",
        "price_ranges.md-1b",
        "price_ranges.md-1c",
        "price_ranges.md-2",
        "price_ranges.lg-1",
        "price_ranges.lg-2",
        "price_ranges.lg-3",
    ],
    "numericFilters": ["price>=0", "price<=5000"],
    ApiPayloadParams.PAGE: 0,
    "hitsPerPage": ApiParams.PRODUCTS_PER_PAGE,
}


class ListingWorkerInput:
    def __init__(self, raw_data_repository: BMDataLakeRepository) -> None:
        self._raw_data_repository: BMDataLakeRepository = raw_data_repository
        self.products: DataFrame = DataFrame(columns=[field.name for field in fields(BMProductColumns)])
        self.merchants: DataFrame = DataFrame(columns=[field.name for field in fields(BMMerchantColumns)])
        self._run_id: datetime | None = None

    def cached_data_update(self, config: ListingConfig, run_id: datetime) -> None:
        """Retrieve product and merchant data for given run_id.
        Query only once for specific run_id

        :param config: worker configuration
        :param run_id: run id datetime
        """
        if self._run_id is None or self._run_id != run_id:
            self.products = self._raw_data_repository.read_prefixed_data_frame(
                config.products_file_name,
                config.raw_product_path,
                run_id,
            )
            self.merchants = self._raw_data_repository.read_prefixed_data_frame(
                config.merchants_file_name,
                config.raw_merchant_path,
                run_id,
            )
            self._run_id = run_id


class ListingWorkerEtl(ListingBaseEtl):
    _config: ListingConfig

    def __init__(
        self,
        config: ListingConfig,
        run_id: datetime,
        http_client: OxyHTTPClient,
        algolia_client: AlgoliaClient,
        pubsub_client: PublisherClient,
        raw_data_repository: BMDataLakeRepository,
        transformed_data_repository: BMDataLakeRepository,
        global_input: ListingWorkerInput,
        message: ListingMessage,
    ):
        super().__init__(
            config,
            run_id,
            http_client,
            pubsub_client,
            raw_data_repository,
            transformed_data_repository,
        )
        self._global_input = global_input
        self._message = message
        self._algolia_client = algolia_client

    def extract(self) -> ListingWorkerData:
        """Fetch and parse first merchant html page, fetch all pages via API,
        Save raw html and json responses for later review/debug

        :return: ListingWorkerData including merchant data from html and API call results
        """
        html = self._http_client.get_page(self._message.url)
        file_name = f"{url_to_filename(self._message.url).name}.html"
        self._save_raw(html, file_name)

        parsed_page = transformer.parse_html(html)
        merchant_data = transformer.extract_merchant_params(parsed_page)

        pages_from_api = self._get_all_pages_via_api(merchant_data)
        for i, page in enumerate(pages_from_api):
            file_name = f"{url_to_filename(self._message.url).name}_page{i}.json"
            self._save_raw(page, file_name)

        return ListingWorkerData(merchant_data, pages_from_api)

    def transform(
        self,
        data: ListingWorkerData,
    ) -> DataFrame:
        """Parse api response payload, extract product information, calculate similarity columns

        :param data: extracted first html page parameters and api response
        :return: tuple of products dataframe and child urls
        """
        products = []
        for products_json in data.pages_from_api:
            parsed = AlgoliaClient.parse_response(products_json)
            df = self._filter_columns(parsed.hits)
            products.append(df)

        df = concat(products)
        df = transformer.set_base_columns(df, self._message.url, self._config, data.merchant_data.name)
        df = transformer.calculate_product_similarity(df, self._global_input.products)
        df = transformer.calculate_merchant_similarity(df, self._global_input.merchants)
        df[ListColumns.scrapped_at] = self._run_id
        df = df[LISTING_OUTPUT_COLUMNS]

        return df

    def load(self, transformed: DataFrame) -> int:
        """Save products to 'transformed' datalake.

        :param transformed: products dataframe
        :return: number of inserted records
        """
        file_name = url_to_filename(self._message.url).with_suffix(".parquet")
        self._transformed_data_repository.write_prefixed_data_frame(
            transformed, file_name.name, self._config.transformed_path, self._run_id
        )

        return len(transformed)

    def run(self) -> int:
        data = self.extract()
        transformed = self.transform(data)
        loaded_count = self.load(transformed)

        return loaded_count

    @staticmethod
    def _filter_columns(hits: list[AlgoliaHit]) -> DataFrame:
        """Get selected hit properties from parsed hits

        :param hits: input hits
        :return: dataframe with selected columns
        """
        rows = []
        for hit in hits:
            instance = ApiParams.INSTANCE_SEPARATOR.join(hit.sub_title_elements) if hit.sub_title_elements else NA
            rows.append([hit.title_model, instance, hit.price])
        df = DataFrame(
            rows,
            columns=[
                BMProductColumns.product_name,
                ListColumns.instance_name,
                ListColumns.price,
            ],
        )

        return df

    def _save_raw(self, content: str, file_name: str) -> None:
        """Save file in raw bucket for future reference

        :param content: file content to be saved
        :param file_name: file name
        """
        self._raw_data_repository.write_prefixed_file_content(
            content,
            file_name,
            self._config.raw_scrapped_path,
            self._run_id,
        )

    def _get_all_pages_via_api(self, merchant: MerchantPage) -> list[str]:
        """Fetch all pages via API

        :param merchant: MerchantPage with all input parameters from merchant's first page
        :return: list of API call json results
        """
        algolia_secret = AlgoliaSecret(merchant.api_key, merchant.app_id)
        api_results = []
        page_num = ceil(merchant.product_count / ApiParams.PRODUCTS_PER_PAGE)
        for page in range(0, min(page_num, self._config.max_child_urls)):
            logger.info(f"Getting page {page} for merchant {merchant.name}")
            payload = self._algolia_client.payload(MERCHANT_PAGE_PAYLOAD, merchant.id, page)
            api_response = self._algolia_client.get_hits(payload, merchant.language, algolia_secret)
            api_results.append(api_response)
        return api_results
