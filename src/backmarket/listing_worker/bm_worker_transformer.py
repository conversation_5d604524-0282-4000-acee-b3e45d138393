import json
import re
from typing import Optional

from bm_shared.bm_model import ListingConfig
from bm_shared.bm_schema import BMMerchantColumns, BMProductColumns, ListColumns
from bm_shared.listing_worker_model import (
    MerchantConst,
    MerchantPage,
    NoAppId,
    NoLanguage,
    NoMerchantId,
    NoMerchantName,
    NoProductCount,
)
from bm_shared.retrieve_algolia_key import extract_api_key
from bs4 import BeautifulSoup, Tag
from pandas import DataFrame, Series, isna
from thefuzz import fuzz

from common.logger import get_logger

logger = get_logger()

EMPTY_PRODUCT_DF = DataFrame(
    columns=[
        BMProductColumns.product_name,
        ListColumns.instance_name,
        ListColumns.price,
        ListColumns.merchant_name,
    ]
)


def parse_html(html: str) -> BeautifulSoup:
    """parse html input

    :param html: html content to parse
    :return: parsed html
    """
    return BeautifulSoup(html, "html.parser")


def clean_str(value: Optional[str]) -> str:
    """Switch to lowercase, remove special characters from column

    :param value: input string to clean
    :return: updated string
    """
    if value is None or isna(value):
        return ""
    result = re.sub(MerchantConst.TO_REMOVE, "", value.lower())
    return result


def set_base_columns(df: DataFrame, url: str, config: ListingConfig, merchant_business_name: str) -> DataFrame:
    """add merchant url, name, country columns, update product name

    :param df: input DataFrame
    :param url: scrapped url
    :param config: bm listing configuration
    :param merchant_business_name: merchant's business name
    :return: DataFrame with updated and added columns
    """
    df[BMProductColumns.product_name] = df[BMProductColumns.product_name].apply(clean_str)
    df[ListColumns.merchant_url] = url
    df[ListColumns.merchant_name] = df[ListColumns.merchant_url].str.extract(config.merchant_name_regex_pattern)
    df[ListColumns.country] = df[ListColumns.merchant_url].str.extract(config.country_regex_pattern)
    df[ListColumns.merchant] = merchant_business_name
    return df


def extract_merchant_name(card: BeautifulSoup) -> str:
    """extract merchant name from merchant's first page

     <h1 class="heading-1">CasePhone</h1>

    :param card: parsed product card
    :raises ValueError("no_merchant_name") when name not found
    :return: merchant name,
    """
    merchant_name_h1 = card.find("h1", {"class": MerchantConst.HEADING_1})
    if not merchant_name_h1 or not merchant_name_h1.text:
        raise NoMerchantName

    return merchant_name_h1.text.strip()


def calculate_product_similarity(df: DataFrame, products: DataFrame) -> DataFrame:
    """Calculate similarity ratios between scrapped products and refurbed products

    :param df: Backmarket products
    :param products: refurbed products
    :return: DataFrame with similarity columns
    """
    df[ListColumns.best_product_score] = Series(dtype="float")
    df[ListColumns.refurbed_product_name] = None
    df[BMProductColumns.product_id] = Series(dtype="Int64")
    df[ListColumns.cluster] = df[BMProductColumns.product_name].str.split(r"\s+", expand=False).str[0]
    for cluster in df[ListColumns.cluster].unique():
        filtered_rb_df = products[products[BMProductColumns.product_name].str.contains(cluster)]
        for index_bm, row_bm in df[df[ListColumns.cluster] == cluster].iterrows():
            if not filtered_rb_df.empty:
                similarity_scores = filtered_rb_df[BMProductColumns.product_name].apply(
                    lambda x: fuzz.ratio(row_bm[BMProductColumns.product_name], x)
                )
            else:
                similarity_scores = products[BMProductColumns.product_name].apply(
                    lambda x: fuzz.ratio(row_bm[BMProductColumns.product_name], x)
                )

            if not similarity_scores.empty:
                best_match_index = similarity_scores.idxmax()
                best_match_score = similarity_scores.max()
                best_match_product_name = products.loc[best_match_index, BMProductColumns.product_name]
                best_match_product_id = products.loc[best_match_index, BMProductColumns.product_id]
                df.at[index_bm, ListColumns.best_product_score] = best_match_score
                df.at[index_bm, ListColumns.refurbed_product_name] = best_match_product_name
                df.at[index_bm, BMProductColumns.product_id] = best_match_product_id
            else:
                logger.debug(f"No matches found for cluster: {cluster}")
    logger.debug("product mapping done")
    df = df.drop([ListColumns.cluster], axis=1)
    return df


def calculate_merchant_similarity(df: DataFrame, merchants: DataFrame) -> DataFrame:
    """Calculate similarity ratios between scrapped merchants and refurbed merchants

    :param df: Backmarket merchants
    :param merchants: refurbed merchants
    :return: DataFrame with similarity columns
    """
    df[ListColumns.best_merchant_score] = Series(dtype="float")
    df[ListColumns.refurbed_merchant_name] = None
    df[BMMerchantColumns.merchant_id] = Series(dtype="Int64")

    df[ListColumns.merchant] = df[ListColumns.merchant_name].str.replace("-", " ")

    unique_merchants_bm = df[ListColumns.merchant].unique()
    merchant_similarity_threshold = 70
    for merchant in unique_merchants_bm:
        filtered_rb_df = merchants[
            merchants[BMMerchantColumns.ref_merchant_name].apply(lambda x: fuzz.ratio(merchant, x))
            > merchant_similarity_threshold
        ]
        similarity_scores = filtered_rb_df[BMMerchantColumns.ref_merchant_name].apply(lambda x: fuzz.ratio(merchant, x))
        if not similarity_scores.empty:
            best_match_index = similarity_scores.idxmax()
            best_match_score = similarity_scores.max()
            best_match_merchant_name = merchants.loc[best_match_index, BMMerchantColumns.ref_merchant_name]
            best_match_merchant_id = merchants.loc[best_match_index, BMMerchantColumns.merchant_id]
            df.loc[
                df[ListColumns.merchant_name] == merchant,
                ListColumns.best_merchant_score,
            ] = best_match_score
            df.loc[
                df[ListColumns.merchant_name] == merchant,
                ListColumns.refurbed_merchant_name,
            ] = best_match_merchant_name
            df.loc[df[ListColumns.merchant_name] == merchant, BMMerchantColumns.merchant_id] = best_match_merchant_id
    df = df.drop([ListColumns.merchant], axis=1)
    return df


def extract_product_count(soup: BeautifulSoup) -> int:
    """Extract total number of products from merchant's 1st page

    :param soup: parsed page
    :raises NoProductCount: when product not found
    :return: number of products
    """

    product_count_label = soup.find(attrs={MerchantConst.DATA_TEST: MerchantConst.TOTAL_PRODUCTS})
    if not product_count_label or not product_count_label.text:
        raise NoProductCount

    product_count_str = product_count_label.text.split(" ")[0]
    try:
        count = int(product_count_str)
        return count
    except ValueError:
        raise NoProductCount


def extract_merchant_id(page: BeautifulSoup) -> str:
    """Extract merchant id in "merchant_id:d+" format

    :param page: merchant's first page
    :raises NoMerchantId: when merchant id not found
    :return: merchant:id,
    """
    tag = page.find("script", {"id": MerchantConst.NUXT_DATA})
    if tag:
        parsed = json.loads(tag.text)
        for e in parsed:
            if isinstance(e, str):
                if e.startswith(MerchantConst.MERCHANT_ID):
                    merchant_id = e
                    return merchant_id

    raise NoMerchantId


def extract_app_id(page: BeautifulSoup) -> str:
    """Extract Algolia API id

    :param page: merchant's first page
    :raises NoAppId when app_id not found
    :return: Algolia API id
    """
    tag = page.find("script", string=MerchantConst.NUXT_SCRIPT_REGEX)
    if tag:
        app_group = re.search(MerchantConst.ALGOLIA_ID_REGEX, tag.text.replace(MerchantConst.NUXT_SCRIPT, ""))
        if app_group:
            app_id = app_group.group(1)
            return app_id

    raise NoAppId


def extract_lang(page: BeautifulSoup) -> str:
    """extract language code eg 'de-at'

    :param page: merchant's first page
    :raises NoLanguage: when tag not found
    :return: language code
    """
    tag = page.find(name=MerchantConst.HTML)
    if tag and isinstance(tag, Tag):
        lang = tag.get(key=MerchantConst.LANG)
        if lang:
            return str(lang)

    raise NoLanguage


def extract_merchant_params(page: BeautifulSoup) -> MerchantPage:
    """Extract all parameters required for API calls from first html page

    :param page: merchant's first page of listing
    :return: tuple of : extracted MerchantPage, parsing stats Counter
    """

    product_count = extract_product_count(page)
    name = extract_merchant_name(page)
    merchant_id = extract_merchant_id(page)
    api_key = extract_api_key(page)
    app_id = extract_app_id(page)
    language = extract_lang(page)
    merchant = MerchantPage(
        product_count=product_count, name=name, id=merchant_id, api_key=api_key, app_id=app_id, language=language
    )

    return merchant
