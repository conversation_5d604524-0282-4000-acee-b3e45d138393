from datetime import date
from os import environ
from pathlib import Path
from typing import Optional

from compaction_bq_repo import CompactionBqRepository
from compaction_model import CompactionConfig
from pyarrow import parquet

from common.cloud_storage_client import CloudStorageClient
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.time_service import TimeService
from common.typings import DataLakeTimePartition
from common.utils import format_bytes_pretty

logger = get_logger()


class BackmarketCompaction:
    def __init__(
        self,
        config: CompactionConfig,
        local_temp_path: Path,
        cloud_storage_client: CloudStorageClient,
        bq_repository: CompactionBqRepository,
        transformed_data_lake_repository: DataLakeRepository,
        time_service: Optional[TimeService] = None,
    ) -> None:
        self._config = config
        self._local_temp_path = local_temp_path
        self._cloud_storage_client = cloud_storage_client
        self._transformed_data_lake_repository = transformed_data_lake_repository
        self._bq_repository = bq_repository
        self._time_service = time_service if time_service else TimeService()

    @property
    def compaction_date(self) -> date:
        """
        Compaction date based on the starter run hour, if not provided, uses today.
        """
        compaction_date = environ.get("COMPACTION_DATE")

        return date.fromisoformat(compaction_date) if compaction_date else self._time_service.today

    @property
    def compacted_file_name(self) -> str:
        """
        Compaction file name with 'YYYMMDD' identifier.
        Intentionally, it is not unique, since, running the crawler every hour,
        it will overwrite the file from the previous run.
        """

        date_id = self.compaction_date.strftime("%Y%m%d")

        return f"{self._config.compacted_file_prefix}_{date_id}.parquet"

    @property
    def data_lake_path(self) -> str:
        """
        Data lake path for the crawler worker results.
        """
        return str(
            DataLakeTimePartition(
                base_path=self._config.transformed_path,
                year=self.compaction_date.year,
                month=self.compaction_date.month,
                day=self.compaction_date.day,
            ).path
        )

    @property
    def compacted_partition(self) -> DataLakeTimePartition:
        """
        Compacted data lake partition.
        """

        return DataLakeTimePartition(
            base_path=self._config.compaction_base_path,
            year=self.compaction_date.year,
            month=self.compaction_date.month,
            day=self.compaction_date.day,
        )

    def download_files(self) -> list[Path]:
        """
        Downloads all files from the data lake crawler bucket location.
        :returns: locations of downloaded files on the local filesystem.
        """

        logger.info(
            f"Downloading files from '{self._config.transformed_bucket}/{self.data_lake_path}' bucket location..."
        )

        downloaded_file_paths = self._cloud_storage_client.download_directory(
            self.data_lake_path, self._local_temp_path, include_subdirectories=False
        )
        logger.info(f"Downloaded {len(downloaded_file_paths)} file(s).")

        return downloaded_file_paths

    def compact(self) -> Path:
        """
        Compacts all files into a single parquet file.
        :returns: location of the compacted file on the local filesystem.
        """

        logger.info(f"Compacting all files into '{self.compacted_file_name}'...")

        file_path = Path(self._local_temp_path, self.compacted_file_name)
        table = parquet.read_table(self._local_temp_path)

        parquet.write_table(table, where=file_path)

        return file_path

    def load(self, compacted_file: Path) -> None:
        self.load_data_lake(compacted_file)
        self.refresh_bq_table()

    def load_data_lake(self, compacted_file: Path) -> None:
        """
        Loads the compacted file into the data lake compaction location.
        :param compacted_file: location of the compacted file on the local filesystem.
        """
        logger.info(f"Loading '{self.compacted_file_name}' file into '{self.compacted_partition.path}'...")

        self._transformed_data_lake_repository.write_file(compacted_file, self.compacted_partition)

        logger.info(f"File '{self.compacted_file_name}' loaded.")

    def refresh_bq_table(self) -> None:
        """
        Refreshes the BigQuery product ranges table for the given date.
        """

        logger.info(
            f"Refreshing '{self._bq_repository.get_table().full_name}' "
            f"BigQuery table for '{self.compaction_date.isoformat()}'..."
        )

        results = self._bq_repository.refresh_product_ranges(self.compaction_date)
        logger.info(
            f"Running quality check for '{self._bq_repository.get_table().full_name}' "
            f"BigQuery table for '{self.compaction_date.isoformat()}'..."
        )
        self._bq_repository.check_product_ranges(self.compaction_date)

        logger.info(f"Processed {format_bytes_pretty(results)} to refresh the table.")

    def run(self) -> None:
        """
        Runs all the steps of the compaction process.
        """

        self.download_files()
        compacted_file = self.compact()
        self.load(compacted_file)
