from datetime import date, timedelta

from compaction_model import AlgoliaProductRange
from google.api_core.exceptions import BadRequest

from common.bq_repository import BigQueryRepository, BQProcedure
from common.logger import get_logger

logger = get_logger()


class CompactionBqRepository(BigQueryRepository):
    """
    Backmarket Compaction BigQuery repository
    """

    TABLE: str = "backmarket_product_ranges"
    DATASET: str = "analytics_transformed"
    LOAD_PROCEDURE: BQProcedure = BQProcedure(DATASET, "refresh_backmarket_product_ranges")
    CHECK_PROCEDURE: BQProcedure = BQProcedure(DATASET, "check_backmarket_product_ranges")

    def refresh_product_ranges(self, refresh_date: date) -> int:
        """
        Refreshes `analytics_transformed.backmarket_product_ranges` table for the given date.
        """
        result = self.call_procedure(procedure=self.LOAD_PROCEDURE, args=[refresh_date.isoformat()])

        return result.total_bytes_processed

    def select_product_ranges(self, day: date) -> list[AlgoliaProductRange]:
        """
        Select products with start and end dates range calculated from 'analytics_transformed.backmarket_product_ranges'
        :param day: day to select
        :return: DataFrame with transformed data
        """

        sql = f"""select * from {self.get_table().full_name} as bm
                  where bm.start_date between '{day.isoformat()}' and '{(day + timedelta(days=1)).isoformat()}'
              """

        return [AlgoliaProductRange(*row) for row in self._client.run_sql(sql).rows]

    def check_product_ranges(self, check_date: date) -> bool:
        """
        Checks `analytics_transformed.backmarket_product_ranges` table for data anomalies.

        :param check_date: load date
        :returns: true if table has no anomalies, false otherwise
        """
        try:
            result = self.call_procedure(self.CHECK_PROCEDURE, [check_date.isoformat()])
        except BadRequest as ex:
            logger.warning(f"Quality check failed for '{self.get_table().full_name}': {ex.errors}")
            return False

        return result.total_rows == 0
