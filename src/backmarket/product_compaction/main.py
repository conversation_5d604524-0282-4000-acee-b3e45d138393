import dataclasses
import os
import tempfile
from datetime import date
from pathlib import Path

from compaction import BackmarketCompaction, CompactionConfig
from compaction_bq_repo import CompactionBqRepository

from common.cloud_storage_client import CloudStorageClient, CloudStorageClientConfig
from common.data_lake_repository import DataLakeRepository
from common.logger import setup_logger
from common.timing import timing

config = CompactionConfig()
logger = setup_logger(config)

cloud_storage_client = CloudStorageClient(config=CloudStorageClientConfig(bucket_name=config.transformed_bucket))
transformed_data_lake_repo = DataLakeRepository(cs_client_or_bucket=cloud_storage_client)
bq_repo = CompactionBqRepository()


@timing(logger, "compaction")
def run() -> None:
    """
    Code entry point, called from crawler workflow.
    """

    with tempfile.TemporaryDirectory(delete=config.remove_local_files) as local_path:
        etl = BackmarketCompaction(
            config=config,
            local_temp_path=Path(local_path),
            cloud_storage_client=cloud_storage_client,
            bq_repository=bq_repo,
            transformed_data_lake_repository=transformed_data_lake_repo,
        )
        logger.info(f"Starting Backmarket products compaction for '{etl.compaction_date.isoformat()}'...")
        etl.run()


if __name__ == "__main__":
    if config.is_local:
        logger.info("Running locally...")
        config = dataclasses.replace(config, remove_local_files=False)
        # existing files on staging transformed bucket
        os.environ["COMPACTION_DATE"] = date(2025, 4, 28).isoformat()

    run()
