from dataclasses import dataclass
from datetime import datetime
from functools import cached_property
from pathlib import Path

from bm_shared.bm_model import ApiDataLakeConfig

from common.config import Config


@dataclass(frozen=True)
class CompactionConfig(Config, ApiDataLakeConfig):
    compacted_parent_path: str = "backmarket"
    compacted_file_prefix: str = "products"
    remove_local_files: bool = True

    @cached_property
    def compaction_base_path(self) -> str:
        """
        Compaction file name
        """

        return str(Path(self.compacted_parent_path, self.compacted_file_prefix))


@dataclass(frozen=True)
class AlgoliaProduct:
    product_name: str
    instance_features: str
    price: float
    grade: str
    url: str
    country: str
    extra_features: str
    title: str
    id1: str
    id2: str
    currency: str
    scrapped_at: datetime
    merchant_id: int


@dataclass(frozen=True, order=True)
class AlgoliaProductRange:
    product_name: str
    instance_features: str
    price: float
    grade: str
    url: str
    country: str
    extra_features: str
    title: str
    id1: str
    id2: str
    currency: str
    start_date: datetime
    end_date: datetime
    merchant_id: int
