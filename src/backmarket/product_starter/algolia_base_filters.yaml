# Common filters:
#
# offer_type:
#   0 : original battery
#   7 : new battery
#
# cat_id:
#   2   - smartphones
#   5   - Apple tablets
#   117 - laptops
#   110 - MacBooks
#   170 - Apple smartwatches
#   158 - non Apple smartwatches
#   217 - airpods
#   528 - MacBooks with M1,M2... processors
#   559 - Android tablets

groups:
  - name: "common"
    schedule: "0 * * * *"
    filters:
      # all iphones, https://www.backmarket.at/de-at/l/iphone/aabc736a-cb66-4ac0-a3b7-0f449781ed39
      - (cat_id:"2") AND (brand:"0  Apple") AND special_offer_type=0

      # all smartphones with new battery, mostly iphones but not only iphones,
      # note that landing page reads iphones but it covers more:
      # https://www.backmarket.at/de-at/l/iphones-mit-neuem-akku/ac9fb5ba-6133-406b-a75c-d8da4f08fb75
      - (cat_id:"2") AND special_offer_type=7

      # selected non Apple smartphones
      # https://www.backmarket.at/de-at/l/samsung-s-modell/d457fc36-2a87-496d-ad39-12751f0510c6
      - (cat_id:"2") AND (serie:"Samsung Galaxy S") AND special_offer_type=0

      # https://www.backmarket.at/de-at/l/samsung-galaxy-a/64913977-ca32-4937-bc30-705ef2eb7b5b
      - (cat_id:"2") AND (brand_clean:"Samsung") AND (serie:"Samsung Galaxy A")
        AND (model:"999 Galaxy A40" OR model:"999 Galaxy A53 5G" OR model:"999 Galaxy A51"
        OR model:"999 Galaxy A50" OR model:"999 Galaxy A54" OR model:"999 Galaxy A52s 5G"
        OR model:"999 Galaxy A52" OR model:"999 Galaxy A55" OR model:"999 Galaxy A52 5G")
        AND special_offer_type=0
      - (cat_id:"2") AND (brand_clean:"Samsung") AND (model_clean:"Galaxy Z Flip 5 5G" OR model_clean:"Galaxy Z Flip 5"
        OR model_clean:"Galaxy Z Flip5" OR model_clean:"Galaxy Z Flip5 5G")
        AND special_offer_type=0

      # https://www.backmarket.at/de-at/l/google-pixel-7-modelle/c84e8a93-55b8-4b6d-b57e-73e94c673336
      - (cat_id:"2") AND (brand_clean:"Google") AND (model_clean:"google pixel 7" OR model_clean:"google pixel 7 pro"
        OR model_clean:"google pixel7 pro" OR model_clean:"pixel 7 pro" OR model_clean:"pixel 7"
        OR model_clean:"google pixel 6" OR model_clean:"google pixel 6 pro" OR model_clean:"google pixel 6a")
        AND special_offer_type=0

      # tablets, all brands, https://www.backmarket.at/de-at/l/tablet/5a3cfa21-b588-49b1-b4e9-2636bec68ada
      - (cat_id:"5" OR cat_id:"559") AND special_offer_type=0

      # Microsoft laptops - Surface devices, classified as tablets by refurbed
      - (cat_id:"117") AND (brand:"99 Microsoft") AND special_offer_type=0

      # smartwatches, all brands
      # https://www.backmarket.at/de-at/l/smartwatch-uhren/0894adca-7735-40d3-a34b-5a77358e3937
      - (cat_id:"170" OR cat_id:"158") AND special_offer_type=0

      # airpods,  https://www.backmarket.at/de-at/l/airpods/5091ca6b-c36c-4d50-b644-fb3c5bdd5f16
      - (cat_id:"217") AND special_offer_type=0

  # mackbooks are scrapped every 2 hours, split into 2 groups to keep even number of requests every hour
  - name: "macbooks_even_hours"
    schedule: "0 */2 * * *"
    filters: #1610
      # macbooks, per year, https://www.backmarket.at/de-at/l/macbook/297f69c7-b41c-40dd-aa9b-93ab067eb691 with filters
      - (year_date_release:"75 2024") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=0
      - (year_date_release:"78 2021") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=0
      - (year_date_release:"79 2020") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=0
      - (year_date_release:"75 2024") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=7
      - (year_date_release:"78 2021") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=7
      - (year_date_release:"79 2020") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=7

  - name: "macbooks_odd_hours"
    schedule: "0 1-23/2 * * *"
    filters:
      # macbooks, per year, https://www.backmarket.at/de-at/l/macbook/297f69c7-b41c-40dd-aa9b-93ab067eb691 with filters
      - (year_date_release:"76 2023") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=0
      - (year_date_release:"77 2022") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=0
      - (year_date_release:"80 2019") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=0
      - (year_date_release:"76 2023") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=7
      - (year_date_release:"77 2022") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=7
      - (year_date_release:"80 2019") AND (cat_id:"110" OR cat_id:"528") AND special_offer_type=7
