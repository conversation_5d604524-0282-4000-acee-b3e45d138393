import json
from unittest.mock import Mock

import functions_framework
from bm_shared.bm_model import BMApiConfig
from cloudevents.abstract import CloudEvent
from product_starter_etl import BMProductStarterEtl

from common.cloud_events import create_http_event
from common.logger import setup_logger
from common.pubsub_client import (
    PubSubPublisher,
    PubSubSubscriber,
    PubSubSubscriberConfig,
)
from common.typings import APPLICATION_JSON_HEADER, HttpResponse

config = BMApiConfig()
logger = setup_logger(config)
publisher = PubSubPublisher(config.pubsub_topic)
subscriber = PubSubSubscriber(
    PubSubSubscriberConfig(topic_name=config.pubsub_topic, subscription_name=config.pubsub_subscription)
)


logger.info(f"Backmarket API starter created with:{config=}")


@functions_framework.http
def run(_: CloudEvent) -> HttpResponse:
    """
    Cloud function entry point, triggered by crawler workflow.
    """
    etl = BMProductStarterEtl(config, publisher, subscriber)
    messages_in_queue = etl.acknowledge_all_messages()

    if messages_in_queue == 0:
        logger.info("Backmarket product starter: message queue empty.")
    else:
        logger.error(f"Backmarket product starter: {messages_in_queue} messages purged from queue.")

    results = etl.run()

    summary = (
        f"Backmarket product starter finished for {results.run_hour}, triggered: {results.calls_published} requests."
    )
    logger.info(summary)

    return HttpResponse(
        body=json.dumps({"run_date": results.run_hour.date().isoformat()}), headers=APPLICATION_JSON_HEADER
    )


if __name__ == "__main__":
    publisher = Mock()
    subscriber = Mock()
    subscriber.receive_messages.return_value = []
    run(create_http_event("Running locally..."))
