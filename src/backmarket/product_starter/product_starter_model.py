from dataclasses import dataclass
from datetime import datetime
from typing import Self

from dataclasses_json import DataClassJsonMixin
from yaml import safe_load


@dataclass(frozen=True)
class FilterGroup:
    name: str
    schedule: str
    filters: list[str]


@dataclass(frozen=True)
class ConfigGroups(DataClassJsonMixin):
    groups: list[FilterGroup]

    @classmethod
    def from_yaml(cls, yaml: str) -> Self:
        """build ConfigGroups from yaml

        :param yaml: yaml string
        :return: ConfigGroups object
        """
        return cls.from_dict(safe_load(yaml))


@dataclass(frozen=True)
class StarterRunResult(DataClassJsonMixin):
    run_hour: datetime
    calls_published: int
