from datetime import datetime
from functools import cached_property
from pathlib import Path
from typing import Optional

from bm_shared.api import (
    AlgoliaClient,
    ApiFilter,
    ApiFilterAttribute,
    ApiGrade,
    ApiPayloadParams,
)
from bm_shared.bm_model import AlgoliaWorkerMessage, BMApiConfig
from pandas import DataFrame
from product_starter_model import ConfigGroups, StarterRunResult
from pycron import is_now

from common.logger import get_logger
from common.pubsub_client import PubSubPublisher, PubSubSubscriber
from common.time_service import TimeService

logger = get_logger()


class BMProductStarterEtl:
    CURRENT_DIR = Path(__file__).resolve().parent
    CONFIG_FILE = "algolia_base_filters.yaml"
    LOCALE = "locale"
    GRADE = "grade"
    FILTER = "filter"

    def __init__(
        self,
        config: BMApiConfig,
        publisher: PubSubPublisher,
        subscriber: PubSubSubscriber,
        time_service: Optional[TimeService] = None,
    ):
        self._config = config
        self._publisher = publisher
        self._subscriber = subscriber
        self._time_service = time_service if time_service else TimeService()

    @cached_property
    def run_hour(self) -> datetime:
        """
        The most recent full hour's date and time, used to select filters and calculate hour container for GCS files.
        """
        return self._time_service.last_hour

    def run(self) -> StarterRunResult:
        """

        Trigger crawler workers for predefined filters by sending requests to PubSub topic.
        :return: run hour and the number of searches/calls triggered
        """

        with open(self.CURRENT_DIR / self.CONFIG_FILE, "r") as file:
            config = file.read()

        config = ConfigGroups.from_yaml(config)
        active_filters = self.active_filters(config)
        calls_published = self._trigger_api_workers(active_filters)

        return StarterRunResult(run_hour=self.run_hour, calls_published=calls_published)

    def active_filters(self, config: ConfigGroups) -> list[str]:
        """Filter active filters based on a run hour and group configuration

        :param config: ConfigGroup, includes filters and schedule
        :return: list of active filters
        """
        active_filters = []
        for group in config.groups:
            if is_now(group.schedule, self.run_hour):
                logger.info(f"Backmarket product starter, filter group {group.name}: ACTIVE")
                active_filters.extend(group.filters)
            else:
                logger.info(f"Backmarket product starter, filter group {group.name}: INACTIVE")
        return active_filters

    def _trigger_api_workers(self, filters: list[str]) -> int:
        """Trigger algolia API crawling for list of filters via Pubsub

        :param filters: filters in algolia syntax
        :return: number of calls triggered
        """
        df_filters = DataFrame(filters, columns=[self.FILTER])
        df_locales = DataFrame(self._config.locales, columns=[self.LOCALE])
        df_filters = df_filters.merge(df_locales, how="cross")

        df_grades = DataFrame.from_records(ApiGrade.FILTERS, columns=[self.LOCALE, self.GRADE])
        df_filters = df_filters.merge(df_grades, on=self.LOCALE)

        df_filters = df_filters.sample(frac=1)  # shuffle

        for base_filter, locale, grade in df_filters[[self.FILTER, self.LOCALE, self.GRADE]].to_records(index=False):
            backbox_grade = ApiFilterAttribute(ApiFilter.BACKBOX_GRADE, grade)
            query_filter = ApiFilter.prepend_attribute(backbox_grade, base_filter)
            page = 0
            request = AlgoliaWorkerMessage(
                run_timestamp=self.run_hour.timestamp(),
                query=query_filter,
                payload=AlgoliaClient.payload(ApiPayloadParams.IPHONE_PAYLOAD, query_filter, page),
                page=page,
                language_country=locale,
            )
            self._publisher.publish_message(request.to_json())

        return len(df_filters)

    def acknowledge_all_messages(self) -> int:
        """Counts and purges all messages from a subscription queue.

        :return: Number of messages that were purged from the queue
        """
        num_of_messages = 0
        while True:
            messages = self._subscriber.receive_messages(max_messages=self._config.max_purged_messages)
            if not messages:
                break
            self._subscriber.acknowledge_messages(messages)
            num_of_messages += len(messages)
        return num_of_messages
