"""Common ETL base class for starter and worker function"""

from datetime import datetime

from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig
from bm_shared.oxy_http_client import OxyHTTPClient
from google.cloud.pubsub_v1 import PublisherClient


class ListingBaseEtl:
    def __init__(
        self,
        config: ListingConfig,
        run_id: datetime,
        http_client: OxyHTTPClient,
        pubsub_client: PublisherClient,
        raw_data_repository: BMDataLakeRepository,
        transformed_data_repository: BMDataLakeRepository,
    ):
        self._config = config
        self._run_id = run_id
        self._http_client = http_client
        self._pubsub_client = pubsub_client
        self._raw_data_repository = raw_data_repository
        self._transformed_data_repository = transformed_data_repository

    def publish_message(self, data: str) -> None:
        """Publish pubsub message

        :param data: data to be sent
        """
        topic_path = self._pubsub_client.topic_path(self._config.project_id, topic=self._config.pubsub_topic)
        future = self._pubsub_client.publish(topic_path, data.encode("utf-8"))
        future.result()
