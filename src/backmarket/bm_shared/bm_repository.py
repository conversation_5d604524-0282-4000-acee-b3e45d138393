from pandas import DataFrame

from common.logger import get_logger
from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository

logger = get_logger()


class BMRepository(SqlRepository):
    """
    Backmarket listing specific repository to encapsulate SQL operations within a single class
    """

    # SQL functions: analytics
    SELECT_PRODUCTS = SqlFunction(schema_name="analytics", function_name="select_products_bm_listing")
    SELECT_MERCHANTS = SqlFunction(schema_name="analytics", function_name="select_merchants_bm_listing")

    def select_products(self) -> DataFrame:
        product_rows = self.select_from_function(self.SELECT_PRODUCTS)
        products = DataFrame(product_rows)

        return products

    def select_merchants(self) -> DataFrame:
        merchant_rows = self.select_from_function(self.SELECT_MERCHANTS)
        merchants = DataFrame(merchant_rows)

        return merchants
