import re
from dataclasses import dataclass, field


class NoMerchantName(Exception):
    """No merchant name found in html page"""


class NoMerchantId(Exception):
    """No merchant id found in html page"""


class NoApiKey(Exception):
    """No API key found in html page"""


class NoAppId(Exception):
    """No application ID found in html page"""


class NoProductCount(Exception):
    """No product count found in html page"""


class NoProductsError(Exception):
    """No products section found in API response"""


class NoLanguage(Exception):
    """No language attribute found"""


class NoProductOrPrice(Exception):
    """No products or price found in some entry of product entry API response"""


@dataclass(frozen=True)
class MerchantPage:
    name: str = ""
    product_count: int = 0
    api_key: str = ""
    id: str = ""
    app_id: str = ""
    language: str = ""


@dataclass(frozen=True)
class ListingWorkerData:
    """Container for results of extract.
    Includes parsed data from merchant first page and api results over all page (also page 1)
    """

    merchant_data: MerchantPage = field(default_factory=MerchantPage)
    pages_from_api: list[str] = field(default_factory=list)


@dataclass(frozen=True)
class MerchantConst:
    NUXT_SCRIPT: str = "window.__NUXT__={};window.__NUXT__.config="
    NUXT_SCRIPT_REGEX: re.Pattern[str] = re.compile(NUXT_SCRIPT + ".*")
    ALGOLIA_ID_REGEX: re.Pattern[str] = re.compile('ALGOLIA_ID:"([0-9A-Z]+)"')
    NUXT_DATA: str = "__NUXT_DATA__"
    TOTAL_PRODUCTS: str = "total-products"
    DATA_TEST: str = "data-test"
    HEADING_1: str = "heading-1"
    TO_REMOVE: re.Pattern[str] = re.compile(r'[|\\()+"]')
    MERCHANT_ID: str = "merchant_id"
    MIN_API_KEY_LEN: int = 700
    HTML = "html"
    LANG = "lang"
