from json.decoder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Self

from bm_shared.oxy_http_client import AlgoliaHTTPError, OxyHTTPError

from common.logger import get_logger

logger = get_logger()


class WorkerStats:
    def __init__(self) -> None:
        self.total_requests: int = 0
        self.algolia_http_errors: int = 0
        self.other_errors: int = 0
        self.oxy_http_429_errors: int = 0
        self.oxy_http_server_errors: int = 0
        self.network_errors: int = 0
        self.json_decode_errors: int = 0

    def record_exception(self, e: Exception) -> Self:
        """Increment error counters based on exception type

        :param e: exception to classify and count
        :return: self"""

        self.total_requests += 1
        if isinstance(e, OxyHTTPError):
            if e.status_code == 429:
                self.oxy_http_429_errors += 1
            elif (500 <= e.status_code < 600) or e.status_code == 613:
                self.oxy_http_server_errors += 1
            else:
                self.other_errors += 1
        elif isinstance(e, AlgoliaHTTPError):
            self.algolia_http_errors += 1
        elif isinstance(e, JSONDecodeError):
            self.json_decode_errors += 1
        elif isinstance(e, ConnectionError):
            self.network_errors += 1
        else:
            self.other_errors += 1
        return self

    def record_successful_request(self) -> Self:
        """Record a successful request

        :return: self"""

        self.total_requests += 1
        return self

    def count_total_errors(self) -> int:
        """Count all errors
        :return: total error count
        """
        return sum([value for name, value in vars(self).items() if name != "total_requests"])

    def count_error_rate(self) -> float:
        """Calculate rate of all errors relative to total requests (0 if no requests)
        :return: error rate as float"""
        if self.total_requests == 0:
            return 0
        return round(self.count_total_errors() / self.total_requests, 4)

    def to_str(self) -> str:
        """String representation. Compared to __str__ adds selected properties
        :return: string representation
        """
        return ", ".join(
            [f"error_rate={self.count_error_rate()}"]
            + [f"total_errors={self.count_total_errors()}"]
            + [f"{name}={value}" for name, value in vars(self).items()]
        )

    def log_summary(self, count_error_rate_threshold: float) -> None:
        """Logs a summary of worker job's statistics.
        :param count_error_rate_threshold: error rate threshold; when exceeded error is logged, info message otherwise
        """

        if self.count_error_rate() > count_error_rate_threshold:
            logger.error(
                f"Backmarket API worker job finished total error rate above threshold:"
                f"{self.to_str()}. Error threshold: {count_error_rate_threshold}"
            )
        else:
            logger.info(f"Backmarket API worker job finished : {self.to_str()}.")
