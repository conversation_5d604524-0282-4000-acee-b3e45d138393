"""HTTP client going through oxylabs proxy"""

import base64
import json
from abc import ABC
from dataclasses import dataclass
from http import <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPStatus
from typing import Any, Optional

import requests
import requests.structures
from dataclasses_json import DataClassJsonMixin
from jsonschema import validate

from common.logger import get_logger

logger = get_logger()


class BaseHTTPError(Exception, ABC):
    """Base class for API HTTP Exceptions"""

    def __init__(self, status_code: int, message: str = "") -> None:
        super().__init__(message)
        self.status_code = status_code

    def __str__(self) -> str:
        return f"HTTP Error:{self.status_code},{super().__str__()}"


class OxyHTTPError(BaseHTTPError):
    """Http response error from OxyLabs proxy"""


class AlgoliaHTTPError(BaseHTTPError):
    """Http response error from Algolia relayed by OxyLabs proxy"""


type OxyFeatureDict = dict[str, list[dict[str, bool | str | dict[str, str]]]]


class OxyParams:
    ENDPOINT: str = "https://realtime.oxylabs.io/v1/queries"
    SOURCE: str = "source"
    UNIVERSAL_ECOMMERCE: str = "universal_ecommerce"
    URL: str = "url"
    RENDER_OPTION: str = "render"
    CONTEXT: str = "context"
    KEY: str = "key"
    VALUE = "value"
    FORCE_HEADERS: str = "force_headers"
    HEADERS: str = "headers"
    HTTP_METHOD: str = "http_method"
    POST: str = "post"
    CONTENT: str = "content"
    SUCCESSFUL_STATUS_CODES = "successful_status_codes"
    RESPONSE_SCHEMA: dict[Any, Any] = {
        "type": "object",
        "properties": {
            "results": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "content": {"type": "string"},
                        "status_code": {"type": "integer"},
                        "job_id": {"type": "string"},
                    },
                    "required": ["content", "status_code"],
                },
                "minItems": 1,
            }
        },
        "required": ["results"],
    }
    HTTP_TIMEOUT: int = 20
    MAX_CONTENT: int = 100
    # NO_RETRY_HTTP_STATUS_CODES instructs oxy proxy to return result if this status code occurs.
    # If these codes are not provided proxy will always retry a call if result is not 200
    # In case of failed retry proxy does not return original status code - only 613 'Faulted after too many retries'
    # In such case we cannot tell what happened behind proxy
    # See https://developers.oxylabs.io/scraper-apis/e-commerce-scraper-api/all-domains ,
    # setting of context:successful_status_codes parameter
    NO_RETRY_HTTP_STATUS_CODES: list[int] = [
        code.value for code in HTTPStatus if code.is_client_error or code.is_server_error
    ]


@dataclass(frozen=True)
class ProxyConfig(DataClassJsonMixin):
    # username, keep 'name' field to be consistent with existing
    # production secret
    name: str
    password: str


class OxyHTTPClient:
    def __init__(self, proxy_config: str | ProxyConfig) -> None:
        self._proxy_config = (
            proxy_config if isinstance(proxy_config, ProxyConfig) else ProxyConfig.from_json(proxy_config)
        )

    @staticmethod
    def parse_response(response: requests.Response) -> str:
        """parse response from oxy proxy

        https://developers.oxylabs.io/scraper-apis/e-commerce-scraper-api/all-domains#output-example

        Note: oxy response format allows for multiple 'results' but in case of just getting page
        content as we do , only results[0] list element includes actual response. This info is
        based on Oxy examples, and confirmed by testing. Cannot confirm it in documentation
        nor from support

        :param response: proxy server response wrapping server target response
        :raises OxyHTTPError: for HTTP errors from proxy server
        :raises AlgoliaHTTPError: for HTTP errors for target Algolia server
        :return: target server response
        """
        # handle response for proxy request
        if response.status_code != HTTPStatus.OK:
            raise OxyHTTPError(status_code=response.status_code, message=response.reason)
        json_response = json.loads(response.text)
        validate(json_response, OxyParams.RESPONSE_SCHEMA)
        status_code = json_response["results"][0]["status_code"]
        content = str(json_response["results"][0]["content"])
        job_id = str(json_response["results"][0]["job_id"])
        # handle response from target site
        if status_code != HTTPStatus.OK:
            raise AlgoliaHTTPError(
                status_code=status_code, message=f"job_id:{job_id},{content[:OxyParams.MAX_CONTENT]}"
            )

        return content

    @staticmethod
    def get_features(
        target_url: str,
        headers: Optional[dict[str, str]] = None,
        method: str = "GET",
        json_data: Optional[dict[str, Any]] = None,
    ) -> OxyFeatureDict:
        """
        Prepare json content for POST request.
        Vendor documentation:
        https://developers.oxylabs.io/scraper-apis/e-commerce-scraper-api/features/headers-cookies-post#custom-headers

        :param target_url: url to retrieve
        :param headers: headers for request
        :param method: HTTP method - GET or POST
        :param json_data: json payload for POST request
        :return: dictionary to build Oxylab's endpoint  request
        """

        features: dict[Any, Any] = dict()
        features[OxyParams.SOURCE] = OxyParams.UNIVERSAL_ECOMMERCE
        features[OxyParams.URL] = target_url
        context: list[dict[str, Any]] = [
            {OxyParams.KEY: OxyParams.SUCCESSFUL_STATUS_CODES, OxyParams.VALUE: OxyParams.NO_RETRY_HTTP_STATUS_CODES}
        ]

        if headers:
            features[OxyParams.CONTEXT] = context
            context.append({OxyParams.KEY: OxyParams.FORCE_HEADERS, OxyParams.VALUE: True})
            context.append({OxyParams.KEY: OxyParams.HEADERS, OxyParams.VALUE: headers})

        if method == HTTPMethod.POST:
            features[OxyParams.CONTEXT] = context
            body = base64.b64encode(json.dumps(json_data).encode()).decode()
            context.append({OxyParams.KEY: OxyParams.HTTP_METHOD, OxyParams.VALUE: OxyParams.POST})
            context.append({OxyParams.KEY: OxyParams.CONTENT, OxyParams.VALUE: body})

        return features

    def get_page(
        self,
        target_url: str,
        headers: Optional[dict[str, str]] = None,
        method: HTTPMethod = HTTPMethod.GET,
        json_data: Optional[dict[str, Any]] = None,
    ) -> str:
        """retrieve page content via proxy

        :param target_url: url to retrieve
        :param headers: headers for request
        :param method: HTTP method - GET or POST
        :param json_data: json payload for POST request
        :return: HTTP response content
        """
        logger.debug(f"Oxylabs http client getting:{target_url}")
        features = self.get_features(target_url=target_url, headers=headers, method=method, json_data=json_data)
        response = requests.request(
            "POST",
            OxyParams.ENDPOINT,
            auth=(self._proxy_config.name, self._proxy_config.password),
            json=features,
            timeout=OxyParams.HTTP_TIMEOUT,
        )

        return self.parse_response(response)
