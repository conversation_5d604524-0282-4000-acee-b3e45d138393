from datetime import datetime
from pathlib import Path
from tempfile import TemporaryDirectory

from pandas import DataFrame, read_parquet

from common.data_lake_repository import DataLakeRepository
from common.typings import DataLakeTimePartition
from common.utils import get_run_id


class BMDataLakeRepository(DataLakeRepository):
    def read_prefixed_data_frame(self, file_name: str, data_lake_folder: str, run_id: datetime) -> DataFrame:
        """Read parquet dataframe from data lake repository

        :param file_name: base file name; data lake file name is prefixed with run_id
        :param data_lake_folder: data lake folder
        :param run_id: unique run identifier
        :return: dataframe"""

        with TemporaryDirectory() as tmp:
            file_name_run_id = self.add_run_id_prefix(file_name, run_id)
            partition = DataLakeTimePartition(
                data_lake_folder,
                run_id.year,
                run_id.month,
                run_id.day,
            )
            local_path = self.read_file(file_name_run_id.name, partition, Path(tmp))
            return read_parquet(local_path)

    def write_prefixed_data_frame(
        self, df: DataFrame, file_name: str, data_lake_folder: str, run_date: datetime
    ) -> Path:
        """Write data frame as parquet to data lake repository with name prefixed with run_id

        :param df: data frame to write
        :param file_name: base file name; to be prefixed with run_id
        :param run_date: the datetime when the scrapping process started
        :param data_lake_folder: data lake folder/table"""

        with TemporaryDirectory() as tmp:
            file_path = tmp / self.add_run_id_prefix(file_name, run_date)
            df.to_parquet(file_path, index=False, engine="pyarrow")
            df["year"] = run_date.year
            df["month"] = run_date.month
            df["day"] = run_date.day

            partition = DataLakeTimePartition(data_lake_folder, run_date.year, run_date.month, run_date.day)

            return self.write_file(file_path, partition)

    def write_prefixed_file_content(
        self, content: str, file_name: str, data_lake_folder: str, run_id: datetime
    ) -> Path:
        """Write content to file in data lake repository with name prefixed with run_id

        :param content: content to save
        :param file_name: base file name; to be prefixed with run_id
        :param run_id: unique run identifier
        :param data_lake_folder: data lake folder"""

        with TemporaryDirectory() as tmp:
            file_path = tmp / self.add_run_id_prefix(file_name, run_id)
            with open(tmp / file_path, "w") as file:
                file.write(content)
            partition = DataLakeTimePartition(
                data_lake_folder,
                run_id.year,
                run_id.month,
                run_id.day,
            )
            return self.write_file(file_path, partition)

    @staticmethod
    def add_run_id_prefix(file_name: str | Path, run_datetime: datetime) -> Path:
        """Add unique prefix based on time to filename

        :param file_name: original file name
        :param run_datetime: run id datetime
        :return: file name with prefix
        """
        run_id = get_run_id(run_datetime)

        return Path(f"{run_id}_{file_name}")
