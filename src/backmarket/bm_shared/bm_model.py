"""Common config base class for starter and worker function"""

import re
import string
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from functools import cached_property
from typing import Any

from dataclasses_json import DataClassJsonMixin

from common.config import Config


class BMConst:
    JSON_CONTENT_TYPE: dict[str, str] = {"Content-Type": "application/json"}
    BM_OXY_PROXY_SECRET_ID: str = "OXYLAB"  # name compatible with existing production name
    BM_ALGOLIA_SECRET_ID: str = "backmarket-algolia-api"
    VALID_CHARS: str = f"_{string.ascii_letters}{string.digits}"
    MAX_QUERY_LEN: int = 200


@dataclass(frozen=True)
class BMDataLakeConfig(DataClassJsonMixin):
    raw_scrapped_path: str = "backmarket/scrapped/"
    raw_product_path: str = "backmarket/products/"
    raw_merchant_path: str = "backmarket/merchants/"
    transformed_path: str = "backmarket/listing/"


@dataclass(frozen=True)
class ListingConfig(BMDataLakeConfig, Config):
    merchant_pattern: str = r"https://www\.backmarket\.\w+/\w+\-\w+/s/"
    merchant_name_regex_pattern = r"/s/([^/]+)/"
    country_regex_pattern = r"https://www\.backmarket\.([a-z]+)*"
    pubsub_topic = "backmarket-listing-worker"
    max_urls_to_scrap: int = 10000  # for testing: limit maximum number of urls per sitemap
    max_child_urls: int = 1000  # for testing: limit maximum number of sub-pages
    proxy_secret_id = BMConst.BM_OXY_PROXY_SECRET_ID
    algolia_secret_id = BMConst.BM_ALGOLIA_SECRET_ID
    products_file_name = "products.parquet"
    merchants_file_name = "merchants.parquet"

    @cached_property
    def countries(self) -> list[str]:
        return ["at", "de"]

    @cached_property
    def sitemaps(self) -> dict[str, str]:
        return {country: f"https://www.backmarket.{country}/sitemap_general.xml" for country in self.countries}


@dataclass(frozen=True)
class ApiDataLakeConfig(DataClassJsonMixin):
    raw_scrapped_path: str = "backmarket/api_response/"
    transformed_path: str = "backmarket/api_results/"


@dataclass(frozen=True)
class BMApiConfig(Config, ApiDataLakeConfig):
    proxy_secret_id: str = BMConst.BM_OXY_PROXY_SECRET_ID
    algolia_secret_id: str = BMConst.BM_ALGOLIA_SECRET_ID
    pubsub_topic: str = "backmarket-api-worker"
    pubsub_subscription: str = "product-crawler-subscription"
    max_received_messages: int = 10
    max_purged_messages: int = 100
    error_rate_threshold: float = 0.01  # 1%

    @cached_property
    def locales(self) -> list[str]:
        return ["de-at", "de-de", "it-it", "sv-se", "nl-nl", "pt-pt", "fr-be", "en-ie"]


@dataclass(frozen=True)
class AlgoliaSecret(DataClassJsonMixin):
    api_key: str
    # Hardcoding since it's not changing
    app_id: str = "9X8ZUDUNN9"


class Source(Enum):
    STARTER = 1
    WORKER = 2


@dataclass(frozen=True)
class ListingMessage(DataClassJsonMixin):
    url: str
    source: Source
    run_timestamp: float  # "POSIX timestamp as float"


@dataclass(frozen=True)
class AlgoliaWorkerMessage(DataClassJsonMixin):
    run_timestamp: float  # "POSIX timestamp as float"
    query: str
    payload: dict[str, Any]
    page: int
    language_country: str

    @cached_property
    def run_id(self) -> datetime:
        """Single run identifier. Single run includes requests for all products from one series
        :return: timestamp as datetime
        """
        return datetime.fromtimestamp(self.run_timestamp, tz=timezone.utc)

    @cached_property
    def id(self) -> str:
        """Unique request identifier used for output file names and tracing
        :return: string request id
        """
        request_id = f"{self.query[:BMConst.MAX_QUERY_LEN]}_{self.language_country}_{self.page}"
        request_id = "".join(c if c in BMConst.VALID_CHARS else "_" for c in request_id)
        request_id = re.sub(r"(_)\1+", r"\1", request_id)  # remove duplicates
        request_id = request_id.strip("_").lower()
        return request_id
