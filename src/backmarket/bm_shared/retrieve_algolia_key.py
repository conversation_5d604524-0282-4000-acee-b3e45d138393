import json

import requests
from bm_shared.listing_worker_model import MerchantConst, No<PERSON><PERSON><PERSON><PERSON>
from bs4 import BeautifulSoup

from common.logger import get_logger

logger = get_logger()
# Central URL to scrape the updated Algolia key
BASE_URL = "https://www.backmarket.de"


def extract_api_key(page: BeautifulSoup) -> str:
    """Extract the Algolia api key from a Backmarket website.

    :param page: merchant's first page
    :raises NoApiKey: when key not found
    :return: api_key
    """
    tag = page.find("script", {"id": MerchantConst.NUXT_DATA})
    if tag:
        parsed = json.loads(tag.text)
        api_key = None
        for e in parsed:
            if isinstance(e, dict):
                if "apiKey" in e:
                    idx = int(e["apiKey"])
                    if idx < len(parsed):
                        api_key = parsed[idx]
                    if api_key and isinstance(api_key, str) and len(api_key) >= MerchantConst.MIN_API_KEY_LEN:
                        return api_key

    raise NoApiKey("Couldn't find the Algolia API key!")


def fetch_algolia_key_from_website(url: str = BASE_URL) -> str:
    """
    Fetches the Algolia API key from the specified Backmarket website (defaults to .de website).

    :param url: The URL of the Backmarket website to fetch the key from.
    """
    response = requests.get(url)
    response.raise_for_status()
    key = extract_api_key(page=BeautifulSoup(response.text, "html.parser"))
    return key
