import base64
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

from bm_shared.bm_model import BMConst


def emulate_pubsub_message(data: str) -> dict[str, Any]:
    """Create simulated PubSub message structure. Used for local runs

    :param data: string wrapped into message
    :return: data dict for data field
    """
    encoded_data = base64.b64encode(data.encode("utf-8"))
    return {"message": {"data": encoded_data}}


def url_to_filename(url: str) -> Path:
    """Convert url to file name used in GCS or local file system

    :param url: URL to convert to name
    :return: file name
    """
    file_name = url.replace("https://", "").replace("http://", "")
    file_name = "".join(c if c in BMConst.VALID_CHARS else "_" for c in file_name)
    return Path(file_name)


def run_id_local_test() -> datetime:
    """Run id timestamp for local tests, needs to be same for starter and worker

    :return: run id datetime
    """
    return datetime(2020, 1, 2, 3, 4, tzinfo=timezone.utc)
