from dataclasses import replace
from datetime import datetime
from functools import cached_property
from math import ceil
from time import perf_counter

from api_worker_model import (
    ATTEMPT,
    RECEIVE_RETRY_ATTEMPTS,
    RETRIER,
    ProcessedRequest,
    RequestTiming,
)
from bm_shared import api
from bm_shared.api import AlgoliaClient, AlgoliaHit, ApiParams
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import AlgoliaSecret, AlgoliaWorkerMessage, BMApiConfig
from bm_shared.bm_schema import ALGOLIA_PRODUCTS_SCHEMA, AlgoliaProductColumns
from bm_shared.bm_worker_stats import WorkerStats
from pandas import NA, DataFrame, concat

from common.logger import get_logger
from common.pubsub_client import PubSubMessage, PubSubPublisher, PubSubSubscriber
from common.timing import measure_execution, timing
from common.utils import get_unique_identifier

logger = get_logger()


class BMApiWorker:
    def __init__(
        self,
        config: BMApiConfig,
        algolia_secret: AlgoliaSecret,
        algolia_client: AlgoliaClient,
        publisher: PubSubPublisher,
        subscriber: PubSubSubscriber,
        raw_data_repository: BMDataLakeRepository,
        transformed_data_repository: BMDataLakeRepository,
    ):
        self._config = config
        self._algolia_secret = algolia_secret
        self._algolia_client = algolia_client
        self._publisher = publisher
        self._subscriber = subscriber
        self._raw_data_repository = raw_data_repository
        self._transformed_data_repository = transformed_data_repository

    @cached_property
    def run_id(self) -> str:
        """
        Unique id for every run
        For a cloud run job, get the last part of execution id
        e.g. 'backmarket-product-worker-job-4mpv5' -> '4mpv5'
        For local run and tests, generate random id
        """

        if cloud_execution_id := self._config.cloud_run_execution:
            return cloud_execution_id.split("-")[-1]

        return get_unique_identifier()

    @timing(logger, "BMApiWorker.run")
    def run(self) -> WorkerStats:
        """Process messages from input subscription"""

        logger.info(f"Backmarket API worker job started with config: {self._config}")

        stats = WorkerStats()
        transformed = []

        while messages := self._receive_messages():
            for message in messages:
                try:
                    request = AlgoliaWorkerMessage.from_json(message.value)
                    processed_request = RETRIER(self.process_request, request)
                    if not processed_request.transformed.empty:
                        transformed.append(processed_request.transformed)

                    logger.debug(
                        f"BM API request done: "
                        f"retries={RETRIER.statistics.get(ATTEMPT, 0) - 1}, "
                        f"api={processed_request.timing.api_call}s, "
                        f"gcs-raw={processed_request.timing.save_extracted}s, "
                        f"tx={processed_request.timing.transform}s, "
                        f"republish-request={processed_request.timing.republish_subsequent_requests}s, "
                        f"id={request.id}, "
                        f"page={request.page}, "
                        f"subsequent_requests={processed_request.timing.subsequent_requests}",
                    )
                    stats = stats.record_successful_request()
                except Exception as e:
                    stats = stats.record_exception(e)
                    logger.info("BM API request error:", exc_info=True)

        if not transformed:
            logger.info("Empty transformed data! Load skipped.")
        else:
            transformed_df = concat(transformed, ignore_index=True)
            self.load(transformed_df)

        return stats

    def process_request(self, request: AlgoliaWorkerMessage) -> ProcessedRequest:
        """
        Process a single request and returns a ProcessedRequest
        containing transformed results and timing information for various stages
        of processing.

        :param request: Algolia API request
        :returns: processing request transformed data and timing
        """

        extracted = measure_execution(self.extract, request)
        extracted_saved = measure_execution(self._load_extracted, request, extracted.result)
        response = self._algolia_client.parse_response(extracted.result)

        subsequent_requests = self._get_first_page_subsequent_requests(request, response.nb_hits, request.page)
        subsequent_requests_published = measure_execution(self.republish_requests, subsequent_requests)

        country_code = self._algolia_client.country_code(request.language_country)
        transformed = measure_execution(self.transform, response.hits, request.run_id, country_code)

        timing = RequestTiming(
            api_call=extracted.timing,
            save_extracted=extracted_saved.timing,
            transform=transformed.timing,
            republish_subsequent_requests=subsequent_requests_published.timing,
            records=len(transformed.result),
            subsequent_requests=len(subsequent_requests),
        )

        return ProcessedRequest(transformed.result, timing)

    def extract(self, request: AlgoliaWorkerMessage) -> str:
        """
        Calls Algolia's API to fetch search results.

        :param request: Algolia request
        :return: response JSON string
        """
        logger.debug(f"Extracting page id: {request.id}, page:{request.page}")
        api_response = self._algolia_client.get_hits(request.payload, request.language_country, self._algolia_secret)

        return api_response

    def republish_requests(self, requests: list[AlgoliaWorkerMessage]) -> None:
        """
        Republishes a list of AlgoliaWorkerMessage requests
        :param requests: list of requests to republish
        """

        for request in requests:
            self._publisher.publish_message(request.to_json())

        if requests:
            logger.info(f"Republished {len(requests)} subsequent request(s).")

    @staticmethod
    def transform(
        hits: list[AlgoliaHit],
        scrapped_at: datetime,
        country_code: str,
    ) -> DataFrame:
        """
        Transforms a list of Algolia hits into a pandas DataFrame containing structured product data.

        :param hits: A list of AlgoliaHit objects to be transformed.
        :param scrapped_at: The datetime when the Backmarket stater started
        :param country_code: Country code where the data was collected.
        :return: Parsed products Dataframe
        """

        if not hits:
            logger.info("No Algolia hits to transform.")
            return DataFrame()

        return DataFrame(
            [
                {
                    AlgoliaProductColumns.product_name: hit.title_model,
                    AlgoliaProductColumns.instance_features: (
                        ApiParams.INSTANCE_SEPARATOR.join(hit.sub_title_elements) if hit.sub_title_elements else NA
                    ),
                    AlgoliaProductColumns.price: hit.price,
                    AlgoliaProductColumns.grade: hit.backbox_grade_label,
                    AlgoliaProductColumns.url: hit.link_grade_v2.href,
                    AlgoliaProductColumns.country: country_code,
                    AlgoliaProductColumns.scrapped_at: scrapped_at,
                    AlgoliaProductColumns.extra_features: ApiParams.NEW_BATTERY if hit.is_battery_new else NA,
                    AlgoliaProductColumns.title: hit.title,
                    AlgoliaProductColumns.id1: hit.backmarket_id,
                    AlgoliaProductColumns.id2: hit.id,
                    AlgoliaProductColumns.currency: hit.currency,
                    AlgoliaProductColumns.merchant_id: hit.merchant_id,
                }
                for hit in hits
            ]
        ).astype(ALGOLIA_PRODUCTS_SCHEMA.dtypes)

    def load(self, transformed: DataFrame) -> int:
        """
        Loads transformed data into a transformed datalake container.

        :param transformed: products dataframe
        :return: the number of saved records
        """
        if transformed.empty:
            logger.info("No transformed data to save.")
            return 0

        last_scrapped_at = transformed[AlgoliaProductColumns.scrapped_at].max().to_pydatetime()
        file_name = f"{self.run_id}_bm_products.parquet"

        logger.info(f"Saving transformed data for the '{last_scrapped_at}' date...")
        path = self._transformed_data_repository.write_prefixed_data_frame(
            transformed, file_name, self._config.transformed_path, last_scrapped_at
        )

        loaded = len(transformed)
        logger.info(f"Saved {loaded} record(s) to the '{path}'...")

        return loaded

    def _load_extracted(self, request: AlgoliaWorkerMessage, api_response: str) -> None:
        """
        Loads the raw API response to a file in a raw datalake container
        :param request: Algolia API request used to determine the file name and its path
        :param api_response: raw API response to be saved
        """
        self._raw_data_repository.write_prefixed_file_content(
            api_response,
            f"{request.id}.json",
            self._config.raw_scrapped_path,
            request.run_id,
        )

    def _receive_messages(self) -> list[PubSubMessage]:
        """
        Pulls messages from a subscription with retries and acknowledges them if messages
        are successfully retrieved.
        Retries are required to make sure that there are no messages to receive.
        Ref. https://cloud.google.com/pubsub/docs/pull

        1. The subscriber client explicitly calls the pull method, which requests messages for delivery
        2. A response with zero messages or with an error does not necessarily indicate that there are no messages
           available to receive

        :returns: List of messages. Empty list means when no more messages are in the queue
        """
        messages = []
        attempt = 0
        start = perf_counter()

        for attempt in range(1, RECEIVE_RETRY_ATTEMPTS + 1):
            messages = self._subscriber.receive_messages(self._config.max_received_messages)

            if messages:
                self._subscriber.acknowledge_messages(messages)
                break

        logger.info(
            f"Received {len(messages)} message(s) in {round(perf_counter() - start, 2)}s, " f"total attempts={attempt}"
        )

        return messages

    @staticmethod
    def _get_first_page_subsequent_requests(
        request: AlgoliaWorkerMessage, total_hits: int, page: int
    ) -> list[AlgoliaWorkerMessage]:
        """
        Requests for subsequent pages with the same API filter as the first page

        :param request: Algolia API request
        :param total_hits: total number of hits, possibly over multiple pages
        :param page: current response page number
        :return: list of requests for subsequent pages
        """
        requests: list[AlgoliaWorkerMessage] = []

        if page != 0:
            return requests

        pages = range(1, ceil(total_hits / api.ApiParams.PRODUCTS_PER_PAGE))
        for page in pages:
            payload = AlgoliaClient.payload(request.payload, request.query, page)
            request = replace(request, page=page, payload=payload)
            requests.append(request)

        return requests
