from unittest.mock import Mock

from api_worker_etl import BMApiWorker
from bm_shared.api import AlgoliaClient, ApiPayloadParams
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import AlgoliaSecret, AlgoliaWorkerMessage, BMApiConfig
from bm_shared.bm_utils import run_id_local_test
from bm_shared.oxy_http_client import OxyHTTPClient
from bm_shared.retrieve_algolia_key import fetch_algolia_key_from_website

from common.logger import setup_logger
from common.pubsub_client import (
    PubSubMessage,
    PubSubPublisher,
    PubSubSubscriber,
    PubSubSubscriberConfig,
)
from common.secret_manager_client import get_secret
from common.timing import timing

config = BMApiConfig()
logger = setup_logger(config)
oxy_client = OxyHTTPClient(get_secret(config.proxy_secret_id, config.project_id))
algolia_client = AlgoliaClient(oxy_client)

raw_data_repository = BMDataLakeRepository(config.raw_bucket)
transformed_data_repository = BMDataLakeRepository(config.transformed_bucket)

pubsub_publisher = PubSubPublisher(config.pubsub_topic)
pubsub_subscriber = PubSubSubscriber(
    PubSubSubscriberConfig(topic_name=config.pubsub_topic, subscription_name=config.pubsub_subscription)
)


@timing(logger)
def run() -> None:
    """
    Code entry point, create worker and process all messages in input subscription
    """
    # Get the Algolia API key from .de Backmarket live website
    api_secret = AlgoliaSecret(api_key=fetch_algolia_key_from_website())
    etl = BMApiWorker(
        config,
        api_secret,
        algolia_client,
        pubsub_publisher,
        pubsub_subscriber,
        raw_data_repository,
        transformed_data_repository,
    )
    processed = etl.run()
    processed.log_summary(config.error_rate_threshold)


if __name__ == "__main__":
    if config.is_local:
        logger.info("API worker running locally...")
        query = (
            '(backbox_grade: "9 Premium") AND (cat_id:"2") AND (brand_clean:"Samsung")'
            ' AND (model_clean:"Galaxy S22 Ultra 5G"'
            ' OR model_clean:"Galaxy S22 Ultra")'
            " AND special_offer_type=0"
        )
        page = 0
        request = AlgoliaWorkerMessage(
            run_timestamp=run_id_local_test().timestamp(),
            query=query,
            payload=AlgoliaClient.payload(ApiPayloadParams.IPHONE_PAYLOAD, query, page),
            page=page,
            language_country="de-de",
        )
        message = PubSubMessage("1", "10", request.to_json())
        pubsub_publisher = Mock()
        pubsub_subscriber = Mock()
        pubsub_subscriber.receive_messages.side_effect = [[message, message], [message], [], [], []]
    run()
