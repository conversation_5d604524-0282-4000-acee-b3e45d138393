from dataclasses import dataclass

from pandas import DataFrame
from tenacity import Retrying, stop_after_attempt, wait_exponential

REQUEST_RETRY_ATTEMPTS: int = 3
RECEIVE_RETRY_ATTEMPTS: int = 3
ATTEMPT: str = "attempt_number"

RETRIER = Retrying(
    wait=wait_exponential(min=1, max=2),
    stop=stop_after_attempt(REQUEST_RETRY_ATTEMPTS),
    reraise=True,
)


@dataclass(frozen=True)
class Loaded:
    records: int
    requests: int


@dataclass(frozen=True)
class RequestTiming:
    api_call: float
    save_extracted: float
    transform: float
    republish_subsequent_requests: float
    records: int
    subsequent_requests: int


@dataclass(frozen=True)
class ProcessedRequest:
    transformed: DataFrame
    timing: RequestTiming
