"""Starter ETL implementation"""

from dataclasses import dataclass
from datetime import datetime

from bm_shared.bm_base_etl import ListingBaseEtl
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig, ListingMessage, Source
from bm_shared.bm_repository import BMRepository
from bm_shared.bm_utils import url_to_filename
from bm_shared.oxy_http_client import OxyHTTPClient
from bm_starter_transformer import extract_urls, filter_merchant_urls
from google.cloud.pubsub_v1 import PublisherClient
from pandas import DataFrame

from common.logger import get_logger
from common.typings import TypeAlias

logger = get_logger()


@dataclass
class ExtractedType:
    sitemaps: list[str]
    sitemap_urls: list[str]
    products: DataFrame
    merchants: DataFrame


TransformedType: TypeAlias = list[str]
LoadedType: TypeAlias = int


class ListingStarterEtl(ListingBaseEtl):
    _config: ListingConfig

    def __init__(
        self,
        config: ListingConfig,
        run_id: datetime,
        http_client: OxyHTTPClient,
        pubsub_client: PublisherClient,
        raw_data_repository: BMDataLakeRepository,
        transformed_data_repository: BMDataLakeRepository,
        sql_repository: BMRepository,
    ):
        super().__init__(
            config,
            run_id,
            http_client,
            pubsub_client,
            raw_data_repository,
            transformed_data_repository,
        )
        self._sql_repository = sql_repository

    def extract(self) -> ExtractedType:
        """
        Extract: sitemaps from backmarket websites,
           product and merchant data from analytics db

        :return: list of sitemaps, list of URLs pointing to sitemaps,
            products and merchants dataframes
        :raises
            Exception: in case HTTP errors
            ConnectionError: in case network level problems
        """
        products = self._sql_repository.select_products()
        merchants = self._sql_repository.select_merchants()
        logger.debug(f"Starting crawler extract for {len(self._config.sitemaps)} sitemaps")
        sitemaps = []
        sitemap_urls = []
        for country in self._config.sitemaps:
            sitemap_url = self._config.sitemaps[country]
            sitemap = self._http_client.get_page(sitemap_url)
            sitemaps.append(sitemap)
            sitemap_urls.append(sitemap_url)
            logger.info(f"Downloaded sitemap:{sitemap_url}")

        return ExtractedType(sitemaps, sitemap_urls, products, merchants)

    def transform(self, extracted: ExtractedType) -> TransformedType:
        """Transform: get list of merchant page URLs from sitemaps

        :param extracted: list of sitemaps, list of URLs pointing to sitemaps
        :return: filtered URLs of merchant pages
        """
        all_urls_in_sitemaps = []
        for sitemap in extracted.sitemaps:
            urls = extract_urls(sitemap)
            all_urls_in_sitemaps.extend(urls)

        filtered = filter_merchant_urls(all_urls_in_sitemaps, self._config.merchant_pattern)
        logger.info(f"Filtered {len(filtered)} out of {len(all_urls_in_sitemaps)} URLs in sitemaps")
        return filtered

    def load(self, transformed: TransformedType) -> LoadedType:
        """Load: send list of curated URLs for processing

        :param transformed: merchant page URLs from sitemaps
        :return: number URLs sent to workers
        """
        logger.info(f"Loading {len(transformed[:self._config.max_urls_to_scrap])} URLs to crawl")
        for url in transformed[: self._config.max_urls_to_scrap]:
            message = ListingMessage(url, Source.STARTER, self._run_id.timestamp())
            self.publish_message(message.to_json())
        return len(transformed)

    def save_extracted_data(self, extracted: ExtractedType) -> None:
        """Save data in 'raw' datalake: sitemaps for future reference,
        products and merchants as input for workers

        :param extracted: includes sitemaps and their URLs
        """
        for sitemap, url in zip(extracted.sitemaps, extracted.sitemap_urls):
            self._raw_data_repository.write_prefixed_file_content(
                sitemap,
                url_to_filename(url).name,
                self._config.raw_scrapped_path,
                self._run_id,
            )
        self._raw_data_repository.write_prefixed_data_frame(
            extracted.products,
            self._config.products_file_name,
            self._config.raw_product_path,
            self._run_id,
        )
        self._raw_data_repository.write_prefixed_data_frame(
            extracted.merchants,
            self._config.merchants_file_name,
            self._config.raw_merchant_path,
            self._run_id,
        )

    def run(self) -> LoadedType:
        extracted: ExtractedType = self.extract()
        self.save_extracted_data(extracted)
        transformed: TransformedType = self.transform(extracted)
        result: LoadedType = self.load(transformed)

        return result
