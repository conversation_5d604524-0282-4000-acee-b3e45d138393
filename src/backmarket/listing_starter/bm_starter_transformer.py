import re

from lxml import etree


def filter_merchant_urls(urls: list[str], pattern: str) -> list[str]:
    """Keep only URLs that match a pattern

    :param urls: list of urls
    :param pattern: filter regex
    """
    compiled = re.compile(pattern)
    return [url for url in urls if compiled.match(url)]


def extract_urls(sitemap: str) -> list[str]:
    """Extract list of URLS from sitemap

    :param sitemap: sitemap content
    :return: List of URLs found
    """
    tree = etree.fromstring(str.encode(sitemap), parser=None)
    urls = tree.xpath("//xmlns:loc", namespaces={"xmlns": tree.nsmap[None]})
    url_list = [url.text for url in urls]
    return url_list
