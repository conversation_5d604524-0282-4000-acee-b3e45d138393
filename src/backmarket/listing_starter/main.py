from datetime import datetime, timezone
from os import environ

from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig
from bm_shared.bm_repository import BMRepository
from bm_shared.bm_utils import run_id_local_test
from bm_shared.oxy_http_client import OxyHTTPClient
from cloudevents.abstract import CloudEvent
from etl import ListingStarterEtl
from functions_framework import cloud_event
from google.cloud.pubsub_v1 import PublisherClient
from google.cloud.sql.connector import Connector

from common.consts import ANALYTICS_CLOUD_SQL_CF_READER_SECRET_ID
from common.logger import setup_logger
from common.secret_manager_client import get_secret
from common.sql_client import DatabaseConfig

config = ListingConfig()
logger = setup_logger(config)

oxy_client = OxyHTTPClient(get_secret(config.proxy_secret_id, config.project_id))
pubsub_client = PublisherClient()

raw_data_repository = BMDataLakeRepository(config.raw_bucket)
transformed_data_repository = BMDataLakeRepository(config.transformed_bucket)
db_secret = get_secret(ANALYTICS_CLOUD_SQL_CF_READER_SECRET_ID, config.project_id)


@cloud_event
def run(_: CloudEvent) -> None:
    """Cloud function entry point. Function triggered by PubSub"""

    run_id = datetime.now(tz=timezone.utc)
    _run(run_id)


def _run(run_id: datetime) -> None:
    """Execute Starter pipeline

    :param run_id: datetime run identifier
    """

    logger.info(f"Starter invoked with  run_id{run_id.isoformat()} {config=}")
    with Connector() as connector:
        repository = BMRepository(db_config=db_secret, connector=connector)
        etl = ListingStarterEtl(
            config,
            run_id,
            oxy_client,
            pubsub_client,
            raw_data_repository,
            transformed_data_repository,
            repository,
        )
        etl.run()


if __name__ == "__main__":
    logger.info("Running locally...")
    data = "start"
    config = ListingConfig(max_urls_to_scrap=0)
    db_secret = DatabaseConfig(
        host=environ.get("POSTGRES_HOST", "localhost"),
        database=environ.get("POSTGRES_DB", "platform"),
        username=environ.get("POSTGRES_USER", "postgres"),
        password=environ.get("POSTGRES_PASSWORD", "postgres"),
        port=int(environ.get("POSTGRES_PORT", 5433)),
    )
    _run(run_id_local_test())
