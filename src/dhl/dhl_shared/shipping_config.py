from dataclasses import dataclass
from os import environ

from dataclasses_json import DataClassJsonMixin

from common.config import Config
from common.utils import string_to_bool

DHL_SFTP_SECRET_ID = "dhl-sftp-config"


@dataclass(frozen=True)
class AccountConfig(DataClassJsonMixin):
    outbound_account: str = "*********"
    inbound_account: str = "*********"


@dataclass(frozen=True)
class DataLakeConfig(DataClassJsonMixin):
    billing_raw_path: str = "shipping_billing"
    billing_transformed_path: str = "shipping_billing_charges/"
    order_items_transformed_path: str = "shipping_billing_order_items/"


@dataclass(frozen=True)
class ShippingConfig(AccountConfig, DataLakeConfig, Config):
    provider: str = "DHL"
    country: str = "ES"
    support_email: str = "<EMAIL>"
    sftp_secret_id: str = DHL_SFTP_SECRET_ID
    file_suffix: str = ".csv"
    encoding: str = "utf-8"
    quoted_char: str = '"'
    decimal_sep: str = "."

    @property
    def remote_folder(self) -> str:
        """
        Remote folder for billing files.
        - TEST env (local or staging) is using different sFTP folder than PROD!
        """
        input_folder = "in/test" if self.is_test else "in/work"
        return f"{input_folder}/{self.country.lower()}"

    @property
    def provider_name(self) -> str:
        """
        Provider name used for logging purposes.
        """
        return f"{self.provider}:{self.country}".lower()

    @property
    def remove_processed_files(self) -> bool:
        """
        Whether to remove the files from the sFTP. Set in terraform as follows:
        - For local and staging, we have manually uploaded files, hence do not want to delete them;
        - For prod, after the files are successfully processed and copied to RAW bucket they should be removed;
        """

        value = environ.get("REMOVE_PROCESSED_FILES", False)
        return bool(string_to_bool(value))
