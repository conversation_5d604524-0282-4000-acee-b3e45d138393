import numpy as np
from dhl_shared.shipping_config import AccountConfig, ShippingConfig
from pandas import DataFrame, to_numeric
from schema import (
    ORDER_ITEMS_COLUMNS,
    SB_COLUMNS,
    SB_PARTITIONS,
    SBC_COLUMNS,
    ChargeColumn,
    ShippingBillingValues,
)

from common.consts import MAX_SERIAL
from common.logger import get_logger
from common.typings import FileInfo

logger = get_logger()


class TransformationError(ValueError):
    """ETL transformation error"""


def add_file_info(df: DataFrame, file_info: FileInfo) -> DataFrame:
    """
    Add metadata about imported file.
    Use .`loc` to avoid SettingWithCopyWarning warnings:
    - https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy
    - https://stackoverflow.com/questions/********/how-to-deal-with-settingwithcopywarning-in-pandas

    :param df: input data frame
    :param file_info: file metadata
    :return: enriched data frame
    """
    file_path = str(file_info.path)
    file_created = file_info.created
    logger.info(f"Adding metadata for '{file_path}' created at '{file_created}'...")

    df.loc[:, [SBC_COLUMNS.file_path.name]] = file_path
    df.loc[:, [SBC_COLUMNS.file_created.name]] = file_created
    df.loc[:, [SBC_COLUMNS.file_size.name]] = file_info.size

    return df.astype(SBC_COLUMNS.file_dtypes)


def add_package_type(df: DataFrame, config: AccountConfig) -> DataFrame:
    """
    Add package_type calculated column based on the export/import account.
    Use `numpy.select` instead of `apply` for performance reason

    Reference: https://stackoverflow.com/questions/********/
               create-new-column-based-on-values-from-other-columns-apply-a-function-of-multi

    :param df: input data frame
    :param config: account configuration

    :returns: enriched data frame
    """
    billing_account = SB_COLUMNS.billing_account
    logger.info(f"Adding package_type for {billing_account} ...")

    if billing_account not in df.columns:
        raise TransformationError(f"No {billing_account} in a DataFrame!")

    outbound = df[billing_account] == config.outbound_account
    inbound = df[billing_account] == config.inbound_account

    df[SBC_COLUMNS.package_type.name] = np.select(
        [outbound, inbound], [ShippingBillingValues.OUTBOUND, ShippingBillingValues.INBOUND], ShippingBillingValues.NA
    )

    return df.astype(SBC_COLUMNS.package_type.astype)


def add_charge_columns(df: DataFrame) -> DataFrame:
    """
    Add new charge type columns to the given data frame, based on the following:
    - Translate charge codes in existing XC1-XC9 columns into human-readable charge type columns;
    - Use `SB_COLUMNS.charge_codes_to_columns` mapping table to match between the code and corresponding (new) column;
    - If multiple XC columns match the same code, the first XC column is selected as given charge type.

    :param df: input data frame

    :returns: enriched data frame
    """
    logger.info(f"Adding charge columns for {len(SBC_COLUMNS.charge_codes_to_columns)} codes ...")
    for charge_code, charge_column in SBC_COLUMNS.charge_codes_to_columns.items():
        df = _add_charge_column(df, charge_code, charge_column)

    return df


def _add_charge_column(df: DataFrame, charge_code: str, charge_column: ChargeColumn) -> DataFrame:
    logger.debug(f"Adding {charge_column.column_name} for '{charge_code}' charge code ...")

    xc1 = df[SB_COLUMNS.xc1_code] == charge_code
    xc2 = df[SB_COLUMNS.xc2_code] == charge_code
    xc3 = df[SB_COLUMNS.xc3_code] == charge_code
    xc4 = df[SB_COLUMNS.xc4_code] == charge_code
    xc5 = df[SB_COLUMNS.xc5_code] == charge_code
    xc6 = df[SB_COLUMNS.xc6_code] == charge_code
    xc7 = df[SB_COLUMNS.xc7_code] == charge_code
    xc8 = df[SB_COLUMNS.xc8_code] == charge_code
    xc9 = df[SB_COLUMNS.xc9_code] == charge_code

    df[charge_column.net_column] = np.select(
        [xc1, xc2, xc3, xc4, xc5, xc6, xc7, xc8, xc9],
        [
            df[SB_COLUMNS.xc1_net],
            df[SB_COLUMNS.xc2_net],
            df[SB_COLUMNS.xc3_net],
            df[SB_COLUMNS.xc4_net],
            df[SB_COLUMNS.xc5_net],
            df[SB_COLUMNS.xc6_net],
            df[SB_COLUMNS.xc7_net],
            df[SB_COLUMNS.xc8_net],
            df[SB_COLUMNS.xc9_net],
        ],
        0,
    )

    df[charge_column.gross_column] = np.select(
        [xc1, xc2, xc3, xc4, xc5, xc6, xc7, xc8, xc9],
        [
            df[SB_COLUMNS.xc1_gross],
            df[SB_COLUMNS.xc2_gross],
            df[SB_COLUMNS.xc3_gross],
            df[SB_COLUMNS.xc4_gross],
            df[SB_COLUMNS.xc5_gross],
            df[SB_COLUMNS.xc6_gross],
            df[SB_COLUMNS.xc7_gross],
            df[SB_COLUMNS.xc8_gross],
            df[SB_COLUMNS.xc9_gross],
        ],
        0,
    )

    return df.astype(charge_column.astype)


def extract_order_item_ids(
    input_df: DataFrame, splitter: str = ",", min_id: int = 0, max_id: int = MAX_SERIAL
) -> DataFrame:
    """
    Extract valid shipping billing order item ids:
    - Select the rows with non-empty shipment_reference_1;
    - Limit DataFrame to `shipment_number` and `shipment_reference_1 columns only;
    - Parse `shipment_reference_1`
        - remove non-digit characters, except comma separator
        - explode concatenated numbers into rows;
        - select only valid numbers from 0 to 4 bytes integer (2147483647)

    :param input_df: input dataframe (cannot be changed!)
    :param splitter: splitting character, defaulted to ","
    :param min_id: minimum value for valid order item id
    :param max_id: maximum value for valid order item id

    :returns: extracted dataframe with valid shipping order item ids
    """
    logger.info(f"Extracting order_item_id's from {len(input_df)} rows ...")

    # Not null items limited to shipment_number, shipment_reference_1
    not_null = (
        input_df[SB_COLUMNS.shipment_number].notnull()
        & (input_df[SB_COLUMNS.shipment_number].str.strip() != "")
        & (input_df[SB_COLUMNS.shipment_reference_1].notnull())
        & (input_df[SB_COLUMNS.shipment_reference_1].str.strip() != "")
    )
    df = input_df[not_null][[SB_COLUMNS.shipment_number, SB_COLUMNS.shipment_reference_1]]

    # Clean non digit characters except comma (,) separator
    df[SB_COLUMNS.shipment_reference_1] = df[SB_COLUMNS.shipment_reference_1].str.replace(r"[^0-9,]+", "", regex=True)

    # Split and explode items: https://pandas.pydata.org/pandas-docs/stable/user_guide/reshaping.html#explode
    df = df.assign(shipment_reference_1=df[SB_COLUMNS.shipment_reference_1].str.split(pat=splitter)).explode(
        SB_COLUMNS.shipment_reference_1
    )

    # Convert to order_item_id: invalid parsing will be set as NaN.
    order_item_id = SBC_COLUMNS.order_item_id.name
    df[order_item_id] = to_numeric(df[SB_COLUMNS.shipment_reference_1], errors="coerce", downcast="unsigned")
    df.drop(columns=[SB_COLUMNS.shipment_reference_1], inplace=True)

    # return only valid ids
    all_ids = len(df)
    valid_ids = df[order_item_id].notnull() & (df[order_item_id] <= max_id) & (df[order_item_id] >= min_id)
    df = df[valid_ids].reset_index(drop=True)

    logger.info(f"Extracted {len(df)} valid ids from total of {all_ids} order_item_id's.")
    return df.astype(SBC_COLUMNS.order_item_id.astype)


def partition_shipping_billing(data_frame: DataFrame, config: ShippingConfig) -> DataFrame:
    """
    Enrich shipping billing data frame by adding partition columns
    :param data_frame: shipping billing transformed data frame
    :param config: shipping billing configuration

    :return: DataFrame
    """
    cols = SB_PARTITIONS
    date_col = SBC_COLUMNS.file_created.name

    data_frame[cols.country] = config.country
    data_frame[cols.provider] = config.provider
    data_frame[cols.year] = data_frame[date_col].dt.year
    data_frame[cols.month] = data_frame[date_col].dt.month
    data_frame[cols.day] = data_frame[date_col].dt.day

    return data_frame


def partition_order_items(shipping_billing: DataFrame, order_items: DataFrame) -> DataFrame:
    """
    Enrich order items data frame by adding partition columns as a result of merging with shipping billing
    :param shipping_billing: transformed and already partitioned data frame
    :param order_items: order items data frame

    :return: DataFrame
    """
    joined = shipping_billing.merge(order_items, on=ORDER_ITEMS_COLUMNS.shipment_number)

    return joined[ORDER_ITEMS_COLUMNS.all_columns + SB_PARTITIONS.all_columns]
