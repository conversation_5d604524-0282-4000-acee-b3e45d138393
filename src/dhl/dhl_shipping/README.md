# Shipping billing

This is a shipping billing module.

Business contact: [<PERSON><PERSON>](<EMAIL>)

## DHL shipping billing

Documentation:

- Notion [page](https://www.notion.so/refurbed/DHL-Express-Integration-Info-10db6a8983ab80a29853eed4807e9ee1)
- Interface description [DHL Shipping billing fields (003).xlsx](https://refurbed-my.sharepoint.com/:x:/r/personal/tomas<PERSON>_<PERSON><PERSON><PERSON>_refurbed_com/Documents/DHL/DHL%20Shipping%20billing%20fields%20\(003\).xlsx?d=w30daf7d436384ed8994c958ba0d89f54&csf=1&web=1&e=hwWt8y)

### High-level design

```mermaid
sequenceDiagram
    loop For each shipping provider
        Cloud Function ->> sFTP: Download all input files from sFTP folder
        Cloud Function ->> Cloud Function: Extract files into pandas DataFrame
        Cloud Function ->> Cloud Function: Transform data in pandas
        Cloud Function ->> Cloud SQL (import): Save data into staging tables
        Cloud Function ->> Cloud SQL (analytics): SQL MERGE from staging to target
        loop For each processed file
            Cloud Function ->> Cloud Storage: Copy raw files into RAW bucket
            Cloud Function ->> Cloud Storage: Copy transformed files into TRANSFORMED bucket
            Cloud Function -->> sFTP: remove file
        end
    end
    Cloud Function ->> Cloud SQL (analytics): Run QA check and issue warning to MM channel if necessary
```

### On-boarding new country

1. Initiate conversation with DHL
   attaching [interface description]([DHL Shipping billing fields (003).xlsx](https://refurbed-my.sharepoint.com/:x:/r/personal/tomasz_saluszewski_refurbed_com/Documents/DHL/DHL%20Shipping%20billing%20fields%20\(003\).xlsx?d=w30daf7d436384ed8994c958ba0d89f54&csf=1&web=1&e=qregfH)).

- Ensure
  that [DHL-etl-config](https://refurbed-my.sharepoint.com/:x:/r/personal/tomasz_saluszewski_refurbed_com/Documents/DHL/DHL%20Shipping%20billing%20fields%20(003).xlsx?d=w30daf7d436384ed8994c958ba0d89f54&csf=1&web=1&e=tIKkMM&nav=MTVfezVCODUxMUUyLTgwNkItNEE2OS1BRkY2LTc3MjUzMTczMEUxNn0)
  sheet contains account configuration for the new country!

2. Always ask [Victor Fernandez Casado (DHL ES)](<EMAIL>) to assist in the setup,
   as he can guide a DHL country billing team to follow the `ES` setup.

3. Once the files arrive at `sFTP` server, check them whether they comply with the interface:

- Compare the actual number of columns with the expected 55;
- Write new `read` test for `xy` country in [test_read](../tests/unit/test_dhl_shipping/test_read.py) module;
- Remember to anonymize file content for testing purposes;

4. Add new entry to `dhl_shipping_config`:

- See `src/sql-pg/migrations/V47__add_dhl_shipping_config_for_italy.sql` for reference

5. Run the final e2e flow with the new files:

- Upload the files to sFTP test folder (`/in/test/xy`) and run [main](main.py)
- Ensure the files were loaded without errors into PG tables:
    - `analytics.shipping_billing_charges`
    - `analytics.shipping_billing_order_items`
- Ensure there were no errors in `analytics.shipping_billing_errors`

### On-boarded countries

```sql
select *
from analytics.dhl_shipping_config
where is_active = true
```

- ES
- NL
- DE
- IT
