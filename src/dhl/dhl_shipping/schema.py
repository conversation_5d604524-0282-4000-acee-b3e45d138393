from collections import defaultdict
from dataclasses import dataclass
from datetime import UTC, datetime
from functools import cached_property
from pathlib import Path

import pandas as pd
from dataclasses_json import DataClassJsonMixin
from pandas import DataFrame
from pandas.core.dtypes.base import ExtensionDtype

from common.typings import DataLakeTimePartition


@dataclass(frozen=True)
class CalculatedColumn(DataClassJsonMixin):
    name: str
    dtype: ExtensionDtype = pd.StringDtype()

    @cached_property
    def astype(self) -> dict[str, ExtensionDtype]:
        return {self.name: self.dtype}


@dataclass(frozen=True)
class ChargeColumn(DataClassJsonMixin):
    column_name: str

    @cached_property
    def net_column(self) -> str:
        return f"{self.column_name}_net"

    @cached_property
    def gross_column(self) -> str:
        return f"{self.column_name}_gross"

    @cached_property
    def astype(self) -> dict[str, ExtensionDtype]:
        return {self.net_column: pd.Float32Dtype(), self.gross_column: pd.Float32Dtype()}


@dataclass(frozen=True)
class ShippingBillingColumns(DataClassJsonMixin):
    """DHL specific CSV file input columns in the order they appear in the file"""

    line_type: str = "line_type"
    billing_source: str = "billing_source"
    billing_account: str = "billing_account"
    billing_country_code: str = "billing_country_code"
    invoice_date: str = "invoice_date"
    invoice_number: str = "invoice_number"
    shipment_number: str = "shipment_number"
    shipment_date: str = "shipment_date"
    shipment_reference_1: str = "shipment_reference_1"
    shipment_reference_2: str = "shipment_reference_2"
    shipment_reference_3: str = "shipment_reference_3"
    weight_kg: str = "weight_kg"
    weight_flag: str = "weight_flag"
    senders_name: str = "senders_name"
    senders_country: str = "senders_country"
    receivers_country: str = "receivers_country"
    currency: str = "currency"
    amount_net: str = "amount_net"
    amount_gross: str = "amount_gross"

    # XC: charges
    xc1_code: str = "xc1_code"
    xc1_name: str = "xc1_name"
    xc1_net: str = "xc1_net"
    xc1_gross: str = "xc1_gross"

    xc2_code: str = "xc2_code"
    xc2_name: str = "xc2_name"
    xc2_net: str = "xc2_net"
    xc2_gross: str = "xc2_gross"

    xc3_code: str = "xc3_code"
    xc3_name: str = "xc3_name"
    xc3_net: str = "xc3_net"
    xc3_gross: str = "xc3_gross"

    xc4_code: str = "xc4_code"
    xc4_name: str = "xc4_name"
    xc4_net: str = "xc4_net"
    xc4_gross: str = "xc4_gross"

    xc5_code: str = "xc5_code"
    xc5_name: str = "xc5_name"
    xc5_net: str = "xc5_net"
    xc5_gross: str = "xc5_gross"

    xc6_code: str = "xc6_code"
    xc6_name: str = "xc6_name"
    xc6_net: str = "xc6_net"
    xc6_gross: str = "xc6_gross"

    xc7_code: str = "xc7_code"
    xc7_name: str = "xc7_name"
    xc7_net: str = "xc7_net"
    xc7_gross: str = "xc7_gross"

    xc8_code: str = "xc8_code"
    xc8_name: str = "xc8_name"
    xc8_net: str = "xc8_net"
    xc8_gross: str = "xc8_gross"

    xc9_code: str = "xc9_code"
    xc9_name: str = "xc9_name"
    xc9_net: str = "xc9_net"
    xc9_gross: str = "xc9_gross"

    @cached_property
    def fields(self) -> dict[str, str]:
        """All CSV fields as dict"""
        return self.to_dict()

    @cached_property
    def all_columns(self) -> list[str]:
        """All columns from the CSV file header"""
        return list(self.fields.values())

    @cached_property
    def used_columns(self) -> list[str]:
        """
        Used columns only, add `remove` for the column that should not be loaded into memory!
        Note: need a copy of `as_dict` so cannot use `all_columns` directly!
        """
        columns = list(self.fields.values())
        columns.remove(self.xc1_name)
        columns.remove(self.xc2_name)
        columns.remove(self.xc3_name)
        columns.remove(self.xc4_name)
        columns.remove(self.xc5_name)
        columns.remove(self.xc6_name)
        columns.remove(self.xc7_name)
        columns.remove(self.xc8_name)
        columns.remove(self.xc9_name)

        return columns


@dataclass(frozen=True)
class ShippingBillingCalculated(DataClassJsonMixin):
    """Calculated columns"""

    package_type: CalculatedColumn = CalculatedColumn("package_type")
    order_item_id: CalculatedColumn = CalculatedColumn("order_item_id", pd.Int32Dtype())

    # ----- FILE METADATA -----
    file_path: CalculatedColumn = CalculatedColumn("file_path")
    file_created: CalculatedColumn = CalculatedColumn("file_created", pd.DatetimeTZDtype(tz=UTC))
    file_size: CalculatedColumn = CalculatedColumn("file_size", pd.Int32Dtype())

    @cached_property
    def file_dtypes(self) -> dict[str, ExtensionDtype]:
        dtypes = self.file_path.astype
        dtypes.update(self.file_created.astype)
        dtypes.update(self.file_size.astype)

        return dtypes

    # ----- CHARGE COLUMNS -----
    remote_area_delivery: ChargeColumn = ChargeColumn("remote_area_delivery")
    remote_area_pickup: ChargeColumn = ChargeColumn("remote_area_pickup")
    fuel_surcharge: ChargeColumn = ChargeColumn("fuel_surcharge")
    change_of_billing: ChargeColumn = ChargeColumn("change_of_billing")
    duty_tax_paid: ChargeColumn = ChargeColumn("duty_tax_paid")
    emergency_situation: ChargeColumn = ChargeColumn("emergency_situation")
    export_declaration: ChargeColumn = ChargeColumn("export_declaration")
    carbon_reduced_fe: ChargeColumn = ChargeColumn("carbon_reduced_fe")
    carbon_reduced_fd: ChargeColumn = ChargeColumn("carbon_reduced_fd")
    carbon_reduced_ft: ChargeColumn = ChargeColumn("carbon_reduced_ft")
    import_export_duties: ChargeColumn = ChargeColumn("import_export_duties")
    import_export_taxes: ChargeColumn = ChargeColumn("import_export_taxes")
    oversize_piece: ChargeColumn = ChargeColumn("oversize_piece")
    overweight_piece: ChargeColumn = ChargeColumn("overweight_piece")
    plastic_flyer: ChargeColumn = ChargeColumn("plastic_flyer")
    shipment_insurance: ChargeColumn = ChargeColumn("shipment_insurance")
    demand_surcharge: ChargeColumn = ChargeColumn("demand_surcharge")

    @cached_property
    def charge_codes_to_columns(self) -> dict[str, ChargeColumn]:
        """
        XC1-XC9 charge codes mappings to new calculated columns:

        OO - Remote Area Delivery
        OB - Remote Area Pickup
        FF - Fuel Surcharge
        KA - Change of Billing
        DD - Duty Tax Paid
        CR - Emergency Situation
        WO - Export Declaration
        FE - GoGreen Plus - Carbon Reduced
        FD - GoGreen Plus - Carbon Reduced
        FT - GoGreen Plus - Carbon Reduced
        XX - Import Export Duties
        XB - Import Export Taxes
        YB - Oversize Piece
        YY - Overweight Piece
        GP - Plastic Flyer
        II - Shipment Insurance
        NX - Demand Surcharge
        """
        return {
            "OO": self.remote_area_delivery,
            "OB": self.remote_area_pickup,
            "FF": self.fuel_surcharge,
            "KA": self.change_of_billing,
            "DD": self.duty_tax_paid,
            "CR": self.emergency_situation,
            "WO": self.export_declaration,
            "FE": self.carbon_reduced_fe,
            "FD": self.carbon_reduced_fd,
            "FT": self.carbon_reduced_ft,
            "XX": self.import_export_duties,
            "XB": self.import_export_taxes,
            "YB": self.oversize_piece,
            "YY": self.overweight_piece,
            "GP": self.plastic_flyer,
            "II": self.shipment_insurance,
            "NX": self.demand_surcharge,
        }

    @cached_property
    def charge_columns_dtypes(self) -> dict[str, ExtensionDtype]:
        dtypes: dict[str, ExtensionDtype] = dict()
        for v in self.charge_codes_to_columns.values():
            dtypes.update(v.astype)

        return dtypes


SHIPPING_BILLING_COLUMNS = ShippingBillingColumns()
SB_COLUMNS = SHIPPING_BILLING_COLUMNS

SHIPPING_BILLING_CALCULATED = ShippingBillingCalculated()
SBC_COLUMNS = SHIPPING_BILLING_CALCULATED


@dataclass(frozen=True)
class OrderItemsColumns(DataClassJsonMixin):
    """Order items dataframe columns extracted from shipping billing"""

    shipment_number: str = SB_COLUMNS.shipment_number
    order_item_id: str = SBC_COLUMNS.order_item_id.name

    @cached_property
    def all_columns(self) -> list[str]:
        """All columns"""
        return list(self.to_dict())


ORDER_ITEMS_COLUMNS = OrderItemsColumns()


class ShippingBillingSchema:
    # Default data format for all date fields
    DATE_FORMAT: str = "%Y%m%d"

    # Date columns
    DATE_COLUMNS: list[str] = [SB_COLUMNS.invoice_date, SB_COLUMNS.shipment_date]

    # XC columns
    XC_CODES: list[str] = [
        SB_COLUMNS.xc1_code,
        SB_COLUMNS.xc2_code,
        SB_COLUMNS.xc3_code,
        SB_COLUMNS.xc4_code,
        SB_COLUMNS.xc5_code,
        SB_COLUMNS.xc6_code,
        SB_COLUMNS.xc7_code,
        SB_COLUMNS.xc8_code,
        SB_COLUMNS.xc9_code,
    ]

    XC_FLOATS: list[str] = [
        SB_COLUMNS.xc1_net,
        SB_COLUMNS.xc1_gross,
        SB_COLUMNS.xc2_net,
        SB_COLUMNS.xc2_gross,
        SB_COLUMNS.xc3_net,
        SB_COLUMNS.xc3_gross,
        SB_COLUMNS.xc4_net,
        SB_COLUMNS.xc4_gross,
        SB_COLUMNS.xc5_net,
        SB_COLUMNS.xc5_gross,
        SB_COLUMNS.xc6_net,
        SB_COLUMNS.xc6_gross,
        SB_COLUMNS.xc7_net,
        SB_COLUMNS.xc7_gross,
        SB_COLUMNS.xc8_net,
        SB_COLUMNS.xc8_gross,
        SB_COLUMNS.xc9_net,
        SB_COLUMNS.xc9_gross,
    ]

    # Float columns
    FLOAT_COLUMNS: list[str] = [
        SB_COLUMNS.weight_kg,
        SB_COLUMNS.amount_net,
        SB_COLUMNS.amount_gross,
    ] + XC_FLOATS

    # String columns
    STRING_COLUMNS = list(set(SB_COLUMNS.used_columns) - set(FLOAT_COLUMNS) - set(DATE_COLUMNS))

    # https://stackoverflow.com/questions/49684951/pandas-read-csv-dtype-read-all-columns-but-few-as-string
    DATA_TYPES: dict[str, str] = defaultdict(pd.StringDtype, {k: pd.Float32Dtype() for k in FLOAT_COLUMNS})


class ShippingBillingValues:
    NA = "NA"
    DETAIL_LINE_TYPE = "S"
    INBOUND = "inbound"
    OUTBOUND = "outbound"

    @staticmethod
    def create_empty_df() -> DataFrame:
        """
        Creates empty `Shipping Billing` data frame:
        - for transformations to avoid if `df.empty` checks in all methods
        - for unit tests

        :returns: empty data frame with valid schema
        """
        return DataFrame(columns=SB_COLUMNS.used_columns)


@dataclass(frozen=True)
class ShippingBillingDataLakePartition(DataLakeTimePartition):
    """
    Schema for shipping billing partition
    """

    country: str
    provider: str

    @property
    def path(self) -> Path:
        return Path(
            f"{self.base_path}/country={self.country}/provider={self.provider}/",
            f"year={self.year}/month={self.month}/day={self.day}",
        )


@dataclass(frozen=True)
class ShippingBillingPartitionColumns(DataClassJsonMixin):
    country: str = "country"
    provider: str = "provider"
    year: str = "year"
    month: str = "month"
    day: str = "day"

    @cached_property
    def all_columns(self) -> list[str]:
        """All columns"""
        return list(self.to_dict())


SB_PARTITIONS = ShippingBillingPartitionColumns()


@dataclass(frozen=True)
class ShippingBillingError(DataClassJsonMixin):
    """
    Schema for analytics.shipping_billing_errors table row.
    """

    error: str
    id: int
    billing_source: str
    billing_account: str
    shipment_number: str
    file_path: str
    file_created: datetime
