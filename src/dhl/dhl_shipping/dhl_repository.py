from dataclasses import dataclass

from dataclasses_json import DataClassJsonMixin
from dhl_shared.shipping_config import ShippingConfig
from pandas import DataFrame
from schema import ShippingBillingError

from common.logger import get_logger
from common.sql_model import SqlFunction, SqlProcedure, SqlTable
from common.sql_repository import SqlRepository

logger = get_logger()


@dataclass(frozen=True)
class ImportResult(DataClassJsonMixin):
    shipping_billing_count: int = 0
    order_items_count: int = 0


class DhlRepository(SqlRepository):
    """
    DHL-specific repository to encapsulate SQL operations within a single class
    """

    # Sql Tables: import
    SHIPPING_BILLING_IMPORT = SqlTable(schema_name="import", table_name="dhl_shipping_billing")
    ORDER_ITEMS_IMPORT = SqlTable(schema_name="import", table_name="dhl_order_items")

    # Sql Tables/Views: analytics
    SOURCE_FILES = SqlTable(schema_name="analytics", table_name="source_files")
    SHIPPING_BILLING = SqlTable(schema_name="analytics", table_name="dhl_shipping_billing")
    SHIPPING_CHARGES = SqlTable(schema_name="analytics", table_name="dhl_shipping_charges")
    ORDER_ITEMS = SqlTable(schema_name="analytics", table_name="dhl_order_items")
    SHIPPING_BILLING_ERRORS = SqlTable(schema_name="analytics", table_name="shipping_billing_errors")

    # Stored Procedures
    UPSERT_SOURCE_FILES = SqlProcedure(schema_name="analytics", procedure_name="upsert_source_files")
    UPSERT_SHIPPING_BILLING = SqlProcedure(schema_name="analytics", procedure_name="upsert_dhl_shipping_billing")
    UPSERT_SHIPPING_CHARGES = SqlProcedure(schema_name="analytics", procedure_name="upsert_dhl_shipping_charges")
    UPSERT_ORDER_ITEMS = SqlProcedure(schema_name="analytics", procedure_name="upsert_dhl_order_items")

    # Functions
    SELECT_SHIPPING_CONFIG = SqlFunction(schema_name="analytics", function_name="select_dhl_shipping_config")

    def insert_into_import(self, shipping_billing: DataFrame, order_items: DataFrame) -> ImportResult:
        """
        Insert transformed data into staging (import) tables.

        :param shipping_billing: shipping billing data
        :param order_items: related order items

        :returns: number of inserted (shipping billing, order items) as tuple
        """
        logger.info("Inserting into staging tables ...")
        return ImportResult(
            self.insert(shipping_billing, self.SHIPPING_BILLING_IMPORT, truncate=True),
            self.insert(order_items, self.ORDER_ITEMS_IMPORT, truncate=True),
        )

    def upsert_into_analytics(self) -> None:
        """
        Upsert staging data into target analytics table by means of stored procedures:
        - starts a transaction to assure ACID compliance
        - call procedures one by one in the correct order (mind FKs!)
        """
        logger.info("Upserting into target tables ...")
        with self.begin_transaction() as transaction:
            self.call(procedure=self.UPSERT_SOURCE_FILES, transaction=transaction)
            self.call(procedure=self.UPSERT_SHIPPING_BILLING, transaction=transaction)
            self.call(procedure=self.UPSERT_SHIPPING_CHARGES, transaction=transaction)
            self.call(procedure=self.UPSERT_ORDER_ITEMS, transaction=transaction)

    def select_shipping_config(self, active: bool = True) -> set[ShippingConfig]:
        """
        Selects `active` DHL shipping configuration.

        :param active: whether to select only active configuration, defaults to true.
        :returns: set of ShippingConfig objects.
        """
        result = self.select_from_function(function=self.SELECT_SHIPPING_CONFIG, args=[str(active)])

        return {
            ShippingConfig(
                country=row.country,
                support_email=row.support_email,
                outbound_account=row.outbound_account,
                inbound_account=row.inbound_account,
                file_suffix=row.file_suffix,
                encoding=row.encoding,
                quoted_char=row.quoted_char,
                decimal_sep=row.decimal_sep,
            )
            for row in result
        }

    def get_shipping_billing_errors(self, limit: int = 10) -> set[ShippingBillingError]:
        """
        Returns entries from analytics.shipping_billing_errors.

        :param limit: Number of rows to return, defaults to 10.
        :returns: Set of ShippingBillingError objects.
        """
        errors = self.select(table=self.SHIPPING_BILLING_ERRORS, limit=limit)
        return {
            ShippingBillingError(
                error=row.error,
                id=row.id,
                billing_source=row.billing_source,
                billing_account=row.billing_account,
                shipment_number=row.shipment_number,
                file_path=row.file_path,
                file_created=row.file_created,
            )
            for row in errors
        }
