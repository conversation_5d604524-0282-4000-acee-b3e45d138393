from dataclasses import dataclass
from pathlib import Path

from dhl_repository import DhlRepository
from dhl_shared.shipping_config import ShippingConfig
from pandas import DataFrame, concat, read_csv
from schema import (
    SB_COLUMNS,
    SB_PARTITIONS,
    ShippingBillingDataLakePartition,
    ShippingBillingSchema,
    ShippingBillingValues,
)
from transformer import (
    add_charge_columns,
    add_file_info,
    add_package_type,
    extract_order_item_ids,
    partition_order_items,
    partition_shipping_billing,
)

from common.base import BaseEtl
from common.data_lake_repository import DataLakeRepository
from common.logger import get_logger
from common.pipeline_logging.pipeline_logger import log_pipeline_execution
from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sftp_client import SftpClient
from common.sql_repository import SqlRepository
from common.typings import FileInfo, TypeAlias

logger = get_logger()

ExtractedType: TypeAlias = dict[str, FileInfo]


@dataclass(frozen=True, slots=True)
class TransformedData:
    """
    Extracted and transformed result used by the Load process
    """

    extracted_files: ExtractedType  # will be used by the `load` to remove processed files from sFTP
    shipping_billing: DataFrame  # import.dhl_shipping_billing
    order_items: DataFrame  # import.dhl_order_items


class DhlShippingEtl(BaseEtl, SupportsPipelineExecution):
    _config: ShippingConfig

    def __init__(
        self,
        config: ShippingConfig,
        pipeline_run: PipelineRun,
        local_path: Path,
        sftp_client: SftpClient,
        dhl_repository: DhlRepository,
        raw_data_repository: DataLakeRepository,
        transformed_data_repository: DataLakeRepository,
    ):
        super().__init__(config)
        self._pipeline_run = pipeline_run
        self._local_path = local_path
        self._sftp_client = sftp_client
        self._cs_client = raw_data_repository
        self._dhl_repository = dhl_repository
        self._raw_data_repository = raw_data_repository
        self._transformed_data_repository = transformed_data_repository

    @property
    def pipeline_name(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.pipeline_name

    @property
    def run_id(self) -> str:
        """SupportsPipelineExecution interface"""
        return self._pipeline_run.run_id

    @property
    def pipeline_execution_repository(self) -> SqlRepository:
        """SupportsPipelineExecution interface"""
        return self._dhl_repository

    def get_step_name(self, function_name: str) -> str:
        """SupportsPipelineExecution interface"""
        return f"{self._config.provider_name} etl"

    @log_pipeline_execution()
    def run(self) -> int:
        """
        ETL runner that performs the following steps:
        - Download files from sFTP
        - Extract the data from the files
        - Transform the data
        - Load to db and data lake
        - Remove already processed files on the sFTP (prod only)

        :return: number of inserted rows into db
        """
        extracted = self.extract()
        transformed = self.transform(extracted)
        result = self.load(transformed)
        self.clean(extracted)

        return result

    def extract(self) -> ExtractedType:
        """
        Extract Shipping provider files from the sFTP server:
        - to local storage for later transformation

        :return: dictionary of [LOCAL_PATH, FileInfo]
        """
        logger.info(f"Extracting files from '{self._config.remote_folder}' sFTP folder...")
        result = self._sftp_client.download_files(
            local_path=self._local_path, remote_folder=self._config.remote_folder, file_suffix=self._config.file_suffix
        )

        return result

    def transform(self, extracted: ExtractedType) -> TransformedData:
        """
        Transform extracted files into target format:
        - filter `S` charges
        - calculate package type from the account number

        :param extracted: list of extracted file paths
        :return: list of transformed data frames
        """
        logger.info(f"Transforming {len(extracted)} files...")
        input_df = self.read_from_csv(extracted)

        shipping_billing = add_package_type(input_df, self._config)
        shipping_billing = add_charge_columns(shipping_billing)
        order_items = extract_order_item_ids(input_df)

        return TransformedData(extracted_files=extracted, shipping_billing=shipping_billing, order_items=order_items)

    def load(self, processed: TransformedData) -> int:
        """
        Loading extracted and transformed data into db and data lake:
        :param processed: already extracted and transformed data

        :return: number of loaded records
        """
        count = len(processed.extracted_files)

        # Load to database
        self._dhl_repository.insert_into_import(processed.shipping_billing, processed.order_items)
        self._dhl_repository.upsert_into_analytics()

        # Load to data lake
        self._write_extracted_files(processed.extracted_files)
        self._write_transformed_data(processed)

        logger.info(f"Loaded {count} files.")
        return count

    def clean(self, extracted: ExtractedType) -> None:
        """
        Remove successfully processed files from the sFTP server
        :param extracted: list of extracted file paths
        """
        if self._config.remove_processed_files:
            remote_file_paths = [str(file_info.path) for file_info in extracted.values()]
            logger.info(
                f"Removing {len(remote_file_paths)} processed files from '{self._config.remote_folder}' sFTP folder..."
            )
            self._sftp_client.remove_files(remote_file_paths)

    def read_from_csv(self, extracted: ExtractedType) -> DataFrame:
        """
        Read all `extracted` files into pandas DataFrame.
        - Rename column headers to refurbed schema
        - Prefilter all rows to `S` items only
        - Skip empty rows

        Note: `python` engine needs to be used to accept different decimal separators
        Ref.: https://github.com/pandas-dev/pandas/issues/52594

        :param extracted: extracted files
        :return: DataFrame
        """
        result = ShippingBillingValues.create_empty_df()
        data_frames = []

        for local_path, file_info in extracted.items():
            logger.info(f"Reading '{local_path}' into pandas dataframe...")
            df = read_csv(
                engine="python",
                filepath_or_buffer=local_path,
                encoding=self._config.encoding,
                quotechar=self._config.quoted_char,
                index_col=False,
                # Read column header, but override the column names
                header=0,
                names=SB_COLUMNS.all_columns,
                # Subset of columns to select,
                usecols=SB_COLUMNS.used_columns,
                # Date parsing
                parse_dates=ShippingBillingSchema.DATE_COLUMNS,
                date_format=ShippingBillingSchema.DATE_FORMAT,
                # Decimal separator
                decimal=self._config.decimal_sep,
                # Data type(s) mapping
                dtype=ShippingBillingSchema.DATA_TYPES,
                # NaN values handling
                na_filter=False,
                keep_default_na=False,
                # Skip empty lines
                skip_blank_lines=True,
            ).query(f"{SB_COLUMNS.line_type} == '{ShippingBillingValues.DETAIL_LINE_TYPE}'")

            if not df.empty:
                df = add_file_info(df, file_info)
                data_frames.append(df)

        if data_frames:
            result = concat(data_frames)
            result.reset_index(drop=True, inplace=True)

        logger.info(f"Read {len(result.index)} rows into pandas dataframe.")

        return result

    def _write_extracted_files(self, extracted: ExtractedType) -> None:
        """
        Write all `extracted` files into storage bucket.

        :param extracted: extracted files
        """
        for local_file_path, file_info in extracted.items():
            logger.info(
                f"Writing extracted `{file_info.path.name}` to '{self._raw_data_repository.bucket_name}' bucket ..."
            )
            file_date = file_info.created
            partition = ShippingBillingDataLakePartition(
                base_path=self._config.billing_raw_path,
                country=self._config.country,
                provider=self._config.provider,
                year=file_date.year,
                month=file_date.month,
                day=file_date.day,
            )
            self._raw_data_repository.write_file(Path(local_file_path), partition)

    def _write_transformed_data(self, transformed: TransformedData) -> None:
        """
        Write processed data frames into storage bucket.
        :param transformed: transformed data
        """
        if transformed.shipping_billing.empty:
            # It is possible there are no files on the input
            logger.info("No shipping billing data found!")
            return

        logger.info(
            f"Writing transformed shipping billing to '{self._transformed_data_repository.bucket_name}' bucket..."
        )
        shipping_billing = partition_shipping_billing(transformed.shipping_billing, self._config)
        self._transformed_data_repository.write_data_frame(
            data_frame=shipping_billing,
            partition_cols=SB_PARTITIONS.all_columns,
            bucket_directory=self._config.billing_transformed_path,
        )

        if transformed.order_items.empty:
            # It is anomaly that billing data has no related order items!
            paths = [str(fileInfo.path) for fileInfo in transformed.extracted_files.values()]
            logger.warning(f"No order items found for {paths}!")
            return

        logger.info(f"Writing transformed order_items to '{self._transformed_data_repository.bucket_name}' bucket...")
        order_items = partition_order_items(shipping_billing, transformed.order_items)
        self._transformed_data_repository.write_data_frame(
            data_frame=order_items,
            partition_cols=SB_PARTITIONS.all_columns,
            bucket_directory=self._config.order_items_transformed_path,
        )

    def check_shipping_billing_errors(self) -> int:
        """
        Check `analytics.shipping_billing_errors` for any entries and log them as warnings.

        :returns: Number of errors (limited to 100)
        """
        errors = self._dhl_repository.get_shipping_billing_errors()
        for error in errors:
            logger.warning(error)

        return len(errors)
