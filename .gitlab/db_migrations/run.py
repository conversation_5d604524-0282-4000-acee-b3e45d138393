import os
from typing import Optional

from migration_config import SLEEP_SECONDS

from common.cloud_run_client.job_client import (
    CloudRunJobClient,
    CloudRunJobClientConfig,
)
from common.cloud_run_client.model import CloudRunJobRunRequest
from common.config import Config
from common.logger import setup_logger

cloud_run_client = CloudRunJobClient(CloudRunJobClientConfig(sleep_in_seconds=SLEEP_SECONDS))

os.environ["ENV_NAME"] = "CICD"
logger = setup_logger(Config())


def run_migration_job(job_name: str, job_request: Optional[CloudRunJobRunRequest] = None) -> None:
    """
    Runs a Cloud Run job for database migrations.
    :param job_name: name of the job to run
    :param job_request: request body parameters
    """
    job = cloud_run_client.run(
        job_name=job_name,
        body=job_request,
        wait_for_completion=True,
    )

    if job.is_succeeded:
        logger.info(f"'{job_name}' migration job succeeded in '{job.duration}'.")
        logger.info(f"'Logs URL: {job.log_uri}")
    else:
        raise RuntimeError(
            f"'{job_name}' migration job finished with status: '{job.completion_status}'. Logs: {job.log_uri}"
        )
