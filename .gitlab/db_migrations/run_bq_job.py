from migration_config import FlywayCommand, MigrationTarget
from run import run_migration_job

from common.cloud_run_client.model import CloudRunJobRunRequest

if __name__ == "__main__":
    env_variables = {
        "FLYWAY_COMMAND": FlywayCommand.from_environ().name,
        "MIGRATION_TARGET": MigrationTarget.BIGQUERY.name,
    }

    job_request = CloudRunJobRunRequest(environment_variables=env_variables)
    run_migration_job(job_name="migrate-bq", job_request=job_request)
