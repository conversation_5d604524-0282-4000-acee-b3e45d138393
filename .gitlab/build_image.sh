#!/bin/bash
set -euo pipefail

build_and_push_image() {
  : '
    Builds and optionally pushes Docker image.
   '
  local IMAGE_TAG="${1}"
  local DOCKERFILE="${2}"
  local PUSH_IMAGE="${3:-true}"
  DUMP_PATH="${DUMP_PATH:-./.temp/preview_dump_2025_07_16.sql}"

  docker build -t "${IMAGE_TAG}" -f "${DOCKERFILE}" --build-arg DUMP_PATH="${DUMP_PATH}" .

  if [[ "${PUSH_IMAGE}" == "false" ]]; then
    echo "Skipping image push (push-image set to false)"
  else
    echo "Pushing image ${IMAGE_TAG}..."
    docker push "${IMAGE_TAG}"
  fi
}

build_and_push_image "$@"
