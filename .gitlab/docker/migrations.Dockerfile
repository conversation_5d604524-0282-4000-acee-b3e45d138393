# Stage aliases
FROM python:3.12-slim AS python
FROM gcr.io/google.com/cloudsdktool/google-cloud-cli:533.0.0-slim AS gcloud
FROM gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.18 AS cloud-sql-proxy

# Use Debian-based Flyway image to avoid netty errors
FROM flyway/flyway:11.8.1

# Install required dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    jq \
    unzip \
    postgresql-client && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy python and tools from official images
COPY --from=python /usr/local /usr/local
COPY --from=gcloud /usr/lib/google-cloud-sdk /usr/lib/google-cloud-sdk
COPY --from=cloud-sql-proxy /cloud-sql-proxy /cloud-sql-proxy/

# Downloads JDBC Big Query drivers and uzip the archive to the Flyway drivers directory
# Ref.: https://cloud.google.com/bigquery/docs/reference/odbc-jdbc-drivers#current_jdbc_driver
RUN curl -L https://storage.googleapis.com/simba-bq-release/jdbc/SimbaJDBCDriverforGoogleBigQuery42_1.6.3.1004.zip -o temp.zip && \
    unzip temp.zip -d /flyway/drivers/ && \
    rm temp.zip

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PATH="${PATH}:/cloud-sql-proxy/:/usr/lib/google-cloud-sdk/bin/"

# Clear flyway entrypoint, otherwise it will run flyway by default
ENTRYPOINT []
