# This is base image for all Docker builds with Artifact Regsitry Authentication.

FROM gcr.io/google.com/cloudsdktool/google-cloud-cli:533.0.0-alpine AS gcloud
FROM flyway/flyway:11.8.1-alpine AS flyway
FROM python:3.12-alpine AS python
FROM docker:28.3.3-alpine3.21

COPY --from=gcloud /google-cloud-sdk /google-cloud-sdk
COPY --from=python / /
COPY --from=flyway /flyway /flyway
COPY --from=flyway /opt/java/openjdk /opt/java/openjdk

RUN apk add bash make curl postgresql-client

ENV PATH="${PATH}:/google-cloud-sdk/bin/:/opt/java/openjdk/bin:/flyway/"
ENV PYTHONUNBUFFERED=1
