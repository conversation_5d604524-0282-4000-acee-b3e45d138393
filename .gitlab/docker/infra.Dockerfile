# This is image for infra builds.
FROM hashicorp/terraform:1.12 AS tf
FROM python:3.12-slim-bookworm AS python
FROM debian:12.11-slim

COPY --from=python / /
COPY --from=tf /bin/terraform /bin/terraform
RUN apt-get update -y && apt-get upgrade -y
RUN apt-get install -y zip curl
RUN curl https://sdk.cloud.google.com | bash
RUN apt-get install -y docker.io


ENV PATH="${PATH}:/usr/bin/:/root/google-cloud-sdk/bin"
ENV PYTHONUNBUFFERED=1
