# Image with platform preview database populated
# https://cadu.dev/creating-a-docker-image-with-database-preloaded/
FROM postgres:15.13 AS dumper

# To be passed from Makefile via .env file
ARG DUMP_PATH=./.temp/preview_dump_2025_07_16.sql
ENV POSTGRES_DB=platform
ENV POSTGRES_USER=postgres
ENV POSTGRES_PASSWORD=postgres
ENV PGDATA=/data

RUN echo "Copying ${DUMP_PATH} ..."
COPY ${DUMP_PATH} /docker-entrypoint-initdb.d/

# Remove the exec "$@" content that exists in the docker-entrypoint.sh,
# so it will not start the PostgreSQL daemon (we don’t need it on this step).
RUN ["sed", "-i", "s/exec \"$@\"/echo \"skipping...\"/", "/usr/local/bin/docker-entrypoint.sh"]

# Execute the entrypoint itself:
# It will execute the dump and load the data into /data folder.
# Since we executed the sed command to remove the $@ content it will not run the PostgreSQL daemon
RUN ["/usr/local/bin/docker-entrypoint.sh", "postgres"]

# Final build stage
FROM postgres:15.13

# This will copy all files from /data folder from the dumper step into the $PGDATA
# from this current step, making our data preloaded when we start the container
# (without needing to run the dump every time we create a new container).
COPY --from=dumper /data $PGDATA
