services:
  flyway:
    image: ${APP_IMAGE}
    command: >
      flyway -configFiles=/app/src/sql-pg/flyway.toml -locations=filesystem:/app/src/sql-pg/
      -environment=cicd-docker-build -user=${POSTGRES_USER} -password=${POSTGRES_PASSWORD} migrate
    depends_on:
      *********************:
        condition: service_healthy

# Postgres image with platform database, see  .gitlab/docker/database.Dockerfile
  *********************:
    image: ${POSTGRES_IMAGE}
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    user: ${POSTGRES_USER}
    volumes:
      - analytics_data:/var/lib/postgresql/data:rw
    healthcheck:
      # Same as in `docker-compose.yml` in root folder
      test: ["CMD-SHELL",
             "psql -U ${POSTGRES_USER} -d ${POSTGRES_DB} -c 'select created_at from public.healthcheck' || exit 1"]
      start_period: 30s
      interval: 10s
      retries: 6
networks:
  default:
    name: ${NETWORK_NAME}

volumes:
  analytics_data:
