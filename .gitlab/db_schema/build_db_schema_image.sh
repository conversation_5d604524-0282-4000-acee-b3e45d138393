#!/usr/bin/env bash
set -euo pipefail

init() {
  : '
    Performs some init steps like setting environment variables by reading .env file or console arguments.
   '

  DIR="${BASH_SOURCE%/*}"
  if [[ ! -d "$DIR" ]]; then DIR="$PWD"; fi

  # Export env vars
  cp "$DIR"/../../.env.dist .env
  # shellcheck disable=SC2046
  export $(grep -v '^#' .env | xargs)

  # Parse arguments and export them to env vars, e.g. -local will result in "export local=1"
  while [ $# -gt 0 ]; do
    if [[ $1 == "-"* ]]; then
      param="${1/-/}"
      export "$param"=1
    fi
    shift
  done

  local="${local:-"0"}"
  IMAGE_NAME="${IMAGE_NAME:-"analytics-db-schema"}"
  IMAGE_NAME_LATEST="${IMAGE_NAME%:*}:latest"

  # Ensure parametrized variables are expanded
  CICD_DOCKER_REGISTRY=$(eval echo "$CICD_DOCKER_REGISTRY")
  POSTGRES_IMAGE=$(eval echo "$POSTGRES_IMAGE")

  # Ensure APP_IMAGE is exported as ENV variable
  APP_IMAGE="${APP_IMAGE:-${CICD_DOCKER_REGISTRY}/cicd-app:latest}"
  export APP_IMAGE=$APP_IMAGE

  # Dedicated network for each gitlab pipeline to avoid "network is ambiguous" errors
  NETWORK_NAME="analytics-db-$(date "+%Y%m%d-%H%M%S%3N")"
  export NETWORK_NAME=$NETWORK_NAME

  echo "DIR: $DIR"
  echo "local: $local"
  echo "IMAGE_NAME: $IMAGE_NAME"
  echo "APP_IMAGE: $APP_IMAGE"
  echo "POSTGRES_IMAGE: $POSTGRES_IMAGE"
  echo "NETWORK_NAME: $NETWORK_NAME"
  echo "POSTGRES_SERVICE_NAME: $POSTGRES_SERVICE_NAME"
}

clean_up() {
  : '
    Cleans up already running Docker services.
   '

  echo "Cleaning up already running Docker services..."

  pushd "$DIR"
    docker compose down --remove-orphans --volumes
    # shellcheck disable=SC2046
    PG_CONTAINER_ID=$(docker ps -a -q -f name="${POSTGRES_SERVICE_NAME}")
    if [ -n "${PG_CONTAINER_ID}" ]; then
      docker rm -v -f "${PG_CONTAINER_ID}"
    fi
  popd
}

run_migrations() {
  : '
    Runs flyway migrations over clean database.
   '
  pushd "$DIR"
    echo "Running flyway migrations..."
    docker compose run flyway
  popd
}

copy_pg_data() {
  : '
    Copies all Postgres internal files from the DB container already migrated by Flyway
   '
  PG_CONTAINER_ID=$(docker ps -n 1 -q -f name="${POSTGRES_SERVICE_NAME}")
  PG_DATA_LOCATION="$DIR"/data
  rm -rf "${PG_DATA_LOCATION}"

  echo "Copying Postgres data from Docker container '$PG_CONTAINER_ID' to '$PG_DATA_LOCATION'..."
  docker cp "$PG_CONTAINER_ID:/var/lib/postgresql/data/." "${PG_DATA_LOCATION}"
}

build_image() {
  : '
    Builds a new DB image with all the migrations applied by Flyway.
   '

  echo "Building Docker image $IMAGE_NAME..."
  pushd "$DIR"
    docker build --force-rm --progress=plain --cache-from "${IMAGE_NAME_LATEST}" --tag "${IMAGE_NAME}" .
  popd
}

push_image() {
  : '
    Pushes a new DB image to the registry.
   '

  echo "Pushing Docker image: $IMAGE_NAME"
  docker push "$IMAGE_NAME"
}

main() {
  : '
    Builds Postgres DB image with the schema already migrated by Flyway.
    Use switch -local to run the script locally:
      ./.gitlab/db_schema/build_db_schema_image.sh -local
   '

  echo "Running ${BASH_SOURCE[0]} ..."
  init "$@"

  # fix: runs clean_up before the migrations to be sure no leftover old DB containers running on a GitLab runner
  clean_up

  run_migrations
  copy_pg_data
  build_image
  if [[ $local -ne 1 ]]; then push_image; fi
  clean_up
}

main "$@"
