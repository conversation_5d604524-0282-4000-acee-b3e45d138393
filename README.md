# analytics-pipelines

Monorepo for all Analytics data pipelines.
All new pipelines should be added to this repository and should follow the standards used in this repository.

## Gitlab flow
We are using [gitlab flow](https://about.gitlab.com/topics/version-control/what-is-gitlab-flow/):

With GitLab Flow, all features and fixes go to the `main` branch while enabling production and stable branches.

Please follow [best practices](https://about.gitlab.com/topics/version-control/what-are-gitlab-flow-best-practices/)
while contributing to this repository, in particular:
1. Use feature branches rather than direct commits on the main branch.
2. Test all commits, not only ones on the main branch.
3. Write unit tests for all your new/updated features.
4. Perform code reviews before merging into the main branch.
5. Everyone starts from main and targets main.
6. Fix bugs in main first and release branches second.
7. Commit messages reflect intent.

### Commit message convention

Please follow [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) guidelines.

See example commit message below:
```
feat(shipping-billing): implement sFTP client

Implement SFTP client, secret add SFTP secrets to google secret manager via terraform, add integration tests.

Closes: #12582
```

## Coding conventions

We are using [PEP 8](https://peps.python.org/pep-0008/) style guide for Python Code.

It is enforced with the use of the following tools:
- [pre-commit](https://pre-commit.com/) hook that automates running linters;
- [black](https://github.com/psf/black) is the uncompromising Python code formatter;
- [flake8](https://pypi.org/project/flake8/) is a tool for style guide enforcement;
- [mypy](https://mypy-lang.org/) is a static type checker for Python that aims to combine the benefits of dynamic (or "duck") typing and static typing.
- [isort](https://pycqa.github.io/isort/) sorts imports alphabetically, and automatically separated into sections and by type.
- [SQLFluff](https://docs.sqlfluff.com/en/stable/gettingstarted.html) as a sql code formatter and linter;

### Toolset explained
- [How to use black, flake8, isort, and pre-commit framework to format Python codes](https://www.sefidian.com/2021/08/03/how-to-use-black-flake8-and-isort-to-format-python-codes/).
- Using [pyproject-toml](https://packaging.python.org/en/latest/guides/writing-pyproject-toml/) to configure linters, type checkers, etc.

### PyCharm / IntelliJ IDEA integration

The tools can be integrated with PyCharm using a [filewatcher](https://www.jetbrains.com/help/pycharm/using-file-watchers.html) plugin:
- Configuring [black filewatcher](https://black.readthedocs.io/en/stable/integrations/editors.html#as-file-watcher)
- Configuring `SQLFluff` filewatcher:

![sqlfluff_watcher](resources/misc/sqlfluff_watcher.png)

## Code Review Guidelines

Please read fully [GitLab code review guidelines](https://docs.gitlab.com/ee/development/code_review.html) and [best practices](https://docs.gitlab.com/ee/development/code_review.html#best-practices) in particular!

### Draft merge requests
- Always start with the [draft merge request](https://docs.gitlab.com/ee/user/project/merge_requests/drafts.html).
- Draft MR does not have to meet the code standards and cannot be merged.
- Ask for a live review of draft MR if necessary, but limit it to specific questions.

### Merge request size and reviewers
- Keep the MR below 1000 lines (exc. resources) if possible (so good task breakdown is of help!).
- Everyone is encouraged to do a code review!
  We should rotate within a team to not create silos and learn from each other.

### Code review check list
- An author should always be the first one to do a code review;
- Ensure your pipeline passes before requesting a review;
- Ensure the code is logically structured, uses intuitive names for modules, classes and methods;
- Naming is hard, but important. Follow conventions in the repo, before creating new standards;
- Ensure not used, commented out code or debug scripts are not committed;
- `TODO` comments are allowed for missing functionality that will be delivered in the next iteration:
  - Do not enter comments like `TODO: fix me in the future`
  - Use `TODO: to be implemented in gitlab issue XYZ`
- Unit test your business logic and integration test your infrastructure:
  - Separate infrastructure concerns from the core logic using
    [repository pattern](https://www.cosmicpython.com/book/chapter_02_repository.html);
  - Structure your code like an [onion](https://jeffreypalermo.com/2008/07/the-onion-architecture-part-1/)
    with the core logic inside and infrastructure outside the onion;
  - Proper encapsulation and [SOLID](https://en.wikipedia.org/wiki/SOLID) principles help to achieve this;
- Please send a message to the [Data Engineering GitLab CI/CD](https://ozwiena.refurbed.io/refurbed/channels/data-engineering-gitlab-cicd)
channel whenever you deploy your changes to `staging`;

### Code review comments
- Use [conventional: comments](https://conventionalcomments.org) - comments that are easy to grok and grep;
- Each comment by default is an `issue:` if not stated otherwise and should be addressed, at least by ✔️ checkmark;
- Use `nitpick:` or `suggestion:` for nice to have comments that can be ignored (but addressed) by the author;
- The merge request author resolves only the threads they have fully addressed;
- If there’s an open reply, an open thread, a suggestion, a question, or anything else,
  the thread should be left to be resolved by the reviewer;

### Further reading
- [conventional: comments](https://conventionalcomments.org)
- [The Broken Window Theory](https://blog.codinghorror.com/the-broken-window-theory/)
- [Software simplicity is a prerequisite to reliability](https://sre.google/sre-book/simplicity/)
- [The Pragmatic Programmer](https://pragprog.com/titles/tpp20/the-pragmatic-programmer-20th-anniversary-edition/)

## Local development setup

### Install / update Python 3.12
Python version 3.12 is used for development of all pipelines.

Simple Python Version Management [pyenv](https://github.com/pyenv/pyenv#installation) was used for installing Python
3.12:
1. Install  [pyenv](https://github.com/pyenv/pyenv#installation) if not installed already.
2. Update `pyenv` with `pyenv update`.
3. Look for the latest python 3.12 with the `pyenv install --list | grep 3.12` command.
4. Install the latest python 3.12 and set it as local interpreter:

```bash
pyenv install 3.12.9
pyenv local 3.12.9
```

#### Troubleshooting python installation (Ubuntu)

If you have issues installing python version with `pyenv` you may be missing `build` libraries:
```bash
sudo apt update; sudo apt install build-essential libssl-dev zlib1g-dev \
libbz2-dev libreadline-dev libsqlite3-dev curl \
libncursesw5-dev xz-utils tk-dev libxml2-dev libxmlsec1-dev libffi-dev liblzma-dev
```

### Create and activate virtual environment

Using virtualenv allows you to avoid installing Python packages globally,
which could break system tools or other projects!

```bash
python -m venv .venv
source .venv/bin/activate
```

#### Ensure Python is in 3.12 version

```bash
python --version
```

should produce `Python 3.12.*`

### Install project dependencies

```bash
make env
```

### Local DB setup
We use [flyway](https://flywaydb.org/) free open source tool for DB schema migrations.
Copy .env.dist to .env and make sure it has the following config:

```
POSTGRES_DB=platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
```

Run the make command to start db
```bash
make pg-up
```
You can now connect to the local Postgres instance on `5433` port with `.env` credentials.

### GCP staging account [authentication](https://cloud.google.com/docs/authentication/provide-credentials-adc#local-dev)
In order to easily access `refb-analytics-staging` GCP project, set up [Application Default Credentials (ADC)](https://cloud.google.com/docs/authentication/application-default-credentials) locally:
- [install](https://cloud.google.com/sdk/docs/install) the Google Cloud CLI, then [initialize](https://cloud.google.com/sdk/docs/initializing) it by running the following command::
  ```bash
  gcloud init
  ```
- Create local credentials to authenticate to `refb-analytics-staging` project by logging in with the necessary scopes for accessing Google Sheets and Google Cloud resources:
  - These scopes allow access to user info, the full cloud platform, and spreadsheet data.
  ```bash
  gcloud auth application-default login \
            --scopes=openid,https://www.googleapis.com/auth/userinfo.email,https://www.googleapis.com/auth/cloud-platform,https://www.googleapis.com/auth/spreadsheets
  ```
- make sure it works correctly by running the command (you should see the list of staging buckets in the output):
  ```bash
  gcloud storage ls
  ````
- to ensure you are on the correct project you can also run `gcloud info`, it will list active configuration and the project.

### PyCharm IDE specifics

- Mark the `src` and every function directory as sources and `tests` as test folder:

  ![Pyharm working directories](resources/misc/pycharm_working_directories.png)
- Use code inspections compatible with current Python version:

  ![Python 3.12](resources/misc/pycharm_inspections.png)


### Testing PostgreSQL locally
You need [local installation](https://www.psycopg.org/psycopg3/docs/basic/install.html#local-installation) of postgres binaries in order to run postgres unit tests.

The easiest is to install it using the [homebrew](https://brew.sh/) package manager:
```bash
brew install postgresql
```

### Connecting to the Cloud SQL PostgreSQL on staging project
We are using IAM based authentication, and all data engineers have writer role (full `CRUD` allowed).

In order to connect to the instance:
- Install [Cloud SQL Proxy](https://cloud.google.com/sql/docs/mysql/sql-proxy).
- Sign in to your account.
```bash
gcloud auth login
```
- Establish a connection using the proxy as follows with `--auto-iam-authn` switch as a must:
```bash
./cloud_sql_proxy <PROJECT>:<REGION>:<CLOUDSQL_INSTANCE_NAME> --auto-iam-authn
```
- Access PostgreSQL:
```bash
psql "host=127.0.0.1 sslmode=disable dbname=platform user=<your email>"
```
- Alternatively, for IDE like `PGAdmin` or `DataGrip` the authentication token can be used as the password:
```bash
gcloud auth print-access-token
```
- `BONUS 🤓` With `DataGrip` you don't have to install and run `CloudSQL Proxy` at all, just use the driver `PostgresSQL via CloudSQL Proxy` and then pass your e-mail and token as user and password:

  ![Cloud SQL](resources/misc/cloud_sql.png)

## Make commands

See all `make` commands in the [Makefile](Makefile). Below are the most commonly used.

#### Run pre-commit code checks

```bash ⚙️
make code-check
```

#### Run unit tests 🧪

```bash
make test
```

#### Plan and deploy infrastructure to staging env 🚀
Before you run following commands:
- read a [README](./infra/README.md) in the `infra` directory,
- make sure you have [GitLab token](https://docs.gitlab.com/ee/user/profile/personal_access_tokens.html) set as follows:
```bash
export TF_HTTP_PASSWORD=<your gitlab token>
```

To prepare Terraform plan:
```bash
make plan-staging
```

To deploy Terraform changes:
```bash
make deploy-staging
```

If you would like to deploy changes to specific resource(s) only (e.g. one cloud function only),
add the `args` parameter and run the command as follows:
```bash
make deploy-staging args="\
-target=module.resource_X \
-target=module.resource_Y"
```
where `-target` is a [Terraform parameter](https://developer.hashicorp.com/terraform/tutorials/state/resource-targeting) and `module.resource_X` is your changed resource full name.

If you would like to deploy your function/job even if its code has not been changed,
add the `-var=force_deploy=true` parameter to your command and run it as follows::
```bash
make deploy-staging args="\
-target=module.function_X \
-target=module.job_Y \
-var=force_deploy=true"
```
