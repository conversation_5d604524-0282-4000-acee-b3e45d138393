import re

import pandas as pd
import pytest
from gspread.exceptions import APIError, SpreadsheetNotFound, WorksheetNotFound

from common.google_sheet_client import GoogleSheetConfig, GoogleSheetsClient


def test_reload_google_sheet(
    dummy_dataframe: pd.DataFrame, google_sheets_config: GoogleSheetConfig, google_sheets_client: GoogleSheetsClient
) -> None:
    """
    Test writing a valid DataFrame to a Google Sheet and verify the data was correctly written.
    """
    # act
    google_sheets_client.reload_google_sheet(df=dummy_dataframe)

    # assert
    # Fetch the sheet data to verify it was written correctly
    sheet = google_sheets_client.client.open_by_key(google_sheets_config.spreadsheet_id).worksheet(
        google_sheets_config.sheet_name
    )
    actual = sheet.get_all_values()

    assert actual[0] == ["Name", "Age", "City"]
    assert actual[1] == ["Alice", "25", "New York"]
    assert actual[2] == ["Bob", "30", "San Francisco"]


def test_reload_google_sheet_empty_dataframe(
    google_sheets_client: GoogleSheetsClient, google_sheets_config: GoogleSheetConfig
) -> None:
    """
    Test clearing a Google Sheet by uploading an empty DataFrame and verify that the sheet is empty.
    """
    # Arrange: Create an empty DataFrame
    empty_df = pd.DataFrame()

    # Act: Update the Google Sheet with the empty DataFrame
    google_sheets_client.reload_google_sheet(df=empty_df)

    # Assert: The sheet should be cleared but no data added
    sheet = google_sheets_client.client.open_by_key(google_sheets_config.spreadsheet_id).worksheet(
        google_sheets_config.sheet_name
    )
    sheet_data = sheet.get_all_values()

    assert sheet_data == [[]]


def test_reload_google_sheet_invalid_spreadsheet_id(
    dummy_dataframe: pd.DataFrame, google_sheets_config: GoogleSheetConfig
) -> None:
    """
    Test handling an invalid spreadsheet ID and ensure a SpreadsheetNotFound exception is raised.
    """
    # Arrange: Use an invalid spreadsheet ID
    invalid_google_sheets_config = GoogleSheetConfig(
        spreadsheet_id="invalid_id",
        sheet_name=google_sheets_config.sheet_name,
        starting_cell=google_sheets_config.starting_cell,
    )
    new_google_sheets_client = GoogleSheetsClient(gs_config=invalid_google_sheets_config)

    # Act & Assert: Ensure that an SpreadsheetNotFound is raised due to an invalid spreadsheet ID
    with pytest.raises(SpreadsheetNotFound, match=re.escape("<Response [404]>")):
        new_google_sheets_client.reload_google_sheet(df=dummy_dataframe)


def test_reload_google_sheet_invalid_sheet_name(
    dummy_dataframe: pd.DataFrame, google_sheets_config: GoogleSheetConfig
) -> None:
    """
    Test handling an invalid sheet name and ensure a WorksheetNotFound exception is raised.
    """
    # Arrange: Use an invalid sheet name
    invalid_google_sheets_config = GoogleSheetConfig(
        spreadsheet_id=google_sheets_config.spreadsheet_id,
        sheet_name="InvalidSheetName",
        starting_cell=google_sheets_config.starting_cell,
    )
    new_google_sheets_client = GoogleSheetsClient(gs_config=invalid_google_sheets_config)

    # Act & Assert: Ensure that an WorksheetNotFound is raised due to an invalid sheet name
    with pytest.raises(WorksheetNotFound, match="InvalidSheetName"):
        new_google_sheets_client.reload_google_sheet(df=dummy_dataframe)


def test_reload_google_sheet_invalid_starting_cell(
    dummy_dataframe: pd.DataFrame, google_sheets_config: GoogleSheetConfig
) -> None:
    """
    Test handling an invalid starting cell and ensure an APIError is raised.
    """
    # Arrange: Use an invalid sheet name
    invalid_google_sheets_config = GoogleSheetConfig(
        spreadsheet_id=google_sheets_config.spreadsheet_id,
        sheet_name=google_sheets_config.sheet_name,
        starting_cell="bad_cell",
    )
    new_google_sheets_client = GoogleSheetsClient(gs_config=invalid_google_sheets_config)

    # Act & Assert: Ensure that an APIError is raised due to an invalid starting cell name
    with pytest.raises(APIError, match="Unable to parse range: 'Sheet1'!bad_cell"):
        new_google_sheets_client.reload_google_sheet(df=dummy_dataframe)
