import pandas as pd
import pytest

from common.google_sheet_client import GoogleSheetConfig, GoogleSheetsClient


@pytest.fixture(scope="session")
def google_sheets_config() -> GoogleSheetConfig:
    """
    Getting the test Google Sheet -- [CI/CD Integration Test]
    https://docs.google.com/spreadsheets/d/1tJKpKiayNipibOg-YwvIWgUzRXqhdSxEhpI_R5nmEyU/edit?gid=0#gid=0
    """
    return GoogleSheetConfig(
        spreadsheet_id="1tJKpKiayNipibOg-YwvIWgUzRXqhdSxEhpI_R5nmEyU", sheet_name="Sheet1", starting_cell="A1"
    )


@pytest.fixture(scope="session")
def dummy_dataframe() -> pd.DataFrame:
    # Sample DataFrame for testing
    df = pd.DataFrame({"Name": ["<PERSON>", "<PERSON>"], "Age": [25, 30], "City": ["New York", "San Francisco"]})

    return df


@pytest.fixture(scope="session")
def google_sheets_client(google_sheets_config: GoogleSheetConfig) -> GoogleSheetsClient:
    # GoogleSheetsClient handles authorization using application default credentials
    return GoogleSheetsClient(google_sheets_config)
