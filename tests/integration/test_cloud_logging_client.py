from unit.test_alerting.conftest import FUNCTION_NAME, JOB_NAME

from common.cloud_logging_client.client import CloudLogging<PERSON>lient
from common.cloud_logging_client.model import (
    CloudRunFunctionLogFilter,
    CloudRunJobLogFilter,
)


def test_list_job_log_entries(cloud_logging_client: CloudLoggingClient) -> None:
    # arrange
    log_filter = CloudRunJobLogFilter(
        severity="WARNING",
        job_name=JOB_NAME,
        max_results=1,
    )

    # act
    log_entries = cloud_logging_client.list_log_entries(log_filter)

    # assert
    if log_entries:
        assert log_entries[0].payload != ""


def test_list_function_log_entries(cloud_logging_client: CloudLoggingClient) -> None:
    # arrange
    log_filter = CloudRunFunctionLogFilter(
        severity="ERROR",
        function_name=FUNCTION_NAME,
        max_results=1,
    )

    # act
    log_entries = cloud_logging_client.list_log_entries(log_filter)

    # assert
    if log_entries:
        assert log_entries[0].payload != ""
