from typing import Iterator

import pytest

from common.pubsub_client import (
    PubSubPublisher,
    PubSubPublisherConfig,
    PubSubSubscriber,
    PubSubSubscriberConfig,
)

TEST_TOPIC = "analytics-pipelines-staging-tests"


@pytest.fixture(scope="session")
def test_topic_name() -> str:
    return TEST_TOPIC


@pytest.fixture(scope="session")
def pubsub_publisher_config(test_topic_name: str) -> PubSubPublisherConfig:
    return PubSubPublisherConfig(topic_name=test_topic_name)


@pytest.fixture(scope="session")
def pubsub_subscriber_config(test_topic_name: str) -> PubSubSubscriberConfig:
    return PubSubSubscriberConfig(
        subscription_name=f"{test_topic_name}-subscription",
        topic_name=test_topic_name,
        subscription_wait_sec_after_purge=30,
    )


@pytest.fixture(scope="session")
def subscriber_client(pubsub_subscriber_config: PubSubSubscriberConfig) -> Iterator[PubSubSubscriber]:
    subscriber = PubSubSubscriber(config=pubsub_subscriber_config)
    subscriber.subscribe()
    subscriber.purge_subscription()

    yield subscriber

    subscriber.close()


@pytest.fixture(scope="session")
def publisher_client(pubsub_publisher_config: PubSubPublisherConfig) -> PubSubPublisher:
    return PubSubPublisher(config=pubsub_publisher_config)
