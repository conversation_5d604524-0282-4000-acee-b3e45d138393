import time

import pytest
from utils import TestMessage

from common.pubsub_client import PubSubPublisher, PubSubSubscriber


@pytest.mark.parametrize(
    "given_message",
    ["foo", "bar", TestMessage(1, "baz").to_json()],
)
def test_publish_receive(
    given_message: str, publisher_client: PubSubPublisher, subscriber_client: PubSubSubscriber
) -> None:
    # Act
    message_id = publisher_client.publish_message(given_message)
    # Wait for message to be available for consumption
    time.sleep(5)
    messages = subscriber_client.receive_messages(max_messages=1)
    subscriber_client.acknowledge_messages(messages)

    # Assert
    assert messages != []
    actual_message = messages[0]
    assert actual_message.id == message_id
    assert actual_message.value == given_message


def test_purge_subscription(
    test_topic_name: str, publisher_client: PubSubPublisher, subscriber_client: PubSubSubscriber
) -> None:
    # Arrange
    publisher_client.publish_message("to")
    publisher_client.publish_message("be")
    publisher_client.publish_message("discarded")

    expected_message = "test"

    # Act
    subscriber_client.purge_subscription()
    publisher_client.publish_message(expected_message)
    messages = subscriber_client.receive_messages(max_messages=5)
    subscriber_client.acknowledge_messages(messages)

    # Assert
    assert messages != []
    assert messages[0].value == expected_message
