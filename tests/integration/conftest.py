from os import environ

import pytest
from google.cloud.storage import Bucket
from pipedrive_model import Piped<PERSON>Transformed

from common.cloud_logging_client.client import CloudLoggingClient
from common.cloud_run_client.function_client import CloudRunFunctionClient
from common.cloud_run_client.job_client import (
    CloudRunJobClient,
    CloudRunJobClientConfig,
)
from common.cloud_run_client.service_client import CloudRunServiceClient
from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientConfig,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    CloudSQLCreateInstanceParams,
    InstanceCreateSettings,
    IpConfiguration,
)
from common.config import Config
from common.data_lake_repository import DataLakeRepository
from common.time_service import TimeService


@pytest.fixture(scope="session")
def pipedrive_transformed(time_service: TimeService) -> list[PipedriveTransformed]:
    return [
        PipedriveTransformed(
            new_sales_manager="<PERSON>",
            new_sales_manager_email="<EMAIL>",
            merchant_id=123,
            merchant_name="<PERSON>",
            primary_email="<EMAIL>",
            all_emails=[
                {"label": "work", "value": "<EMAIL>", "primary": True},
                {"label": "help", "value": "<EMAIL>", "primary": False},
            ],
            account_manager="Max Mustermann",
            account_manager_email="<EMAIL>",
            account_name="John Doe Org",
            first_name="John",
            last_name="Doe",
            contact_roles=["General", "Performance"],
            has_performance_role=True,
            live_selling=time_service.now,
            date_entered=time_service.now.date(),
            date_closed=time_service.now.date(),
            probability_of_default=0.1132,
            vat_number="AT123456789",
            credit_score_on_monitor="Yes",
            credit_score_when_live="Green",
        ),
    ]


@pytest.fixture(scope="session")
def platform_export_timestamp() -> int:
    """
    Returns same timestamp for the whole platform_export test suite
    - ********** = 2025/01/21 12:00:00 UTC

    Configurable via `pyproject.toml` pyenv variable.
    """
    return int(environ.get("EXPORT_TIMESTAMP", **********))


@pytest.fixture(scope="session")
def cloud_run_job_client_config() -> CloudRunJobClientConfig:
    return CloudRunJobClientConfig()


@pytest.fixture(scope="session")
def cloud_run_job_client(cloud_run_job_client_config: CloudRunJobClientConfig) -> CloudRunJobClient:
    return CloudRunJobClient(cloud_run_job_client_config)


@pytest.fixture(scope="session")
def cloud_logging_client(config: Config) -> CloudLoggingClient:
    return CloudLoggingClient(config)


@pytest.fixture(scope="session")
def cloud_run_function_client(config: Config) -> CloudRunFunctionClient:
    return CloudRunFunctionClient(config)


@pytest.fixture(scope="session")
def cloud_run_service_client(config: Config) -> CloudRunServiceClient:
    return CloudRunServiceClient(config)


@pytest.fixture(scope="session")
def raw_data_lake_repository(config: Config) -> DataLakeRepository:
    return DataLakeRepository(config.raw_bucket)


@pytest.fixture(scope="session")
def raw_data_lake_bucket(raw_data_lake_repository: DataLakeRepository) -> Bucket:
    return raw_data_lake_repository.bucket


@pytest.fixture(scope="session")
def transformed_data_lake_repository(config: Config) -> DataLakeRepository:
    return DataLakeRepository(config.transformed_bucket)


@pytest.fixture(scope="session")
def transformed_data_lake_bucket(transformed_data_lake_repository: DataLakeRepository) -> Bucket:
    return transformed_data_lake_repository.bucket


@pytest.fixture(scope="session")
def cloud_sql_instance_client_config() -> CloudSQLInstanceClientConfig:
    return CloudSQLInstanceClientConfig()


@pytest.fixture(scope="session")
def cloud_sql_instance_client(cloud_sql_instance_client_config: CloudSQLInstanceClientConfig) -> CloudSQLInstanceClient:
    return CloudSQLInstanceClient(cloud_sql_instance_client_config)


@pytest.fixture(scope="session")
def new_cloud_sql_instance_params() -> CloudSQLCreateInstanceParams:
    return CloudSQLCreateInstanceParams(
        name="integration-tests-instance",
        root_password="Test-123@",
        settings=InstanceCreateSettings(
            backup_configuration=BackupConfiguration(),
            ip_configuration=IpConfiguration(),
        ),
    )
