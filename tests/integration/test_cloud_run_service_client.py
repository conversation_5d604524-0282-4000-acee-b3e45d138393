from unit.test_alerting.conftest import FUNCTION_NAME, SERVICE_FULL_NAME

from common.cloud_run_client.service_client import CloudRunServiceClient


def test_describe_function_service(cloud_run_service_client: CloudRunServiceClient) -> None:
    # act
    cloud_function = cloud_run_service_client.describe_resource(FUNCTION_NAME)

    # assert
    assert cloud_function.name == SERVICE_FULL_NAME


def test_describe_function_service_when_does_not_exists(cloud_run_service_client: CloudRunServiceClient) -> None:
    # act
    cloud_function = cloud_run_service_client.describe_resource("non-existing-function")

    # assert
    assert cloud_function is None
