import pytest
from integration.test_workflow.workflow_client import Workflow<PERSON>lient


@pytest.mark.parametrize("workflow_name", ["analytics-workflow", "platform-export", "unmanaged-workflow"])
def test_workflow(workflow_client: WorkflowClient, workflow_name: str) -> None:
    execution = workflow_client.create_execution(workflow_name)
    result = workflow_client.await_execution(execution)

    assert workflow_client.is_execution_successful(result)
