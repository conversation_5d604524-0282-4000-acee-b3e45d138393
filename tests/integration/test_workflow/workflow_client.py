from dataclasses import dataclass
from time import sleep
from typing import Optional

from dataclasses_json import DataClassJsonMixin
from google.cloud import workflows_v1
from google.cloud.workflows import executions_v1
from google.cloud.workflows.executions_v1 import Execution
from google.cloud.workflows.executions_v1.types import executions

from common.consts import GCP_REGION
from common.logger import Config, get_logger

logger = get_logger()


@dataclass(frozen=True)
class WorkflowConfig(DataClassJsonMixin):
    """Workflow client configuration"""

    project_id: str = Config.project_id
    location: str = GCP_REGION

    # Await configuration
    initial_delay_in_sec: int = 10
    backoff_multiplier: int = 1
    max_wait_time_in_seconds: int = 900  # 15 minutes


class WorkflowClient:
    """GCP workflows client"""

    def __init__(self, config: Optional[WorkflowConfig] = None):
        self._config = config or WorkflowConfig()
        self._execution_client = executions_v1.ExecutionsClient()
        self._workflows_client = workflows_v1.WorkflowsClient()

    def create_execution(self, workflow: str = "analytics-workflow") -> Execution:
        """
        Creates a new workflow execution and returns its execution object.

        :param workflow: workflow name
        :returns: Workflow execution
        """
        parent = self._workflows_client.workflow_path(self._config.project_id, self._config.location, workflow)
        execution = self._execution_client.create_execution(request={"parent": parent})
        logger.info(f"Started {workflow} with execution = `{execution.name}`")

        return execution

    def await_execution(self, execution: Execution) -> Execution:
        """
        Awaits workflow execution until its completion.

        :param execution: Workflow execution (active or finished)
        :returns: workflow execution result
        """
        delay = self._config.initial_delay_in_sec
        execution = self._get_execution(execution.name)
        execution_finished = self.is_execution_finished(execution)

        while not execution_finished and delay <= self._config.max_wait_time_in_seconds:
            logger.info(f"Waiting {delay} seconds for '{execution.name}'...")
            sleep(delay)
            delay *= self._config.backoff_multiplier
            execution = self._get_execution(execution.name)
            execution_finished = self.is_execution_finished(execution)

        if execution_finished:
            logger.info(f"Execution finished with result: {execution.result}")
        else:
            logger.warning(f"Execution exceeded wait time: {self._config.max_wait_time_in_seconds} seconds.")

        logger.info(f"Execution state: {execution.state.name}")

        return execution

    @staticmethod
    def is_execution_finished(execution: Execution) -> bool:
        """
        Whether workflow execution finished.

        :param execution: Workflow execution
        :return: bool
        """
        return execution.state != executions.Execution.State.ACTIVE

    @staticmethod
    def is_execution_successful(execution: Execution) -> bool:
        """
        Whether workflow execution is successful.

        :param execution: Workflow execution
        :return: bool
        """
        return execution.state == executions.Execution.State.SUCCEEDED

    def _get_execution(self, execution_name: str) -> Execution:
        return self._execution_client.get_execution(request={"name": execution_name})
