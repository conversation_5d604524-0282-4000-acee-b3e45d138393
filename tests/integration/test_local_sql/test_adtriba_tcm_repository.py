from datetime import date
from decimal import Decimal
from typing import Iterator

import pytest
from pandas import DataFrame, Timestamp
from sqlalchemy import Connection
from tcm_model import BackendConversions as BackendConversions
from tcm_pg_repo import AdtribaTcmPgRepository as AdtribaTcmPgRepository

from common.sql_client import DatabaseConfig
from common.sql_model import SqlTable


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig, order_item_offers: DataFrame) -> AdtribaTcmPgRepository:
    return AdtribaTcmPgRepository(db_config)


@pytest.fixture(scope="function")
def transaction(repository: AdtribaTcmPgRepository) -> Iterator[Connection]:
    with repository.begin_transaction() as transaction:
        yield transaction
        transaction.rollback()


def test_get_backend_conversions_when_no_data(
    repository: AdtribaTcmPgRepository, transaction: Connection, order_item_offers_table: SqlTable
) -> None:
    # arrange:
    repository.truncate_table(table=order_item_offers_table, transaction=transaction)
    export_date = date(2099, 12, 31)

    # act
    actual = repository.get_backend_conversions(export_date, export_date, transaction)

    # assert
    assert actual == []


def test_get_backend_conversions_when_data_found(
    repository: AdtribaTcmPgRepository,
    transaction: Connection,
    order_item_offers: DataFrame,
    order_item_offers_table: SqlTable,
) -> None:
    # arrange: insert test data
    repository.truncate_table(table=order_item_offers_table, transaction=transaction)
    repository.insert(df=order_item_offers, table=order_item_offers_table, truncate=True, transaction=transaction)

    # arrange: select export date
    paid_at: Timestamp = order_item_offers["paid_at"].max()
    export_date = date(paid_at.year, paid_at.month, paid_at.day)
    expected = [
        BackendConversions(
            date=date(2023, 8, 16),
            costumer_type="returning",
            device_type="web",
            total_conversions=1,
            total_revenue=Decimal("308.80"),  # noqa
            brand="refurbed",
            country="IE",
            conversion_name="Transaction",
            currency="EUR",
        )
    ]

    # act
    actual = repository.get_backend_conversions(export_date, export_date, transaction)

    # assert
    assert actual == expected
