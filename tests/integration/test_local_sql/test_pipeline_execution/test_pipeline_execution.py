from typing import Optional

import pytest
from integration.test_local_sql.test_pipeline_execution.model import (
    PipelineExecution,
    PipelineExecutionStep,
    PipelineExecutionTestClass,
)

from common.sql_model import SqlFunction
from common.sql_repository import SqlRepository
from common.utils import escape_sql


def test_pipeline_execution(
    base_number: int, test_class: PipelineExecutionTestClass, sql_repository: SqlRepository
) -> None:
    # arrange
    multiplier = 3
    expected = base_number * multiplier

    # act
    actual = test_class.run(multiplier, "test", 100)

    # assert
    assert actual == expected
    assert_pipeline_execution(test_class, sql_repository)


def test_pipeline_execution_when_error(test_class: PipelineExecutionTestClass, sql_repository: SqlRepository) -> None:
    # arrange
    multiplier = "test"
    python_error = f"Multiplier must be an integer, got {type(multiplier)}"

    # act
    with pytest.raises(ValueError, match=python_error):
        _ = test_class.run(multiplier, "test", 100)  # noqa

    # assert
    sql_error = escape_sql(python_error)
    assert_pipeline_execution(test_class, sql_repository, sql_error)


def assert_pipeline_execution(
    test_class: PipelineExecutionTestClass, sql_repository: SqlRepository, error: Optional[str] = None
) -> None:
    # assert `log.pipeline_execution` content
    executions = [
        PipelineExecution.from_dict(rows._mapping)  # noqa
        for rows in sql_repository.select_from_function(
            function=SqlFunction(schema_name="log", function_name="pipeline_execution_summary"),
            args=[test_class.run_id],
        )
    ]

    assert len(executions) == 1
    execution = executions[0]
    assert execution.pipeline_name == "pipeline execution test class"
    assert execution.run_id == test_class.run_id
    assert execution.finished >= execution.started
    assert execution.succeeded is (error is None)
    assert error in execution.error if error else execution.error is None

    # assert `log.pipeline_execution_step` content
    execution_steps = [
        PipelineExecutionStep.from_dict(rows._mapping)  # noqa
        for rows in sql_repository.select_from_function(
            function=SqlFunction(schema_name="log", function_name="pipeline_execution_step_summary"),
            args=[test_class.run_id],
        )
    ]

    assert len(execution_steps) == 2
    assert_execution_step(execution, execution_steps[0], "dummy", test_class.run_id)
    assert_execution_step(execution, execution_steps[1], "multiply", test_class.run_id, error)


def assert_execution_step(
    execution: PipelineExecution,
    execution_step: PipelineExecutionStep,
    step_name: str,
    run_id: str,
    error: Optional[str] = None,
) -> None:
    # assert step execution
    assert execution_step.pipeline_name == "pipeline execution test class"
    assert execution_step.step_name == step_name
    assert execution_step.run_id == run_id
    assert execution_step.finished >= execution_step.started
    assert execution_step.succeeded is (error is None)
    assert error in execution_step.error if error else execution_step.error is None

    # step dates must be between pipeline execution dates
    assert execution.started <= execution_step.started
    assert execution.finished >= execution_step.finished
