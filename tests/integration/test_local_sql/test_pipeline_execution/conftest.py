import pytest
from integration.test_local_sql.test_pipeline_execution.model import (
    PipelineExecutionTestClass,
)

from common.sql_repository import SqlRepository


@pytest.fixture(scope="function")
def base_number() -> int:
    """Base number for the test class."""
    return 5


@pytest.fixture(scope="function")
def test_class(base_number: int, sql_repository: SqlRepository) -> PipelineExecutionTestClass:
    return PipelineExecutionTestClass(base_number, sql_repository)
