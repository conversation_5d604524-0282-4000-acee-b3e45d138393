-- #################################################
-- This is demo file for setting SQLFluff rules
-- #################################################

--------------------------------------------------
-- DDL: begin / create / commit
--------------------------------------------------
begin;
drop table if exists "users";
create table users (
    id integer primary key,
    username varchar(50) not null
);
commit;

--------------------------------------------------
-- DML: select
--------------------------------------------------
select
    t1.id,
    t1.a,
    t2.b,
    sum(t2.c) as suma
from
    public.table1 as t1
inner join
    public.table2 as t2
on t1.id = t2.id
where
    t1.price > 1000
    and t2.price < 2000;
--------------------------------------------------
-- DML: CTE
--------------------------------------------------
with some_cte as (
    select
        id,
        a
    from table1
),

some_other_cte as (
    select
        id,
        b
    from table1
)

select
    t1.id,
    t1.a,
    t2.b
from
    some_cte as t1
inner join
    some_other_cte as t2
on t1.id = t2.id;
