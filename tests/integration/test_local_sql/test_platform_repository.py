from datetime import timed<PERSON><PERSON>
from pathlib import Path
from typing import Iterator, Optional

import pytest
from pandas import DataFrame, Int32Dtype, StringDtype, read_csv
from pandas._testing import assert_frame_equal

from common.pandas_utils import DataFrameSchema
from common.sql_client import DatabaseConfig
from common.sql_model import SqlTable
from platform_export.etl.platform_repository import PlatformRepository
from platform_export.export_shared.export_model import (
    CUT_OFF_DATETIME,
    ExportStatus,
    ExportType,
)
from platform_export.export_shared.export_target import PRODUCTS_SOURCE

CUT_OFF = CUT_OFF_DATETIME.isoformat()
ORDER_ITEM_OFFERS_VIEW = SqlTable(schema_name="export_etl", table_name="v_order_item_offers")
ORDER_ITEMS_OFFERS_COUNT = 11132  # Adjust to your local table count!


PRODUCTS_SCHEMA = DataFrameSchema(
    datetime_columns=["created_at", "updated_at", "deleted_at"],
    data_types={
        "id": Int32Dtype(),
        "category_id": Int32Dtype(),
        "listing_mode": StringDtype(),
        "name": StringDtype(),
        "name_en": StringDtype(),
        "slug": StringDtype(),
    },
)


@pytest.fixture(scope="function")
def products_df(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "platform_products.csv",
        # Date parsing
        parse_dates=PRODUCTS_SCHEMA.datetime_columns,
        # Data type(s) mapping
        dtype=PRODUCTS_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    ).astype(dtype=PRODUCTS_SCHEMA.dtypes)


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig, products_df: DataFrame) -> Iterator[PlatformRepository]:
    repo = PlatformRepository(db_config)

    yield repo

    # clean-up after each test
    repo.truncate_table(PRODUCTS_SOURCE)


@pytest.mark.parametrize(
    "export_type, export_status, expected",
    [
        (ExportType.INCREMENTAL, ExportStatus(), ""),
        (ExportType.TIME_TRAVEL, ExportStatus(), ""),
        (ExportType.SNAPSHOT, ExportStatus(), ""),
        (
            ExportType.INCREMENTAL,
            ExportStatus(last_id=1),
            f"id > 1 or updated_at >= '{CUT_OFF}' or deleted_at >= '{CUT_OFF}'",
        ),
        (
            ExportType.TIME_TRAVEL,
            ExportStatus(last_id=2),
            f"id > 2 or valid_from >= '{CUT_OFF}' or valid_to >= '{CUT_OFF}'",
        ),
    ],
)
def test_build_change_condition(
    export_type: ExportType, export_status: ExportStatus, expected: Optional[str], repository: PlatformRepository
) -> None:
    result = repository.build_change_condition(export_type=export_type, export_status=export_status)
    assert result == expected


def select_products_changes(repository: PlatformRepository, export_status: ExportStatus) -> DataFrame:
    condition = repository.build_change_condition(export_type=ExportType.INCREMENTAL, export_status=export_status)
    where = f" where {condition}" if condition else ""
    sql = f"{PRODUCTS_SOURCE.select_all_from}{where};"

    return repository.select_to_df(sql=sql, schema=PRODUCTS_SCHEMA)


def test_select_changes_when_no_source_data(repository: PlatformRepository, products_df: DataFrame) -> None:
    # arrange
    export_status = ExportStatus()

    # act
    actual_df = select_products_changes(repository, export_status)

    # assert
    assert actual_df.empty


def test_select_changes_when_no_changes_in_source(repository: PlatformRepository, products_df: DataFrame) -> None:
    # arrange
    last_id = products_df["id"].max()
    last_modified = (products_df["deleted_at"].max()).to_pydatetime() + timedelta(seconds=1)
    export_status = ExportStatus(last_id=last_id, last_modified=last_modified)

    repository.insert(df=products_df, table=PRODUCTS_SOURCE, truncate=True)

    # act
    actual_df = select_products_changes(repository, export_status)

    # assert
    assert actual_df.empty


def test_select_changes_when_full_load(repository: PlatformRepository, products_df: DataFrame) -> None:
    # arrange
    export_status = ExportStatus()
    repository.insert(df=products_df, table=PRODUCTS_SOURCE, truncate=True)

    # act
    actual_df = select_products_changes(repository, export_status).sort_values(by=["id"])

    # assert
    assert_frame_equal(actual_df, products_df)


@pytest.mark.parametrize(
    "date_column",
    [
        "created_at",
        "updated_at",
        "deleted_at",
    ],
)
def test_select_changes_when_created_updated_deleted(
    date_column: str, repository: PlatformRepository, products_df: DataFrame
) -> None:
    # arrange
    max_id = products_df["id"].max()
    last_id = max_id - 1 if date_column == "created_at" else max_id
    last_modified = (products_df[date_column].max()).to_pydatetime()
    export_status = ExportStatus(last_id=last_id, last_modified=last_modified)

    expected_df: DataFrame = products_df.loc[products_df.id == max_id, :].reset_index(drop=True)
    repository.insert(df=products_df, table=PRODUCTS_SOURCE, truncate=True)

    # act
    actual_df = select_products_changes(repository, export_status)

    # assert
    assert_frame_equal(actual_df, expected_df)
