from integration.sql_schema import USER, assert_users
from sqlalchemy import Engine, text

from common.sql_model import SqlTable
from common.sql_repository import SqlRepository


def test_engine_driver(engine: Engine) -> None:
    # assert
    assert engine.name == "postgresql"
    assert engine.driver == "pg8000"


def test_insert_sql(engine: Engine, sql_repository: SqlRepository, sql_table: SqlTable) -> None:
    # arrange
    insert_sql = text(f"insert into {sql_table.full_name} values({USER.id}, '{USER.username}')")

    # act
    with engine.begin() as connection:
        # https://docs.sqlalchemy.org/en/20/core/connections.html#connect-and-begin-once-from-the-engine
        connection.execute(insert_sql)

    # assert
    assert_users(sql_repository, sql_table, [USER])


def test_select_sql(engine: Engine) -> None:
    # arrange
    select_sql = text("select installed_rank from analytics.schema_history limit 5")
    # act
    with engine.connect() as connection:
        result = connection.execute(select_sql)

        # assert
        actual_ids = [int(row.installed_rank) for row in result]
        assert actual_ids
