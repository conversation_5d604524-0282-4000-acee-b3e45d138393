import datetime
from dataclasses import dataclass
from decimal import Decimal

from dataclasses_json import DataClassJsonMixin
from dhl_repository import DhlRepository
from integration.sql_schema import ZERO, assert_result


@dataclass(frozen=True)
class SourceFiles(DataClassJsonMixin):
    id: int
    file_path: str
    file_created: datetime.datetime
    file_size: int


@dataclass(frozen=True)
class ShippingBilling(DataClassJsonMixin):
    id: int
    shipment_number: str
    source_files_id: int
    package_type: str
    line_type: str
    billing_source: str
    billing_account: str
    billing_country_code: str
    invoice_date: datetime.date
    invoice_number: str
    shipment_date: datetime.date
    shipment_reference_1: str
    shipment_reference_2: str
    shipment_reference_3: str
    weight_kg: Decimal
    weight_flag: str
    senders_name: str
    senders_country: str
    receivers_country: str
    currency: str
    amount_net: Decimal
    amount_gross: Decimal


@dataclass(frozen=True)
class ShippingCharges(DataClassJsonMixin):
    shipping_billing_id: int = 1
    remote_area_delivery_net: Decimal = ZERO
    remote_area_delivery_gross: Decimal = ZERO
    remote_area_pickup_net: Decimal = ZERO
    remote_area_pickup_gross: Decimal = ZERO
    fuel_surcharge_net: Decimal = ZERO
    fuel_surcharge_gross: Decimal = ZERO
    change_of_billing_net: Decimal = ZERO
    change_of_billing_gross: Decimal = ZERO
    duty_tax_paid_net: Decimal = ZERO
    duty_tax_paid_gross: Decimal = ZERO
    emergency_situation_net: Decimal = ZERO
    emergency_situation_gross: Decimal = ZERO
    export_declaration_net: Decimal = ZERO
    export_declaration_gross: Decimal = ZERO
    carbon_reduced_fe_net: Decimal = ZERO
    carbon_reduced_fe_gross: Decimal = ZERO
    carbon_reduced_fd_net: Decimal = ZERO
    carbon_reduced_fd_gross: Decimal = ZERO
    carbon_reduced_ft_net: Decimal = ZERO
    carbon_reduced_ft_gross: Decimal = ZERO
    import_export_duties_net: Decimal = ZERO
    import_export_duties_gross: Decimal = ZERO
    import_export_taxes_net: Decimal = ZERO
    import_export_taxes_gross: Decimal = ZERO
    oversize_piece_net: Decimal = ZERO
    oversize_piece_gross: Decimal = ZERO
    overweight_piece_net: Decimal = ZERO
    overweight_piece_gross: Decimal = ZERO
    plastic_flyer_net: Decimal = ZERO
    plastic_flyer_gross: Decimal = ZERO
    shipment_insurance_net: Decimal = ZERO
    shipment_insurance_gross: Decimal = ZERO
    demand_surcharge_net: Decimal = ZERO
    demand_surcharge_gross: Decimal = ZERO
    total_net: Decimal = ZERO
    total_gross: Decimal = ZERO


@dataclass(frozen=True)
class OrderItems(DataClassJsonMixin):
    shipping_billing_id: int
    order_item_id: int


def assert_source_files(dhl_repository: DhlRepository, expected: set[SourceFiles]) -> None:
    result = dhl_repository.select(dhl_repository.SOURCE_FILES)
    assert_result(SourceFiles, result, expected)


def assert_shipping_billing(dhl_repository: DhlRepository, expected: set[ShippingBilling]) -> None:
    result = dhl_repository.select(dhl_repository.SHIPPING_BILLING)
    assert_result(ShippingBilling, result, expected)


def assert_shipping_charges(dhl_repository: DhlRepository, expected: set[ShippingCharges]) -> None:
    result = dhl_repository.select(dhl_repository.SHIPPING_CHARGES)
    assert_result(ShippingCharges, result, expected)


def assert_order_items(dhl_repository: DhlRepository, expected: set[OrderItems]) -> None:
    result = dhl_repository.select(dhl_repository.ORDER_ITEMS)
    assert_result(OrderItems, result, expected)


def assert_no_errors(dhl_repository: DhlRepository) -> None:
    errors = dhl_repository.get_shipping_billing_errors()
    assert not errors
