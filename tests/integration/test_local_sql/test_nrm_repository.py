from datetime import date
from typing import Iterator

import pytest
from pandas import DataFrame, Timestamp
from sqlalchemy import Connection

from common.sql_client import DatabaseConfig
from common.sql_model import SqlTable
from nrm.nrm_model import REVENUE_COUNTRIES_SCHEMA, RevenueCountriesColumns
from nrm.nrm_pg_repository import NrmPgRepository


@pytest.fixture(scope="function")
def repository(db_config: DatabaseConfig, order_item_offers: DataFrame) -> NrmPgRepository:
    return NrmPgRepository(db_config)


@pytest.fixture(scope="function")
def transaction(repository: NrmPgRepository) -> Iterator[Connection]:
    with repository.begin_transaction() as transaction:
        yield transaction
        transaction.rollback()


def test_select_revenue_countries_when_no_data(
    repository: NrmPgRepository, transaction: Connection, order_item_offers_table: SqlTable
) -> None:
    # arrange: truncate data
    repository.truncate_table(table=order_item_offers_table, transaction=transaction)
    load_date = date(2024, 1, 1)

    # act
    result = repository.select_revenue_countries(start_date=load_date, transaction=transaction)

    # assert
    assert result.empty is True
    assert set(result.columns) == set(REVENUE_COUNTRIES_SCHEMA.columns)


def test_select_revenue_countries_when_data_found(
    repository: NrmPgRepository,
    transaction: Connection,
    order_item_offers: DataFrame,
    order_item_exchange_rate: DataFrame,
    order_item_offers_table: SqlTable,
) -> None:
    # arrange: insert test data
    repository.insert(df=order_item_offers, table=order_item_offers_table, truncate=True, transaction=transaction)

    table = SqlTable(schema_name="analytics", table_name="order_item_exchange_rate")
    repository.insert(df=order_item_exchange_rate, table=table, truncate=True, transaction=transaction)

    # arrange: select export date
    paid_at: Timestamp = order_item_offers[RevenueCountriesColumns.DATE].max()
    load_date = date(paid_at.year, paid_at.month, paid_at.day)

    # act
    result = repository.select_revenue_countries(start_date=load_date, transaction=transaction)
    assert result.empty is False

    actual: Timestamp = result[RevenueCountriesColumns.DATE][0]
    actual_date = date(actual.year, actual.month, actual.day)

    # assert
    assert len(result) == 1
    assert actual_date == load_date
