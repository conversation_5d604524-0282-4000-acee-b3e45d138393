import pytest

from backmarket.bm_shared.bm_repository import BMRepository
from backmarket.bm_shared.bm_schema import BMMerchantColumns, BMProductColumns
from common.sql_client import DatabaseConfig


@pytest.fixture(scope="function")
def bm_repository(db_config: DatabaseConfig) -> BMRepository:
    return BMRepository(db_config)


def test_products_function(bm_repository: BMRepository) -> None:
    # arrange
    expected_columns = [BMProductColumns.product_id, BMProductColumns.product_name]

    # act
    actual = bm_repository.select_products()

    # assert
    assert not actual.empty
    assert list(actual.columns) == expected_columns


def test_merchant_function(bm_repository: BMRepository) -> None:
    # arrange
    expected_columns = [BMMerchantColumns.merchant_id, BMMerchantColumns.ref_merchant_name]

    # act
    actual = bm_repository.select_merchants()

    # assert
    assert not actual.empty
    assert list(actual.columns) == expected_columns
