import datetime
from dataclasses import replace
from decimal import Decimal
from pathlib import Path
from typing import Iterator

import pytest
from dhl_shared.shipping_config import ShippingConfig
from dhl_shipping.dhl_repository import DhlRepository, ImportResult
from dhl_shipping.schema import SB_COLUMNS
from pandas import DataFrame

from common.sql_client import DatabaseConfig
from common.typings import FileInfo
from tests.dhl.mocks import (
    INBOUND,
    OUTBOUND,
    mock_multiple_charges_order_items,
    mock_multiple_charges_shipping_billing,
    mock_quoted_identifier_order_items,
    mock_quoted_identifier_shipping_billing,
)
from tests.integration.test_local_sql.dhl_schema import (
    OrderItems,
    ShippingBilling,
    ShippingCharges,
    SourceFiles,
    assert_no_errors,
    assert_order_items,
    assert_shipping_billing,
    assert_shipping_charges,
    assert_source_files,
)

current_dir = Path(__file__).resolve().parent


@pytest.fixture(scope="function")
def dhl_repository(db_config: DatabaseConfig) -> Iterator[DhlRepository]:
    repo = DhlRepository(db_config)

    yield repo

    # clean-up after each test
    repo.truncate_table(repo.SHIPPING_BILLING_IMPORT)
    repo.truncate_table(repo.ORDER_ITEMS_IMPORT)

    repo.truncate_table(repo.SOURCE_FILES)
    repo.truncate_table(repo.SHIPPING_BILLING)
    repo.truncate_table(repo.SHIPPING_CHARGES)
    repo.truncate_table(repo.ORDER_ITEMS)


@pytest.fixture(scope="function")
def file_info() -> FileInfo:
    return FileInfo(path=current_dir / "test.csv", created=datetime.datetime.now(datetime.UTC), size=123)


def test_insert_into_import_when_no_data(dhl_repository: DhlRepository) -> None:
    # arrange
    expected = ImportResult()

    # act
    result = dhl_repository.insert_into_import(DataFrame(), DataFrame())

    # assert
    assert result == expected


def test_insert_into_import_when_quoted_identifier(
    dhl_repository: DhlRepository,
    file_info: FileInfo,
) -> None:
    # arrange
    shipping_billing = mock_quoted_identifier_shipping_billing(file_info)
    order_items = mock_quoted_identifier_order_items()
    expected = ImportResult(len(shipping_billing), len(order_items))

    # act
    result = dhl_repository.insert_into_import(shipping_billing, order_items)

    # assert
    assert_no_errors(dhl_repository)
    assert result == expected


def test_upsert_into_analytics_when_no_data(dhl_repository: DhlRepository) -> None:
    # arrange
    source_files: set[SourceFiles] = set()
    shipping_billing: set[ShippingBilling] = set()
    shipping_charges: set[ShippingCharges] = set()
    order_items: set[OrderItems] = set()

    # act
    dhl_repository.upsert_into_analytics()

    # assert
    assert_no_errors(dhl_repository)
    assert_source_files(dhl_repository, source_files)
    assert_shipping_billing(dhl_repository, shipping_billing)
    assert_shipping_charges(dhl_repository, shipping_charges)
    assert_order_items(dhl_repository, order_items)


def test_upsert_into_analytics_when_quoted_identifier(dhl_repository: DhlRepository, file_info: FileInfo) -> None:
    # arrange

    # insert quoted_identifier data into import schema
    shipping_billing_import = mock_quoted_identifier_shipping_billing(file_info)
    order_items_import = mock_quoted_identifier_order_items()
    dhl_repository.insert_into_import(shipping_billing_import, order_items_import)

    # expected data in analytics schema
    source_files: set[SourceFiles] = {
        SourceFiles(id=1, file_path=str(file_info.path), file_created=file_info.created, file_size=file_info.size)
    }
    shipping_billing: set[ShippingBilling] = {
        ShippingBilling(
            id=1,
            shipment_number="**********",
            source_files_id=1,
            package_type=OUTBOUND,
            line_type="S",
            billing_source="ES-TD",
            billing_account="*********",
            billing_country_code="ES",
            invoice_date=datetime.date(2024, 3, 11),
            invoice_number="xxxx",
            shipment_date=datetime.date(2024, 3, 1),
            shipment_reference_1="1001",
            shipment_reference_2="",
            shipment_reference_3="",
            weight_kg=Decimal("13.5000"),
            weight_flag="B",
            senders_name="Supplier A",
            senders_country="TH",
            receivers_country="ES",
            currency="EUR",
            amount_net=Decimal("90.4000"),
            amount_gross=Decimal("90.4000"),
        )
    }
    shipping_charges: set[ShippingCharges] = {
        ShippingCharges(
            fuel_surcharge_net=Decimal("21.5000"),
            fuel_surcharge_gross=Decimal("21.5000"),
            total_net=Decimal("21.5000"),
            total_gross=Decimal("21.5000"),
        )
    }
    order_items: set[OrderItems] = {OrderItems(shipping_billing_id=1, order_item_id=1001)}

    # act
    dhl_repository.upsert_into_analytics()

    # assert
    assert_no_errors(dhl_repository)
    assert_source_files(dhl_repository, source_files)
    assert_shipping_billing(dhl_repository, shipping_billing)
    assert_shipping_charges(dhl_repository, shipping_charges)
    assert_order_items(dhl_repository, order_items)


def test_upsert_into_analytics_when_multiple_charges(dhl_repository: DhlRepository, file_info: FileInfo) -> None:
    # arrange

    # insert multiple_charges data into import schema
    shipping_billing_import = mock_multiple_charges_shipping_billing(file_info)
    order_items_import = mock_multiple_charges_order_items()
    dhl_repository.insert_into_import(shipping_billing_import, order_items_import)

    # expected data in analytics schema
    source_files: set[SourceFiles] = {
        SourceFiles(id=1, file_path=str(file_info.path), file_created=file_info.created, file_size=file_info.size)
    }

    sb = ShippingBilling(
        id=1,
        shipment_number="**********",
        source_files_id=1,
        package_type=OUTBOUND,
        line_type="S",
        billing_source="ES-TD",
        billing_account="*********",
        billing_country_code="ES",
        invoice_date=datetime.date(2024, 3, 11),
        invoice_number="ES/*********/********",
        shipment_date=datetime.date(2024, 3, 1),
        shipment_reference_1="1001",
        shipment_reference_2="",
        shipment_reference_3=OUTBOUND,
        weight_kg=Decimal("5.0000"),
        weight_flag="B",
        senders_name="Supplier A",
        senders_country="TH",
        receivers_country="ES",
        currency="EUR",
        amount_net=Decimal("30.0000"),
        amount_gross=Decimal("36.0000"),
    )

    shipping_billing: set[ShippingBilling] = {
        sb,
        replace(
            sb,
            id=2,
            shipment_number="**********",
            shipment_reference_1="1002, 1003",
            amount_net=Decimal("50.0000"),
            amount_gross=Decimal("60.0000"),
        ),
        replace(
            sb,
            id=3,
            shipment_number="**********",
            shipment_reference_1="1004",
            weight_kg=Decimal("3.5000"),
            amount_net=Decimal("10.4000"),
            amount_gross=Decimal("12.4800"),
        ),
        replace(
            sb,
            id=4,
            shipment_number="3500000010",
            shipment_date=datetime.date(2024, 3, 2),
            invoice_date=datetime.date(2024, 3, 12),
            invoice_number="ES/*********/20240312",
            senders_country="DE",
            senders_name="Supplier B",
            shipment_reference_1="1005",
            weight_kg=Decimal("5.0000"),
            amount_net=Decimal("45.0000"),
            amount_gross=Decimal("54.0000"),
        ),
        replace(
            sb,
            id=5,
            shipment_number="3500000011",
            shipment_date=datetime.date(2024, 3, 2),
            invoice_date=datetime.date(2024, 3, 12),
            invoice_number="ES/*********/20240312",
            senders_country="DE",
            senders_name="Supplier C",
            shipment_reference_1="1006",
            weight_kg=Decimal("5.0000"),
            amount_net=Decimal("45.0000"),
            amount_gross=Decimal("54.0000"),
        ),
        replace(
            sb,
            id=6,
            shipment_number="**********",
            shipment_date=datetime.date(2024, 3, 3),
            invoice_date=datetime.date(2024, 3, 13),
            invoice_number="ES/*********/********",
            shipment_reference_1="1005",
            shipment_reference_3=INBOUND,
            billing_account="*********",
            package_type=INBOUND,
            senders_country="ES",
            senders_name="Supplier B",
            receivers_country="DE",
            weight_kg=Decimal("5.0000"),
            amount_net=Decimal("45.0000"),
            amount_gross=Decimal("54.0000"),
        ),
        replace(
            sb,
            id=7,
            shipment_number="**********",
            shipment_date=datetime.date(2024, 3, 3),
            invoice_date=datetime.date(2024, 3, 13),
            invoice_number="ES/*********/********",
            shipment_reference_1="1006",
            shipment_reference_3=INBOUND,
            billing_account="*********",
            package_type=INBOUND,
            senders_country="ES",
            senders_name="Supplier C",
            receivers_country="DE",
            weight_kg=Decimal("5.0000"),
            amount_net=Decimal("45.0000"),
            amount_gross=Decimal("54.0000"),
        ),
        replace(
            sb,
            id=8,
            shipment_number="**********",
            shipment_date=datetime.date(2024, 3, 4),
            invoice_date=datetime.date(2024, 3, 14),
            invoice_number="ES/*********/********",
            shipment_reference_1="2001",
            senders_country="UK",
            senders_name="Supplier D",
            receivers_country="ES",
            weight_kg=Decimal("3.0000"),
            amount_net=Decimal("75.0000"),
            amount_gross=Decimal("90.0000"),
        ),
    }
    sc = ShippingCharges()
    shipping_charges: set[ShippingCharges] = {
        sc,
        replace(sc, shipping_billing_id=2),
        replace(
            sc,
            shipping_billing_id=3,
            duty_tax_paid_net=Decimal("5.0000"),
            duty_tax_paid_gross=Decimal("6.0000"),
            total_net=Decimal("5.0000"),
            total_gross=Decimal("6.0000"),
        ),
        replace(sc, shipping_billing_id=4),
        replace(sc, shipping_billing_id=5),
        replace(sc, shipping_billing_id=6),
        replace(sc, shipping_billing_id=7),
        replace(
            sc,
            shipping_billing_id=8,
            remote_area_delivery_net=Decimal("0.1000"),
            remote_area_delivery_gross=Decimal("0.1200"),
            remote_area_pickup_net=Decimal("0.2000"),
            remote_area_pickup_gross=Decimal("0.2400"),
            fuel_surcharge_net=Decimal("5.0000"),
            fuel_surcharge_gross=Decimal("6.000"),
            duty_tax_paid_net=Decimal("0.4000"),
            duty_tax_paid_gross=Decimal("0.4800"),
            emergency_situation_net=Decimal("0.5000"),
            emergency_situation_gross=Decimal("0.6000"),
            export_declaration_net=Decimal("0.6000"),
            export_declaration_gross=Decimal("0.7200"),
            carbon_reduced_fe_net=Decimal("0.7000"),
            carbon_reduced_fe_gross=Decimal("0.8400"),
            oversize_piece_net=Decimal("0.8000"),
            oversize_piece_gross=Decimal("0.9600"),
            shipment_insurance_net=Decimal("0.3000"),
            shipment_insurance_gross=Decimal("0.3600"),
            total_net=Decimal("8.6000"),
            total_gross=Decimal("10.3200"),
        ),
    }
    oi = OrderItems(shipping_billing_id=1, order_item_id=1001)
    order_items: set[OrderItems] = {
        oi,
        replace(oi, shipping_billing_id=2, order_item_id=1002),
        replace(oi, shipping_billing_id=2, order_item_id=1003),
        replace(oi, shipping_billing_id=3, order_item_id=1004),
        replace(oi, shipping_billing_id=4, order_item_id=1005),
        replace(oi, shipping_billing_id=5, order_item_id=1006),
        replace(oi, shipping_billing_id=6, order_item_id=1005),
        replace(oi, shipping_billing_id=7, order_item_id=1006),
        replace(oi, shipping_billing_id=8, order_item_id=2001),
    }

    # act
    dhl_repository.upsert_into_analytics()

    # assert
    assert_no_errors(dhl_repository)
    assert_source_files(dhl_repository, source_files)
    assert_shipping_billing(dhl_repository, shipping_billing)
    assert_shipping_charges(dhl_repository, shipping_charges)
    assert_order_items(dhl_repository, order_items)


def test_select_shipping_config_when_active(dhl_repository: DhlRepository) -> None:
    # arrange
    expected = {
        ShippingConfig(),
        ShippingConfig(
            country="DE",
            support_email="<EMAIL>",
            outbound_account="*********",
            inbound_account="*********",
            decimal_sep=",",
        ),
        ShippingConfig(
            country="NL",
            support_email="<EMAIL>",
            outbound_account="*********",
            inbound_account="*********",
        ),
        ShippingConfig(
            country="IT",
            support_email="<EMAIL>",
            outbound_account="*********",
            inbound_account="*********",
        ),
    }

    # act
    actual = dhl_repository.select_shipping_config(active=True)

    # assert
    assert actual == expected


def test_select_shipping_config_when_inactive(dhl_repository: DhlRepository) -> None:
    # act
    result = dhl_repository.select_shipping_config(active=False)
    actual = {row.country for row in result}

    # assert
    assert actual == set()


def test_get_shipping_billing_errors_when_no_order_items(dhl_repository: DhlRepository, file_info: FileInfo) -> None:
    # arrange
    shipping_billing_import = mock_multiple_charges_shipping_billing(file_info)
    expected_shipment_numbers = set(shipping_billing_import[SB_COLUMNS.shipment_number].unique())

    dhl_repository.insert_into_import(shipping_billing_import, DataFrame())
    dhl_repository.upsert_into_analytics()

    # act
    errors = dhl_repository.get_shipping_billing_errors()
    actual_shipment_numbers = set(e.shipment_number for e in errors)

    # assert
    assert len(errors) == len(shipping_billing_import)
    assert actual_shipment_numbers == expected_shipment_numbers
