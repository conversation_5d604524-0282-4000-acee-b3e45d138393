from datetime import datetime
from pathlib import Path
from time import sleep
from typing import Iterator

import pytest
from pandas import BooleanDtype, DataFrame, Int32Dtype, StringDtype, read_csv
from sqlalchemy.exc import InterfaceError

from common.pandas_utils import DataFrameSchema
from common.sql_client import DatabaseConfig
from common.sql_model import SqlTable
from common.sql_repository import SqlRepository
from query_terminator.query_timeout_model import (
    TerminationStatus,
    TimeoutQueriesLog,
    TimeoutQueriesRow,
)
from query_terminator.query_timeout_repository import QueryTimeoutRepository

QUERY_TIMEOUT_CONFIG_SCHEMA = DataFrameSchema(
    datetime_columns=["created_at", "valid_from", "valid_to"],
    data_types={
        "id": Int32Dtype(),
        "created_by": StringDtype(),
        "description": StringDtype(),
        "terminate": BooleanDtype(),
        "timeout_sec": Int32Dtype(),
        "program_name": StringDtype(),
        "login_name": StringDtype(),
        "sql_text": StringDtype(),
    },
)


@pytest.fixture(scope="function")
def query_timeout_config(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "query_timeout_config.csv",
        # Date parsing
        parse_dates=QUERY_TIMEOUT_CONFIG_SCHEMA.datetime_columns,
        # Data type(s) mapping
        dtype=QUERY_TIMEOUT_CONFIG_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def query_timeout_repository(
    db_config: DatabaseConfig, query_timeout_config: DataFrame
) -> Iterator[QueryTimeoutRepository]:
    repository = QueryTimeoutRepository(db_config)
    repository.insert(df=query_timeout_config, table=repository.QUERY_TIMEOUT_CONFIG_TABLE, truncate=True)

    yield repository

    repository.truncate_table(table=repository.QUERY_TIMEOUT_CONFIG_TABLE)


def test_timeout_queries_when_no_running_queries(query_timeout_repository: QueryTimeoutRepository) -> None:
    # act
    result = query_timeout_repository.timeout_queries()

    # assert
    assert result == []


def test_timeout_queries_when_query_is_not_terminated(
    db_config: DatabaseConfig,
    query_timeout_config: DataFrame,
    query_timeout_repository: QueryTimeoutRepository,
    sql_repository: SqlRepository,
) -> None:
    """
    Long-running query is detected by single config=1, but not terminated.
    """
    # arrange
    table = query_timeout_repository.QUERY_TIMEOUT_CONFIG_TABLE
    with sql_repository.begin_transaction() as transaction:
        # Simulate query session with `sql_repository`
        result = sql_repository.select(table=table, transaction=transaction)
        assert len(result) == len(query_timeout_config)
        sleep(1)

        # Check queries running over 1 sec
        result = query_timeout_repository.timeout_queries()

        # Assert query was detected by single config and not terminated
        assert len(result) == 1
        assert_timeout_queries_result(
            result=result,
            db_config=db_config,
            sql_table=table,
            status=TerminationStatus.REPORTED,
            config_id=1,
            description="Queries running longer than 1 sec",
        )

        # Ensure the query is still running
        result = query_timeout_repository.timeout_queries()
        assert len(result) == 1


def test_timeout_queries_when_query_is_terminated(
    db_config: DatabaseConfig,
    query_timeout_repository: QueryTimeoutRepository,
    sql_repository: SqlRepository,
    sql_table: SqlTable,
) -> None:
    """
    Long-running query is detected by 2 configs, and terminated by config=2.
    """

    # arrange
    with pytest.raises(InterfaceError):
        # pg_terminate_backend() raises an InterfaceError for terminated transaction!
        with sql_repository.begin_transaction() as transaction:
            # Simulate query session with `sql_repository`
            sql_repository.select(table=sql_table, transaction=transaction)
            sleep(1)

            # Check queries running over 1 sec
            result = query_timeout_repository.timeout_queries()

            # Assert query was detected by 2 configurations and terminated
            assert len(result) == 2

            assert_timeout_queries_result(
                result=result,
                db_config=db_config,
                sql_table=sql_table,
                status=TerminationStatus.REPORTED,
                config_id=1,
                description="Queries running longer than 1 sec",
            )
            assert_timeout_queries_result(
                result=result,
                db_config=db_config,
                sql_table=sql_table,
                status=TerminationStatus.TERMINATED,
                config_id=2,
                description="public.user queries running longer than 1 sec",
            )

            # Ensure no query is running as it was terminated
            result = query_timeout_repository.timeout_queries()
            assert result == []


@pytest.mark.parametrize(
    "data, expected",
    [
        (None, 0),
        ([], 0),
        (
            [
                TimeoutQueriesLog(
                    start_time=datetime(2024, 12, 17, hour=12, minute=59, second=59),
                    duration="00:20:59",
                    terminated=False,
                    login_name="test",
                    program_name=None,
                    sql_text="select * from test",
                    sql_hash=1,
                    history_slug=None,
                    config_id=1,
                    host_ip=None,
                ),
                TimeoutQueriesLog(
                    start_time=datetime(2024, 12, 17, hour=13),
                    duration="00:31:00",
                    terminated=True,
                    login_name="platform",
                    program_name="looker",
                    sql_text=""""-- Looker Query Context '{"user_id":753,"history_slug":"6bfb7ff328db53938b64e45343306455","instance_slug":"030e3995d13f016f0512e2ef1e8677ad"}' WITH order_item_refunds_dt AS (select oi.id as order_item_id, min(orr.created_at) as min_refund_created, sum(oir.refunded) as total_refunded from order_item_refunds oir join order_items oi on oir.order_item_id = oi.id join order_refunds orr on oir.order_refund_id = orr.id where orr.status = 'succeeded' group by 1 ) SELECT lower(invoice_addresses."country") AS "invoice_addresses.country", csat_typeform_responses_dt."q5_ans" AS "csat_typeform_responses_dt.q5_ans", merchants."name" AS "merchants.name", (TO_CHAR(DATE_TRUNC('second', (csat_typeform_responses_dt."submitted_at" )::timestamptz AT TIME ZONE 'Europe/Vienna'), 'YYYY-MM-DD HH24:MI:SS')) AS "csat_typeform_responses_dt.submitted_at_time", CAST( tickets."topic" AS TEXT) AS "tickets.topic", """,  # noqa
                    sql_hash=1769218704084502753,
                    history_slug="6bfb7ff328db53938b64e45343306455",
                    config_id=2,
                    host_ip="**************",
                ),
            ],
            2,
        ),
    ],
)
def test_log_timeout_queries(
    query_timeout_repository: QueryTimeoutRepository, data: list[TimeoutQueriesLog], expected: int
) -> None:
    actual = query_timeout_repository.log_timeout_queries(data)
    assert actual == expected


def assert_timeout_queries_result(
    result: list[TimeoutQueriesRow],
    db_config: DatabaseConfig,
    sql_table: SqlTable,
    status: str,
    config_id: int,
    description: str,
) -> None:
    row = result[config_id - 1]
    sql_text = f"select * from {sql_table.full_name} limit 100;"

    assert row.program_name == db_config.application_name
    assert row.login_name == db_config.username
    assert row.sql_text == sql_text
    assert row.status == status
    assert row.config_id == config_id
    assert row.description == description
