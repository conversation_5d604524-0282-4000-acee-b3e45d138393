from pathlib import Path

import pytest
from _pytest.logging import LogCaptureFixture
from utils import TestFile

from common.cloud_storage_client import CloudStorageClient, CloudStorageClientError


@pytest.mark.parametrize(
    "bucket_test_folder",
    [
        Path("path/to/my/test/file/foo=123/bar=456/"),
        # overwrite the first one
        Path("path/to/my/test/file/foo=123/bar=456/"),
    ],
)
def test_upload_download_delete_file(
    bucket_test_folder: Path,
    cloud_storage_client_dir: str,
    cloud_storage_client: CloudStorageClient,
    test_file: TestFile,
    tmpdir: str,
) -> None:
    # Arrange
    bucket_folder = Path(cloud_storage_client_dir, bucket_test_folder)

    # Act
    uploaded_file_path = cloud_storage_client.upload_file(test_file.file_path, bucket_folder)

    # File exists on the bucket
    assert cloud_storage_client.file_exist(uploaded_file_path)

    downloaded_file_path = cloud_storage_client.download_file(uploaded_file_path, Path(tmpdir))

    # File exists on the local storage
    assert downloaded_file_path.exists()

    with open(downloaded_file_path) as f:
        actual_file_content = f.read()

    # Check the content
    assert actual_file_content == test_file.file_content

    # Delete the file on the bucket
    cloud_storage_client.delete_file(uploaded_file_path)

    # File does not exist on the bucket
    assert not cloud_storage_client.file_exist(uploaded_file_path)


@pytest.mark.parametrize(
    "bucket_test_folder",
    [
        "",
        "foo",
        "foo/bar/",
        "foo/bar/baz=100/",
    ],
)
def test_upload_directory_download_delete(
    bucket_test_folder: str,
    cloud_storage_client_dir: Path,
    cloud_storage_client: CloudStorageClient,
    test_file: TestFile,
    tmpdir: str,
) -> None:
    # Arrange
    bucket_folder = str(Path(cloud_storage_client_dir, bucket_test_folder))

    local_dir = Path(__file__).resolve().parent
    local_file_paths = [file_path for file_path in local_dir.glob("*") if file_path.is_file()]

    local_file_contents = {}
    for file_path in local_file_paths:
        with open(file_path) as f:
            file_content = f.read()
            local_file_contents.update({file_path.name: file_content})

    # Act
    uploaded_file_paths = cloud_storage_client.upload_directory(local_dir, bucket_folder, include_subdirectories=False)

    for file_path in uploaded_file_paths:
        # File exists on the bucket
        assert cloud_storage_client.file_exist(file_path)

        downloaded_file_path = cloud_storage_client.download_file(file_path, Path(tmpdir))

        # File exists on the local storage
        assert downloaded_file_path.exists()

        with open(downloaded_file_path) as f:
            actual_file_content = f.read()

        # Check the content
        assert actual_file_content == local_file_contents[downloaded_file_path.name]

        # Delete the file on the bucket
        cloud_storage_client.delete_file(file_path)

        # File does not exist on the bucket
        assert not cloud_storage_client.file_exist(file_path)


@pytest.mark.parametrize(
    "file_path",
    [Path("path/to/the/non/existing/file.txt")],
)
def test_upload_file_should_raise_error_when_file_does_not_exist(
    file_path: Path,
    cloud_storage_client: CloudStorageClient,
) -> None:
    # Act & Arrange
    with pytest.raises(
        CloudStorageClientError,
        match=f"The file '{file_path}' does not exist!",
    ):
        cloud_storage_client.upload_file(file_path, Path("foo"))


@pytest.mark.parametrize(
    "local_directory",
    [Path("path/to/the/non/existing/directory")],
)
def test_upload_directory_should_raise_error_when_directory_does_not_exist(
    local_directory: Path,
    cloud_storage_client: CloudStorageClient,
) -> None:
    # Act & Arrange
    with pytest.raises(
        CloudStorageClientError,
        match=f"The directory '{local_directory}' does not exist!",
    ):
        cloud_storage_client.upload_directory(local_directory, "foo")


def test_upload_directory_should_log_warning_when_directory_is_empty(
    cloud_storage_client: CloudStorageClient,
    tmpdir: str,
    caplog: LogCaptureFixture,
) -> None:
    # Arrange
    empty_dir = Path(tmpdir)
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)

    # Act
    cloud_storage_client.upload_directory(empty_dir, "foo")

    # Assert
    assert caplog.records[0].levelname == expected_logger_level
    assert f"The directory '{empty_dir}' is empty!" in caplog.messages


@pytest.mark.parametrize(
    "bucket_path",
    [
        Path("path/to/the/non/existing/file.txt"),
    ],
)
def test_download_file_should_raise_error_when_file_does_not_exist(
    bucket_path: Path, cloud_storage_client: CloudStorageClient, test_bucket_name: str, test_file: TestFile, tmpdir: str
) -> None:
    # Act
    assert not cloud_storage_client.file_exist(bucket_path)
    with pytest.raises(
        CloudStorageClientError,
        match=f"The file does not exist in the '{bucket_path}' path of the '{test_bucket_name}' bucket!",
    ):
        cloud_storage_client.download_file(bucket_path, Path(tmpdir))


@pytest.mark.parametrize(
    "bucket_test_directory",
    [
        "",
        "foo/",
        "foo/bar/baz/nested/",
    ],
)
def test_download_directory(
    bucket_test_directory: str,
    cloud_storage_client_dir: Path,
    cloud_storage_client: CloudStorageClient,
    tmpdir: str,
) -> None:
    # Arrange
    bucket_directory = str(Path(cloud_storage_client_dir, bucket_test_directory))

    local_dir = Path(__file__).resolve().parent
    local_file_paths = [file_path for file_path in local_dir.glob("*") if file_path.is_file()]

    local_file_contents = {}
    for file_path in local_file_paths:
        with open(file_path) as f:
            file_content = f.read()
            local_file_contents.update({file_path.name: file_content})

    # Upload multiple files to the bucket directory
    uploaded_file_paths = cloud_storage_client.upload_directory(
        local_dir, bucket_directory, include_subdirectories=False
    )

    # Act
    downloaded_file_paths = cloud_storage_client.download_directory(
        bucket_directory, Path(tmpdir), include_subdirectories=False
    )

    # Assert
    assert len(downloaded_file_paths) == len(uploaded_file_paths)

    # Check each downloaded file
    for downloaded_file_path in downloaded_file_paths:
        assert downloaded_file_path.exists()

        with open(downloaded_file_path) as f:
            actual_file_content = f.read()

        # Check the content matches the original file
        original_filename = downloaded_file_path.name
        assert original_filename in local_file_contents
        assert actual_file_content == local_file_contents[original_filename]


def test_download_directory_in_recursive_mode(cloud_storage_client: CloudStorageClient, tmpdir: str) -> None:
    # Arrange
    bucket_directory = "dir_with_subdirs"

    # Create a temporary directory structure with subdirectories
    source_dir = Path(tmpdir, "source")
    source_dir.mkdir(parents=True, exist_ok=True)

    # Create some files in the main directory
    main_file = source_dir / "main.txt"
    main_file.write_text("Main directory file")

    # Create a subdirectory with files
    subdir = source_dir / "subdir"
    subdir.mkdir(parents=True, exist_ok=True)
    subdir_file = subdir / "sub.txt"
    subdir_file.write_text("Subdirectory file")

    # Create a nested subdirectory with files
    nested_subdir = subdir / "nested"
    nested_subdir.mkdir(parents=True, exist_ok=True)
    nested_file = nested_subdir / "nested.txt"
    nested_file.write_text("Nested subdirectory file")

    # Upload the directory structure
    uploaded_file_paths = cloud_storage_client.upload_directory(
        source_dir, bucket_directory, include_subdirectories=True
    )

    # Act
    downloaded_file_paths = cloud_storage_client.download_directory(bucket_directory, Path(tmpdir), recursive=True)

    # Assert
    assert len(downloaded_file_paths) == len(uploaded_file_paths)

    # Check the main directory file
    main_download = Path(tmpdir, bucket_directory, "main.txt")
    assert main_download.exists()
    assert main_download.read_text() == "Main directory file"

    # Check a subdirectory file
    sub_download = Path(tmpdir, bucket_directory, "subdir", "sub.txt")
    assert sub_download.exists()
    assert sub_download.read_text() == "Subdirectory file"

    # Check a nested subdirectory file
    nested_download = Path(tmpdir, bucket_directory, "subdir", "nested", "nested.txt")
    assert nested_download.exists()
    assert nested_download.read_text() == "Nested subdirectory file"


def test_download_directory_in_non_recursive_mode(cloud_storage_client: CloudStorageClient, tmpdir: str) -> None:
    # Arrange
    bucket_directory = "dir_with_subdirs_non_recursive/"

    # Create a temporary directory structure with subdirectories
    source_dir = Path(tmpdir, "source")
    source_dir.mkdir(parents=True, exist_ok=True)

    # Create some files in the main directory
    main_file = source_dir / "main.txt"
    main_file.write_text("Main directory file")

    # Create a subdirectory with files
    subdir = source_dir / "subdir"
    subdir.mkdir(parents=True, exist_ok=True)
    subdir_file = subdir / "sub.txt"
    subdir_file.write_text("Subdirectory file")

    # Upload the directory structure
    uploaded_file_paths = cloud_storage_client.upload_directory(
        source_dir, bucket_directory, include_subdirectories=True
    )

    # Act
    downloaded_file_paths = cloud_storage_client.download_directory(bucket_directory, Path(tmpdir), recursive=False)

    # Assert
    assert len(uploaded_file_paths) == 2
    assert len(downloaded_file_paths) == 1

    # Check the main directory file - should exist
    main_download = Path(tmpdir, bucket_directory, "main.txt")
    assert main_download.exists()
    assert main_download.read_text() == "Main directory file"

    # Check a subdirectory file - should not exist
    sub_download = Path(tmpdir, bucket_directory, "subdir", "sub.txt")
    assert not sub_download.exists()


def test_download_directory_empty_directory(
    cloud_storage_client: CloudStorageClient, tmpdir: str, caplog: LogCaptureFixture
) -> None:
    # Arrange
    bucket_directory = "non_existent_directory/"

    download_dir = Path(tmpdir) / "download_empty_test"
    download_dir.mkdir(parents=True, exist_ok=True)
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)

    # Act
    downloaded_file_paths = cloud_storage_client.download_directory(bucket_directory, download_dir)

    # Assert
    assert downloaded_file_paths == []
    assert caplog.records[0].levelname == expected_logger_level
    assert (
        f"No files found in the '{bucket_directory}' directory of the '{cloud_storage_client.bucket_name}' bucket!"
        in caplog.messages
    )


@pytest.mark.parametrize(
    "bucket_path",
    [
        Path("path/to/the/non/existing/file.txt"),
    ],
)
def test_delete_file_should_raise_error_when_file_does_not_exist(
    bucket_path: Path, cloud_storage_client: CloudStorageClient, test_bucket_name: str, test_file: TestFile, tmpdir: str
) -> None:
    # Act
    assert not cloud_storage_client.file_exist(bucket_path)
    with pytest.raises(
        CloudStorageClientError,
        match=f"The file does not exist in the '{bucket_path}' path of the '{test_bucket_name}' bucket!",
    ):
        cloud_storage_client.delete_file(bucket_path)
