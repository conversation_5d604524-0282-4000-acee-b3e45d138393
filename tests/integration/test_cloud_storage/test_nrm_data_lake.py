from datetime import date
from pathlib import Path

import pytest
from pandas import DataFrame, read_parquet
from pandas._testing import assert_frame_equal

from common.cloud_storage_client import CloudStorageClient
from common.data_lake_repository import DataLakeRepository
from nrm.nrm_model import (
    NEURAL_FORECAST_SCHEMA,
    MarketingCountriesColumns,
    NeuralForecastColumns,
    RevenueCountriesColumns,
    TableName,
)


@pytest.fixture(scope="function")
def nrm_data_lake(nrm_cloud_storage_client: CloudStorageClient) -> DataLakeRepository:
    return DataLakeRepository(nrm_cloud_storage_client)


@pytest.mark.parametrize(
    "table_name, load_date, expected_test_path",
    [
        ("nrm", date(2024, 1, 2), Path("crystal_ball/nrm/year=2024/month=1/day=2/nrm_2024_01_02.parquet")),
        (
            "revenue_countries",
            date(2024, 12, 31),
            Path("crystal_ball/revenue_countries/year=2024/month=12/day=31/revenue_countries_2024_12_31.parquet"),
        ),
    ],
)
def test_upload_table_when_data(
    nrm_cloud_storage_dir: str,
    nrm_data_lake: DataLakeRepository,
    table_name: TableName,
    load_date: date,
    expected_test_path: Path,
    tmpdir: str,
) -> None:
    # arrange
    root_folder = str(Path(nrm_cloud_storage_dir, "crystal_ball"))
    expected_path = Path(nrm_cloud_storage_dir, expected_test_path)

    cs_client = nrm_data_lake._cs_client
    df = DataFrame(
        {
            NeuralForecastColumns.UNIQUE_ID: ["at", "de", "fr"],
            NeuralForecastColumns.DS: [load_date, load_date, load_date],
            NeuralForecastColumns.Y: [0.1, 1.2, 2.3],
            RevenueCountriesColumns.REVENUE: [1.1, 2.2, 3.3],
            RevenueCountriesColumns.N_ORDERS: [1, 2, 3],
            RevenueCountriesColumns.N_ORDER_ITEMS: [2, 4, 6],
            MarketingCountriesColumns.COST: [2.1, 3.2, 4.3],
            MarketingCountriesColumns.COST_ONLINE: [3.1, 4.2, 5.3],
        }
    ).astype(NEURAL_FORECAST_SCHEMA.dtypes)

    # act
    actual_path = nrm_data_lake.upload_table_partition(
        data_frame=df, table_name=table_name, load_date=load_date, root_folder=root_folder
    )

    # assert file exists in bucket in expected path
    assert cs_client.file_exist(actual_path)
    assert actual_path == expected_path

    # assert file can be read into DataFrame with the expected data types
    local_path = cs_client.download_file(actual_path, Path(tmpdir))
    actual_df = read_parquet(local_path, engine="pyarrow")
    assert_frame_equal(actual_df, df)


def test_upload_table_when_no_data(nrm_data_lake: DataLakeRepository) -> None:
    # arrange
    df = DataFrame()

    # act
    actual_path = nrm_data_lake.upload_table_partition(df, "nrm", date(2024, 1, 2))

    # assert
    assert actual_path is None
