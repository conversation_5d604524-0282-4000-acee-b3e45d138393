from pathlib import Path
from typing import Iterator

import pytest

from common.cloud_storage_client import CloudStorageClient, CloudStorageClientConfig

test_bucket = "analytics-pipelines-staging-tests"
current_dir = Path(__file__).resolve().parent
test_file = current_dir / "test.txt"


@pytest.fixture(scope="session")
def test_bucket_name() -> str:
    return test_bucket


@pytest.fixture(scope="session")
def cloud_storage_client_dir() -> str:
    return "cloud-storage-dir"


@pytest.fixture(scope="session")
def nrm_cloud_storage_dir() -> str:
    return "nrm-storage-dir"


@pytest.fixture(scope="session")
def test_bucket_config(test_bucket_name: str) -> CloudStorageClientConfig:
    return CloudStorageClientConfig(bucket_name=test_bucket_name)


@pytest.fixture(scope="function")
def cloud_storage_basic_client(test_bucket_config: CloudStorageClientConfig) -> CloudStorageClient:
    return CloudStorageClient(test_bucket_config)


@pytest.fixture(scope="function")
def cloud_storage_client(
    cloud_storage_basic_client: CloudStorageClient, cloud_storage_client_dir: str
) -> Iterator[CloudStorageClient]:
    yield cloud_storage_basic_client

    cloud_storage_basic_client.delete_files(cloud_storage_client_dir)


@pytest.fixture(scope="function")
def nrm_cloud_storage_client(
    cloud_storage_basic_client: CloudStorageClient, nrm_cloud_storage_dir: str
) -> Iterator[CloudStorageClient]:
    yield cloud_storage_basic_client

    cloud_storage_basic_client.delete_files(nrm_cloud_storage_dir)
