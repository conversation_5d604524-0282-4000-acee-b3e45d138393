import pytest
from bm_shared.api import AlgoliaClient, ApiPayloadParams
from bm_shared.bm_model import AlgoliaSecret
from bm_shared.oxy_http_client import AlgoliaHTTPError, OxyHTTPClient, OxyHTTPError


def test_algolia_iphone_request(
    algolia_client: AlgoliaClient, iphone_filter: str, language_country: str, api_secret: AlgoliaSecret
) -> None:
    # arrange
    payload = algolia_client.payload(ApiPayloadParams.IPHONE_PAYLOAD, iphone_filter, 0)

    # act
    api_response = algolia_client.get_hits(payload, language_country, api_secret)
    parsed = algolia_client.parse_response(api_response)
    items = len(parsed.hits)
    iphone_count = len([hit.title_model for hit in parsed.hits if hit.title_model.startswith("iPhone")])

    # assert
    assert items > 0
    assert iphone_count == items
    assert parsed.nb_hits > 0


def test_invalid_algolia_key(
    algolia_client: AlgoliaClient, iphone_filter: str, language_country: str, api_invalid_secret: AlgoliaSecret
) -> None:
    # arrange
    payload = algolia_client.payload(ApiPayloadParams.IPHONE_PAYLOAD, iphone_filter, 0)

    # act and assert
    with pytest.raises(AlgoliaHTTPError, match="Invalid API key"):
        algolia_client.get_hits(payload, language_country, api_invalid_secret)


def test_oxy_proxy(oxy_client: OxyHTTPClient) -> None:
    # act
    response = oxy_client.get_page("https://sandbox.oxylabs.io/products")

    # assert
    assert "Video Games to scrape" in response


def test_oxy_proxy_unauthorized(oxy_client_invalid_authorization: OxyHTTPClient) -> None:
    # act and assert
    with pytest.raises(OxyHTTPError, match="Unauthorized"):
        oxy_client_invalid_authorization.get_page("https://sandbox.oxylabs.io/products")
