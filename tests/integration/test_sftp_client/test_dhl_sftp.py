from pathlib import Path

from _pytest.logging import LogCaptureFixture
from utils import TestFile

from common.sftp_client import SftpClient
from tests.dhl.mocks import (
    INVALID_CONTENT_CSV,
    MULTIPLE_CHARGES_CSV,
    QUOTED_IDENTIFIER_CSV,
)


def test_connect(dhl_sftp_client: SftpClient) -> None:
    """Assert connection to DHL SFTP server is working"""
    dhl_sftp_client.connect()


def test_download_files_when_no_files(dhl_sftp_client: SftpClient, tmpdir: str) -> None:
    # arrange
    local_path = Path(tmpdir)

    # act
    result = dhl_sftp_client.download_files(local_path, None, None)

    # assert
    assert not result


def test_download_files_when_remote_files(dhl_sftp_client: SftpClient, tmpdir: str) -> None:
    # arrange
    local_path = Path(tmpdir)
    valid_csv_files: set[Path] = {INVALID_CONTENT_CSV, MULTIPLE_CHARGES_CSV, QUOTED_IDENTIFIER_CSV}
    remote_folder = "in/test/es"

    expected_file_paths: set[Path] = set([])
    expected_file_names = set([])
    for csv_file in valid_csv_files:
        remote_path = dhl_sftp_client.copy_file(csv_file, remote_folder)
        expected_file_paths.add(Path(remote_path))
        expected_file_names.add(csv_file.name)

    # act
    result = dhl_sftp_client.download_files(local_path, remote_folder, ".csv")
    downloaded_paths = set(result.keys())

    # assert returned result is the same as content of the local path
    actual_paths = {str(item) for item in local_path.iterdir() if item.is_file()}
    assert actual_paths == downloaded_paths

    # assert expected file names & paths
    actual_file_names = {str(item.name) for item in local_path.iterdir() if item.is_file()}
    result_file_paths = {f.path for f in result.values()}

    assert actual_file_names == expected_file_names
    assert result_file_paths == expected_file_paths


def test_copy_and_remove_files(test_file: TestFile, dhl_sftp_client: SftpClient, tmpdir: str) -> None:
    local_path = Path(tmpdir)

    # assert remote folder is empty
    remote_folder = assert_remote_folder_is_empty(dhl_sftp_client, local_path)
    expected_remote_path = f"{remote_folder}/{test_file.file_path.name}"

    # copy test file into remote folder
    actual_remote_path = dhl_sftp_client.copy_file(test_file.file_path, remote_folder)
    assert actual_remote_path == expected_remote_path

    # remove test file at remote location
    dhl_sftp_client.remove_files([actual_remote_path])
    assert_remote_folder_is_empty(dhl_sftp_client, local_path)


def test_remove_files_should_warn_when_file_does_not_exist(
    dhl_sftp_client: SftpClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    file_path = "non/existing/file.txt"

    # act
    dhl_sftp_client.remove_files([file_path])

    # assert
    assert caplog.records[0].levelname == expected_logger_level
    assert f"'{file_path}' does not exists!" in caplog.messages


def assert_remote_folder_is_empty(dhl_sftp_client: SftpClient, local_path: Path) -> str:
    remote_folder = "out/refurbed"
    downloaded_paths = dhl_sftp_client.download_files(local_path, remote_folder)
    assert len(downloaded_paths) == 0

    return remote_folder
