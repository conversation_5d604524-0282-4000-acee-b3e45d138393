from datetime import UTC, datetime
from pathlib import Path

import pytest

from common.sftp_client import SftpClient, SshConfig
from common.typings import FileInfo


def test_connect(demo_sftp_client: SftpClient) -> None:
    """Assert connection to demo SFTP server is working"""
    demo_sftp_client.connect()


def test_sftp_client_init(demo_ssh_secret: str, demo_sftp_client: SftpClient) -> None:
    ssh_config = SshConfig.from_json(demo_ssh_secret)
    assert demo_sftp_client._config == ssh_config


@pytest.mark.parametrize(
    "remote_folder, file_suffix, expected_files",
    [
        # root folder with no files
        (None, None, set()),
        # download folder with single txt file
        (
            "download",
            ".txt",
            {
                FileInfo(
                    path=Path("download/version.txt"),
                    # local date/time: 2024-03-28 01:54:53
                    created=datetime(2024, 3, 28, 0, 54, 53, tzinfo=UTC),
                    size=109337,
                )
            },
        ),
    ],
)
def test_download_files(
    remote_folder: str, file_suffix: str, expected_files: set[FileInfo], demo_sftp_client: SftpClient, tmpdir: str
) -> None:
    local_path = Path(tmpdir)
    result = demo_sftp_client.download_files(local_path, remote_folder, file_suffix)
    downloaded_paths = set(result.keys())

    # assert returned result is the same as content of the local path
    actual_paths = {str(item) for item in local_path.iterdir() if item.is_file()}
    assert downloaded_paths == actual_paths

    # assert expected file info
    actual_files = set(result.values())
    assert actual_files == expected_files
