from pipedrive_api_client import PipedriveApiClient


def test_get_users(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_users = ["<PERSON><PERSON>", "Legal Team"]

    # act
    all_users = pipedrive_api_client.get_users()

    # assert
    assert len(all_users) > 30  # We already have more than 30 users of Pipedrive

    for existing_real_user in existing_real_users:
        actual = [user for user in all_users if user.name == existing_real_user]
        assert len(actual) > 0
        assert actual[0].name == existing_real_user


def test_get_organizations(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_orgs = ["4 Gadgets LTD", "ReMarkable", "TECH PLUS"]

    # act
    all_orgs = pipedrive_api_client.get_organisations()

    # assert
    assert len(all_orgs) > 3000  # We already have more than 3k organizations in Pipedrive

    for existing_real_org in existing_real_orgs:
        actual = [org for org in all_orgs if org.name == existing_real_org]
        assert len(actual) > 0
        assert actual[0].name == existing_real_org


def test_get_organization_fields(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_org_fields = ["Shipping from", "Owner", "VAT number"]

    # act
    all_org_fields = pipedrive_api_client.get_organisation_fields()

    # assert
    assert len(all_org_fields) > 50  # We already have more than 500 organizations in Pipedrive

    for existing_real_org in existing_real_org_fields:
        actual = [org for org in all_org_fields if org.name == existing_real_org]
        assert len(actual) > 0
        assert actual[0].name == existing_real_org


def test_get_persons(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_orgs = ["4 Gadgets LTD", "ReMarkable", "TECH PLUS"]

    # act
    all_persons = pipedrive_api_client.get_persons()

    # assert
    assert len(all_persons) > 3000  # We already have more than 3k organizations' persons in Pipedrive

    for existing_real_org in existing_real_orgs:
        actual = [person for person in all_persons if person.org_name == existing_real_org]
        assert len(actual) > 0
        assert actual[0].org_name == existing_real_org


def test_get_person_fields(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_person_fields = ["Won deals", "Lost deals", "Closed deals"]

    # act
    person_fields = pipedrive_api_client.get_person_fields()

    # assert
    assert len(person_fields) > 20  # We already have more than 20 persons' fields in Pipedrive

    for existing_real_org in existing_real_person_fields:
        actual = [person_field for person_field in person_fields if person_field.name == existing_real_org]
        assert len(actual) > 0
        assert actual[0].name == existing_real_org


def test_get_deals(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_deals = ["Giocard srl", "ECOPLACE", "DIGITAL LIFE"]

    # act
    all_deals = pipedrive_api_client.get_deals()

    # assert
    assert len(all_deals) > 4000  # We already have more than 4k deals in Pipedrive

    for existing_real_deal in existing_real_deals:
        actual = [deal for deal in all_deals if deal.title == existing_real_deal]
        assert len(actual) > 0
        assert actual[0].title == existing_real_deal


def test_get_deal_fields(pipedrive_api_client: PipedriveApiClient) -> None:
    # arrange
    existing_real_deal_fields = ["Pipeline", "Won time", "Email messages count"]

    # act
    deal_fields = pipedrive_api_client.get_deal_fields()

    # assert
    assert len(deal_fields) > 40  # We already have more than 40 deals' fields in Pipedrive

    for existing_real_deal_field in existing_real_deal_fields:
        actual = [deal_field for deal_field in deal_fields if deal_field.name == existing_real_deal_field]
        assert len(actual) > 0
        assert actual[0].name == existing_real_deal_field
