from typing import Iterator

import pytest
from integration.test_big_query.repository import UserRepository

from common.bq_client import BigQueryClient, BigQueryConfig
from google_analytics.repository import GAPurchasesAndClicksRepository
from platform_export.etl.analytics_bq_repository import AnalyticsBQRepository
from ranker.ranker_repository import RankerBQRepository


@pytest.fixture(scope="session")
def bq_config() -> BigQueryConfig:
    return BigQueryConfig()


@pytest.fixture(scope="session")
def bq_client(bq_config: BigQueryConfig) -> BigQueryClient:
    return BigQueryClient(bq_config)


@pytest.fixture(scope="session")
def user_repository(bq_client: BigQueryClient) -> Iterator[UserRepository]:
    repository = UserRepository(bq_client)
    repository.create_dataset()
    repository.drop_table()

    yield repository

    repository.drop_table()


@pytest.fixture(scope="session")
def pac_repository(bq_client: BigQueryClient) -> GAPurchasesAndClicksRepository:
    return GAPurchasesAndClicksRepository(bq_client)


@pytest.fixture(scope="session")
def analytics_repository(bq_client: BigQueryClient) -> AnalyticsBQRepository:
    return AnalyticsBQRepository(bq_client)


@pytest.fixture(scope="session")
def ranker_repository(bq_client: BigQueryClient) -> RankerBQRepository:
    return RankerBQRepository(bq_client)
