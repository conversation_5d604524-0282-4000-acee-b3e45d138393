from datetime import date

import pytest

from nrm.nrm_funnel_repository import NrmFunnelRepository
from nrm.nrm_model import MARKETING_COUNTRIES_SCHEMA, MarketingCountriesColumns


@pytest.fixture(scope="session")
def repository() -> NrmFunnelRepository:
    return NrmFunnelRepository()


def test_select_marketing_countries_when_no_data(repository: NrmFunnelRepository) -> None:
    # arrange: select export date
    load_date = date(2000, 1, 1)

    # act
    result = repository.select_marketing_countries(load_date)

    # assert
    assert result.empty is True
    assert set(result.columns) == set(MARKETING_COUNTRIES_SCHEMA.columns)


def test_select_marketing_countries_when_data_found(repository: NrmFunnelRepository) -> None:
    # arrange: select export date
    load_date = date(2024, 8, 1)

    # act
    result = repository.select_marketing_countries(load_date)
    date_min = result[MarketingCountriesColumns.DATE].min()
    date_max = result[MarketingCountriesColumns.DATE].max()

    # assert
    assert len(result) > 1
    assert date_min == date_max
    assert date_min == load_date
