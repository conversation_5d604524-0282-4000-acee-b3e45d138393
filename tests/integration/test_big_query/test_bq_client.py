from integration.test_big_query.repository import User, UserRepository


def test_bq_client_with_crud(user_repository: UserRepository) -> None:
    expected_users = {User(), User(id=2, username="user2")}

    # CREATE TABLE
    result = user_repository.create_table()
    assert result.total_rows == 0
    assert result.rows == []

    # INSERT INTO TABLE
    for user in expected_users:
        result = user_repository.insert(user)
        assert result.total_rows == 1
        assert result.rows == []

    # SELECT FROM TABLE (1)
    result = user_repository.select()
    actual_rows = {User(*r) for r in result.rows}

    assert actual_rows == expected_users
    assert result.total_rows == len(expected_users)
    assert result.total_bytes_billed > 0
    assert result.total_bytes_processed > 0

    # TRUNCATE TABLE
    result = user_repository.truncate_table()
    assert result.total_rows == 2
    assert result.rows == []

    # SELECT FROM TABLE (2)
    result = user_repository.select()
    assert result.total_rows == 0
    assert result.rows == []
