from datetime import datetime

from bm_shared.bm_schema import ALGOLIA_PRODUCTS_SCHEMA
from compaction_model import AlgoliaProduct, AlgoliaProductRange
from google.cloud.storage import Bucket
from integration.test_utils import upload_test_file
from pandas import DataFrame
from pandas._testing import assert_frame_equal

from backmarket.product_compaction.compaction_bq_repo import CompactionBqRepository


def test_refresh_product_ranges(
    compaction_repository: CompactionBqRepository,
    transformed_data_lake_bucket: Bucket,
    test_compaction_date: datetime,
    test_compaction_path: str,
    algolia_test_products: list[AlgoliaProduct],
    algolia_test_product_ranges: list[AlgoliaProductRange],
) -> None:
    # arrange
    products_df = DataFrame(algolia_test_products).astype(ALGOLIA_PRODUCTS_SCHEMA.dtypes)
    upload_test_file(transformed_data_lake_bucket, f"{test_compaction_path}/test_data.parquet", products_df)
    expected = DataFrame(sorted(algolia_test_product_ranges))  # noqa

    # act
    compaction_repository.refresh_product_ranges(test_compaction_date.date())
    results = compaction_repository.select_product_ranges(test_compaction_date.date())
    actual = DataFrame(sorted(results))  # noqa

    # assert
    assert_frame_equal(actual, expected)

    # check for anomalies
    assert compaction_repository.check_product_ranges(test_compaction_date.date())
