from datetime import datetime

from api_worker_etl import BMApiWorker
from bm_shared.api import AlgoliaParsedResponse
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from compaction_model import CompactionConfig
from integration.test_big_query.test_backmarket.api_worker_repository import (
    BackmarketBqRepository,
)
from pandas.testing import assert_frame_equal


def test_save_and_read_transformed_data(
    bm_bq_repository: BackmarketBqRepository,
    bm_parsed_response: AlgoliaParsedResponse,
    bm_run_id: datetime,
    bm_compaction_config: CompactionConfig,
) -> None:
    # arrange
    data_lake_repo = BMDataLakeRepository(bm_compaction_config.transformed_bucket)

    # act
    transformed_df = BMApiWorker.transform(bm_parsed_response.hits, bm_run_id, country_code="de")
    data_lake_repo.write_prefixed_data_frame(
        transformed_df, "test.parquet", bm_compaction_config.compaction_base_path, bm_run_id
    )
    read_from_bq = bm_bq_repository.select_products(bm_run_id)

    # assert
    assert_frame_equal(read_from_bq.reset_index(drop=True), transformed_df.reset_index(drop=True), check_dtype=False)
