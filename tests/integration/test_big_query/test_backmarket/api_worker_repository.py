import logging
from datetime import datetime

from pandas import DataFrame

from backmarket.bm_shared.bm_schema import ALGOLIA_PRODUCTS_SCHEMA
from common.bq_repository import BigQueryRepository

logger = logging.getLogger(__name__)


class BackmarketBqRepository(BigQueryRepository):
    """
    BackMarket repository, used for integration tests only
    """

    DATASET: str = "analytics_transformed"
    TABLE: str = "backmarket_products"

    def select_products(self, day: datetime) -> DataFrame:
        """
        Select transformed results from BQ transformed table
        :param day: day to select
        :return: DataFrame with transformed data
        """

        query = f"""select distinct * from {self.DATASET}.{self.TABLE}
                where year={day.year} and month={day.month} and day={day.day}"""
        result = self._client.run_sql(query)

        logger.info(f"backmarket_product count for {day.year}-{day.month}-{day.date}: {result.log_totals}")

        return result.job_result.to_dataframe(dtypes=ALGOLIA_PRODUCTS_SCHEMA.dtypes)
