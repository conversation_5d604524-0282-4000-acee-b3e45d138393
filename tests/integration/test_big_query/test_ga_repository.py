from datetime import date, datetime

import pytest

from common.bq_client import Query<PERSON><PERSON><PERSON>
from google_analytics.repository import (
    GAPurchasesAndClicksRepository,
    GARepositoryError,
)


@pytest.fixture(scope="session")
def partition_with_data() -> date:
    return date(2025, 5, 5)


@pytest.fixture(scope="session")
def empty_partition() -> date:
    return date(2024, 6, 30)


def test_get_target_partitions(partition_with_data: date, pac_repository: GAPurchasesAndClicksRepository) -> None:
    # arrange
    expected_partition = partition_with_data
    expected_total_rows = 1000

    # act
    result = pac_repository.get_target_partitions()

    # assert
    assert len(result) > 1
    assert expected_partition in result

    actual = result[expected_partition]
    assert actual.dataset == pac_repository.DATASET
    assert actual.table == pac_repository.TABLE
    assert actual.partition == expected_partition
    # Cannot provide an exact datetime value due to migration scripts being constantly re-run in staging
    assert isinstance(actual.last_modified, datetime) and actual.last_modified.date() > expected_partition
    assert actual.total_rows == expected_total_rows


def test_load_partition_when_empty_partition(
    empty_partition: date,
    pac_repository: GAPurchasesAndClicksRepository,
) -> None:
    # act
    result = pac_repository.load_partition(empty_partition)

    # assert
    assert result is not None
    assert result.total_rows == 0
    assert result.total_bytes_billed == 0
    assert result.total_bytes_processed == 0


def test_load_partition_when_data_in_partition(
    partition_with_data: date,
    pac_repository: GAPurchasesAndClicksRepository,
) -> None:
    # act
    result = pac_repository.load_partition(partition_with_data)

    # assert
    assert result is not None
    assert result.total_rows == 0
    assert result.total_bytes_billed > 0
    assert result.total_bytes_processed > 0


def test_get_source_partition_when_empty_partition(
    empty_partition: date, pac_repository: GAPurchasesAndClicksRepository
) -> None:
    # act
    result = pac_repository.get_source_partition(partition=empty_partition)

    # assert
    assert result is None


def test_get_source_partition_when_data_in_partition(
    partition_with_data: date, pac_repository: GAPurchasesAndClicksRepository
) -> None:
    # arrange
    expected_dataset = "analytics_304326635"
    expected_table = "events_fresh"
    expected_total_rows = 1000

    # act
    result = pac_repository.get_source_partition(partition=partition_with_data)

    # assert
    assert result is not None
    assert result.dataset == expected_dataset
    assert result.table == expected_table
    assert result.partition == partition_with_data

    # Cannot provide an exact datetime value due to migration scripts being constantly re-run in staging
    assert isinstance(result.last_modified, datetime) and result.last_modified.date() > partition_with_data
    assert result.total_rows == expected_total_rows


def test_get_source_partition_raises_error(pac_repository: GAPurchasesAndClicksRepository) -> None:
    # arrange
    table_partition = date(2000, 1, 1)

    # fake data anomaly - more than one row for a given partition
    rows = [0, 1]
    pac_repository.select = lambda table, where: QueryResult(
        job_result=rows, total_rows=2, total_bytes_billed=0, total_bytes_processed=0  # noqa
    )

    # act & assert
    with pytest.raises(GARepositoryError):
        _ = pac_repository.get_source_partition(partition=table_partition)
