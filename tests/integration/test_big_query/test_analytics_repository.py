import pytest
from _pytest.logging import LogCaptureFixture
from google.api_core.exceptions import BadRequest

from common.sql_model import SqlTable
from platform_export.etl.analytics_bq_repository import AnalyticsBQRepository
from platform_export.export_shared.export_model import ExportStatus
from platform_export.export_shared.export_target import PRODUCTS_SOURCE, PRODUCTS_TARGET


def test_get_export_status_when_invalid_table(analytics_repository: AnalyticsBQRepository) -> None:
    # act & assert
    with pytest.raises(BadRequest):
        _ = analytics_repository.get_export_status("invalid_table")


def test_get_export_status_when_valid_table(analytics_repository: AnalyticsBQRepository) -> None:
    # arrange
    expected = ExportStatus()

    # act
    actual = analytics_repository.get_export_status(PRODUCTS_TARGET)

    # assert
    assert actual.last_id >= expected.last_id
    assert actual.last_modified >= expected.last_modified


def test_extract_when_invalid_source(analytics_repository: AnalyticsBQRepository) -> None:
    # arrange
    source = SqlTable(schema_name="invalid_schema", table_name="invalid_table")
    target = PRODUCTS_TARGET

    # act & assert
    with pytest.raises(BadRequest, match="Query error"):
        _ = analytics_repository.extract_from_source(source, target)


@pytest.mark.parametrize("condition", ["", "id > 14549 or updated_at >= '2025-07-10T13:52:35.886414+00:00'"])
def test_extract_when_valid_source(analytics_repository: AnalyticsBQRepository, condition: str) -> None:
    # arrange
    source = PRODUCTS_SOURCE
    target = PRODUCTS_TARGET

    # act
    result = analytics_repository.extract_from_source(source, target, condition)

    # assert
    assert result > 0


def test_load_from_platform(analytics_repository: AnalyticsBQRepository) -> None:
    result = analytics_repository.load_from_platform(PRODUCTS_TARGET)
    assert result > 0


def test_check_table_when_ok(analytics_repository: AnalyticsBQRepository) -> None:
    result = analytics_repository.check_table(table_name=PRODUCTS_TARGET)
    assert result is True


def test_check_table_when_error(analytics_repository: AnalyticsBQRepository, caplog: LogCaptureFixture) -> None:
    # arrange
    caplog.set_level("WARNING")
    table_name = "error"
    expected_warning = f"Quality check failed for {table_name=}"

    # act
    result = analytics_repository.check_table(table_name=table_name)

    # assert only 1 warning was produced
    assert not result
    assert len(caplog.messages) == 1
    assert expected_warning in caplog.messages[0]
