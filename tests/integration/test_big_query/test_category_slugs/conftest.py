from datetime import date
from typing import Iterator

import pytest
from integration.test_utils import delete_test_files
from pandas import DataFrame

from category_slug_mapper.cs_bq_repository import CategorySlugsBQRepository
from category_slug_mapper.cs_model import (
    CATEGORY_SLUGS_MAPPING_SCHEMA,
    COUNTRIES_SCHEMA,
    CategorySlugsColumns,
    CategorySlugsConfig,
    CountriesColumns,
)
from common.data_lake_repository import DataLakeRepository
from common.pandas_utils import DataFrameWithSchema


@pytest.fixture(scope="session")
def config() -> CategorySlugsConfig:
    return CategorySlugsConfig(load_date=date(2099, 1, 1))


@pytest.fixture(scope="session")
def raw_countries() -> DataFrameWithSchema:
    """
    select * from analytics_raw.ranking_countries
    where
        year = 2025 and month = 5 and day = 2
    and code in ("de", "at", "cz");
    """
    dataframe = DataFrame(
        {
            CountriesColumns.CODE: ["at", "de", "cz"],
            CountriesColumns.NAME: ["Austria", "Germany", "Czechia"],
            CountriesColumns.MAIN_LANGUAGE: ["de", "de", "cs"],
            CountriesColumns.CURRENCY: ["EUR", "EUR", "CZK"],
            CountriesColumns.IS_SITE: [True, True, True],
        }
    ).astype(COUNTRIES_SCHEMA.dtypes)

    return DataFrameWithSchema(dataframe, COUNTRIES_SCHEMA)


@pytest.fixture(scope="session")
def raw_category_slugs_mapping() -> DataFrameWithSchema:
    """
    select *
    from analytics_raw.category_slugs_mapping
    where
        year = 2025 and month = 5 and day = 2
    and category_id in (1,2)
    and (language is null or language in ('', 'de', 'cs', 'en'))
    order by category_id, language;
    """
    c1 = date(2017, 10, 30)
    u1 = date(2024, 11, 26)

    c2 = date(2017, 10, 30)
    u2 = date(2024, 11, 26)

    dataframe = DataFrame(
        {
            CategorySlugsColumns.CREATED_AT: [c1, c1, c1, c2, c2, c2],
            CategorySlugsColumns.UPDATED_AT: [u1, u1, u1, u2, u2, u2],
            CategorySlugsColumns.CATEGORY_ID: [1, 1, 1, 2, 2, 2],
            CategorySlugsColumns.LANGUAGE: ["", "cs", "en", "", "cs", "en"],
            CategorySlugsColumns.CATEGORY_SLUG: [
                "smartphones",
                "smartphony",
                "smartphones",
                "laptops",
                "notebooky",
                "laptops",
            ],
        }
    ).astype(CATEGORY_SLUGS_MAPPING_SCHEMA.dtypes)

    return DataFrameWithSchema(dataframe, CATEGORY_SLUGS_MAPPING_SCHEMA)


@pytest.fixture(scope="function")
def repository(
    config: CategorySlugsConfig,
    raw_countries: DataFrameWithSchema,
    raw_category_slugs_mapping: DataFrameWithSchema,
    raw_data_lake_repository: DataLakeRepository,
) -> Iterator[CategorySlugsBQRepository]:
    repository = CategorySlugsBQRepository()
    _upload_raw_data(config, raw_countries, raw_category_slugs_mapping, raw_data_lake_repository)
    repository.truncate_table()

    yield repository

    repository.truncate_table()
    _clean_raw_data(config, raw_data_lake_repository)


def _upload_raw_data(
    config: CategorySlugsConfig,
    raw_countries: DataFrameWithSchema,
    raw_category_slugs_mapping: DataFrameWithSchema,
    raw_data_lake_repository: DataLakeRepository,
) -> None:
    raw_data_lake_repository.upload_table_partition(
        data_frame=raw_countries.dataframe_with_schema,
        table_name="countries",
        load_date=config.load_date,
        root_folder=config.root_folder,
    )
    raw_data_lake_repository.upload_table_partition(
        data_frame=raw_category_slugs_mapping.dataframe_with_schema,
        table_name="category_slugs_mapping",
        load_date=config.load_date,
        root_folder=config.root_folder,
    )


def _clean_raw_data(config: CategorySlugsConfig, raw_data_lake_repository: DataLakeRepository) -> None:
    partition = f"year={config.load_date.year}/month={config.load_date.month}/day={config.load_date.day}"
    prefixes = [
        f"{config.root_folder}/countries/{partition}",
        f"{config.root_folder}/category_slugs_mapping/{partition}",
    ]

    for prefix in prefixes:
        delete_test_files(raw_data_lake_repository.bucket, prefix)
