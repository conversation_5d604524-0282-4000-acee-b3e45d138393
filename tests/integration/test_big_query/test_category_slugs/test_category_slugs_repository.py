from datetime import datetime

from category_slug_mapper.cs_bq_repository import CategorySlugsBQRepository
from category_slug_mapper.cs_model import CategorySlugsConfig
from common.pandas_utils import DataFrameWithSchema


def test_load_from_raw_and_check_table(
    repository: CategorySlugsBQRepository, config: CategorySlugsConfig, raw_category_slugs_mapping: DataFrameWithSchema
) -> None:
    load_date = config.load_date

    # 1. Test INSERT
    # `load_category_slugs_mapping` should insert all rows from RAW
    repository.load_from_raw(load_date)
    result = repository._client.run_sql(f"select count(*) as row_count from {repository.TABLE_NAME}")

    assert result.total_rows == 1
    assert result.rows[0].row_count == len(raw_category_slugs_mapping.dataframe)

    # 2. Test DELETE
    # Insert a new row with non-existing category_id = 999
    sql = f"insert into {repository.TABLE_NAME}"
    sql += " values (999, 'market', 'language', 'category_slug', current_timestamp, current_timestamp, null);"
    result = repository._client.run_sql(sql)

    assert result.total_rows == 1

    # `load_category_slugs_mapping` should mark it as deleted as it's not in RAW source data
    repository.load_from_raw(load_date)
    sql = f"select * from {repository.TABLE_NAME} where deleted_at is not null;"
    result = repository._client.run_sql(sql)

    assert result.total_rows == 1
    row = result.rows[0]
    assert row.category_id == 999
    assert row.market == "market"
    assert row.language == "language"
    assert row.category_slug == "category_slug"
    assert isinstance(row.created_at, datetime)
    assert isinstance(row.updated_at, datetime)
    assert isinstance(row.deleted_at, datetime)
    assert row.deleted_at.date() == load_date

    # 3. Test UPDATE
    # select category to be updated and list languages
    where = "category_id=1"
    result = repository.select(columns=["language"], where=where)
    total_rows = result.total_rows
    original_languages = {row.language for row in result.rows}

    # update language to different value using updated_at before current updated_at
    updated_language = "test_language"
    assert updated_language not in original_languages

    sql = (
        f"update {repository.TABLE_NAME} "
        f"set language = '{updated_language}', updated_at = cast('2017-01-01' as timestamp) where {where};"
    )
    repository._client.run_sql(sql)

    assert result.total_rows == total_rows
    result = repository.select(columns=["language"], where=where)

    assert result.total_rows == total_rows
    assert {updated_language} == {row.language for row in result.rows}

    # `load_category_slugs_mapping` should overwrite it with RAW source languages
    # as it has updated_at > 2017-01-01
    repository.load_from_raw(load_date)
    result = repository.select(columns=["language"], where=where)

    assert result.total_rows == total_rows
    assert original_languages == {row.language for row in result.rows}

    # 4. Test for data anomalies
    qa_check = repository.check_table(load_date)
    assert qa_check
