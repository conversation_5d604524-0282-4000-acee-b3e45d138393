from datetime import date
from typing import Iterator

import pytest
from google.cloud.storage import Bucket
from integration.test_utils import delete_test_files, upload_test_file
from pandas import Data<PERSON>rame
from tcm_bq_repo import AdtribaTcmBqRepository
from tcm_model import ADTRIBA_TCM_DATA_LAKE_PARTITION

INT_TEST_BASE_PATH = f"{ADTRIBA_TCM_DATA_LAKE_PARTITION.path}/run_id=int-tests"


@pytest.fixture(scope="session")
def repository(raw_data_lake_bucket: Bucket) -> Iterator[AdtribaTcmBqRepository]:
    yield AdtribaTcmBqRepository()

    delete_test_files(raw_data_lake_bucket, INT_TEST_BASE_PATH)


def test_get_last_export_date(repository: AdtribaTcmBqRepository, raw_data_lake_bucket: Bucket) -> None:
    # arrange
    expected = date(2099, 12, 31)

    given_path = f"{INT_TEST_BASE_PATH}/date={expected.isoformat()}/int-tests.parquet"
    upload_test_file(raw_data_lake_bucket, given_path, DataFrame({"foo": ["bar"]}))

    # act
    actual = repository.get_last_export_date()

    # assert
    assert actual == expected
