import pytest
from google.cloud.storage import Bucket
from pandas import <PERSON><PERSON>rame
from pipedrive_bq_repo import PipedriveBqRepository
from pipedrive_model import PipedriveSchema, PipedriveTransformed

from common.pandas_utils import DataFrameWithSchema


@pytest.fixture(scope="session")
def repository(raw_data_lake_bucket: Bucket) -> PipedriveBqRepository:
    return PipedriveBqRepository()


def test_reload_table(repository: PipedriveBqRepository, pipedrive_transformed: list[PipedriveTransformed]) -> None:
    # arrange
    given = DataFrameWithSchema(schema=PipedriveSchema.SCHEMA, dataframe=DataFrame(pipedrive_transformed))

    # act
    repository.reload_table(given)
    actual = repository.select_table()

    # assert
    assert sorted(actual) == sorted(pipedrive_transformed)  # noqa
