from integration.sql_schema import User

from common.bq_client import QueryResult
from common.bq_repository import BigQueryRepository
from common.consts import GCP_MULTI_REGION


class UserRepository(BigQueryRepository):
    """
    User repository for testing BigQuery client.
    """

    DATASET = "analytics_test"
    TABLE = "user"

    def create_dataset(self) -> QueryResult:
        sql = (
            f"create schema if not exists {self.DATASET} "
            f"options (location = '{GCP_MULTI_REGION}', default_table_expiration_days = 1);"
        )
        return self._client.run_sql(sql)

    def create_table(self) -> QueryResult:
        table = self.get_table()
        sql = f"create table if not exists {table.full_name}(id int64, username string);"
        return self._client.run_sql(sql)

    def insert(self, user: User) -> QueryResult:
        table = self.get_table()
        sql = f'insert into {table.full_name}(id, username) values ({user.id}, "{user.username}");'
        return self._client.run_sql(sql)
