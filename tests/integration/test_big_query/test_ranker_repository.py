from datetime import date

import pytest
from ranker_repository import RankerBQRepository

ZERO_BYTES = "0 B"


@pytest.fixture(scope="session")
def event_date() -> date:
    return date(2025, 7, 29)


def test_load_procedures(ranker_repository: RankerBQRepository, event_date: date) -> None:
    bytes_billed = ranker_repository.load_category_view_item_clicks(event_date)
    assert bytes_billed != ZERO_BYTES

    bytes_billed = ranker_repository.load_category_page_impression_and_clicks(event_date)
    assert bytes_billed != ZERO_BYTES

    bytes_billed = ranker_repository.load_category_view_item_clicks_purchases(event_date)
    assert bytes_billed != ZERO_BYTES
