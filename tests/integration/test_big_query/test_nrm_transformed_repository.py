from datetime import date

import pytest

from nrm.nrm_transformed_repository import NrmTransformedRepository


@pytest.fixture(scope="session")
def repository() -> NrmTransformedRepository:
    return NrmTransformedRepository()


@pytest.mark.parametrize("load_date, row_count", [(date(2000, 1, 1), 0), (date(2024, 8, 31), 61)])
def test_count_rows(repository: NrmTransformedRepository, load_date: date, row_count: int) -> None:
    # act
    result = repository.count_rows(load_date)

    # assert
    assert result == row_count
