import pytest
from zendesk_client import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    NoZendesk<PERSON>serFor<PERSON>mail,
    TicketCreationError,
    TicketDeletionError,
    TicketPayload,
    ZendeskClient,
    ZendeskConfig,
)


##############################################################################
# Test: Create & Delete Ticket - Entire Happy Path
##############################################################################
def test_create_ticket_success_delete_after(
    zendesk_client: ZendeskClient, valid_payload: TicketPayload, caplog: pytest.LogCaptureFixture
) -> None:
    """
    Creates a ticket in the Zendesk sandbox and deletes it immediately (delete_after_creation=True).
    Verifies the client logs the correct success messages for creation and deletion.
    """
    # Act
    zendesk_client.create_ticket(valid_payload, delete_after_creation=True)

    # Assert: check logs
    create_logs = [rec.message for rec in caplog.records if "created successfully" in rec.message]
    delete_logs = [rec.message for rec in caplog.records if "deleted successfully" in rec.message]

    assert len(create_logs) == 1, "Expected one 'created successfully' message in logs."
    assert len(delete_logs) == 1, "Expected one 'deleted successfully' message in logs."


def test_create_ticket_success_no_delete(
    zendesk_client: ZendeskClient, valid_ticket_payload: TicketPayload, caplog: pytest.LogCaptureFixture
) -> None:
    """
    Creates a ticket in the Zendesk sandbox without immediate deletion.
    We manually delete it right after to clean up.
    """
    # Act: we expect the updated create_ticket() to return a ticket_id.
    ticket_id = zendesk_client.create_ticket(valid_ticket_payload, delete_after_creation=False)

    # Assert
    create_logs = [rec.message for rec in caplog.records if "created successfully" in rec.message]
    assert len(create_logs) == 1, "Expected one 'created successfully' message in logs."
    assert ticket_id is not None, "create_ticket should return the new ticket ID."
    assert isinstance(ticket_id, int), f"Ticket ID should be an integer. Found: {ticket_id}"

    # Cleanup: now delete the created ticket for test cleanliness
    zendesk_client.delete_ticket(ticket_id)


##############################################################################
# Test: Deleting Ticket
##############################################################################
def test_delete_ticket_not_found(zendesk_client: ZendeskClient, caplog: pytest.LogCaptureFixture) -> None:
    """
    Deleting a random ID that doesn't exist in Zendesk.
    TicketDeletionError for non-204 response status codes.
    """
    non_existent_id = 9999999999999  # Impossible ID

    with pytest.raises(TicketDeletionError) as exc_info:
        zendesk_client.delete_ticket(non_existent_id)

    assert f"Failed to delete ticket {non_existent_id}:" in str(exc_info.value)


##############################################################################
# Test: Missing Fields
##############################################################################


@pytest.mark.parametrize(
    "payload_fixture, expected_error",
    [
        ("missing_brand_id_ticket_payload", "brand_id"),
        ("missing_ticket_form_id_ticket_payload", "ticket_form_id"),
        ("missing_requester_id_ticket_payload", "requester_id"),
        ("missing_submitter_id_ticket_payload", "submitter_id"),
        ("missing_subject_ticket_payload", "subject"),
        ("missing_body_ticket_payload", "body"),
    ],
)
def test_create_ticket_missing_fields(
    zendesk_client: ZendeskClient, request: pytest.FixtureRequest, payload_fixture: str, expected_error: str
) -> None:
    """
    Ensures the client raises MissingTicketField if subject/body/cc_emails are absent.
    """
    # https://stackoverflow.com/questions/42014484/pytest-using-fixtures-as-arguments-in-parametrize
    ticket_payload = request.getfixturevalue(payload_fixture)

    with pytest.raises(MissingTicketField) as exc_info:
        zendesk_client.create_ticket(ticket_payload)

    assert f"Not all requirements are filled in: {expected_error} are required." in str(exc_info.value)


##############################################################################
# Test: Creation Failure (Bad URL or Auth)
##############################################################################
@pytest.mark.parametrize("override_subdomain", ["lol-domain", ""])
def test_create_ticket_failure_bad_subdomain(
    zendesk_client: ZendeskClient,
    zendesk_config: ZendeskConfig,
    valid_ticket_payload: TicketPayload,
    override_subdomain: str,
) -> None:
    """
    Forces a TicketCreationError by overriding the config with a bad subdomain
    so the POST call fails (e.g. 404 or DNS error).
    """
    # Create a local client with a broken subdomain.
    broken_config = ZendeskConfig(
        email=zendesk_config.email,
        subdomain=override_subdomain,
        token=zendesk_config.token,
        headers=zendesk_config.headers,
    )
    broken_client = ZendeskClient(broken_config)

    with pytest.raises(TicketCreationError) as exc_info:
        broken_client.create_ticket(valid_ticket_payload)

    # Just check that the error message indicates a failure
    assert "Failed to create ticket" in str(exc_info.value)
    assert override_subdomain in str(exc_info.value)


def test_delete_ticket_failure_bad_token(
    zendesk_client: ZendeskClient, zendesk_config: ZendeskConfig, valid_ticket_payload: TicketPayload
) -> None:
    """
    Forces TicketDeletionError by using an invalid auth token, then trying to delete a ticket.
    """
    # Step 1: create a real ticket with correct auth
    ticket_id = zendesk_client.create_ticket(valid_ticket_payload)

    # Step 2: new config with bogus token
    broken_config = ZendeskConfig(
        email=zendesk_config.email,
        subdomain=zendesk_config.subdomain,
        token="wrong_token",
        headers=zendesk_config.headers,
    )
    broken_client = ZendeskClient(broken_config)

    # Step 3: try to delete with broken client -> expect a TicketDeletionError or 401
    with pytest.raises(TicketDeletionError) as exc_info:
        broken_client.delete_ticket(ticket_id)

    # Cleanup with real client
    zendesk_client.delete_ticket(ticket_id)

    assert "Failed to delete ticket" in str(exc_info.value), f"Expected a TicketDeletionError, got: {exc_info.value}"


##############################################################################
# Test: Get Ticket
##############################################################################
def test_get_ticket_success(zendesk_client: ZendeskClient, valid_ticket_payload: TicketPayload) -> None:
    """
    Tests the get_ticket method by creating a ticket, retrieving it, and then deleting it.
    Verifies that the retrieved ticket has the expected subject.
    """
    # Create a ticket to retrieve
    ticket_id = zendesk_client.create_ticket(valid_ticket_payload)

    try:
        # Get the ticket
        ticket_data = zendesk_client.get_ticket(ticket_id)

        # Verify ticket data
        assert ticket_data is not None, "Expected ticket data to be returned"
        assert ticket_data["id"] == ticket_id, f"Expected ticket ID {ticket_id}, got {ticket_data['id']}"
        assert ticket_data["subject"] == valid_ticket_payload.subject, "Ticket subject doesn't match"
    finally:
        # Clean up
        zendesk_client.delete_ticket(ticket_id)


def test_get_ticket_not_found(zendesk_client: ZendeskClient) -> None:
    """
    Tests that get_ticket raises a ValueError when trying to retrieve a non-existent ticket.
    """
    non_existent_id = 9999999999999  # Impossible ID

    with pytest.raises(ValueError) as exc_info:
        zendesk_client.get_ticket(non_existent_id)

    assert f"Failed to get ticket {non_existent_id}:" in str(exc_info.value)


def test_get_ticket_bad_auth(
    zendesk_client: ZendeskClient, zendesk_config: ZendeskConfig, valid_ticket_payload: TicketPayload
) -> None:
    """
    Tests that get_ticket raises a ValueError when using invalid authentication.
    """
    # Create a ticket with valid auth
    ticket_id = zendesk_client.create_ticket(valid_ticket_payload)

    try:
        # Create a client with invalid authentication
        broken_config = ZendeskConfig(
            email=zendesk_config.email,
            subdomain=zendesk_config.subdomain,
            token="wrong_token",
            headers=zendesk_config.headers,
        )
        broken_client = ZendeskClient(broken_config)

        # Try to get the ticket with invalid auth
        with pytest.raises(ValueError) as exc_info:
            broken_client.get_ticket(ticket_id)

        assert f"Failed to get ticket {ticket_id}:" in str(exc_info.value)
    finally:
        # Clean up
        zendesk_client.delete_ticket(ticket_id)


##############################################################################
# Test: Find User ID via Email
##############################################################################
def test_find_user_id_via_email_success(zendesk_client: ZendeskClient, test_email: str) -> None:
    """
    Tests the find_user_id_via_email method with a valid email address.
    Verifies that a user ID is returned.
    """
    user_id = zendesk_client.find_user_id_via_email(test_email)

    assert user_id is not None, "Expected a user ID to be returned"
    assert isinstance(user_id, int), f"Expected user ID to be an integer, got {type(user_id)}"


def test_find_user_id_via_email_not_found(zendesk_client: ZendeskClient, non_existent_email: str) -> None:
    """
    Tests that find_user_id_via_email raises a ValueError when trying to find a non-existent email.
    """
    with pytest.raises(NoZendeskUserForEmail) as exc_info:
        zendesk_client.find_user_id_via_email(non_existent_email)

    assert f"No user found with email {non_existent_email}" in str(exc_info.value)


def test_find_user_id_via_email_bad_auth(
    zendesk_client: ZendeskClient, zendesk_config: ZendeskConfig, test_email: str
) -> None:
    """
    Tests that find_user_id_via_email raises a ValueError when using invalid authentication.
    """
    # Create a client with invalid authentication
    broken_config = ZendeskConfig(
        email=zendesk_config.email,
        subdomain=zendesk_config.subdomain,
        token="wrong_token",
        headers=zendesk_config.headers,
    )
    broken_client = ZendeskClient(broken_config)

    with pytest.raises(NoZendeskUserForEmail) as exc_info:
        broken_client.find_user_id_via_email(test_email)

    assert "Failed to search for user with email" in str(exc_info.value)
