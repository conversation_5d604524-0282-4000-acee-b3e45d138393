import pytest
from pytest import LogCaptureFixture, param

from common.pipeline_logging.pipeline_model import (
    PipelineRun,
    SupportsPipelineExecution,
)
from common.sql_model import SqlTable
from platform_export.dispatcher.export_dispatcher import PlatformExportDispatcher
from platform_export.etl import main
from platform_export.export_shared.export_model import ExportConfig
from platform_export.export_shared.export_target import SOURCE_TO_TARGET


@pytest.mark.parametrize(
    "sql_table",
    # `id` is used to name the test cases by the SQL table name
    [param(cs, id=cs.full_name) for cs in PlatformExportDispatcher.CONFIGURED_SOURCES],
)
def test_etl(sql_table: SqlTable, platform_export_timestamp: int, caplog: LogCaptureFixture) -> None:
    # arrange
    caplog.set_level("INFO")
    pipeline_run = PipelineRun()
    SupportsPipelineExecution.set_environment(pipeline_run)
    config = ExportConfig(timestamp=platform_export_timestamp, source=sql_table)

    expected_extracted = f"Postgres '{sql_table.full_name}' extracted"
    target_table = SOURCE_TO_TARGET[sql_table].table_name
    expected_loaded = f"BigQuery table '{target_table}' loaded"
    expected_qa = "qa_passed=True"

    # act
    main.run(config)

    # assert
    assert any(expected_extracted in s for s in caplog.messages)
    assert any(expected_loaded in s for s in caplog.messages)
    assert any(expected_qa in s for s in caplog.messages)
