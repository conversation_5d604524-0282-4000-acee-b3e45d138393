import pytest
from google.cloud.sql.connector import Connector
from sqlalchemy import text

from common.sql_client import DatabaseConfig, DatabaseError, get_database_engine


def test_get_database_engine_raises_error_without_connector(
    db_config: DatabaseConfig,
) -> None:
    with pytest.raises(DatabaseError):
        _ = get_database_engine(db_config=db_config)


def test_get_database_engine_with_connector(db_config: DatabaseConfig) -> None:
    # arrange
    select_sql = text("select row_number() over() as rn from analytics.schema_history limit 5")
    expected_ids = list(range(1, 6))

    # act: initialize Cloud SQL Python Connector
    with Connector() as connector:
        engine = get_database_engine(db_config=db_config, connector=connector)

        # interact with Cloud SQL database using connection pool
        with engine.connect() as connection:
            assert connection.closed is False
            result = connection.execute(select_sql)

    # assert
    actual_ids = [int(row.rn) for row in result]
    assert actual_ids == expected_ids
