from json import JSONDecodeError

import pytest
from google.cloud.sql.connector import Connector
from integration.sql_schema import US<PERSON>, User, assert_users, insert_user
from pandas import DataFrame

from common.sql_model import SqlTable
from common.sql_repository import SqlRepository


def test_sql_repository_when_invalid_secret() -> None:
    with pytest.raises(JSONDecodeError):
        _ = SqlRepository(db_config="invalid string")


def test_sql_repository_when_valid_secret(db_secret: str, sql_repository: SqlRepository) -> None:
    with Connector() as connector:
        sql_repository_from_secret = SqlRepository(db_config=db_secret, connector=connector)
        assert sql_repository_from_secret.engine.url == sql_repository.engine.url


def test_sql_repository_insert(sql_repository: SqlRepository, sql_table: SqlTable) -> None:
    """Simplified tests to ensure it works with cloud-sql, more tests in `test_local_sql.py`"""
    # arrange
    inserted = User(2, "test2")
    df = DataFrame.from_dict({inserted.columns[0]: [inserted.id], inserted.columns[1]: [inserted.username]})

    insert_user(sql_repository.engine, sql_table, USER)
    assert_users(sql_repository, sql_table, [USER])

    # act
    result = sql_repository.insert(df=df, table=sql_table, truncate=True)

    # assert
    assert result == 1
    assert_users(sql_repository, sql_table, [inserted])
