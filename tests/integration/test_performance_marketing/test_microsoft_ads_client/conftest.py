import pytest
from microsoft_ads_api_client import MicrosoftAdsApiClient
from microsoft_ads_api_model import MICROSOFT_ADS_SECRET_ID, MicrosoftAdsApiConfig

from common.config import Config
from common.secret_manager_client import get_secret


@pytest.fixture(scope="session")
def microsoft_ads_secret(config: Config) -> str:
    return get_secret(MICROSOFT_ADS_SECRET_ID, config.project_id)


@pytest.fixture(scope="session")
def microsoft_ads_api_client_config(microsoft_ads_secret: str) -> MicrosoftAdsApiConfig:
    return MicrosoftAdsApiConfig.from_json(microsoft_ads_secret)


@pytest.fixture(scope="session")
def microsoft_ads_api_client(microsoft_ads_api_client_config: MicrosoftAdsApiConfig) -> MicrosoftAdsApiClient:
    return MicrosoftAdsApiClient(config=microsoft_ads_api_client_config)


@pytest.fixture(scope="session")
def access_token(microsoft_ads_api_client: MicrosoftAdsApiClient) -> str:
    """Get the access token here since it's being reused by other tests"""
    return microsoft_ads_api_client.get_access_token()
