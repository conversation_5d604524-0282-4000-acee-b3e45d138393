from microsoft_ads_api_client import MicrosoftAdsApiClient
from microsoft_ads_api_model import (
    MicrosoftAdsReportSchema,
    ReportRequestResponse,
    ReportRequestStatus,
    ReportStatus,
)

from common.pandas_utils import DataFrameWithSchema


def test_get_access_token(access_token: str) -> None:
    """
    Test that access token is retrieved successfully and has expected format.
    get_performance_report() contains the get_access_token() + submit_report_request() + poll_and_download_report()
    """
    # assert
    assert isinstance(access_token, str)
    # Access tokens are long strings (consistently ~2746 chars), so testing over 2000 to be safe.
    assert len(access_token) > 2000


def test_submit_report_request(microsoft_ads_api_client: MicrosoftAdsApiClient, access_token: str) -> None:
    """Test that report request is submitted successfully and returns valid response."""
    # act
    report_request_response = microsoft_ads_api_client.submit_report_request(access_token=access_token)

    # assert
    assert isinstance(report_request_response, ReportRequestResponse)
    assert isinstance(report_request_response.ReportRequestId, str)
    # Report request IDs are 32 chars, so testing over 30 to be safe
    assert len(report_request_response.ReportRequestId) > 30


def test_poll_report_status(microsoft_ads_api_client: MicrosoftAdsApiClient, access_token: str) -> None:
    """
    Test that report polling completes successfully and returns valid download URL.
    poll_and_download_report() contains poll_report_status() + download_report() + parse_zip_report()
    """
    # act
    report_request_response = microsoft_ads_api_client.submit_report_request(access_token=access_token)
    report_request_status = microsoft_ads_api_client.poll_report_status(
        report_response=report_request_response, access_token=access_token
    )

    # assert
    assert isinstance(report_request_status, ReportRequestStatus)
    assert isinstance(report_request_status.Status, str)
    assert report_request_status.Status == ReportStatus.SUCCESS
    assert isinstance(report_request_status.ReportDownloadUrl, str)
    # Download URLs are ~457 chars, so testing over 400 to be safe
    assert len(report_request_status.ReportDownloadUrl) > 400
    assert report_request_status.ReportDownloadUrl.startswith("https://")


def test_download_report(microsoft_ads_api_client: MicrosoftAdsApiClient, access_token: str) -> None:
    """Test that report download returns valid bytes content."""
    # act
    report_request_response = microsoft_ads_api_client.submit_report_request(access_token=access_token)
    report_request_status = microsoft_ads_api_client.poll_report_status(
        report_response=report_request_response, access_token=access_token
    )
    report_bytes = microsoft_ads_api_client.download_report(report_request_status.ReportDownloadUrl)

    # assert
    assert isinstance(report_bytes, bytes)
    # Report files are typically ~20KB, so testing over 20_000 bytes to be safe
    assert len(report_bytes) > 20_000


def test_parse_zip_report(microsoft_ads_api_client: MicrosoftAdsApiClient, access_token: str) -> None:
    """Test that ZIP report parsing returns valid DataFrame with expected schema and data validation."""
    # act
    report_request_response = microsoft_ads_api_client.submit_report_request(access_token=access_token)
    report_request_status = microsoft_ads_api_client.poll_report_status(
        report_response=report_request_response, access_token=access_token
    )
    report_bytes = microsoft_ads_api_client.download_report(report_request_status.ReportDownloadUrl)
    result = microsoft_ads_api_client.parse_zip_report(content=report_bytes)

    # assert
    assert isinstance(result, DataFrameWithSchema)
    assert not result.dataframe.empty
    assert result.schema == MicrosoftAdsReportSchema.SCHEMA

    # Validate time_period values are between 0 and 23 (hour of day)
    time_period_values = result.dataframe[MicrosoftAdsReportSchema.TIME_PERIOD]
    assert all(
        0 <= hour <= 23 for hour in time_period_values
    ), f"All time_period values must be between 0-23, found: {time_period_values.unique()}"

    # Validate campaign_name contains expected strings from known campaigns
    campaign_names = result.dataframe[MicrosoftAdsReportSchema.CAMPAIGN_NAME]
    # Expected campaign patterns from known campaigns:
    # AT - Search - Smartphones - Generic + Brands | 01s - All Brands
    # DE - Search - Smartphones - Generic + Brands | 01s - All Brands
    expected_campaign_strings = ["AT - Search - Smartphones", "DE - Search - Smartphones"]
    all_strings_found = all(campaign_names.str.contains(string).any() for string in expected_campaign_strings)
    assert all_strings_found, f"Campaign names must contain all of: {expected_campaign_strings}"
