from datetime import date

import pytest
from google_ads_client import GoogleAdsClient
from google_ads_client_model import GoogleAdsCustomer

from common.time_service import TimeService


def test_get_customers(google_ads_client: GoogleAdsClient) -> None:
    # Act
    customers = google_ads_client.get_customers()

    # Assert
    assert customers != []


def test_get_costs(google_ads_client: GoogleAdsClient, time_service: TimeService) -> None:
    # Arrange
    customer = next(
        iter([customer for customer in google_ads_client.get_customers() if customer.name == "Refurbed Search | DACH"]),
        None,
    )
    assert customer is not None

    # Act
    costs = google_ads_client.get_costs(customer, time_service.days_ago(7), time_service.today)

    # Assert
    assert costs != []


def test_get_costs_raises_error_on_invalid_dates(google_ads_client: GoogleAdsClient) -> None:
    # Arrange
    today = date(2025, 7, 18)
    yesterday = date(2025, 7, 17)

    # Act & Assert
    with pytest.raises(AttributeError, match="Start date 2025-07-18 is after end date 2025-07-17!"):
        google_ads_client.get_costs(GoogleAdsCustomer(id="123", name="Test"), today, yesterday)
