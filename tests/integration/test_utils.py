from io import BytesIO

from google.cloud.storage import Bucket
from pandas import DataFrame


def upload_test_file(bucket: Bucket, bucket_file_path: str, df: DataFrame) -> None:
    """
    Helper function to upload a test file to the bucket.

    :param bucket: Bucket to upload the file to
    :param bucket_file_path: a path to the file on the bucket
    :param df: pandas DataFrame to be uploaded as a parquet file
    """
    buffer = BytesIO()

    blob = bucket.blob(bucket_file_path)
    df.to_parquet(buffer)  # noqa
    buffer.seek(0)

    blob.upload_from_string(buffer.getvalue())


def delete_test_files(bucket: Bucket, bucket_file_path: str) -> None:
    """
    Helper function to delete test files from the bucket.

    :param bucket: Bucket to delete the file from
    :param bucket_file_path: a path to the file on the bucket
    """
    blobs = bucket.list_blobs(prefix=bucket_file_path)
    for blob in blobs:
        blob.delete()
