import pytest
from _pytest.logging import LogCaptureFixture

from common.cloud_sql_instance_client.instance_client import (
    CloudSQLInstanceClient,
    CloudSQLInstanceClientError,
)
from common.cloud_sql_instance_client.instance_model import (
    BackupConfiguration,
    BackupType,
    CloudSQLCreateInstanceParams,
    CloudSQLRestoreBackupParams,
    InstanceCreateSettings,
    IpConfiguration,
    RestoreBackupContext,
)
from common.consts import DEFAULT_GOOGLE_PROJECT_ID, DEFAULT_GOOGLE_SQL_INSTANCE


@pytest.mark.skip(reason="Creates a real CLoud SQL instance")
def test_create_restore_drop_instance(
    cloud_sql_instance_client: CloudSQLInstanceClient, new_cloud_sql_instance_params: CloudSQLCreateInstanceParams
) -> None:
    # arrange
    staging_instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    staging_latest_backup = cloud_sql_instance_client.get_latest_backup(staging_instance_name)
    assert staging_latest_backup is not None

    backup_params = CloudSQLRestoreBackupParams(
        restore_backup_context=RestoreBackupContext(
            instance_id=staging_instance_name, project=DEFAULT_GOOGLE_PROJECT_ID, backup_run_id=staging_latest_backup.id
        )
    )

    # act & assert
    actual_instance = cloud_sql_instance_client.create_instance(params=new_cloud_sql_instance_params)
    assert actual_instance.name == new_cloud_sql_instance_params.name
    assert new_cloud_sql_instance_params.name in actual_instance.connection_name
    assert actual_instance.ip_addresses != []

    # act
    cloud_sql_instance_client.restore_backup(instance_name=actual_instance.name, backup_params=backup_params)

    # act & assert
    cloud_sql_instance_client.drop_instance(name=actual_instance.name)
    instance = cloud_sql_instance_client.describe_instance(name=actual_instance.name)
    assert instance is None


@pytest.mark.skip(reason="Restore a real production backup into big CLoud SQL instance")
def test_create_instance_restore_from_production(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    project_id = "refb-analytics"
    prod_instance_name = "analytics-08ce"  # real name of Cloud SQL instance on production
    prod_latest_backup = cloud_sql_instance_client.get_latest_backup(prod_instance_name, project_id)
    assert prod_latest_backup is not None

    backup_params = CloudSQLRestoreBackupParams(
        restore_backup_context=RestoreBackupContext(
            instance_id=prod_instance_name, project=project_id, backup_run_id=prod_latest_backup.id
        )
    )

    instance_params = CloudSQLCreateInstanceParams(
        name="analytics-db-prod-restore",
        root_password="Test-123@",
        settings=InstanceCreateSettings(
            tier="db-custom-32-65536",  # 32 CPUs, 64 GB RAM
            data_disk_size_gb=600,
            backup_configuration=BackupConfiguration(),
            ip_configuration=IpConfiguration(),
        ),
    )

    # act & assert
    actual_instance = cloud_sql_instance_client.create_instance(params=instance_params)
    assert actual_instance.name == instance_params.name
    assert instance_params.name in actual_instance.connection_name
    assert actual_instance.ip_addresses != []

    # act
    cloud_sql_instance_client.restore_backup(instance_name=actual_instance.name, backup_params=backup_params)


@pytest.mark.skip(reason="Creates a real CLoud SQL instance")
def test_create_stop_start_drop_instance(
    cloud_sql_instance_client: CloudSQLInstanceClient, new_cloud_sql_instance_params: CloudSQLCreateInstanceParams
) -> None:
    # act & assert
    actual_instance = cloud_sql_instance_client.create_instance(params=new_cloud_sql_instance_params)
    assert actual_instance.name == new_cloud_sql_instance_params.name
    assert new_cloud_sql_instance_params.name in actual_instance.connection_name
    assert actual_instance.ip_addresses != []

    # act & assert
    stopped_instance = cloud_sql_instance_client.stop_instance(instance_name=actual_instance.name)
    assert stopped_instance.settings.activation_policy == "NEVER"

    # act & assert
    started_instance = cloud_sql_instance_client.start_instance(instance_name=actual_instance.name)
    assert started_instance.settings.activation_policy == "ALWAYS"

    # act & assert
    cloud_sql_instance_client.drop_instance(name=actual_instance.name)
    instance = cloud_sql_instance_client.describe_instance(name=actual_instance.name)
    assert instance is None


def test_describe_existing_instance(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # act
    actual_instance = cloud_sql_instance_client.describe_instance(name=DEFAULT_GOOGLE_SQL_INSTANCE)

    # assert
    assert actual_instance.name == DEFAULT_GOOGLE_SQL_INSTANCE


def test_describe_non_existing_instance(
    cloud_sql_instance_client: CloudSQLInstanceClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "INFO"
    caplog.set_level(expected_logger_level)
    instance_name = "non-existing-instance"

    # act
    actual_instance = cloud_sql_instance_client.describe_instance(name=instance_name)

    # assert
    assert actual_instance is None
    assert caplog.records[0].levelname == expected_logger_level
    assert f"The Cloud SQL instance '{instance_name}' does not exist." in caplog.messages


def test_get_instances(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # act
    actual_instances = cloud_sql_instance_client.get_instances()

    # assert
    assert DEFAULT_GOOGLE_SQL_INSTANCE in [instance.name for instance in actual_instances]


def test_get_backups(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    # act
    actual_backups = cloud_sql_instance_client.get_backups(instance_name)

    # assert
    assert actual_backups != []


def test_get_latest_backup(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    # act
    actual_backup = cloud_sql_instance_client.get_latest_backup(instance_name)

    # assert
    assert actual_backup is not None


def test_describe_backup(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    # Get the latest backup to use its ID
    latest_backup = cloud_sql_instance_client.get_latest_backup(instance_name)
    assert latest_backup is not None

    # act
    backup = cloud_sql_instance_client.describe_backup(instance_name, latest_backup.id)

    # assert
    assert backup is not None
    assert backup.id == latest_backup.id
    assert backup.instance == instance_name


def test_describe_non_existing_backup(
    cloud_sql_instance_client: CloudSQLInstanceClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    backup_id = "999999999"

    # act
    backup = cloud_sql_instance_client.describe_backup(instance_name, "999999999")

    # assert
    assert backup is None
    assert caplog.records[0].levelname == expected_logger_level
    assert f"Backup with ID '{backup_id}' not found!" in caplog.messages


@pytest.mark.skip(reason="Creates a real backup of a Cloud SQL instance")
def test_create_delete_backup(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = "Test backup created by integration test"

    # act & assert - create backup
    backup = cloud_sql_instance_client.create_backup(instance_name, description)
    assert backup is not None
    assert backup.instance == instance_name
    assert backup.description == description
    assert backup.type == BackupType.ON_DEMAND

    # act & assert - delete backup
    cloud_sql_instance_client.delete_backup(instance_name, backup.id)

    # Verify backup is deleted by checking it's not in the list of backups
    backups = cloud_sql_instance_client.get_backups(instance_name)
    assert not any(b.id == backup.id for b in backups)


def test_delete_non_existing_backup(
    cloud_sql_instance_client: CloudSQLInstanceClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    backup_id = "999999999"

    # act & assert
    cloud_sql_instance_client.delete_backup(instance_name, backup_id)

    # assert
    assert caplog.records[0].levelname == expected_logger_level
    assert f"Backup with ID '{backup_id}' not found!" in caplog.messages


@pytest.mark.skip(reason="Creates and deletes real backups of a Cloud SQL instance")
def test_delete_backups(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = "Test backup created for delete_backups test"

    # Create multiple backups with the same description
    backup1 = cloud_sql_instance_client.create_backup(instance_name, description)
    backup2 = cloud_sql_instance_client.create_backup(instance_name, description)

    assert backup1 is not None
    assert backup2 is not None
    assert backup1.description == description
    assert backup2.description == description
    assert backup1.type == BackupType.ON_DEMAND
    assert backup2.type == BackupType.ON_DEMAND

    # act
    deleted_count = cloud_sql_instance_client.delete_backups(instance_name, description)

    # assert
    assert deleted_count >= 2  # At least the two backups created

    # Verify backups are deleted by checking they're not in the list of backups
    backups = cloud_sql_instance_client.get_backups(instance_name)
    assert not any(b.id == backup1.id for b in backups)
    assert not any(b.id == backup2.id for b in backups)


def test_delete_backups_empty_description(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = ""

    with pytest.raises(CloudSQLInstanceClientError, match="Cannot specify empty description!"):
        cloud_sql_instance_client.delete_backups(instance_name, description)


def test_delete_backups_no_matching_backups(
    cloud_sql_instance_client: CloudSQLInstanceClient, caplog: LogCaptureFixture
) -> None:
    # arrange
    expected_logger_level = "WARNING"
    caplog.set_level(expected_logger_level)
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    description = "Non-existent backup description"

    # act
    deleted_count = cloud_sql_instance_client.delete_backups(instance_name, description)

    # assert
    assert deleted_count == 0
    assert caplog.records[0].levelname == expected_logger_level
    assert (
        f"No '{BackupType.ON_DEMAND}' backups with description '{description}' found for instance '{instance_name}'"
        in caplog.messages
    )


@pytest.mark.skip(reason="Creates and deletes real backups of a Cloud SQL instance")
def test_delete_backups_preserves_empty_description(cloud_sql_instance_client: CloudSQLInstanceClient) -> None:
    # arrange
    instance_name = DEFAULT_GOOGLE_SQL_INSTANCE  # real name of Cloud SQL instance on staging
    none_description = None
    empty_description = ""
    non_empty_description = "Test backup with non-empty description"

    # Create a backup with None description
    backup_none_description = cloud_sql_instance_client.create_backup(instance_name, none_description)
    # Create a backup with empty description
    backup_empty_description = cloud_sql_instance_client.create_backup(instance_name, empty_description)
    # Create a backup with non-empty description
    backup_non_empty = cloud_sql_instance_client.create_backup(instance_name, non_empty_description)

    assert backup_none_description is not None
    assert backup_empty_description is not None
    assert backup_non_empty is not None
    assert backup_none_description.description is None
    assert backup_empty_description.description is None
    assert backup_non_empty.description == non_empty_description

    assert backup_none_description.type == BackupType.ON_DEMAND
    assert backup_empty_description.type == BackupType.ON_DEMAND
    assert backup_non_empty.type == BackupType.ON_DEMAND

    # act - delete backups with non-empty description
    deleted_count = cloud_sql_instance_client.delete_backups(instance_name, non_empty_description)

    # assert
    assert deleted_count >= 1  # At least the one backup with non-empty description

    # Verify only the backup with non-empty description is deleted
    backups = cloud_sql_instance_client.get_backups(instance_name)
    assert any(b.id == backup_empty_description.id for b in backups)  # Backup with empty description should still exist
    assert not any(b.id == backup_non_empty.id for b in backups)  # Backup with non-empty description should be deleted

    # Clean up - delete the backup with empty description manually
    cloud_sql_instance_client.delete_backup(instance_name, backup_empty_description.id, False)
