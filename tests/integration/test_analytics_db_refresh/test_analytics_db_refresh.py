from common.cloud_run_client.job_client import CloudRunJobClient
from common.cloud_run_client.model import CloudRunJobRunRequest


def test_analytics_db_refresh(cloud_run_job_client: CloudRunJobClient) -> None:
    # arrange
    request = CloudRunJobRunRequest(environment_variables={"CICD": "True"})

    # act
    cloud_run_job_execution = cloud_run_job_client.run("analytics-db-refresh", body=request, wait_for_completion=True)

    # assert
    assert cloud_run_job_execution.is_succeeded
