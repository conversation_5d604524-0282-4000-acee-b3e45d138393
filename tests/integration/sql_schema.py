from dataclasses import asdict, dataclass
from decimal import Decimal
from functools import cached_property
from typing import Type, TypeVar

from dataclasses_json import DataClassJsonMixin
from sqlalchemy import Engine, Row, text

from common.sql_model import SqlTable
from common.sql_repository import SqlRepository

TTableClass = TypeVar("TTableClass", bound=DataClassJsonMixin)


@dataclass(frozen=True)
class User(DataClassJsonMixin):
    id: int = 1
    username: str = "test-user"

    @cached_property
    def columns(self) -> list[str]:
        return list(asdict(self).keys())


ZERO = Decimal("0.0000")

USER = User()


def create_user_table(engine: Engine, sql_table: SqlTable) -> None:
    sql = text(f"create table {sql_table.full_name} (id integer primary key, username varchar(50) not null)")
    with engine.begin() as connection:
        connection.execute(sql)


def drop_user_table(engine: Engine, sql_table: SqlTable) -> None:
    sql = text(f"drop table if exists {sql_table.full_name}")
    with engine.begin() as connection:
        connection.execute(sql)


def insert_user(engine: Engine, sql_table: SqlTable, user: User) -> None:
    insert_sql = text(f"insert into {sql_table.full_name} values({user.id}, '{user.username}')")
    with engine.begin() as connection:
        connection.execute(insert_sql)


def assert_users(sql_repository: SqlRepository, sql_table: SqlTable, expected: list[User]) -> None:
    result = sql_repository.select(sql_table)
    assert_result(User, result, set(expected))


def assert_result(cls: Type[TTableClass], result: list[Row], expected: set[TTableClass]) -> None:
    actual = set({cls(*row) for row in result})
    assert actual == expected
