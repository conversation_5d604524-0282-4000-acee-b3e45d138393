import pytest
from _pytest.logging import Log<PERSON>ap<PERSON><PERSON>ixture
from unit.test_alerting.conftest import J<PERSON>B_NAME

from common.cloud_run_client.job_client import CloudRun<PERSON>ob<PERSON><PERSON>
from common.cloud_run_client.model import CloudRunJobRunRequest
from common.consts import DEFAULT_GOOGLE_PROJECT_ID, GCP_REGION


def test_describe_job(cloud_run_job_client: CloudRunJobClient) -> None:
    # arrange
    expected_job_full_name = f"projects/{DEFAULT_GOOGLE_PROJECT_ID}/locations/{GCP_REGION}/jobs/{JOB_NAME}"

    # act
    cloud_run_job = cloud_run_job_client.describe_resource(JOB_NAME)

    # assert
    assert cloud_run_job.name == expected_job_full_name


def test_describe_job_when_does_not_exist(cloud_run_job_client: CloudRunJobClient) -> None:
    # act
    cloud_run_job = cloud_run_job_client.describe_resource("non-existing-job")

    # assert
    assert cloud_run_job is None


def test_get_last_completed_job_execution(cloud_run_job_client: CloudRunJobClient) -> None:
    # act
    cloud_run_job_execution = cloud_run_job_client.get_last_completed_execution(JOB_NAME)

    # assert
    if cloud_run_job_execution:
        assert not cloud_run_job_execution.is_in_progress
        assert JOB_NAME in cloud_run_job_execution.short_name


def test_get_execution(cloud_run_job_client: CloudRunJobClient) -> None:
    # arrange
    expected_execution = cloud_run_job_client.get_last_completed_execution(JOB_NAME)

    # act
    actual_execution = cloud_run_job_client.get_execution(JOB_NAME, expected_execution.short_name)

    # assert
    assert actual_execution == expected_execution


def test_get_execution_when_does_not_exist(
    cloud_run_job_client: CloudRunJobClient,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "WARNING"
    caplog.set_level(log_level)
    execution_name = "non-existing-execution"

    # act
    cloud_run_job_execution = cloud_run_job_client.get_execution(JOB_NAME, execution_name)

    # assert
    assert cloud_run_job_execution is None

    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert f"Cloud Run job execution '{execution_name}' not found." in caplog.messages


@pytest.mark.skip(reason="Runs a real Cloud Run job")
def test_run(cloud_run_job_client: CloudRunJobClient) -> None:
    # arrange
    body = CloudRunJobRunRequest(environment_variables={"TEST_ENV_VAR": "test_value"})

    # act
    cloud_run_job_execution = cloud_run_job_client.run(JOB_NAME, body, wait_for_completion=False)

    # assert
    assert cloud_run_job_execution.is_in_progress
    assert JOB_NAME in cloud_run_job_execution.short_name
    assert cloud_run_job_execution.env_variables["TEST_ENV_VAR"] == "test_value"
