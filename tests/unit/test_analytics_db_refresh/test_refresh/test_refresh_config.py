from refresh_shared.model import DbRefreshConfig


def test_config(db_refresh_config: DbRefreshConfig) -> None:
    assert db_refresh_config.temp_schemas == ["a_test", "b_test", "c_test", "public_test"]
    assert db_refresh_config.backed_up_schemas == ["a_backup", "b_backup", "c_backup", "public_backup"]
    assert db_refresh_config.temp_materialized_views_to_refresh == [
        "public_test.foo",
        "public_test.bar",
        "public_test.baz",
    ]
