import pytest
from refresh_shared.dump_restore import PgParams
from refresh_shared.model import DbRefreshConfig

from common.config import MattermostAlertingConfig
from common.migrate_db import FlywayPgParams
from common.sql_client import DatabaseConfig


@pytest.fixture(scope="session")
def db_refresh_config() -> DbRefreshConfig:
    return DbRefreshConfig(
        source_db_config=DatabaseConfig(),
        target_db_config=DatabaseConfig(),
        alerting_config=MattermostAlertingConfig(webhook_url="test"),
        pg_params=PgParams(host="test", schema=["a", "b", "c", "public"]),
        flyway_params=FlywayPgParams(
            host="test",
            port=123,
            database="foo",
            config_files="/test/config",
            locations="sql/locations/",
            environment="test",
            user="bar",
            password="baz",
        ),
        materialized_views_to_refresh=["public.foo", "public.bar", "public.baz"],
        temp_schema_suffix="_test",
        backup_schema_suffix="_backup",
    )
