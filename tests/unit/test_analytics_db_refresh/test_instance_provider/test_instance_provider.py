from unittest.mock import Mock, patch

from refresh_shared.model import DbRefreshConfig
from unit.test_analytics_db_refresh.test_instance_provider.utils import (
    create_instance_provider,
)

from common.cloud_sql_instance_client.instance_model import Backup, CloudSQLInstance
from common.config import Config
from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger


@patch(f"{PipelineExecutionLogger.__module__}.{PipelineExecutionLogger.__name__}")
def test_create_local_environment(
    mock_pipeline_logger: Mock,
    env_config_local: Config,
    mock_refresh_config: Mock,
    db_refresh_config: DbRefreshConfig,
) -> None:
    # Arrange
    provider = create_instance_provider(
        env_config=env_config_local,
        local_config=mock_refresh_config,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance is None
    assert result.target_instance is None
    assert result.refresh_config == db_refresh_config

    mock_refresh_config.source_restore_instance.recreate_instance.assert_not_called()
    mock_refresh_config.source_restore_instance.restore_backup.assert_not_called()
    mock_refresh_config.target_restore_instance.describe_instance.assert_not_called()
    mock_refresh_config.target_restore_instance.recreate_instance.assert_not_called()
    mock_refresh_config.target_restore_instance.restore_backup.assert_not_called()


@patch(f"{PipelineExecutionLogger.__module__}.{PipelineExecutionLogger.__name__}")
@patch.dict("os.environ", {"CICD": "True"})
def test_create_cicd_environment(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_refresh_config: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_refresh_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_refresh_config.source_restore_instance.restore_backup.return_value = mock_backup
    mock_refresh_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config=env_config_staging,
        cicd_config=mock_refresh_config,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance == cloud_sql_instance_with_ip
    assert result.target_instance == cloud_sql_instance_with_ip

    mock_refresh_config.source_restore_instance.recreate_instance.assert_called_once()
    mock_refresh_config.source_restore_instance.restore_backup.assert_called_once()
    mock_refresh_config.target_restore_instance.describe_instance.assert_called_once()
    mock_refresh_config.target_restore_instance.recreate_instance.assert_not_called()
    mock_refresh_config.target_restore_instance.restore_backup.assert_not_called()


@patch(f"{PipelineExecutionLogger.__module__}.{PipelineExecutionLogger.__name__}")
def test_create_staging_environment(
    mock_pipeline_logger: Mock,
    env_config_staging: Config,
    mock_refresh_config: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_refresh_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_refresh_config.source_restore_instance.restore_backup.return_value = mock_backup
    mock_refresh_config.target_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_refresh_config.target_restore_instance.restore_backup.return_value = mock_backup

    provider = create_instance_provider(
        env_config=env_config_staging,
        cloud_config=mock_refresh_config,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance == cloud_sql_instance_with_ip
    assert result.target_instance == cloud_sql_instance_with_ip

    mock_refresh_config.source_restore_instance.recreate_instance.assert_called_once()
    mock_refresh_config.source_restore_instance.restore_backup.assert_called_once()
    mock_refresh_config.target_restore_instance.recreate_instance.assert_called_once()
    mock_refresh_config.target_restore_instance.restore_backup.assert_called_once()


@patch(f"{PipelineExecutionLogger.__module__}.{PipelineExecutionLogger.__name__}")
def test_create_production_environment(
    mock_pipeline_logger: Mock,
    env_config_production: Config,
    mock_refresh_config: Mock,
    cloud_sql_instance_with_ip: CloudSQLInstance,
    mock_backup: Backup,
) -> None:
    # Arrange
    mock_refresh_config.source_restore_instance.recreate_instance.return_value = cloud_sql_instance_with_ip
    mock_refresh_config.source_restore_instance.restore_backup.return_value = mock_backup
    mock_refresh_config.target_restore_instance.describe_instance.return_value = cloud_sql_instance_with_ip

    provider = create_instance_provider(
        env_config=env_config_production,
        cloud_config=mock_refresh_config,
    )

    # Act
    result = provider.create()

    # Assert
    assert result.source_instance == cloud_sql_instance_with_ip
    assert result.target_instance == cloud_sql_instance_with_ip

    mock_refresh_config.source_restore_instance.recreate_instance.assert_called_once()
    mock_refresh_config.source_restore_instance.restore_backup.assert_called_once()
    mock_refresh_config.target_restore_instance.describe_instance.assert_called_once()
    mock_refresh_config.target_restore_instance.recreate_instance.assert_not_called()
    mock_refresh_config.target_restore_instance.restore_backup.assert_not_called()
