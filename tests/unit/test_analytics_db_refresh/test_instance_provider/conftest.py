from unittest.mock import Mock

import pytest
from config.base_config import RefreshBaseConfig
from refresh_shared.model import DbRefreshConfig

from common.cloud_sql_instance_client.instance_model import (
    Backup,
    CloudSQLInstance,
    IpAddress,
)
from common.config import Config
from common.sql_repository import SqlRepository


@pytest.fixture(scope="session")
def cloud_sql_instance_with_ip() -> CloudSQLInstance:
    return CloudSQLInstance(
        name="test-instance",
        connection_name="test-connection",
        database_version="POSTGRES_15",
        settings=Mock(),
        ip_addresses=[
            IpAddress(ip_address="********", type="PRIVATE"),
        ],
    )


@pytest.fixture(scope="session")
def mock_backup() -> Backup:
    return Mock(spec=Backup, id="backup-123")


@pytest.fixture(scope="session")
def env_config_local() -> Config:
    return Mock(spec=Config, is_local=True, is_staging=False, is_production=False)


@pytest.fixture(scope="session")
def env_config_staging() -> Config:
    return Mock(spec=Config, is_local=False, is_staging=True, is_production=False)


@pytest.fixture(scope="session")
def env_config_production() -> Config:
    return Mock(spec=Config, is_local=False, is_staging=False, is_production=True)


@pytest.fixture(scope="function")
def mock_refresh_config(db_refresh_config: DbRefreshConfig) -> RefreshBaseConfig:
    return Mock(spec=RefreshBaseConfig, refresh_db_config=db_refresh_config)


@pytest.fixture(scope="session")
def mock_sql_repository() -> Mock:
    mock_repo = Mock(spec=SqlRepository)
    mock_repo.select_from_function.return_value = [(1,)]  # Return a tuple with execution_id

    return mock_repo
