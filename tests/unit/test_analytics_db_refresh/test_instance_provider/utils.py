from unittest.mock import Mock

from config.base_config import RefreshBaseConfig
from provider import InstanceProvider

from common.config import Config
from common.sql_repository import SqlRepository
from common.time_service import TimeService


def create_instance_provider(
    env_config: Config,
    local_config: Mock = Mock(spec=RefreshBaseConfig),
    cloud_config: Mock = Mock(spec=RefreshBaseConfig),
    cicd_config: Mock = Mock(spec=RefreshBaseConfig),
) -> InstanceProvider:
    """Helper test method to create InstanceProvider instance."""
    return InstanceProvider(
        env_config=env_config,
        local_config=local_config,
        cloud_config=cloud_config,
        cicd_config=cicd_config,
        time_service=Mock(spec=TimeService),
        pipeline_execution_db_repository=Mock(spec=SqlRepository),
    )
