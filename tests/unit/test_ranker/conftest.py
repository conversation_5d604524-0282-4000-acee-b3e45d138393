from unittest.mock import Mock

import pytest
from ranker_repository import RankerBQRepository

from ranker.ranker import Ranker


@pytest.fixture(scope="function")
def repository() -> Mock:
    mock = Mock(spec=RankerBQRepository)
    mock.DATASET = RankerBQRepository.DATASET
    mock.LOAD_CATEGORY_VIEW_ITEM_CLICKS = RankerBQRepository.LOAD_CATEGORY_VIEW_ITEM_CLICKS
    mock.LOAD_CATEGORY_PAGE_IMPRESSION_AND_CLICKS = RankerBQRepository.LOAD_CATEGORY_PAGE_IMPRESSION_AND_CLICKS
    mock.LOAD_CATEGORY_VIEW_ITEM_CLICKS_PURCHASES = RankerBQRepository.LOAD_CATEGORY_VIEW_ITEM_CLICKS_PURCHASES

    return mock


@pytest.fixture(scope="function")
def ranker(repository: Mock) -> Ranker:
    return Ranker(repository)
