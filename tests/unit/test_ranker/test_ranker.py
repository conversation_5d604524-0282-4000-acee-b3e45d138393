from datetime import date
from typing import Any
from unittest.mock import Mock

import pytest

from ranker.ranker import LoadResult, Ranker


@pytest.mark.parametrize(
    "load_date",
    [None, 20250101, date.today()],
)
def test_load_when_type_error(ranker: Ranker, load_date: Any) -> None:
    with pytest.raises(TypeError):
        ranker.load(load_date)


@pytest.mark.parametrize(
    "load_date",
    ["", "2025", "01-01-2025", "2025-01-01 00:00:00"],
)
def test_load_when_value_error(ranker: Ranker, load_date: str) -> None:
    with pytest.raises(ValueError):
        ranker.load(load_date)


def test_load_when_valid_date(ranker: Ranker, repository: Mock) -> None:
    load_date = date.today().isoformat()

    # arrange
    repository.load_category_view_item_clicks.return_value = "1 B"
    repository.load_category_page_impression_and_clicks.return_value = "2 B"
    repository.load_category_view_item_clicks_purchases.return_value = "3 B"

    expected = [
        LoadResult(procedure_name=repository.LOAD_CATEGORY_VIEW_ITEM_CLICKS.full_name, bytes_billed="1 B"),
        LoadResult(procedure_name=repository.LOAD_CATEGORY_PAGE_IMPRESSION_AND_CLICKS.full_name, bytes_billed="2 B"),
        LoadResult(procedure_name=repository.LOAD_CATEGORY_VIEW_ITEM_CLICKS_PURCHASES.full_name, bytes_billed="3 B"),
    ]

    # act
    actual = ranker.load(load_date)

    # assert
    assert actual == expected
