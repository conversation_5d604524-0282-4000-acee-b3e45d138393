import pytest
from dates_provider.dates_provider import DatesProvider, InputDates

from common.time_service import TimeService


@pytest.fixture(scope="session")
def dates_provider(time_service: TimeService) -> DatesProvider:
    return DatesProvider(time_service)


def test_get_dates_when_default_input(dates_provider: DatesProvider, time_service: TimeService) -> None:
    # arrange
    dates = InputDates()
    expected = [
        time_service.days_ago(3).isoformat(),
        time_service.days_ago(2).isoformat(),
        time_service.yesterday.isoformat(),
    ]

    # act
    actual = dates_provider.get_dates(dates)

    # assert
    assert actual == expected


@pytest.mark.parametrize(
    "start_date, end_date",
    [
        ("a", "b"),
        ("2025-01-01", "2025-01"),
        ("2025-01", "2025-01-01"),
        ("2025-30-01", "2025-30-03"),
        ("2025-01-30", "2025-01-01"),
        ("2025-03-01 15:29:57", "2025-03-13 15:29:57"),
    ],
)
def test_get_dates_when_invalid_input(dates_provider: DatesProvider, start_date: str, end_date: str) -> None:
    # arrange
    dates = InputDates(start_date, end_date)

    # act and assert
    with pytest.raises(ValueError):
        dates_provider.get_dates(dates)


def test_get_dates_when_valid_input(dates_provider: DatesProvider) -> None:
    # arrange
    start_date = "2025-01-01"
    end_date = "2025-01-05"
    dates = InputDates(start_date, end_date)
    expected = [start_date, "2025-01-02", "2025-01-03", "2025-01-04", end_date]

    # act
    actual = dates_provider.get_dates(dates)

    # assert
    assert actual == expected
