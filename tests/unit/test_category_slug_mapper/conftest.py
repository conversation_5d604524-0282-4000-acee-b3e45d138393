from pathlib import Path
from unittest.mock import Mock

import pytest
from pandas import read_csv

from category_slug_mapper.cs_bq_repository import CategorySlugsBQRepository
from category_slug_mapper.cs_etl import CategorySlugEtl
from category_slug_mapper.cs_model import (
    CATEGORY_SLUGS_MAPPING_SCHEMA,
    CATEGORY_SLUGS_SCHEMA,
    COUNTRIES_SCHEMA,
    CategorySlugsConfig,
)
from category_slug_mapper.cs_pg_repository import CategorySlugsPGRepository
from common.data_lake_repository import DataLakeRepository
from common.pandas_utils import DataFrameWithSchema
from common.pipeline_logging.pipeline_model import PipelineRun
from common.time_service import TimeService

current_dir = Path(__file__).resolve().parent


@pytest.fixture(scope="session")
def data_dir() -> Path:
    return current_dir / "data"


@pytest.fixture(scope="function")
def category_slugs(data_dir: Path) -> DataFrameWithSchema:
    df = read_csv(
        data_dir / "category_slugs.csv",
        # Date parsing
        parse_dates=CATEGORY_SLUGS_SCHEMA.datetime_columns,
        # Data type mapping
        dtype=CATEGORY_SLUGS_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )

    return DataFrameWithSchema(df, CATEGORY_SLUGS_SCHEMA)


@pytest.fixture(scope="function")
def category_slugs_mapping(data_dir: Path) -> DataFrameWithSchema:
    df = read_csv(
        data_dir / "category_slugs_mapping.csv",
        # Date parsing
        parse_dates=CATEGORY_SLUGS_MAPPING_SCHEMA.datetime_columns,
        # Data type mapping
        dtype=CATEGORY_SLUGS_MAPPING_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )

    return DataFrameWithSchema(df, CATEGORY_SLUGS_MAPPING_SCHEMA)


@pytest.fixture(scope="function")
def countries(data_dir: Path) -> DataFrameWithSchema:
    df = read_csv(
        data_dir / "countries.csv",
        # Data type mapping
        dtype=COUNTRIES_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )

    return DataFrameWithSchema(df, COUNTRIES_SCHEMA)


@pytest.fixture(scope="function")
def category_slugs_config(time_service: TimeService) -> CategorySlugsConfig:
    return CategorySlugsConfig(load_date=time_service.today)


@pytest.fixture(scope="function")
def pg_repository(category_slugs: DataFrameWithSchema, countries: DataFrameWithSchema) -> Mock:
    repository = Mock(spec=CategorySlugsPGRepository)
    repository.select_category_slugs.return_value = category_slugs
    repository.select_countries.return_value = countries

    return repository


@pytest.fixture(scope="function")
def bq_repository() -> Mock:
    return Mock(spec=CategorySlugsBQRepository)


@pytest.fixture(scope="function")
def raw_data_lake() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="function")
def category_slugs_etl(
    test_pipeline_run: PipelineRun,
    category_slugs_config: CategorySlugsConfig,
    pg_repository: Mock,
    bq_repository: Mock,
    raw_data_lake: Mock,
) -> CategorySlugEtl:
    return CategorySlugEtl(
        config=category_slugs_config,
        pipeline_run=test_pipeline_run,
        pg_repository=pg_repository,
        bq_repository=bq_repository,
        raw_data_lake=raw_data_lake,
    )
