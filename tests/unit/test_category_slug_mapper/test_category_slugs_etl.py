from unittest.mock import Mock, patch

import pytest

from category_slug_mapper.cs_etl import (
    CategorySlugEtl,
    CategorySlugEtlError,
    Extracted,
    Transformed,
)
from common.pandas_utils import DataFrameWithSchema
from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from tests.utils import assert_data_frame_schema


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_extract(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    category_slugs_etl: CategorySlugEtl,
    pg_repository: Mock,
    category_slugs: DataFrameWithSchema,
    countries: DataFrameWithSchema,
) -> None:
    # act
    actual = category_slugs_etl.extract()
    actual_category_slugs = actual.category_slugs
    actual_countries = actual.countries

    # assert
    pg_repository.create_category_slugs.assert_called_once()
    pg_repository.select_category_slugs.assert_called_once()
    pg_repository.select_countries.assert_called_once()

    assert_data_frame_schema(actual_category_slugs, category_slugs)
    assert_data_frame_schema(actual_countries, countries)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform_when_no_data(
    mock_start_processing_step: Mock, mock_finish_processing_step: Mock, category_slugs_etl: CategorySlugEtl
) -> None:
    extracted = Extracted()

    with pytest.raises(CategorySlugEtlError):
        _ = category_slugs_etl.transform(extracted)

    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform_when_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    category_slugs_etl: CategorySlugEtl,
    category_slugs: DataFrameWithSchema,
    countries: DataFrameWithSchema,
    category_slugs_mapping: DataFrameWithSchema,
) -> None:
    # arrange
    extracted = Extracted(category_slugs=category_slugs, countries=countries)

    # act
    actual = category_slugs_etl.transform(extracted)
    actual_category_slugs_mapping = actual.category_slugs_mapping

    # assert
    assert actual.extracted == extracted
    assert_data_frame_schema(actual_category_slugs_mapping, category_slugs_mapping)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_when_no_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    category_slugs_etl: CategorySlugEtl,
    raw_data_lake: Mock,
    bq_repository: Mock,
) -> None:
    # arrange
    transformed = Transformed()
    raw_data_lake.upload_table_partition.return_value = None

    # act & assert
    with pytest.raises(CategorySlugEtlError):
        _ = category_slugs_etl.load(transformed)

    raw_data_lake.upload_table_partition.assert_called_once()
    bq_repository.load_from_raw.assert_not_called()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_when_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    category_slugs_etl: CategorySlugEtl,
    category_slugs: DataFrameWithSchema,
    countries: DataFrameWithSchema,
    category_slugs_mapping: DataFrameWithSchema,
    raw_data_lake: Mock,
    bq_repository: Mock,
) -> None:
    # arrange
    expected_rows = len(category_slugs_mapping.dataframe)
    transformed = Transformed(
        extracted=Extracted(category_slugs=category_slugs, countries=countries),
        category_slugs_mapping=category_slugs_mapping,
    )

    # act
    actual_rows = category_slugs_etl.load(transformed)

    # assert
    assert actual_rows == expected_rows

    raw_data_lake.upload_table_partition.assert_called()
    assert raw_data_lake.upload_table_partition.call_count == 2

    bq_repository.load_from_raw.assert_called_once()
    bq_repository.check_table.assert_called_once()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
