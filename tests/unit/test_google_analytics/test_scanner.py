from dataclasses import replace
from datetime import datetime, timedelta
from unittest.mock import Mock, call

import pytest

from common.bq_repository import BQTablePartition, BQTablePartitionStatus, PartitionType
from google_analytics.scanner import PurchasesAndClicks<PERSON>canner


def test_detect_stale_partitions_when_first_run(
    scanner: PurchasesAndClicksScanner,
    pac_repository: Mock,
    pac_partitions: Mock,
) -> None:
    # act
    result = scanner.detect_stale_partitions()

    # assert
    assert result == pac_partitions
    pac_repository.get_source_partition.assert_not_called()


@pytest.mark.parametrize("has_status", [False, True])
def test_detect_stale_partitions_when_no_changes_since_last_run(
    scanner: PurchasesAndClicksScanner,
    pac_repository: Mock,
    pac_partitions: Mock,
    pac_partition_statuses: dict[PartitionType, BQTablePartitionStatus],
    now: datetime,
    has_status: bool,
) -> None:
    # arrange
    pac_repository.get_target_partitions.return_value = pac_partition_statuses
    partition_dates = list(pac_partition_statuses.keys())

    # `query_partition_status` can be either `None` or same status as in the last load
    p1 = pac_partition_statuses[partition_dates[0]]
    pac_repository.get_source_partition.return_value = p1 if has_status else None
    expected_calls = [call(partition) for partition in partition_dates]

    # act
    result = scanner.detect_stale_partitions()

    # assert
    assert result == []
    pac_repository.get_source_partition.assert_has_calls(expected_calls, any_order=True)


def test_detect_stale_partitions_when_one_partition_changed(
    scanner: PurchasesAndClicksScanner,
    pac_repository: Mock,
    pac_partitions: list[BQTablePartition],
    pac_partition_statuses: dict[PartitionType, BQTablePartitionStatus],
    now: datetime,
) -> None:
    # arrange
    pac_repository.get_target_partitions.return_value = pac_partition_statuses
    partition_dates = list(pac_partition_statuses.keys())

    # `query_partition_status` is more recent for one partition
    p1 = pac_partition_statuses[partition_dates[0]]
    fresh_partition: BQTablePartitionStatus = replace(p1, last_modified=now + timedelta(seconds=1))
    pac_repository.get_source_partition.side_effect = lambda p: (fresh_partition if p == p1.partition else None)

    expected_partitions = [fresh_partition.as_table_partition()]
    expected_calls = [call(partition) for partition in partition_dates]

    # act
    result = scanner.detect_stale_partitions()

    # assert
    assert result == expected_partitions
    pac_repository.get_source_partition.assert_has_calls(expected_calls, any_order=True)


def test_detect_stale_partitions_when_all_partitions_changed(
    scanner: PurchasesAndClicksScanner,
    pac_repository: Mock,
    pac_partitions: list[BQTablePartition],
    pac_partition_statuses: dict[PartitionType, BQTablePartitionStatus],
    now: datetime,
) -> None:
    # arrange
    pac_repository.get_target_partitions.return_value = pac_partition_statuses
    partition_dates = list(pac_partition_statuses.keys())

    # `query_partition_status` is more recent for all partitions
    fresh_partitions: list[BQTablePartitionStatus] = [
        replace(p, last_modified=now + timedelta(seconds=1)) for p in pac_partition_statuses.values()
    ]
    pac_repository.get_source_partition.side_effect = fresh_partitions

    expected_partitions = [p.as_table_partition() for p in fresh_partitions]
    expected_calls = [call(partition) for partition in partition_dates]

    # act
    result = scanner.detect_stale_partitions()

    # assert
    assert result == expected_partitions
    pac_repository.get_source_partition.assert_has_calls(expected_calls, any_order=True)


def test_load_stale_partitions_when_no_partitions(scanner: PurchasesAndClicksScanner, pac_repository: Mock) -> None:
    # act
    scanner.load_stale_partitions([])

    # assert
    pac_repository.load_partition.assert_not_called()


def test_load_stale_partitions_when_partitions(
    scanner: PurchasesAndClicksScanner, pac_repository: Mock, pac_partitions: list[BQTablePartition]
) -> None:
    # arrange
    expected_calls = [call(p.partition) for p in pac_partitions]

    # act
    scanner.load_stale_partitions(pac_partitions)

    # assert
    pac_repository.load_partition.assert_has_calls(expected_calls, any_order=True)
