from datetime import date, datetime
from unittest.mock import Mock

import pytest

from common.bq_repository import BQTablePartition, BQTablePartitionStatus, PartitionType
from common.time_service import TimeService
from google_analytics.repository import GAPurchasesAndClicksRepository
from google_analytics.scanner import (
    PurchasesAndClicksScanner,
    PurchasesAndClicksScannerConfig,
)


@pytest.fixture(scope="session")
def scanner_config() -> PurchasesAndClicksScannerConfig:
    return PurchasesAndClicksScannerConfig()


@pytest.fixture(scope="session")
def reload_days(time_service: TimeService, scanner_config: PurchasesAndClicksScannerConfig) -> list[date]:
    days = time_service.last_days(scanner_config.reload_days)
    return list(days)


@pytest.fixture(scope="session")
def pac_partitions(reload_days: list[date]) -> list[BQTablePartition]:
    return [GAPurchasesAndClicksRepository.get_table_partition(d) for d in reload_days]


@pytest.fixture(scope="session")
def pac_partition_statuses(
    pac_partitions: list[BQTablePartition], now: datetime
) -> dict[PartitionType, BQTablePartitionStatus]:
    return {
        p.partition: BQTablePartitionStatus(
            dataset=p.dataset, table=p.table, partition=p.partition, last_modified=now, total_rows=100
        )
        for p in pac_partitions
    }


@pytest.fixture(scope="session")
def pac_repository() -> Mock:
    mock = Mock(spec=GAPurchasesAndClicksRepository)
    mock.get_table = GAPurchasesAndClicksRepository.get_table
    mock.get_table_partition = GAPurchasesAndClicksRepository.get_table_partition
    mock.get_target_partitions.return_value = {}
    mock.get_source_partition.return_value = None

    return mock


@pytest.fixture(scope="session")
def scanner(
    scanner_config: PurchasesAndClicksScannerConfig,
    time_service: TimeService,
    pac_repository: Mock,
) -> PurchasesAndClicksScanner:
    return PurchasesAndClicksScanner(
        config=scanner_config,
        time_service=time_service,
        pac_repository=pac_repository,
    )
