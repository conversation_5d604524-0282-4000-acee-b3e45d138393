from datetime import datetime
from pathlib import Path
from typing import Callable
from unittest.mock import Mock
from zipfile import ZipFile

import pytest
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig, ListingMessage, Source
from bm_shared.bm_schema import BMMerchantColumns, BMProductColumns, ListColumns
from listing_worker.etl import ListingWorkerEtl, ListingWorkerInput
from pandas import DataFrame, read_csv, to_datetime

TEST_URL1 = "https://dummygreatphones.pl/"
current_dir = Path(__file__).resolve().parent
data_dir = current_dir / "data"


@pytest.fixture(scope="session")
def bm_worker_config() -> ListingConfig:
    return ListingConfig()


@pytest.fixture(scope="session")
def worker_global_input() -> ListingWorkerInput:
    return ListingWorkerInput(Mock(spec_set=BMDataLakeRepository))


@pytest.fixture(scope="function")
def bm_worker_etl(
    bm_worker_config: ListingConfig,
    run_id: datetime,
    oxy_client_mock: Mock,
    algolia_client_mock: Mock,
    publisher_client_mock: Mock,
    data_lake_repo_mock: Mock,
    worker_global_input: ListingWorkerInput,
    merchant_page_url: str,
) -> ListingWorkerEtl:
    return ListingWorkerEtl(
        bm_worker_config,
        run_id,
        oxy_client_mock,
        algolia_client_mock,
        publisher_client_mock,
        data_lake_repo_mock,
        data_lake_repo_mock,
        worker_global_input,
        ListingMessage(merchant_page_url, Source.WORKER, run_id.second),
    )


def get_file_content(file_path: Path) -> str:
    with open(file_path, "r") as f:
        return f.read()


@pytest.fixture(scope="session")
def merchant_page1() -> str:
    input_zip = ZipFile(data_dir / "merchant_page1.zip")
    return input_zip.read("merchant_page1.html").decode("utf-8")


@pytest.fixture(scope="session")
def merchant_page2() -> str:
    input_zip = ZipFile(data_dir / "merchant_page2.zip")
    return input_zip.read("asmart_world_merchant.html").decode("utf-8")


@pytest.fixture(scope="session")
def merchant_page_url() -> str:
    return "https://www.backmarket.de/de-de/s/alloccaz/62f443d7-b22b-417e-838c-344131bd18ab"


@pytest.fixture(scope="session")
def expected_merchant2_products_transformed() -> DataFrame:
    column_types = {
        ListColumns.scrapped_at: "object",
        BMProductColumns.product_name: "object",
        ListColumns.instance_name: "object",
        ListColumns.price: "float64",
        ListColumns.refurbed_product_name: "object",
        BMProductColumns.product_id: "Int32",
        ListColumns.best_product_score: "float64",
        ListColumns.best_merchant_score: "float64",
        ListColumns.refurbed_merchant_name: "object",
        BMMerchantColumns.merchant_id: "Int32",
        ListColumns.country: "object",
        ListColumns.merchant_url: "object",
        ListColumns.merchant_name: "object",
    }
    df = read_csv(data_dir / "merchant2_products_transformed.csv", dtype=column_types)
    df[ListColumns.scrapped_at] = to_datetime(df[ListColumns.scrapped_at])
    return df


@pytest.fixture(scope="session")
def bm_config() -> ListingConfig:
    return ListingConfig()


@pytest.fixture(scope="session")
def empty_html() -> str:
    return ""


@pytest.fixture(scope="session")
def similarity_bm_products() -> DataFrame:
    return read_csv(data_dir / "similarity_bm_products.csv")


@pytest.fixture(scope="session")
def similarity_ref_products() -> DataFrame:
    return read_csv(data_dir / "similarity_ref_products.csv")


@pytest.fixture(scope="session")
def similarity_product_expected() -> DataFrame:
    return read_csv(
        data_dir / "similarity_product_expected.csv",
        dtype={"best_product_score": "float64", "product_id": "Int64"},
    )


@pytest.fixture(scope="session")
def similarity_bm_merchants() -> DataFrame:
    return read_csv(data_dir / "similarity_bm_merchants.csv")


@pytest.fixture(scope="session")
def similarity_ref_merchants() -> DataFrame:
    return read_csv(data_dir / "similarity_ref_merchants.csv")


@pytest.fixture(scope="session")
def similarity_merchant_expected() -> DataFrame:
    return read_csv(
        data_dir / "similarity_merchant_expected.csv",
        dtype={"best_merchant_score": "float64", "merchant_id": "Int64"},
    )


@pytest.fixture(scope="session")
def base_columns() -> DataFrame:
    return read_csv(data_dir / "base_columns.csv")


@pytest.fixture(scope="session")
def base_columns_expected_result() -> DataFrame:
    return read_csv(data_dir / "base_columns_result.csv")


@pytest.fixture(scope="session")
def base_columns_empty() -> DataFrame:
    return read_csv(data_dir / "base_columns_empty.csv", dtype="str")


@pytest.fixture(scope="session")
def base_columns_empty_result() -> DataFrame:
    return read_csv(data_dir / "base_columns_empty_result.csv", dtype="str")


@pytest.fixture(scope="session")
def empty_url() -> str:
    return ""


@pytest.fixture(scope="session")
def merchant_business_name() -> str:
    return "Alloccazz"


@pytest.fixture(scope="session")
def product_count() -> int:
    return 123


@pytest.fixture(scope="session")
def gen_api_payloads() -> Callable[[int], list[str]]:
    def gen_payloads(num: int) -> list[str]:
        return [f'{{"key{i}":"value{i}"}}' for i in range(num)]

    return gen_payloads


@pytest.fixture(scope="session")
def test_file() -> Callable[[str], str]:
    def get_content(file_name: str) -> str:
        return get_file_content(data_dir / file_name)

    return get_content
