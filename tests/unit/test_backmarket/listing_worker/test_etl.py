from datetime import datetime
from typing import Callable
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, call

import pytest
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig
from bm_shared.bm_utils import url_to_filename
from bm_shared.listing_worker_model import ListingWorkerData, MerchantPage
from pandas import DataFrame
from pandas.testing import assert_frame_equal

from backmarket.listing_worker.etl import ListingWorkerEtl, ListingWorkerInput


def test_worker_global_input(bm_config: ListingConfig) -> None:
    # arrange
    raw_data_repository = Mock(spec_set=BMDataLakeRepository)
    read_prefixed_data_frame = MagicMock()
    raw_data_repository.read_prefixed_data_frame = read_prefixed_data_frame
    input = ListingWorkerInput(raw_data_repository)
    initial_run_id = datetime(2024, 5, 24, 16, 0, 0, 0)
    expected_initial_calls = (
        call(bm_config.products_file_name, bm_config.raw_product_path, initial_run_id),
        call(bm_config.merchants_file_name, bm_config.raw_merchant_path, initial_run_id),
    )
    another_run_id = datetime(2024, 5, 24, 16, 0, 0, 1)
    expected_more_calls = (
        call(bm_config.products_file_name, bm_config.raw_product_path, another_run_id),
        call(bm_config.merchants_file_name, bm_config.raw_merchant_path, another_run_id),
    )

    # initial call
    # act
    input.cached_data_update(bm_config, initial_run_id)

    # assert
    read_prefixed_data_frame.assert_has_calls(expected_initial_calls)

    # subsequent call
    # act
    input.cached_data_update(bm_config, initial_run_id)

    # assert
    read_prefixed_data_frame.assert_has_calls(expected_initial_calls)

    # new call with new run_id
    # act
    input.cached_data_update(bm_config, another_run_id)

    # assert
    read_prefixed_data_frame.assert_has_calls(expected_initial_calls + expected_more_calls)


@pytest.mark.parametrize("product_count,page_count", [(1, 1), (32, 1), (33, 2)])
def test_get_all_pages_via_api(
    bm_worker_etl: ListingWorkerEtl,
    algolia_client_mock: Mock,
    gen_api_payloads: Callable[[int], list[str]],
    product_count: int,
    page_count: int,
) -> None:

    # arrange
    algolia_client_mock.get_hits.side_effect = gen_api_payloads(page_count)
    merchant = MerchantPage(name="", product_count=product_count, api_key="", id="", app_id="")

    # act
    api_result = bm_worker_etl._get_all_pages_via_api(merchant)

    # assert
    assert api_result == gen_api_payloads(page_count)


def test_extract(
    bm_worker_etl: ListingWorkerEtl,
    oxy_client_mock: Mock,
    algolia_client_mock: Mock,
    merchant_page2: str,
    merchant_page_url: str,
    test_file: Callable[[str], str],
) -> None:
    # arrange
    response1 = test_file("api_response_merchant2_page1.json")
    response2 = test_file("api_response_merchant2_page2.json")
    oxy_client_mock.get_page.side_effect = [merchant_page2]
    algolia_client_mock.get_hits.side_effect = [response1, response2]
    bm_worker_etl._save_raw = Mock()
    html_base_name = f"{url_to_filename(merchant_page_url).name}.html"
    response_name1 = f"{url_to_filename(merchant_page_url).name}_page0.json"
    response_name2 = f"{url_to_filename(merchant_page_url).name}_page1.json"

    # act
    extracted = bm_worker_etl.extract()

    # assert
    assert bm_worker_etl._save_raw.mock_calls == [
        call(merchant_page2, html_base_name),
        call(response1, response_name1),
        call(response2, response_name2),
    ]
    assert extracted.pages_from_api == [response1, response2]


def test_transform(
    bm_worker_etl: ListingWorkerEtl,
    merchant_business_name: str,
    test_file: Callable[[str], str],
    expected_merchant2_products_transformed: DataFrame,
) -> None:
    # arrange
    merchant_data = MerchantPage(name=merchant_business_name)
    worker_data = ListingWorkerData(
        merchant_data,
        pages_from_api=[test_file("api_response_merchant2_page1.json"), test_file("api_response_merchant2_page2.json")],
    )

    # act
    df = bm_worker_etl.transform(worker_data)

    # assert
    assert_frame_equal(
        df.reset_index(drop=True),
        expected_merchant2_products_transformed.reset_index(drop=True),
        check_dtype=False,
    )
