from contextlib import nullcontext
from typing import ContextManager

import pandas as pd
import pytest
from bm_shared.bm_model import ListingConfig
from bm_shared.listing_worker_model import (
    NoAppId,
    NoLanguage,
    NoMerchantId,
    NoMerchantName,
    NoProductCount,
)
from listing_worker.bm_worker_transformer import (
    calculate_merchant_similarity,
    calculate_product_similarity,
    extract_app_id,
    extract_lang,
    extract_merchant_id,
    extract_merchant_name,
    extract_merchant_params,
    extract_product_count,
    parse_html,
    set_base_columns,
)
from pytest import FixtureRequest


def test_product_similarity(
    similarity_bm_products: pd.DataFrame,
    similarity_ref_products: pd.DataFrame,
    similarity_product_expected: pd.DataFrame,
) -> None:
    # act
    actual = calculate_product_similarity(similarity_bm_products, similarity_ref_products)

    # assert
    pd.testing.assert_frame_equal(actual, similarity_product_expected)


def test_merchant_similarity(
    similarity_bm_merchants: pd.DataFrame,
    similarity_ref_merchants: pd.DataFrame,
    similarity_merchant_expected: pd.DataFrame,
) -> None:
    # act
    actual = calculate_merchant_similarity(similarity_bm_merchants, similarity_ref_merchants)

    # assert
    pd.testing.assert_frame_equal(actual, similarity_merchant_expected)


@pytest.mark.parametrize(
    "base_columns_input,url,expected",
    [
        ("base_columns", "merchant_page_url", "base_columns_expected_result"),
        ("base_columns_empty", "empty_url", "base_columns_empty_result"),
    ],
)
def test_set_base_columns(
    base_columns_input: str,
    url: str,
    merchant_business_name: str,
    expected: str,
    request: FixtureRequest,
    bm_worker_config: ListingConfig,
) -> None:
    # arrange
    base_columns_df = request.getfixturevalue(base_columns_input)
    url = request.getfixturevalue(url)
    expected_df = request.getfixturevalue(expected)

    # act
    actual = set_base_columns(base_columns_df, url, bm_worker_config, merchant_business_name)

    # assert
    pd.testing.assert_frame_equal(actual.fillna(""), expected_df.fillna(""))


@pytest.mark.parametrize(
    "page_html,expected_value",
    [
        ("merchant_page1", nullcontext("PLUSDEPC")),
        ("empty_html", pytest.raises(NoMerchantName)),
    ],
)
def test_extract_merchant_name(
    page_html: str,
    expected_value: ContextManager[Exception],
    request: FixtureRequest,
) -> None:
    with expected_value as expected:
        assert extract_merchant_name(parse_html(request.getfixturevalue(page_html))) == expected


@pytest.mark.parametrize(
    "page_html,expected_value",
    [
        ("merchant_page1", nullcontext(227)),
        ("empty_html", pytest.raises(NoProductCount)),
    ],
)
def test_extract_product_count(
    page_html: str,
    expected_value: ContextManager[Exception],
    request: FixtureRequest,
) -> None:
    with expected_value as expected:
        assert extract_product_count(parse_html(request.getfixturevalue(page_html))) == expected


@pytest.mark.parametrize(
    "page_html,expected_value",
    [
        ("merchant_page1", nullcontext("9X8ZUDUNN9")),
        ("empty_html", pytest.raises(NoAppId)),
    ],
)
def test_extract_app_id(
    page_html: str,
    expected_value: ContextManager[Exception],
    request: FixtureRequest,
) -> None:
    with expected_value as expected:
        assert extract_app_id(parse_html(request.getfixturevalue(page_html))) == expected


@pytest.mark.parametrize(
    "page_html,expected_value",
    [("merchant_page1", nullcontext("merchant_id:82")), ("empty_html", pytest.raises(NoMerchantId))],
)
def test_extract_merchant_id(
    page_html: str,
    expected_value: ContextManager[Exception],
    request: FixtureRequest,
) -> None:
    with expected_value as e:
        assert extract_merchant_id(parse_html(request.getfixturevalue(page_html))) == e


@pytest.mark.parametrize(
    "page_html,expected_value",
    [
        ("merchant_page1", nullcontext("de-de")),
        ("empty_html", pytest.raises(NoLanguage)),
    ],
)
def test_extract_lang(
    page_html: str,
    expected_value: ContextManager[Exception],
    request: FixtureRequest,
) -> None:
    with expected_value as expected:
        assert extract_lang(parse_html(request.getfixturevalue(page_html))) == expected


def test_extract_merchant_params(
    empty_html: str,
    product_count: int,
    merchant_business_name: str,
    merchant_id_filter: str,
    api_key: str,
    app_id: str,
    lang: str,
    monkeypatch: pytest.MonkeyPatch,
) -> None:
    # arrange
    monkeypatch.setattr("listing_worker.bm_worker_transformer.extract_product_count", lambda _: product_count)
    monkeypatch.setattr("listing_worker.bm_worker_transformer.extract_merchant_name", lambda _: merchant_business_name)
    monkeypatch.setattr("listing_worker.bm_worker_transformer.extract_merchant_id", lambda _: merchant_id_filter)
    # As I understand this isn't patching the function definition but the usage.
    # This because even if I moved the extract_api_key to bm_shared, that bm_shared path fails here.
    monkeypatch.setattr("listing_worker.bm_worker_transformer.extract_api_key", lambda _: api_key)
    monkeypatch.setattr("listing_worker.bm_worker_transformer.extract_app_id", lambda _: app_id)
    monkeypatch.setattr("listing_worker.bm_worker_transformer.extract_lang", lambda _: lang)

    # act
    parsed_page = parse_html(empty_html)
    merchant = extract_merchant_params(parsed_page)

    # assert
    assert merchant.product_count == product_count
    assert merchant.name == merchant_business_name
    assert merchant.id == merchant_id_filter
    assert merchant.app_id == app_id
    assert merchant.api_key == api_key
    assert merchant.language == lang
