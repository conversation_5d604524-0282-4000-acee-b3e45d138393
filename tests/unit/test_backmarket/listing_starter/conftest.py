from datetime import datetime
from functools import cached_property
from pathlib import Path
from unittest.mock import Mock

import pytest
from bm_shared.bm_model import ListingConfig
from bm_shared.oxy_http_client import OxyHTTPClient
from pandas import DataFrame

from backmarket.listing_starter.etl import ExtractedType, ListingStarterEtl

current_dir = Path(__file__).resolve().parent
data_dir = current_dir / "data"
data_files = [f for f in data_dir.rglob("*") if f.is_file()]

SITEMAP_URL1 = "https://dummygreatphones.pl/sitemap.xml"
SITEMAP_URL2 = "https://dummygreatphones.at/sitemap.xml"


def get_file_content(file_name: str) -> str:
    with open(data_dir / file_name, "r") as f:
        return f.read()


def get_file_as_list(file_name: str) -> list[str]:
    return get_file_content(file_name).splitlines()


@pytest.fixture(scope="session")
def sitemap_url1() -> str:
    return SITEMAP_URL1


@pytest.fixture(scope="session")
def sitemap_url2() -> str:
    return SITEMAP_URL2


class BMListingTestConfig(ListingConfig):
    country1 = "pl"
    country2 = "at"

    @cached_property
    def sitemaps(self) -> dict[str, str]:
        return {self.country1: SITEMAP_URL1, self.country2: SITEMAP_URL2}


@pytest.fixture(scope="session")
def bm_listing_config() -> BMListingTestConfig:
    return BMListingTestConfig()


@pytest.fixture(scope="function")
def bm_starter_etl(
    bm_listing_config: BMListingTestConfig,
    run_id: datetime,
    oxy_client: OxyHTTPClient,
    publisher_client_mock: Mock,
    sql_repo_mock: Mock,
    data_lake_repo_mock: Mock,
) -> ListingStarterEtl:
    etl = ListingStarterEtl(
        bm_listing_config,
        run_id,
        oxy_client,
        publisher_client_mock,
        data_lake_repo_mock,
        data_lake_repo_mock,
        sql_repo_mock,
    )
    return etl


@pytest.fixture(scope="session")
def urls_all() -> list[str]:
    return get_file_as_list("input/URLS_ALL.txt")


@pytest.fixture(scope="session")
def extracted_sitemaps(sitemap1_ok: str, sitemap2_ok: str) -> ExtractedType:
    not_tested_df = DataFrame()
    extracted = ExtractedType([sitemap1_ok, sitemap2_ok], [], not_tested_df, not_tested_df)
    return extracted


@pytest.fixture(scope="session")
def expected_filtered_urls() -> list[str]:
    return get_file_as_list("expected/URLS_FILTERED.txt")


@pytest.fixture(scope="session")
def sitemap1_ok() -> str:
    return get_file_content("input/SITEMAP1_OK.xml")


@pytest.fixture(scope="session")
def sitemap2_ok() -> str:
    return get_file_content("input/SITEMAP2_OK.xml")


@pytest.fixture(scope="session")
def sitemap1_ok_urls() -> list[str]:
    return get_file_as_list("expected/SITEMAP1_OK_URLS.txt")


@pytest.fixture(scope="session")
def sitemap_invalid_loc_tag() -> str:
    return get_file_content("input/SITEMAP_INVALID_LOC_TAG.xml")


@pytest.fixture(scope="session")
def sitemap_invalid_loc_tag_urls() -> list[str]:
    return get_file_as_list("expected/SITEMAP_INVALID_LOC_TAG_URLS.txt")


@pytest.fixture(scope="session")
def sitemap_partial() -> str:
    return get_file_content("input/SITEMAP_PARTIAL.xml")


@pytest.fixture(scope="session")
def sitemap_empty() -> str:
    return get_file_content("input/SITEMAP_EMPTY.xml")
