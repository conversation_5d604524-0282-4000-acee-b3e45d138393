from datetime import datetime
from typing import Callable
from unittest.mock import Mock

import pytest
import responses
from bm_shared.bm_model import ListingMessage, Source
from bm_shared.oxy_http_client import OxyHTTPClient
from requests import ConnectionError

from backmarket.listing_starter.etl import ExtractedType, ListingStarterEtl


@responses.activate
def test_extract_ok(
    bm_starter_etl: ListingStarterEtl,
    oxy_endpoint: str,
    sitemap1_ok: str,
    sitemap2_ok: str,
    sitemap_url1: str,
    sitemap_url2: str,
    oxy_client: OxyHTTPClient,
    oxy_payload: Callable[[str, str, int], str],
) -> None:
    """Test happy path case for extract"""
    # arrange
    expected_sitemaps = [sitemap1_ok, sitemap2_ok]
    responses.post(
        oxy_endpoint,
        body=oxy_payload(sitemap1_ok, "https://example.com", 200),
        match=[responses.matchers.json_params_matcher(oxy_client.get_features(sitemap_url1))],
    )
    responses.post(
        oxy_endpoint,
        body=oxy_payload(sitemap2_ok, "https://example.com", 200),
        match=[responses.matchers.json_params_matcher(oxy_client.get_features(sitemap_url2))],
    )

    # act
    actual = bm_starter_etl.extract()

    # assert
    assert sorted(actual.sitemaps) == sorted(expected_sitemaps)


@responses.activate
def test_extract_http_error(bm_starter_etl: ListingStarterEtl) -> None:
    """Test case: no sitemap present, exit with exception"""
    with pytest.raises(ConnectionError):
        bm_starter_etl.extract()


def test_transform(
    bm_starter_etl: ListingStarterEtl,
    expected_filtered_urls: list[str],
    extracted_sitemaps: ExtractedType,
) -> None:
    """Test happy path case for transformation"""

    # act
    actual = sorted(bm_starter_etl.transform(extracted_sitemaps))

    # assert
    assert actual == sorted(expected_filtered_urls)


def test_load(bm_starter_etl: ListingStarterEtl, run_id: datetime, expected_filtered_urls: list[str]) -> None:
    # arrange
    etl = bm_starter_etl
    # skip test when maximum urls limited to 1 for manual tests
    if etl._config.max_urls_to_scrap < len(expected_filtered_urls):
        pytest.skip(f"test skipped when {etl._config.max_urls_to_scrap=}")
    expected_args = [
        (ListingMessage(url, Source.STARTER, run_id.timestamp()).to_json(),) for url in expected_filtered_urls
    ]
    expected_url_count = len(expected_filtered_urls)
    mocked_publish = Mock()
    etl.publish_message = mocked_publish

    # act
    actual_loaded_number = etl.load(expected_filtered_urls)

    # assert
    actual_args = [call.args for call in mocked_publish.call_args_list]
    assert actual_args == expected_args
    assert actual_loaded_number == expected_url_count
