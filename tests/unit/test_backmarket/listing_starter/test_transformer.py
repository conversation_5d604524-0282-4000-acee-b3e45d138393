import pytest
from bm_shared.bm_model import ListingConfig
from pytest import FixtureRequest

from backmarket.listing_starter.bm_starter_transformer import (
    extract_urls,
    filter_merchant_urls,
)


def test_filter_url(urls_all: list[str], expected_filtered_urls: list[str]) -> None:
    actual = filter_merchant_urls(urls_all, ListingConfig.merchant_pattern)
    assert actual == expected_filtered_urls


@pytest.mark.parametrize(
    "sitemap_file,urls_expected",
    [
        ("sitemap1_ok", "sitemap1_ok_urls"),
        ("sitemap_invalid_loc_tag", "sitemap_invalid_loc_tag_urls"),
    ],
)
def test_extract_urls_ok(sitemap_file: str, urls_expected: str, request: FixtureRequest) -> None:
    actual = extract_urls(request.getfixturevalue(sitemap_file))
    expected = request.getfixturevalue(urls_expected)
    assert actual == expected


@pytest.mark.parametrize(
    "sitemap_file",
    [
        "sitemap_empty",
        "sitemap_partial",
    ],
)
def test_extract_urls_error(sitemap_file: str, request: FixtureRequest) -> None:
    with pytest.raises(Exception):
        extract_urls(request.getfixturevalue(sitemap_file))
