from json.decoder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Callable
from unittest.mock import Mock

import pytest
from api_worker.api_worker_etl import B<PERSON><PERSON><PERSON>or<PERSON>, ProcessedRequest, RequestTiming
from bm_shared.api import AlgoliaSecret
from bm_shared.bm_model import BMApiConfig
from bm_shared.bm_worker_stats import WorkerStats
from bm_shared.oxy_http_client import AlgoliaHTTP<PERSON>rror, OxyHTTPError
from pandas import DataFrame


@pytest.fixture(scope="function")
def api_worker_etl(
    api_config: BMApiConfig,
    algolia_secret: AlgoliaSecret,
    algolia_client_mock: Mock,
    bm_publisher_mock: Mock,
    bm_subscriber_mock: Mock,
    data_lake_repo_mock: Mock,
) -> BMApiWorker:
    return BMApiWorker(
        api_config,
        algolia_secret,
        algolia_client_mock,
        bm_publisher_mock,
        bm_subscriber_mock,
        data_lake_repo_mock,
        data_lake_repo_mock,
    )


@pytest.fixture(scope="function")
def empty_stats() -> WorkerStats:
    return WorkerStats()


@pytest.fixture(scope="session")
def oxy_error() -> Callable[[int], OxyHTTPError]:
    def get_error(code: int) -> OxyHTTPError:
        return OxyHTTPError(code)

    return get_error


@pytest.fixture(scope="session")
def algolia_error() -> Callable[[int], AlgoliaHTTPError]:
    def get_error(code: int) -> AlgoliaHTTPError:
        return AlgoliaHTTPError(code)

    return get_error


@pytest.fixture(scope="session")
def json_decoder_error() -> JSONDecodeError:
    return JSONDecodeError(msg="", doc="", pos=0)


@pytest.fixture(scope="session")
def connection_error() -> ConnectionError:
    return ConnectionError()


@pytest.fixture(scope="session")
def request_timing() -> RequestTiming:
    return RequestTiming(1, 1, 1, 1, 1, 1)


@pytest.fixture(scope="session")
def processed_request(api_response_df: DataFrame, request_timing: RequestTiming) -> ProcessedRequest:
    return ProcessedRequest(api_response_df, request_timing)
