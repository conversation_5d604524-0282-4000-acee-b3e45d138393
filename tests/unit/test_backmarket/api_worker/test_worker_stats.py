from json.decoder import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r
from typing import Callable

import pytest
from _pytest.logging import LogCaptureFixture
from bm_shared.bm_worker_stats import WorkerStats
from bm_shared.oxy_http_client import AlgoliaHTTP<PERSON>rror, OxyHTTP<PERSON>rror


def test_worker_stats_record_successful_request(empty_stats: WorkerStats) -> None:
    stats = empty_stats.record_successful_request()
    assert stats.total_requests == 1


def test_worker_stats_record_exception_oxy_429(
    empty_stats: WorkerStats, oxy_error: Callable[[int], OxyHTTPError]
) -> None:
    # act
    stats = empty_stats.record_exception(oxy_error(429))

    # assert
    assert stats.oxy_http_429_errors == 1
    assert stats.count_total_errors() == 1


def test_worker_stats_record_exception_oxy_5xx(
    empty_stats: WorkerStats, oxy_error: Callable[[int], OxyHTTPError]
) -> None:
    # act
    stats = empty_stats.record_exception(oxy_error(500))
    stats = stats.record_exception(oxy_error(613))

    # assert
    assert stats.oxy_http_server_errors == 2
    assert stats.count_total_errors() == 2


def test_worker_stats_record_exception_oxy_other(
    empty_stats: WorkerStats, oxy_error: Callable[[int], OxyHTTPError]
) -> None:
    # act
    stats = empty_stats.record_exception(oxy_error(404))

    # assert
    assert stats.other_errors == 1
    assert stats.count_total_errors() == 1


def test_worker_stats_record_exception_algolia(
    empty_stats: WorkerStats, algolia_error: Callable[[int], AlgoliaHTTPError]
) -> None:
    # act
    stats = empty_stats.record_exception(algolia_error(400))

    # assert
    assert stats.algolia_http_errors == 1
    assert stats.count_total_errors() == 1


def test_worker_stats_record_exception_json_decode(
    empty_stats: WorkerStats, json_decoder_error: JSONDecodeError
) -> None:
    # act
    stats = empty_stats.record_exception(json_decoder_error)

    # assert
    assert stats.json_decode_errors == 1
    assert stats.count_total_errors() == 1


def test_worker_stats_record_exception_other(empty_stats: WorkerStats) -> None:
    # act
    stats = empty_stats.record_exception(Exception())

    # assert
    assert stats.other_errors == 1
    assert stats.count_total_errors() == 1


def test_worker_stats_count_total_errors(
    empty_stats: WorkerStats, oxy_error: Callable[[int], OxyHTTPError], algolia_error: Callable[[int], AlgoliaHTTPError]
) -> None:
    # act
    stats = empty_stats.record_exception(oxy_error(429))
    stats = stats.record_exception(algolia_error(400))
    stats = stats.record_exception(Exception())

    # assert
    assert stats.count_total_errors() == 3


def test_worker_stats_count_error_rate(empty_stats: WorkerStats) -> None:
    # act
    stats = empty_stats.record_successful_request()
    stats = stats.record_exception(Exception())

    # assert
    assert stats.count_error_rate() == 0.5


def test_worker_stats_to_str(empty_stats: WorkerStats) -> None:
    # arrange
    expected_str = ", ".join(
        [
            "error_rate=0.5",
            "total_errors=1",
            "total_requests=2",
            "algolia_http_errors=0",
            "other_errors=1",
            "oxy_http_429_errors=0",
            "oxy_http_server_errors=0",
            "network_errors=0",
            "json_decode_errors=0",
        ]
    )

    # act
    stats = empty_stats.record_successful_request()
    stats = stats.record_exception(Exception())

    # assert
    assert stats.to_str() == expected_str


@pytest.mark.parametrize(
    "requests, errors, threshold, expected_log_level",
    [
        (1000, 10, 0.01, "INFO"),
        (1000, 11, 0.01, "ERROR"),
    ],
)
def test_log_summary(
    requests: int,
    errors: int,
    threshold: float,
    expected_log_level: str,
    caplog: LogCaptureFixture,
    empty_stats: WorkerStats,
) -> None:

    # arrange
    stats = empty_stats
    stats.total_requests = requests
    stats.other_errors = errors

    # act
    stats.log_summary(threshold)

    # assert
    assert len(caplog.records) == 1
    assert caplog.records[0].levelname == expected_log_level
