import os
from datetime import datetime
from typing import Optional
from unittest.mock import Mock, patch

import pytest
from api_worker.api_worker_etl import B<PERSON>piWorker, ProcessedRequest
from api_worker_model import RECEIVE_RETRY_ATTEMPTS, REQUEST_RETRY_ATTEMPTS
from bm_shared.api import (
    AlgoliaParsedResponse,
    AlgoliaSecret,
    ApiParams,
    ApiPayload,
    ApiPayloadParams,
)
from bm_shared.bm_model import AlgoliaWorkerMessage, BMApiConfig
from pandas import DataFrame
from pandas.testing import assert_frame_equal

from common.pubsub_client import PubSubMessage, PubSubSubscriber


@pytest.mark.parametrize(
    "execution_id, expected_run_id",
    [
        ("foo", "foo"),
        ("foo-bar", "bar"),
        ("", None),
        (None, None),
    ],
)
def test_run_id(
    execution_id: Optional[str],
    expected_run_id: Optional[str],
    api_worker_etl: BMApiWorker,
) -> None:
    # arrange
    if execution_id is not None:
        os.environ["CLOUD_RUN_EXECUTION"] = execution_id
    else:
        os.environ.pop("CLOUD_RUN_EXECUTION", None)

    # act
    actual = api_worker_etl.run_id

    # assert
    if expected_run_id:
        assert actual == expected_run_id
    else:
        assert len(actual) == 8


def test_api_worker_extract(
    api_worker_etl: BMApiWorker,
    algolia_client_mock: Mock,
    algolia_payload: ApiPayload,
    lang: str,
    algolia_secret: AlgoliaSecret,
    api_response: str,
    algolia_worker_message: AlgoliaWorkerMessage,
) -> None:
    # arrange
    algolia_client_mock.get_hits.return_value = api_response

    # act
    actual = api_worker_etl.extract(algolia_worker_message)

    # assert
    algolia_client_mock.get_hits.assert_called_with(algolia_payload, lang, algolia_secret)

    assert actual == api_response


def test_api_worker_transform(
    api_worker_etl: BMApiWorker,
    api_response_parsed: AlgoliaParsedResponse,
    run_id: datetime,
    api_response_df: DataFrame,
    country: str,
) -> None:
    # act
    actual = api_worker_etl.transform(api_response_parsed.hits, run_id, country)

    # assert
    assert_frame_equal(actual, api_response_df)


def test_api_worker_transform_empy_hits(
    api_worker_etl: BMApiWorker,
    run_id: datetime,
    country: str,
) -> None:
    # act
    actual = api_worker_etl.transform([], run_id, country)

    # assert
    assert actual.empty


def test_api_worker_load(
    api_worker_etl: BMApiWorker,
    api_response_df: DataFrame,
    data_lake_repo_mock: Mock,
    api_config: BMApiConfig,
    run_id: datetime,
) -> None:
    # act
    loaded = api_worker_etl.load(api_response_df)

    # assert
    data_lake_repo_mock.write_prefixed_data_frame.assert_called_once_with(
        api_response_df,
        f"{api_worker_etl.run_id}_bm_products.parquet",
        api_config.transformed_path,
        run_id,
    )
    assert loaded > 0


def test_api_worker_load_empty_result(
    api_worker_etl: BMApiWorker,
    empty_response_df: DataFrame,
    algolia_worker_messages: list[AlgoliaWorkerMessage],
    data_lake_repo_mock: Mock,
    bm_publisher_mock: Mock,
    algolia_worker_message: AlgoliaWorkerMessage,
) -> None:
    # act
    loaded = api_worker_etl.load(empty_response_df)

    # assert
    assert not data_lake_repo_mock.write_prefixed_data_frame.called
    assert loaded == 0


def test_api_get_first_page_subsequent_requests(
    api_worker_etl: BMApiWorker,
    total_hits: int,
    algolia_worker_message: AlgoliaWorkerMessage,
) -> None:
    # act
    actual_requests = api_worker_etl._get_first_page_subsequent_requests(algolia_worker_message, total_hits, 0)

    # assert
    assert (len(actual_requests) + 1) * ApiParams.PRODUCTS_PER_PAGE >= total_hits
    assert len(actual_requests) * ApiParams.PRODUCTS_PER_PAGE < total_hits
    for index, request in enumerate(actual_requests):
        assert isinstance(request, AlgoliaWorkerMessage)
        assert index + 1 == request.page
        assert index + 1 == request.payload[ApiPayloadParams.PAGE]


@pytest.mark.parametrize("page_number", [2, 3, 30])
def test_api_get_first_page_subsequent_requests_page_2_or_higher(
    api_worker_etl: BMApiWorker, total_hits: int, page_number: int, algolia_worker_message: AlgoliaWorkerMessage
) -> None:
    # act
    actual_requests = api_worker_etl._get_first_page_subsequent_requests(
        algolia_worker_message, total_hits, page_number
    )

    # assert
    assert len(actual_requests) == 0


@patch.object(BMApiWorker, "process_request")
def test_api_worker_run_no_messages(
    process_request: Mock,
    api_worker_etl: BMApiWorker,
    bm_subscriber_mock: PubSubSubscriber,
    processed_request: ProcessedRequest,
) -> None:
    # arrange
    bm_subscriber_mock.receive_messages.return_value = []
    process_request.return_value = processed_request

    # act
    processed = api_worker_etl.run()

    # assert
    assert processed.total_requests == 0


@patch.object(BMApiWorker, "process_request")
@patch.object(BMApiWorker, "load")
def test_api_worker_run_finish_queue(
    load: Mock,
    process_request: Mock,
    api_worker_etl: BMApiWorker,
    bm_subscriber_mock: Mock,
    bm_pubsub_message: PubSubMessage,
    processed_request: ProcessedRequest,
) -> None:
    # arrange
    bm_subscriber_mock.receive_messages.side_effect = [[bm_pubsub_message], [], [], []]
    process_request.return_value = processed_request

    # act
    processed = api_worker_etl.run()

    # assert
    process_request.assert_called_once()
    bm_subscriber_mock.receive_messages.call_count = RECEIVE_RETRY_ATTEMPTS
    bm_subscriber_mock.acknowledge_messages.assert_called_once()
    load.assert_called_once()
    assert load.call_args.args[0].equals(processed_request.transformed)
    assert processed.total_requests == 1


@patch.object(BMApiWorker, "extract")
@patch.object(BMApiWorker, "_load_extracted")
@patch.object(BMApiWorker, "transform")
@patch.object(BMApiWorker, "_get_first_page_subsequent_requests")
@patch.object(BMApiWorker, "republish_requests")
def test_api_worker_process_request(
    republish_requests: Mock,
    _get_first_page_subsequent_requests: Mock,
    transform: Mock,
    _load_extracted: Mock,
    extract: Mock,
    api_worker_etl: BMApiWorker,
    api_response: str,
    api_response_parsed: AlgoliaParsedResponse,
    algolia_worker_message: AlgoliaWorkerMessage,
    country: str,
) -> None:
    # arrange
    extract.return_value = api_response
    transformed = DataFrame()
    transform.return_value = transformed
    _get_first_page_subsequent_requests.return_value = [algolia_worker_message]

    # act
    api_worker_etl.process_request(algolia_worker_message)

    # assert
    extract.assert_called_once_with(algolia_worker_message)
    _load_extracted.assert_called_once_with(algolia_worker_message, api_response)
    transform.assert_called_once_with(api_response_parsed.hits, algolia_worker_message.run_id, country)
    republish_requests.assert_called_once_with([algolia_worker_message])


@patch.object(BMApiWorker, "process_request")
@patch("tenacity.nap.time.sleep", Mock())
def test_api_worker_run_single_retry(
    process_request: Mock,
    api_worker_etl: BMApiWorker,
    bm_subscriber_mock: Mock,
    bm_pubsub_message: PubSubMessage,
    processed_request: ProcessedRequest,
) -> None:
    """Test scenario: 2 messages in the input queue, 1 retry for first message"""
    # arrange
    messages = [bm_pubsub_message, bm_pubsub_message]
    bm_subscriber_mock.receive_messages.side_effect = [messages, [], [], []]
    process_request.side_effect = [Exception(), processed_request, processed_request]

    # act
    processed = api_worker_etl.run()

    # assert
    assert process_request.call_count == len(messages) + 1  # one retry
    bm_subscriber_mock.acknowledge_messages.assert_called_once()
    assert processed.total_requests == len(messages)
    assert processed.count_total_errors() == 0


@patch.object(BMApiWorker, "process_request")
@patch("tenacity.nap.time.sleep", Mock())
def test_api_worker_run_retry_and_fail(
    process_request: Mock,
    api_worker_etl: BMApiWorker,
    bm_subscriber_mock: Mock,
    bm_pubsub_message: PubSubMessage,
    processed_request: ProcessedRequest,
) -> None:
    """Test scenario: 2 messages in the input queue, 3 retries and exception for first message"""

    # arrange
    messages = [bm_pubsub_message, bm_pubsub_message]
    bm_subscriber_mock.receive_messages.side_effect = [messages, [], [], []]
    process_request.side_effect = [Exception(), Exception(), Exception(), processed_request]
    # act
    processed = api_worker_etl.run()

    # assert
    assert process_request.call_count == REQUEST_RETRY_ATTEMPTS + 1
    bm_subscriber_mock.acknowledge_messages.assert_called_once()
    assert processed.total_requests == len(messages)
    assert processed.count_total_errors() == 1
