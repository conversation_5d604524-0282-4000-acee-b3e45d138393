from unittest.mock import patch

from bm_shared.api import ApiGrade
from bm_shared.bm_model import BMApiConfig
from product_starter_etl import BMProductStarterEtl, ConfigGroups

from common.pubsub_client import PubSubMessage, PubSubSubscriber


def test_product_starter_run(
    api_product_starter_etl: BMProductStarterEtl, api_product_filters: list[str], api_config: BMApiConfig
) -> None:
    # arrange
    expected_calls_count = len(api_product_filters) * len(api_config.locales) * ApiGrade.GRADES_PER_LOCALE

    # act
    with patch.object(BMProductStarterEtl, "active_filters", return_value=api_product_filters) as _:
        results = api_product_starter_etl.run()

    # assert
    assert results.calls_published == expected_calls_count
    assert results.run_hour == api_product_starter_etl.run_hour


def test_product_starter_acknowledge_all_messages_empty(
    api_product_starter_etl: BMProductStarterEtl,
    bm_subscriber_mock: PubSubSubscriber,
) -> None:
    # arrange
    bm_subscriber_mock.receive_messages.return_value = []

    # act
    messages_count = api_product_starter_etl.acknowledge_all_messages()

    # assert
    assert messages_count == 0


def test_product_starter_acknowledge_all_messages_not_empty(
    api_product_starter_etl: BMProductStarterEtl,
    bm_subscriber_mock: PubSubSubscriber,
    bm_pubsub_message: PubSubMessage,
) -> None:
    # arrange
    bm_subscriber_mock.receive_messages.side_effect = [[bm_pubsub_message], []]

    # act
    messages_count = api_product_starter_etl.acknowledge_all_messages()

    # assert
    assert messages_count == 1


def test_active_filters(
    api_product_starter_etl: BMProductStarterEtl,
    filter_config: str,
    odd_hour_filter: str,
    even_hour_filter: str,
) -> None:
    # act
    groups = ConfigGroups.from_yaml(filter_config)
    filters = api_product_starter_etl.active_filters(groups)

    # assert
    assert odd_hour_filter in filters
    assert even_hour_filter not in filters


def test_filter_group_from_yaml(
    filter_config: str, odd_hour_filter: str, even_hour_filter: str, odd_hour_schedule: str, even_hour_schedule: str
) -> None:
    # act
    config = ConfigGroups.from_yaml(filter_config)

    # assert
    for group in config.groups:
        if group.schedule == even_hour_schedule:
            assert even_hour_filter in group.fil
        if group.schedule == odd_hour_schedule:
            assert odd_hour_filter in group.fil
