import pytest
from bm_shared.bm_model import BMApiConfig
from product_starter_etl import BMProductStarterEtl

from common.pubsub_client import PubSubPublisher, PubSubSubscriber
from common.time_service import TimeService


@pytest.fixture(scope="function")
def api_product_starter_etl(
    api_config: BMApiConfig,
    bm_publisher_mock: PubSubPublisher,
    bm_subscriber_mock: PubSubSubscriber,
    time_service: TimeService,
) -> BMProductStarterEtl:
    return BMProductStarterEtl(
        config=api_config, publisher=bm_publisher_mock, subscriber=bm_subscriber_mock, time_service=time_service
    )


@pytest.fixture(scope="session")
def api_product_filters() -> list[str]:
    return [
        '(cat_id:"2") AND (brand:"0  Apple") AND (model:"000 iPhone 13") AND special_offer_type=0',
        '(cat_id:"2") AND (brand_clean:"Apple") AND (model_clean:"iPhone 15") AND special_offer_type=0',
    ]


@pytest.fixture(scope="session")
def even_hour_filter() -> str:
    return 'cat:"1"'


@pytest.fixture(scope="session")
def odd_hour_filter() -> str:
    return 'cat:"2"'


@pytest.fixture(scope="session")
def even_hour_schedule() -> str:
    return '"0 */2 * * *"'


@pytest.fixture(scope="session")
def odd_hour_schedule() -> str:
    return '"0 1-23/2 * * *"'


@pytest.fixture(scope="session")
def filter_config(even_hour_filter: str, odd_hour_filter: str, even_hour_schedule: str, odd_hour_schedule: str) -> str:
    return f"""
groups:
  - name: "even_number_hour"
    schedule: {even_hour_schedule}
    filters:
        - {even_hour_filter}
  - name: "odd_number_hour"
    schedule: {odd_hour_schedule}
    filters:
        - {odd_hour_filter}
"""
