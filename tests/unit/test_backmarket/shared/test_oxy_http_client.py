from http import <PERSON><PERSON><PERSON><PERSON><PERSON>
from logging import WARNING
from typing import Callable

import pytest
import responses
from _pytest.logging import LogCaptureFixture
from bm_shared.oxy_http_client import (
    AlgoliaHTTPError,
    OxyFeatureDict,
    OxyHTTPClient,
    OxyHTTPError,
)
from jsonschema.exceptions import ValidationError
from requests import ConnectionError


@responses.activate
def test_oxy_get(
    oxy_client: OxyHTTPClient,
    oxy_endpoint: str,
    oxy_payload_ok: str,
    page_content: str,
) -> None:
    responses.post(oxy_endpoint, body=oxy_payload_ok)
    html = oxy_client.get_page("https://www.example.com/page.html")
    assert html == page_content


@responses.activate
def test_oxy_get_http_error(
    oxy_client: OxyHTTPClient,
    oxy_endpoint: str,
    oxy_payload_http_error_404: str,
) -> None:
    responses.post(oxy_endpoint, body=oxy_payload_http_error_404)
    with pytest.raises(AlgoliaHTTPError, match=r"404"):
        oxy_client.get_page("https://www.example.com/page.html")


@responses.activate
def test_oxy_get_connection_error(oxy_client: OxyHTTPClient) -> None:
    # lack of responses mapping mocks connection error
    with pytest.raises(ConnectionError):
        oxy_client.get_page("https://www.example.com/page.html")


@responses.activate
@pytest.mark.parametrize(
    "payload_error",
    [
        ("oxy_payload_no_status_code"),
        ("oxy_payload_no_content"),
        ("oxy_payload_empty_results"),
        ("oxy_payload_empty"),
    ],
)
def test_oxy_payload_error(
    oxy_client: OxyHTTPClient,
    oxy_endpoint: str,
    payload_error: str,
    request: pytest.FixtureRequest,
) -> None:
    payload = request.getfixturevalue(payload_error)
    responses.post(oxy_endpoint, payload)
    with pytest.raises(ValidationError):
        oxy_client.get_page("https://www.example.com")


@responses.activate
def test_oxy_get_proxy_http_error(
    oxy_client: OxyHTTPClient,
    oxy_endpoint: str,
) -> None:
    responses.post(oxy_endpoint, body="Bad Request", status=400)
    with pytest.raises(OxyHTTPError, match="Bad Request"):
        oxy_client.get_page("https://www.example.com/page.html")


def test_oxy_get_features(oxy_client: OxyHTTPClient, oxy_url: str, oxy_base_features: OxyFeatureDict) -> None:
    features = oxy_client.get_features(target_url=oxy_url)
    assert features == oxy_base_features


def test_oxy_get_features_with_headers(
    oxy_client: OxyHTTPClient,
    oxy_url: str,
    oxy_headers: dict[str, str],
    oxy_base_features: OxyFeatureDict,
    oxy_headers_features: OxyFeatureDict,
) -> None:
    features = oxy_client.get_features(target_url=oxy_url, headers=oxy_headers)
    assert features == oxy_base_features | oxy_headers_features


def test_oxy_get_features_with_post(
    oxy_client: OxyHTTPClient,
    oxy_url: str,
    oxy_base_features: OxyFeatureDict,
    oxy_post_features: OxyFeatureDict,
    json_content: dict[str, str],
) -> None:
    features = oxy_client.get_features(target_url=oxy_url, method=HTTPMethod.POST, json_data=json_content)
    assert features == oxy_base_features | oxy_post_features


@responses.activate
def test_oxy_rate_limit_ok(
    oxy_client: OxyHTTPClient,
    oxy_endpoint: str,
    oxy_payload_ok: str,
    get_headers_fun: Callable[[str, str], dict[str, str]],
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    caplog.set_level(WARNING)

    # act
    responses.post(oxy_endpoint, body=oxy_payload_ok, headers=get_headers_fun("200000", "900"))
    oxy_client.get_page("https://www.example.com/page.html")

    # assert
    assert "Oxylabs client remaining requests below threshold" not in caplog.text
