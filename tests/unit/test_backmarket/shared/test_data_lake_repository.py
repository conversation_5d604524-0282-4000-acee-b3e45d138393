from datetime import datetime
from pathlib import Path
from unittest.mock import ANY, MagicMock, patch

from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from pandas import DataFrame
from unit.test_backmarket.shared.conftest import DLPartitionMaker


class IncludesString(str):
    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Path):
            return NotImplemented
        return self in str(other)


def test_add_run_id_prefix(file_name: str, run_id: datetime) -> None:
    # arrange
    expected_file_name = Path(f"20230303_030300000_{file_name}")

    # act
    actual_file_name = BMDataLakeRepository.add_run_id_prefix(file_name, run_id)

    # assert
    assert actual_file_name == expected_file_name


def test_read_prefixed_data_frame(
    bm_data_lake_repository: BMDataLakeRepository,
    file_name: str,
    data_lake_folder: str,
    make_dl_partition: DLPartitionMaker,
    run_id: datetime,
) -> None:
    # arrange
    bm_data_lake_repo = bm_data_lake_repository
    bm_data_lake_repo.read_file = MagicMock()
    expected_file_name = str(BMDataLakeRepository.add_run_id_prefix(file_name, run_id))
    expected_partition = make_dl_partition(data_lake_folder, run_id)
    read_parquet = MagicMock()

    # act
    with patch("bm_shared.bm_data_lake_repository.read_parquet", read_parquet):
        bm_data_lake_repo.read_prefixed_data_frame(file_name, data_lake_folder, run_id)

    # assert
    bm_data_lake_repo.read_file.assert_called_once_with(expected_file_name, expected_partition, ANY)
    read_parquet.assert_called_once()


def test_write_prefixed_data_frame(
    bm_data_lake_repository: BMDataLakeRepository,
    data_frame: DataFrame,
    file_name: str,
    data_lake_folder: str,
    run_id: datetime,
    make_dl_partition: DLPartitionMaker,
) -> None:
    # arrange
    bm_data_lake_repo = bm_data_lake_repository
    bm_data_lake_repo.write_file = MagicMock()
    expected_file_name = str(BMDataLakeRepository.add_run_id_prefix(file_name, run_id))
    expected_partition = make_dl_partition(data_lake_folder, run_id)

    # act
    bm_data_lake_repo.write_prefixed_data_frame(data_frame, file_name, data_lake_folder, run_id)

    # assert
    bm_data_lake_repo.write_file.assert_called_once_with(IncludesString(expected_file_name), expected_partition)


def test_write_prefixed_content(
    bm_data_lake_repository: BMDataLakeRepository,
    file_name: str,
    data_lake_folder: str,
    run_id: datetime,
    make_dl_partition: DLPartitionMaker,
    file_content: str,
) -> None:
    # arrange
    bm_data_lake_repo = bm_data_lake_repository
    bm_data_lake_repo.write_file = MagicMock()
    expected_file_name = str(BMDataLakeRepository.add_run_id_prefix(file_name, run_id))
    expected_partition = make_dl_partition(data_lake_folder, run_id)

    # act
    bm_data_lake_repo.write_prefixed_file_content(file_content, file_name, data_lake_folder, run_id)

    # assert
    bm_data_lake_repo.write_file.assert_called_once_with(IncludesString(expected_file_name), expected_partition)
