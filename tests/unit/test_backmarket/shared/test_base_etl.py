import logging
from unittest.mock import MagicMock, Mock

from bm_shared.bm_base_etl import ListingBaseEtl
from google.cloud.pubsub_v1 import PublisherClient

logger = logging.getLogger()


def test_publish_message(bm_base_etl: ListingBaseEtl, topic_path: str) -> None:
    """test if required pubsub Publisher call sequence was done

    :param bm_base_etl: tuple of mocked etl, bm_publisher mock, storage mock
    :param topic_path: PubSub topic
    """
    # arrange
    base_etl = bm_base_etl
    future = MagicMock()
    mock_publisher = Mock(spec_set=PublisherClient)
    mock_publisher.publish.return_value = future
    mock_publisher.topic_path.return_value = topic_path
    base_etl._pubsub_client = mock_publisher

    # act
    base_etl.publish_message("send me!")

    # assert
    mock_publisher.topic_path.assert_called_once_with(base_etl._config.project_id, topic=base_etl._config.pubsub_topic)
    mock_publisher.publish.assert_called_once_with(topic_path, "send me!".encode())
    future.result.assert_called_once()
