import logging
from datetime import datetime
from pathlib import Path
from typing import Callable, <PERSON><PERSON><PERSON><PERSON>
from unittest.mock import Mock
from zipfile import <PERSON><PERSON><PERSON><PERSON>

import pytest
from bm_shared.api import ApiPayload, ApiPayloadParams
from bm_shared.bm_base_etl import ListingBaseEtl
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import ListingConfig
from bm_shared.oxy_http_client import OxyHTTPClient
from bs4 import BeautifulSoup
from google.cloud.pubsub_v1 import PublisherClient
from pandas import DataFrame

from common.data_lake_repository import DataLakeRepository
from common.typings import DataLakeTimePartition

current_dir = Path(__file__).resolve().parent
data_dir = current_dir / "data"

logger = logging.getLogger()

DLPartitionMaker: TypeAlias = Callable[[str, datetime], DataLakeTimePartition]


def get_file_content(file_path: Path) -> str:
    with open(file_path, "r") as f:
        return f.read()


@pytest.fixture(scope="session")
def make_dl_partition() -> DLPartitionMaker:
    def dl_partition(data_lake_folder: str, time: datetime) -> DataLakeTimePartition:
        return DataLakeTimePartition(data_lake_folder, time.year, time.month, time.day)

    return dl_partition


@pytest.fixture(scope="session")
def topic_path() -> str:
    return "projects/cool_project/topics/cool_topic"


@pytest.fixture(scope="session")
def bm_base_etl(run_id: datetime) -> ListingBaseEtl:
    return ListingBaseEtl(
        ListingConfig(),
        run_id,
        Mock(spec_set=OxyHTTPClient),
        Mock(spec_set=PublisherClient),
        Mock(spec_set=DataLakeRepository),
        Mock(spec_set=DataLakeRepository),
    )


@pytest.fixture(scope="session")
def file_content() -> str:
    return "sample text content"


@pytest.fixture(scope="session")
def file_name() -> str:
    return "test.html"


@pytest.fixture(scope="session")
def data_lake_folder() -> str:
    return "sample_folder"


@pytest.fixture(scope="session")
def data_frame() -> DataFrame:
    return DataFrame({"a": [1]})


@pytest.fixture(scope="session")
def bm_data_lake_repository(cloud_storage_client_mock: Mock) -> BMDataLakeRepository:
    return BMDataLakeRepository(cloud_storage_client_mock)


@pytest.fixture(scope="session")
def page_number() -> int:
    return 1


@pytest.fixture(scope="session")
def api_response_no_products_list() -> str:
    return "{}"


@pytest.fixture(scope="session")
def api_response_no_price() -> str:
    return get_file_content(data_dir / "api_response_no_price.json")


@pytest.fixture(scope="session")
def api_payload() -> ApiPayload:
    return {
        "query": "",
        "distinct": 1,
        "clickAnalytics": True,
        ApiPayloadParams.FILTERS: "",
        "facets": [
            "price",
            "page",
            "q",
            "sort",
            "storage",
            "color",
            "sim_lock",
            "backbox_grade",
            "real_screen_size",
            "warranty_with_unit",
            "payment_methods",
            "shipping_delay",
            "price_ranges.sm-1",
            "price_ranges.sm-2",
            "price_ranges.md-1",
            "price_ranges.md-1b",
            "price_ranges.md-1c",
            "price_ranges.md-2",
            "price_ranges.lg-1",
            "price_ranges.lg-2",
            "price_ranges.lg-3",
        ],
        ApiPayloadParams.PAGE: 0,
        "hitsPerPage": 30,
    }


@pytest.fixture(scope="session")
def get_headers_fun() -> Callable[[str, str], dict[str, str]]:
    def get_headers(remaining_monthly: str, remaining_10s: str) -> dict[str, str]:
        return {
            "x-ratelimit-ecommerce-scraper-api-0718-limit": "3000000",
            "x-ratelimit-ecommerce-scraper-api-0718-remaining": str(remaining_monthly),
            "x-ratelimit-internal-total-0718-limit": "1000",
            "x-ratelimit-internal-total-0718-remaining": str(remaining_10s),
        }

    return get_headers


@pytest.fixture(scope="session")
def backmarket_home() -> str:
    """Real HTML content from Backmarket website"""
    input_zip = ZipFile(data_dir / "backmarket_home.zip")
    return input_zip.read("backmarket_home.html").decode("utf-8")


@pytest.fixture(scope="session")
def mock_empty_html(backmarket_home: str) -> str:
    """HTML with empty NUXT_DATA script"""
    soup = BeautifulSoup(backmarket_home, "html.parser")
    nuxt_script = soup.find("script", {"id": "__NUXT_DATA__"})
    if nuxt_script:
        nuxt_script.string = "[]"
    return str(soup)


@pytest.fixture(scope="session")
def mock_invalid_html(backmarket_home: str) -> str:
    """HTML with invalid NUXT_DATA structure"""
    soup = BeautifulSoup(backmarket_home, "html.parser")
    nuxt_script = soup.find("script", {"id": "__NUXT_DATA__"})
    if nuxt_script:
        nuxt_script.string = '[{"apiKey": 1}]'  # Missing the actual key value
    return str(soup)
