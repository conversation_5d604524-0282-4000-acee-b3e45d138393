import pytest
from bm_shared.bm_model import AlgoliaWorkerMessage
from bm_shared.bm_utils import emulate_pubsub_message, url_to_filename

from common.cloud_events import create_http_event, get_event_message


@pytest.mark.parametrize(
    "url, expected",
    [
        ("https://www.example.com/page", "www_example_com_page"),
        ("https://www.example.com/this page", "www_example_com_this_page"),
        (
            "https://www.example.com/test?name=value",
            "www_example_com_test_name_value",
        ),
        (
            "https://www.example.com/test/test#anchor",
            "www_example_com_test_test_anchor",
        ),
        (
            "https://www.example.com/percent%20encoded",
            "www_example_com_percent_20encoded",
        ),
        (
            "https://www.example.com/subpath?query1=abc&query2=xyz",
            "www_example_com_subpath_query1_abc_query2_xyz",
        ),
        ("https://www_example_com/ąćęłśż", "www_example_com_______"),
    ],
)
def test_url_to_filename(url: str, expected: str) -> None:
    assert url_to_filename(url).name == expected


def test_emulate_pubsub_message() -> None:
    # wrap test data into pubsub message and cloud event
    pubsub_message = emulate_pubsub_message("test data")
    cloud_event = create_http_event(pubsub_message)
    # unpack it back
    unpacked = get_event_message(cloud_event)
    # verify
    assert unpacked == "test data"


@pytest.mark.parametrize(
    "filter,locale,page,expected_id",
    [
        (
            '(cat_id:"2") AND (brand:"0  Apple") AND (model:"000 iPhone 13") AND special_offer_type=0',
            "de-at",
            0,
            "cat_id_2_and_brand_0_apple_and_model_000_iphone_13_and_special_offer_type_0_de_at_0",
        ),
        ("a_b__c__d", "de-de", 10, "a_b_c_d_de_de_10"),
        ("_abc_", "it-it", 1, "abc_it_it_1"),
        (".\r\n\"x[]'/\\", "de-at", 0, "x_de_at_0"),
        ("x" * 201, "it-it", 0, f"{'x' * 200}_it_it_0"),
    ],
)
def test_request_id(filter: str, locale: str, page: int, expected_id: str) -> None:
    # act
    request = AlgoliaWorkerMessage(run_timestamp=1, query=filter, payload="", page=page, language_country=locale)

    # assert
    assert request.id == expected_id
