from unittest.mock import patch

import pytest
import requests
from bm_shared.listing_worker_model import <PERSON><PERSON><PERSON><PERSON><PERSON>
from bm_shared.retrieve_algolia_key import (
    extract_api_key,
    fetch_algolia_key_from_website,
)
from listing_worker.bm_worker_transformer import parse_html
from pytest import FixtureRequest


@pytest.mark.parametrize(
    "url,expected_url",
    [
        (None, "https://www.backmarket.de"),
        ("https://www.backmarket.fr", "https://www.backmarket.fr"),
    ],
)
def test_fetch_algolia_key_success(
    backmarket_home: str,
    api_key: str,
    url: None | str,
    expected_url: str,
) -> None:
    """Test successful retrieval of Algolia API key from website (default and custom URL)"""
    with patch("requests.get") as mock_get:
        mock_get.return_value.text = backmarket_home
        mock_get.return_value.raise_for_status = lambda: None

        if url is None:
            result = fetch_algolia_key_from_website()
        else:
            result = fetch_algolia_key_from_website(url)

        assert result == api_key
        mock_get.assert_called_once_with(expected_url)


@pytest.mark.parametrize(
    "html_fixture",
    [
        "mock_empty_html",
        "mock_invalid_html",
    ],
)
def test_fetch_algolia_key_no_api_key_error(
    html_fixture: str,
    request: FixtureRequest,  # needs to be named request for pytest to retrieve the fixture
) -> None:
    """Test handling of missing API key in response (empty and invalid HTML)"""
    html = request.getfixturevalue(html_fixture)
    with patch("requests.get") as mock_get:
        mock_get.return_value.text = html
        mock_get.return_value.raise_for_status = lambda: None

        with pytest.raises(NoApiKey):
            fetch_algolia_key_from_website()


def test_fetch_algolia_key_http_error() -> None:
    """Test handling of HTTP errors"""
    with patch("requests.get") as mock_get:
        mock_get.return_value.raise_for_status.side_effect = requests.HTTPError("404 Not Found")

        with pytest.raises(requests.HTTPError):
            fetch_algolia_key_from_website()


def test_fetch_algolia_key_connection_error() -> None:
    """Test handling of connection errors"""
    with patch("requests.get") as mock_get:
        mock_get.side_effect = requests.ConnectionError("Failed to connect")

        with pytest.raises(requests.ConnectionError):
            fetch_algolia_key_from_website()


def test_extract_api_key(
    backmarket_home: str,
    mock_empty_html: str,
    api_key: str,
) -> None:
    parsed = parse_html(backmarket_home)
    assert extract_api_key(parsed) == api_key

    parsed = parse_html(mock_empty_html)
    with pytest.raises(NoApiKey):
        extract_api_key(parsed)
