from unittest.mock import Mock
from urllib.parse import urlparse

import pytest
from bm_shared.api import (
    AlgoliaClient,
    AlgoliaParsedResponse,
    ApiFilter,
    ApiFilterAttribute,
    ApiFilterOperator,
    ApiHeaders,
    ApiPayload,
    ApiPayloadParams,
)
from bm_shared.bm_model import AlgoliaSecret


def test_algolia_client_api_url(algolia_secret: AlgoliaSecret, app_id: str, lang: str) -> None:
    url = AlgoliaClient._url(lang, algolia_secret)
    parsed = urlparse(url)
    assert app_id.lower() in parsed.netloc
    assert lang in parsed.path


def test_algolia_client_headers(api_key: str, app_id: str, lang: str) -> None:
    headers = AlgoliaClient._headers(api_key, app_id, lang)
    assert headers[ApiHeaders.X_ALGOLIA_API_KEY] == api_key
    assert headers[ApiHeaders.X_ALGOLIA_APPLICATION_ID] == app_id
    assert AlgoliaClient.country_code(lang) in headers[ApiHeaders.REFERER]
    assert AlgoliaClient.country_code(lang) in headers[ApiHeaders.ORIGIN]

    assert len(headers) == len(ApiHeaders.HEADERS)


def test_algolia_client_payload(merchant_id_filter: str, page_number: int, api_payload: ApiPayload) -> None:
    # act
    payload = AlgoliaClient.payload(api_payload, merchant_id_filter, page_number)

    # assert
    assert payload[ApiPayloadParams.FILTERS] == merchant_id_filter
    assert payload[ApiPayloadParams.PAGE] == page_number
    assert len(payload) == len(api_payload)


def test_parse_api_response(api_response: str, api_response_parsed: AlgoliaParsedResponse) -> None:
    # act
    parsed = AlgoliaClient.parse_response(api_response)

    # assert
    assert parsed == api_response_parsed


def test_algolia_client_new_battery(api_response_new_battery: str) -> None:
    # arrange
    parsed = AlgoliaClient.parse_response(api_response_new_battery)

    # act
    assert parsed.hits != []
    assert parsed.hits[0].is_battery_new


def test_parse_api_response_no_products(api_response_no_products_list: str) -> None:
    with pytest.raises(KeyError, match="nb_hits"):
        AlgoliaClient.parse_response(api_response_no_products_list)


def test_parse_api_no_price(api_response_no_price: str) -> None:
    with pytest.raises(KeyError, match="price"):
        AlgoliaClient.parse_response(api_response_no_price)


def test_algolia_client_get_hits(
    algolia_client: AlgoliaClient,
    oxy_client_mock: Mock,
    api_response: str,
    algolia_secret: AlgoliaSecret,
    api_payload: ApiPayload,
    lang: str,
) -> None:
    # arrange
    oxy_client_mock.get_page.return_value = api_response

    # act
    response = algolia_client.get_hits(api_payload, lang, algolia_secret)

    # assert
    assert response == api_response


def test_api_filter_attribute() -> None:
    # arrange
    green = ApiFilterAttribute(key="color", value="green")

    # assert
    assert green.to_text == '(color:"green")'


@pytest.mark.parametrize(
    "query, operator",
    [
        ("size=1", ApiFilterOperator.AND),
        ("color=RED", ApiFilterOperator.OR),
    ],
)
def test_api_filter(query: str, operator: ApiFilterOperator) -> None:

    # arrange
    green = ApiFilterAttribute(key="color", value="green")
    expected = f'(color:"green") {operator} {query}'

    # act
    actual = ApiFilter.prepend_attribute(attribute=green, base_query=query, operator=operator)

    # assert
    assert actual == expected
