import base64
import json
from datetime import datetime, timezone
from math import ceil
from pathlib import Path
from typing import Callable
from unittest.mock import Mock

import pytest
from bm_shared.api import (
    AlgoliaClient,
    AlgoliaParsedResponse,
    ApiParams,
    ApiPayload,
    ApiPayloadParams,
)
from bm_shared.bm_data_lake_repository import BMDataLakeRepository
from bm_shared.bm_model import AlgoliaSecret, AlgoliaWorkerMessage, BMApiConfig
from bm_shared.bm_repository import BMRepository
from bm_shared.bm_schema import ALGOLIA_PRODUCTS_SCHEMA
from bm_shared.oxy_http_client import (
    OxyFeatureDict,
    OxyHTTPClient,
    OxyParams,
    ProxyConfig,
)
from google.cloud.pubsub_v1 import PublisherClient
from pandas import DataFrame, read_csv

from common.pubsub_client import PubSubMessage, PubSubPublisher, PubSubSubscriber

current_dir = Path(__file__).resolve().parent
data_dir = current_dir / "shared/data"


def get_file_content(file_path: Path) -> str:
    with open(file_path, "r") as f:
        return f.read()


API_KEY = "NGQyNGMwZWY2NzRiODgzZjNmMTM3MDk3MGIxMWJkNGRjMTA4NDUxN2I3NTNhMjRhMzk3YWE2ZjIzNzVkYTFmZGF0dHJpYnV0ZXNUb1JldHJpZXZlPWJhY2tib3hfZ3JhZGVfbGFiZWwlMkNiYWNrYm94X2dyYWRlX3ZhbHVlJTJDYmFja21hcmtldElEJTJDYnJhbmQlMkNicmFuZF9jbGVhbiUyQ2NhbWVyYV9zY29yZSUyQ2NhdF9pZCUyQ2NhdGVnb3J5XzElMkNjYXRlZ29yeV8yJTJDY2F0ZWdvcnlfMyUyQ2NvbGxlY3Rpb25fYWklMkNjb2xvciUyQ2Nvbm5lY3RvciUyQ2N1cnJlbmN5JTJDZmFjZV9pZCUyQ2ZsYXNoX3NhbGVzX2Rpc2NvdW50JTJDZ2xvYmFsX3Njb3JlJTJDaGRtaV9vdXRwdXQlMkNpZCUyQ2ltYWdlMSUyQ2lwYWRfY29ubmVjdG9yJTJDbGlmZV9leHBlY3RhbmN5X3Njb3JlJTJDbGluayUyQ2xpbmtfZ3JhZGVfdjIlMkNsaXN0X3ZpZXclMkNsaXN0aW5nSUQlMkNtZXJjaGFudF9pZCUyQ21vZGVsJTJDbW9kZWxfY2xlYW4lMkNtdWx0aW1lZGlhX3Njb3JlJTJDb2JqZWN0SUQlMkNwZXJmb3JtYW5jZXNfc2NvcmUlMkNwcmljZSUyQ3ByaWNlX25ldyUyQ3ByaWNlX25ld193aXRoX2N1cnJlbmN5JTJDcHJpY2Vfd2l0aF9jdXJyZW5jeSUyQ3JlZmVyZW5jZVByaWNlJTJDcmV2aWV3UmF0aW5nJTJDc2NyZWVuX3F1YWxpdHlfc2NvcmUlMkNzaW1fbG9jayUyQ3NwZWNpYWxfb2ZmZXJfdHlwZSUyQ3N0YWZmX3BpY2slMkNzdG9ja1JhdyUyQ3N1Yl90aXRsZV9lbGVtZW50cyUyQ3RpdGxlJTJDdGl0bGVfbW9kZWwlMkN0b3VjaF9iYXIlMkN0b3VjaF9pZCUyQ3ZhcmlhbnRfZmllbGRzJTJDd2FycmFudHkmcmVzdHJpY3RJbmRpY2VzPXByb2RfJTJB"  # noqa


@pytest.fixture(scope="session")
def oxy_payload() -> Callable[[str, str, int], str]:
    def oxy_payload_fun(content: str, url: str, status_code: int = 200) -> str:
        response = {"results": [{"content": content, "status_code": status_code, "url": url, "job_id": "1"}]}
        return json.dumps(response)

    return oxy_payload_fun


@pytest.fixture(scope="session")
def algolia_payload_template() -> ApiPayload:
    return {
        "query": "",
        "distinct": 1,
        "clickAnalytics": True,
        ApiPayloadParams.FILTERS: "",
        "facets": [
            "price",
            "page",
            "q",
        ],
        ApiPayloadParams.PAGE: 0,
        "hitsPerPage": 30,
    }


@pytest.fixture(scope="session")
def oxy_config() -> ProxyConfig:
    return ProxyConfig.from_dict({"name": "x", "password": "y"})


@pytest.fixture(scope="session")
def oxy_client(oxy_config: ProxyConfig) -> OxyHTTPClient:
    return OxyHTTPClient(oxy_config)


@pytest.fixture(scope="session")
def page_content() -> str:
    return "<HTML>foo</HTML>"


@pytest.fixture(scope="session")
def json_content() -> dict[str, str]:
    return {"key": "value"}


@pytest.fixture(scope="session")
def oxy_payload_ok(oxy_payload: Callable[[str, str, int], str], page_content: str) -> str:
    return oxy_payload(page_content, "https://example.com", 200)


@pytest.fixture(scope="session")
def oxy_payload_http_error_404(
    oxy_payload: Callable[[str, str, int], str],
) -> str:
    return oxy_payload("", "", 404)


@pytest.fixture(scope="session")
def oxy_endpoint() -> str:
    return OxyParams.ENDPOINT


@pytest.fixture(scope="session")
def oxy_payload_no_status_code(page_content: str) -> str:
    return json.dumps({"results": [{"content": page_content}]})


@pytest.fixture(scope="session")
def oxy_payload_empty_results() -> str:
    return json.dumps({"results": []})


@pytest.fixture(scope="session")
def oxy_payload_empty() -> str:
    return json.dumps({})


@pytest.fixture(scope="session")
def oxy_payload_no_content() -> str:
    return json.dumps({"results": [{"status_code": 200}]})


@pytest.fixture(scope="session")
def oxy_url() -> str:
    return "https://www.example.com"


@pytest.fixture(scope="session")
def oxy_headers() -> dict[str, str]:
    return {"header": "value", "header2": "value2"}


@pytest.fixture(scope="session")
def oxy_base_features(oxy_url: str) -> dict[str, str]:
    return {OxyParams.SOURCE: OxyParams.UNIVERSAL_ECOMMERCE, OxyParams.URL: oxy_url}


@pytest.fixture(scope="session")
def oxy_headers_features(oxy_headers: dict[str, str]) -> OxyFeatureDict:
    return {
        OxyParams.CONTEXT: [
            {OxyParams.KEY: OxyParams.SUCCESSFUL_STATUS_CODES, OxyParams.VALUE: OxyParams.NO_RETRY_HTTP_STATUS_CODES},
            {OxyParams.KEY: OxyParams.FORCE_HEADERS, OxyParams.VALUE: True},
            {OxyParams.KEY: OxyParams.HEADERS, OxyParams.VALUE: oxy_headers},
        ]
    }


@pytest.fixture(scope="session")
def oxy_post_features(json_content: dict[str, str]) -> OxyFeatureDict:
    return {
        OxyParams.CONTEXT: [
            {OxyParams.KEY: OxyParams.SUCCESSFUL_STATUS_CODES, OxyParams.VALUE: OxyParams.NO_RETRY_HTTP_STATUS_CODES},
            {OxyParams.KEY: OxyParams.HTTP_METHOD, OxyParams.VALUE: OxyParams.POST},
            {
                OxyParams.KEY: OxyParams.CONTENT,
                OxyParams.VALUE: base64.b64encode(json.dumps(json_content).encode()).decode(),
            },
        ]
    }


@pytest.fixture(scope="session")
def publisher_client_mock() -> Mock:
    return Mock(spec_set=PublisherClient)


@pytest.fixture(scope="session")
def sql_repo_mock() -> Mock:
    return Mock(spec_set=BMRepository)


@pytest.fixture(scope="function")
def data_lake_repo_mock() -> Mock:
    return Mock(spec=BMDataLakeRepository)


@pytest.fixture(scope="function")
def oxy_client_mock() -> Mock:
    return Mock(spec=OxyHTTPClient)


@pytest.fixture(scope="session")
def algolia_secret(api_key: str, app_id: str) -> AlgoliaSecret:
    return AlgoliaSecret(api_key, app_id)


@pytest.fixture(scope="function")
def algolia_client(oxy_client_mock: OxyHTTPClient) -> AlgoliaClient:
    return AlgoliaClient(oxy_client_mock)


@pytest.fixture(scope="function")
def algolia_client_mock(api_response_parsed: AlgoliaParsedResponse, country: str) -> Mock:
    client = Mock(spec=AlgoliaClient)
    client.parse_response.return_value = api_response_parsed
    client.country_code.return_value = country
    return client


@pytest.fixture(scope="session")
def run_id() -> datetime:
    return datetime(2023, 3, 3, 3, 3, tzinfo=timezone.utc)


@pytest.fixture(scope="session")
def app_id() -> str:
    return "APPID12345"


@pytest.fixture(scope="session")
def merchant_id_filter() -> str:
    return "merchant_id:123"


@pytest.fixture(scope="session")
def api_key() -> str:
    return API_KEY


@pytest.fixture(scope="session")
def lang() -> str:
    return "de-de"


@pytest.fixture(scope="session")
def country() -> str:
    return "de"


@pytest.fixture(scope="function")
def bm_publisher_mock() -> PubSubPublisher:
    return Mock(spec=PubSubPublisher)


@pytest.fixture(scope="function")
def bm_subscriber_mock() -> PubSubSubscriber:
    return Mock(spec=PubSubSubscriber)


@pytest.fixture(scope="session")
def bm_pubsub_message(algolia_worker_message: AlgoliaWorkerMessage) -> PubSubMessage:
    return PubSubMessage("1", "100", algolia_worker_message.to_json())


@pytest.fixture(scope="session")
def api_config() -> BMApiConfig:
    return BMApiConfig()


@pytest.fixture(scope="session")
def api_response() -> str:
    return get_file_content(data_dir / "api_response.json")


@pytest.fixture(scope="session")
def api_response_new_battery() -> str:
    return get_file_content(data_dir / "api_response_new_battery.json")


@pytest.fixture(scope="session")
def api_response_parsed() -> AlgoliaParsedResponse:
    parsed_as_json = json.loads(get_file_content(data_dir / "api_response_parsed.json"))
    return AlgoliaParsedResponse.from_dict(parsed_as_json)


@pytest.fixture(scope="session")
def api_response_df() -> DataFrame:
    df = read_csv(data_dir / "merchant_page1_products.csv").astype(dtype=ALGOLIA_PRODUCTS_SCHEMA.dtypes)
    return df


@pytest.fixture(scope="session")
def empty_response_df() -> DataFrame:
    df = read_csv(data_dir / "empty_products.csv").astype(dtype=ALGOLIA_PRODUCTS_SCHEMA.dtypes)
    return df


@pytest.fixture(scope="session")
def total_hits() -> int:
    return 405


@pytest.fixture(scope="session")
def algolia_payload(algolia_payload_template: ApiPayload) -> ApiPayload:
    return AlgoliaClient.payload(algolia_payload_template, "", 0)


@pytest.fixture(scope="session")
def algolia_worker_message(run_id: datetime, algolia_payload: ApiPayload, lang: str) -> AlgoliaWorkerMessage:
    return AlgoliaWorkerMessage(run_id.timestamp(), "", algolia_payload, 0, lang)


@pytest.fixture(scope="session")
def algolia_worker_messages(
    run_id: datetime, algolia_payload_template: ApiPayload, lang: str, total_hits: int
) -> list[AlgoliaWorkerMessage]:
    messages = []
    for page in range(1, ceil(total_hits / ApiParams.PRODUCTS_PER_PAGE)):
        payload = AlgoliaClient.payload(algolia_payload_template, "", page)
        messages.append(AlgoliaWorkerMessage(run_id.timestamp(), "", payload, page, lang))
    return messages
