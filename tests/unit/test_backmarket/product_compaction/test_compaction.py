import os
from datetime import date
from pathlib import Path
from unittest.mock import Mock, patch

from backmarket.product_compaction.compaction import (
    BackmarketCompaction,
    CompactionConfig,
)
from common.time_service import TimeService
from common.typings import DataLakeTimePartition


def test_compaction_date_from_env(backmarket_compaction: BackmarketCompaction) -> None:
    # arrange
    expected_date = date(2023, 6, 1)
    os.environ["COMPACTION_DATE"] = expected_date.strftime("%Y-%m-%d")

    # act
    actual_date = backmarket_compaction.compaction_date

    # assert
    assert actual_date == expected_date


def test_compaction_date_from_time_service(
    backmarket_compaction: BackmarketCompaction, time_service: TimeService
) -> None:
    # arrange
    _unset_compaction_env_variable()
    expected_date = time_service.today  # From time_service fixture

    # act

    actual_date = backmarket_compaction.compaction_date

    # assert
    assert actual_date == expected_date


def test_compaction_file_name(backmarket_compaction: BackmarketCompaction, time_service: TimeService) -> None:
    # arrange
    _unset_compaction_env_variable()
    expected_file_name = f"products_{time_service.today.strftime('%Y%m%d')}.parquet"

    # act
    actual_file_name = backmarket_compaction.compacted_file_name

    # assert
    assert actual_file_name == expected_file_name


def test_data_lake_path(backmarket_compaction: BackmarketCompaction, time_service: TimeService) -> None:
    # arrange
    _unset_compaction_env_variable()
    expected_path = (
        f"backmarket/api_results/year={time_service.today.year}/"
        f"month={time_service.today.month}/day={time_service.today.day}"
    )

    # act
    actual_path = backmarket_compaction.data_lake_path

    # assert
    assert actual_path == expected_path


def test_compacted_data_lake_partition(
    compaction_config: CompactionConfig, backmarket_compaction: BackmarketCompaction, time_service: TimeService
) -> None:
    # arrange
    _unset_compaction_env_variable()
    expected_partition = DataLakeTimePartition(
        base_path=compaction_config.compaction_base_path,
        year=time_service.today.year,
        month=time_service.today.month,
        day=time_service.today.day,
    )

    # act
    actual_partition = backmarket_compaction.compacted_partition

    # assert
    assert actual_partition.path == expected_partition.path


def test_download_files(
    backmarket_compaction: BackmarketCompaction, cloud_storage_client_mock: Mock, tmp_path: Path
) -> None:
    # arrange
    expected_files = [Path("file1.parquet"), Path("file2.parquet")]
    cloud_storage_client_mock.download_directory.return_value = expected_files

    # act
    actual_files = backmarket_compaction.download_files()

    # assert
    assert actual_files == expected_files
    cloud_storage_client_mock.download_directory.assert_called_once_with(
        backmarket_compaction.data_lake_path, tmp_path, include_subdirectories=False
    )


@patch("pyarrow.parquet.read_table")
@patch("pyarrow.parquet.write_table")
def test_compact(
    mock_write_table: Mock, mock_read_table: Mock, backmarket_compaction: BackmarketCompaction, tmp_path: Path
) -> None:
    # arrange
    expected_file = Path(tmp_path, backmarket_compaction.compacted_file_name)
    mock_read_table.return_value = "test table"

    # act
    actual_file = backmarket_compaction.compact()

    # assert
    assert actual_file == expected_file
    mock_read_table.assert_called_once_with(tmp_path)
    mock_write_table.assert_called_once_with("test table", where=expected_file)


def test_load_data_lake(
    backmarket_compaction: BackmarketCompaction, transformed_data_lake_repository_mock: Mock
) -> None:
    # arrange
    compacted_file = Path("compacted_file.parquet")

    # act
    backmarket_compaction.load_data_lake(compacted_file)

    # assert
    transformed_data_lake_repository_mock.write_file.assert_called_once_with(
        compacted_file, backmarket_compaction.compacted_partition
    )


def test_refresh_bq_table(backmarket_compaction: BackmarketCompaction, bq_repository_mock: Mock) -> None:
    # arrange
    bq_repository_mock.get_table.return_value.full_name = "project.dataset.table"
    bq_repository_mock.refresh_product_ranges.return_value = 100

    # act
    backmarket_compaction.refresh_bq_table()

    # assert
    bq_repository_mock.refresh_product_ranges.assert_called_once_with(backmarket_compaction.compaction_date)


@patch.object(BackmarketCompaction, "load_data_lake")
@patch.object(BackmarketCompaction, "refresh_bq_table")
def test_load(
    mock_refresh_bq_table: Mock, mock_load_data_lake: Mock, backmarket_compaction: BackmarketCompaction
) -> None:
    # arrange
    compacted_file = Path("compacted_file.parquet")

    # act
    backmarket_compaction.load(compacted_file)

    # assert
    mock_load_data_lake.assert_called_once_with(compacted_file)
    mock_refresh_bq_table.assert_called_once()


@patch.object(BackmarketCompaction, "download_files")
@patch.object(BackmarketCompaction, "compact")
@patch.object(BackmarketCompaction, "load")
def test_run(
    mock_load: Mock, mock_compact: Mock, mock_download_files: Mock, backmarket_compaction: BackmarketCompaction
) -> None:
    # arrange
    downloaded_files = [Path("file1.parquet"), Path("file2.parquet")]
    compacted_file = Path("compacted_file.parquet")

    mock_download_files.return_value = downloaded_files
    mock_compact.return_value = compacted_file

    # act
    backmarket_compaction.run()

    # assert
    mock_download_files.assert_called_once()
    mock_compact.assert_called_once()
    mock_load.assert_called_once_with(compacted_file)


def _unset_compaction_env_variable() -> None:
    os.environ.pop("COMPACTION_DATE", None)
