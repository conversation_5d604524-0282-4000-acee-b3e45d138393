from pathlib import Path
from unittest.mock import Mock

import pytest

from backmarket.product_compaction.compaction import (
    BackmarketCompaction,
    CompactionConfig,
)
from backmarket.product_compaction.compaction_bq_repo import CompactionBqRepository
from common.data_lake_repository import DataLakeRepository
from common.time_service import TimeService


@pytest.fixture(scope="function")
def compaction_config() -> CompactionConfig:
    return CompactionConfig()


@pytest.fixture(scope="function")
def bq_repository_mock() -> Mock:
    return Mock(spec=CompactionBqRepository)


@pytest.fixture(scope="function")
def transformed_data_lake_repository_mock() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="function")
def backmarket_compaction(
    compaction_config: CompactionConfig,
    tmp_path: Path,
    cloud_storage_client_mock: Mock,
    bq_repository_mock: Mock,
    transformed_data_lake_repository_mock: Mock,
    time_service: TimeService,
) -> BackmarketCompaction:
    return BackmarketCompaction(
        config=compaction_config,
        local_temp_path=tmp_path,
        cloud_storage_client=cloud_storage_client_mock,
        bq_repository=bq_repository_mock,
        transformed_data_lake_repository=transformed_data_lake_repository_mock,
        time_service=time_service,
    )
