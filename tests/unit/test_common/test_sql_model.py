import pytest

from common.sql_model import SqlTable


@pytest.mark.parametrize(
    "full_name",
    [
        None,
        "",
        "   ",
        "schema",
        "schema.",
        ".table",
        ".",
        " . ",
    ],
)
def test_sql_table_from_full_name_when_invalid_name(full_name: str) -> None:
    with pytest.raises(ValueError):
        _ = SqlTable.from_full_name(full_name)


@pytest.mark.parametrize(
    "full_name, schema_name, table_name",
    [
        ("schema.table", "schema", "table"),
        ("schema_complex.table_name", "schema_complex", "table_name"),
    ],
)
def test_sql_table_from_full_name_when_valid_name(full_name: str, schema_name: str, table_name: str) -> None:
    sql_table = SqlTable.from_full_name(full_name)
    assert sql_table.schema_name == schema_name
    assert sql_table.table_name == table_name
