import base64
from typing import Any

import pytest
from cloudevents.abstract import CloudEvent

from common.cloud_events import (
    DEFAULT_HTTP_ATTRIBUTES,
    create_http_event,
    get_event_message,
)
from common.typings import HttpResponse


@pytest.mark.parametrize(
    "data, message",
    [
        # No data
        (None, None),
        # Empty data
        ("", ""),
        # string data
        ("test", "test"),
        # Complex type
        (HttpResponse(), HttpResponse()),
        # Pub/sub message without `data` and string
        ({"message": "test string"}, "test string"),
        # Pub/sub message without `data` and complex type
        ({"message": HttpResponse()}, HttpResponse()),
        # Pub/sub message with `data` encoded as string
        ({"message": {"data": "test data"}}, "test data"),
        # Pub/sub message with `data` encoded as bytestring
        ({"message": {"data": "bytestring".encode("utf-8")}}, b"bytestring"),
        # Pub/sub message with `data` encoded as base64
        ({"message": {"data": base64.b64encode(b"base64")}}, "base64"),
    ],
)
def test_get_event_message(data: Any, message: str) -> None:
    # test create_http_event
    event = create_http_event(data)
    actual_data = event.get_data()
    attributes = event.get_attributes()

    assert isinstance(event, CloudEvent)
    assert actual_data == data
    assert attributes["type"] == DEFAULT_HTTP_ATTRIBUTES["type"]
    assert attributes["source"] == DEFAULT_HTTP_ATTRIBUTES["source"]

    # test get_event_message
    actual_message = get_event_message(event)
    assert actual_message == message
