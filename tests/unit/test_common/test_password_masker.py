from typing import Any

import pytest

from common.logger import PasswordMasker


@pytest.mark.parametrize(
    "message, expected",
    [
        # No password - should remain unchanged
        ("This is a normal log message without sensitive data", "This is a normal log message without sensitive data"),
        # Basic password formats
        ("Some password=qwerty123", "Some password='*****'"),
        ("Some password='qwerty123'", "Some password='*****'"),
        ('Some password="qwerty123"', 'Some password="*****"'),
        # Special characters in passwords
        ("Some password='@*&&#$%@#/'", "Some password='*****'"),
        # JSON-like formats
        (
            "JSON log entry: {'password': 'foo'}",
            "JSON log entry: {'password': '*****'}",
        ),
        (
            'JSON log entry: {"password": "foo"}',
            'JSON log entry: {"password": "*****"}',
        ),
        (
            "JSON log entry: {'rootPassword': 'foo', 'bar': 'baz'}",
            "JSON log entry: {'rootPassword': '*****', 'bar': 'baz'}",
        ),
        # FlywayParams format
        (
            "FlywayParams(host='localhost', password='postgres', url='***************************:5433/platform')",
            "FlywayParams(host='localhost', password='*****', url='***************************:5433/platform')",
        ),
        # Flyway command format with quoted command-line arguments
        (
            "Flyway command: 'flyway info -infoOfState=Pending,Outdated repair migrate "
            + "-configFiles=/path/to/flyway.toml -locations=filesystem:/path/to/sql "
            + "-environment=local -user=postgres '-password=postgres'",
            "Flyway command: 'flyway info -infoOfState=Pending,Outdated repair migrate "
            + "-configFiles=/path/to/flyway.toml -locations=filesystem:/path/to/sql -environment=local -user=postgres"
            + " '-password=*****'",
        ),
        # Multiple passwords
        (
            "Multiple passwords: password=first pwd='second' pass=\"third\"",
            "Multiple passwords: password='*****' pwd='*****' pass=\"*****\"",
        ),
        # Edge cases
        ("password=''", "password='*****'"),
        ("password='my secret password'", "password='*****'"),
        ("password='secret,with,commas'", "password='*****'"),
        ("password='@*&&#$%@#/", "password='*****'"),
        # Mixed quotes
        (
            """Config: {"password": 'secret123', "host": "localhost"}""",
            """Config: {"password": '*****', "host": "localhost"}""",
        ),
        # Command-line arguments with single quotes
        ("Command: '-password=secret123'", "Command: '-password=*****'"),
        ("Command: '--password=secret123'", "Command: '--password=*****'"),
        # Command-line arguments with double quotes
        ('Command: "-password=secret123"', 'Command: "-password=*****"'),
        ('Command: "--password=secret123"', 'Command: "--password=*****"'),
        # Mixed with other arguments
        (
            "flyway -user=admin '-password=secret' -url=***************************",
            "flyway -user=admin '-password=*****' -url=***************************",
        ),
    ],
)
def test_mask_password(password_masker: PasswordMasker, message: str, expected: str) -> None:
    """Test password masking for various message formats"""
    result = password_masker.mask_password(message)
    assert result == expected


@pytest.mark.parametrize(
    "data, expected",
    [
        # Direct password key
        (
            {"username": "admin", "password": "secret123", "host": "localhost"},
            {"username": "admin", "password": "*****", "host": "localhost"},
        ),
        # Compound password key
        (
            {"username": "admin", "rootPassword": "secret123", "host": "localhost"},
            {"username": "admin", "rootPassword": "*****", "host": "localhost"},
        ),
        # Nested dictionary
        (
            {"database": {"host": "localhost", "password": "secret123"}, "cache": {"redis_password": "redis_secret"}},
            {"database": {"host": "localhost", "password": "*****"}, "cache": {"redis_password": "*****"}},
        ),
    ],
)
def test_mask_password_in_dict(password_masker: PasswordMasker, data: dict[str, Any], expected: dict[str, Any]) -> None:
    """Test masking passwords in dictionary objects"""
    password_masker.mask_password_in_dict(data)

    assert data == expected


@pytest.mark.parametrize("password_key", ["pwd", "pass", "password"])
def test_all_password_keys(password_masker: PasswordMasker, password_key: str) -> None:
    """Test that all password keys are masked"""
    message = f"Config: {password_key}='secret123'"
    result = password_masker.mask_password(message)
    expected = f"Config: {password_key}='*****'"
    assert result == expected
