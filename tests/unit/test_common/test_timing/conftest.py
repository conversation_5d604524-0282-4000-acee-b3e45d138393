from time import sleep
from typing import Callable
from unittest.mock import Mock

import pytest


@pytest.fixture(scope="function")
def mock_logger() -> Mock:
    return Mock()


@pytest.fixture(scope="function")
def simple_function() -> Callable[[int, int], int]:
    def add(x: int, y: int) -> int:
        return x + y

    return add


@pytest.fixture(scope="function")
def delayed_function() -> Callable[[float], str]:
    def delayed(seconds: float) -> str:
        sleep(seconds)

        return "done"

    return delayed


@pytest.fixture(scope="function")
def failing_function() -> Callable[[], None]:
    def failing() -> None:
        raise ValueError("some error")

    return failing
