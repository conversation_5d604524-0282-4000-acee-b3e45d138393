from typing import Any, Callable

import pytest

from common.timing import measure_execution


def test_with_simple_function(simple_function: Callable[[int, int], int]) -> None:
    # act
    result = measure_execution(simple_function, 2, 3)

    # assert
    assert result.result == 5
    assert result.timing >= 0


def test_with_delayed_function(delayed_function: Callable[[float], str]) -> None:
    # arrange
    delay = 0.1

    # act
    result = measure_execution(delayed_function, delay)

    # assert
    assert result.result == "done"
    assert result.timing >= delay


@pytest.mark.parametrize(
    "args, kwargs, expected",
    [
        ((2, 3), {}, 5),  # positional args
        ((), {"x": 2, "y": 3}, 5),  # keyword args
        ((2,), {"y": 3}, 5),  # mixed args
    ],
)
def test_with_different_arg_types(
    simple_function: Callable[[int, int], int],
    args: tuple[Any, ...],
    kwargs: dict[str, Any],
    expected: int,
) -> None:
    # act
    result = measure_execution(simple_function, *args, **kwargs)

    # assert
    assert result.result == expected
    assert result.timing >= 0


def test_with_exception(failing_function: Callable[[], None]) -> None:
    # act & assert
    with pytest.raises(ValueError, match="some error"):
        measure_execution(failing_function)
