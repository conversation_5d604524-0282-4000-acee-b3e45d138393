from logging import INFO, WARNING
from typing import Callable
from unittest.mock import Mock

import pytest

from common.timing import timing


def test_with_default_message(mock_logger: Mock, simple_function: Callable[[int, int], int]) -> None:
    # arrange
    decorated_function = timing(mock_logger)(simple_function)

    # act
    result = decorated_function(2, 3)

    # assert
    assert result == 5
    mock_logger.log.assert_called_once()
    log_call = mock_logger.log.call_args
    assert log_call.args[0] == INFO
    assert "'add' took" in log_call.args[1]


def test_with_custom_message(mock_logger: Mock, simple_function: Callable[[int, int], int]) -> None:
    # arrange
    decorated_function = timing(mock_logger, message="Custom Operation")(simple_function)

    # act
    decorated_function(2, 3)

    # assert
    mock_logger.log.assert_called_once()
    log_call = mock_logger.log.call_args
    assert "'Custom Operation' took" in log_call.args[1]


def test_with_custom_level(mock_logger: Mock, simple_function: Callable[[int, int], int]) -> None:
    # arrange
    decorated_function = timing(mock_logger, level=WARNING)(simple_function)

    # act
    decorated_function(2, 3)

    # assert
    mock_logger.log.assert_called_once()
    assert mock_logger.log.call_args.args[0] == WARNING


def test_measures_execution_time(mock_logger: Mock, delayed_function: Callable[[float], str]) -> None:
    # arrange
    decorated_function = timing(mock_logger)(delayed_function)
    delay = 0.1

    # act
    decorated_function(delay)

    # assert
    mock_logger.log.assert_called_once()
    log_message = mock_logger.log.call_args.args[1]
    assert "'delayed' took" in log_message
    assert "[00:00:00" in log_message


def test_preserves_function_result(mock_logger: Mock, simple_function: Callable[[int, int], int]) -> None:
    # arrange
    decorated_function = timing(mock_logger)(simple_function)

    # act
    result = decorated_function(2, 3)

    # assert
    assert result == 5


def test_with_exception(mock_logger: Mock, failing_function: Callable[[], None]) -> None:
    # arrange
    decorated_function = timing(mock_logger)(failing_function)

    # act & assert
    with pytest.raises(ValueError, match="some error"):
        decorated_function()

    # Verify that even with an exception, timing was logged
    mock_logger.log.assert_called_once()
