from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional

import pytest

from common.utils import (
    escape_sql,
    format_bytes_pretty,
    format_file,
    get_run_id,
    get_unique_identifier,
    string_to_bool,
    to_camel_case,
    to_kebab_case,
    to_snake_case,
    to_space_separated,
)

CURRENT_DIR = Path(__file__).resolve().parent


def test_get_run_id(now: datetime) -> None:
    # arrange
    milliseconds = 123
    given = now + timedelta(milliseconds=milliseconds)
    expected = f"{now.year}{now.month:02d}{now.day:02d}_{now.hour}{now.minute}{now.second}{milliseconds}"

    # act
    actual = get_run_id(given)

    # assert
    assert actual == expected


@pytest.mark.parametrize(
    "given, expected",
    [
        ["", ""],
        ("FooBar", "foo bar"),
        ("foo Bar", "foo bar"),
        ("fooBar", "foo bar"),
        ("foo_bar", "foo bar"),
        ("foo-bar", "foo bar"),
    ],
)
def test_to_space_separated(given: str, expected: str) -> None:
    # act
    actual = to_space_separated(given)

    # assert
    assert actual == expected


@pytest.mark.parametrize(
    "given, expected",
    [
        ["", ""],
        ("FooBar", "foo_bar"),
        ("foo Bar", "foo_bar"),
        ("foo bar", "foo_bar"),
        ("fooBar", "foo_bar"),
        ("foo_bar", "foo_bar"),
    ],
)
def test_to_snake_case(given: str, expected: str) -> None:
    # act
    actual = to_snake_case(given)

    # assert
    assert actual == expected


@pytest.mark.parametrize(
    "given, expected",
    [
        ["", ""],
        ("FooBar", "foo-bar"),
        ("foo Bar", "foo-bar"),
        ("fooBar", "foo-bar"),
        ("foo-bar", "foo-bar"),
    ],
)
def test_to_kebab_case(given: str, expected: str) -> None:
    # act
    actual = to_kebab_case(given)

    # assert
    assert actual == expected


@pytest.mark.parametrize(
    "given, expected",
    [
        ["", ""],
        ("FooBar", "fooBar"),
        ("foo Bar", "fooBar"),
        ("foo bar", "fooBar"),
        ("fooBar", "fooBar"),
        ("foo-bar", "fooBar"),
    ],
)
def test_to_camel_case(given: str, expected: str) -> None:
    # act
    actual = to_camel_case(given)

    # assert
    assert actual == expected


@pytest.mark.parametrize(
    "given, expected",
    [
        ("true", True),
        ("True", True),
        ("1", True),
        ("y", True),
        ("yes", True),
        ("any other string", False),
    ],
)
def test_string_to_bool(given: str, expected: str) -> None:
    # act
    actual = string_to_bool(given)

    # assert
    assert actual == expected


def test_format_file_with_no_args() -> None:
    # arrange
    file_path = CURRENT_DIR / "query1.sql"
    expected = "select * from schema.table;\n"

    # act
    actual = format_file(file_path)

    # assert
    assert actual == expected


def test_format_file_with_args_when_args() -> None:
    # arrange
    file_path = CURRENT_DIR / "query2.sql"
    expected = "select * from schema.table where a='a' and b between 1 and 9.999;\n"

    # act
    actual = format_file(file_path, a="a", b1=1, b2=9.999)

    # assert
    assert actual == expected


def test_format_file_with_args_when_no_args() -> None:
    # arrange
    file_path = CURRENT_DIR / "query2.sql"

    # act
    with pytest.raises(KeyError):
        _ = format_file(file_path)


@pytest.mark.parametrize(
    "valid_length",
    [4, 8, 16, 32],
)
def test_get_unique_identifier_valid_length(valid_length: int) -> None:
    # act
    identifier = get_unique_identifier(valid_length)

    # assert
    assert len(identifier) == valid_length


@pytest.mark.parametrize(
    "invalid_length",
    [
        0,
        3,
        33,
        -1,
    ],
)
def test_get_unique_identifier_invalid_length(invalid_length: int) -> None:
    # arrange
    expected_error = "Length must be between 4 and 32!"

    # act & assert
    with pytest.raises(ValueError, match=expected_error):
        get_unique_identifier(invalid_length)


def test_get_unique_identifier_uniqueness() -> None:
    # arrange
    identifiers = set()
    iterations = 1000
    length = 8

    # act
    for _ in range(iterations):
        identifiers.add(get_unique_identifier(length))

    # assert
    assert len(identifiers) == iterations


@pytest.mark.parametrize(
    "bytes_value, expected",
    [
        (0, "0 B"),
        (1, "1 B"),
        (1023, "1023 B"),
        (1024, "1.00 KB"),
        (1500, "1.46 KB"),
        (1024 * 1024, "1.00 MB"),
        (1024 * 1024 * 1.5, "1.50 MB"),
        (1024 * 1024 * 10, "10.0 MB"),
        (1024 * 1024 * 100, "100 MB"),
        (1024 * 1024 * 1024, "1.00 GB"),
        (1024 * 1024 * 1024 * 1.5, "1.50 GB"),
        (1024 * 1024 * 1024 * 10, "10.0 GB"),
        (1024 * 1024 * 1024 * 100, "100 GB"),
        (1024 * 1024 * 1024 * 1024, "1.00 TB"),
        (1024 * 1024 * 1024 * 1024 * 1.5, "1.50 TB"),
        (1024 * 1024 * 1024 * 1024 * 10, "10.0 TB"),
        (1024 * 1024 * 1024 * 1024 * 100, "100 TB"),
    ],
)
def test_format_bytes_pretty(bytes_value: int, expected: str) -> None:
    # act
    actual = format_bytes_pretty(bytes_value)

    # assert
    assert actual == expected


def test_format_bytes_pretty_negative_value() -> None:
    # arrange
    bytes_value = -1
    expected_error = "Bytes value cannot be negative"

    # act & assert
    with pytest.raises(ValueError, match=expected_error):
        format_bytes_pretty(bytes_value)


@pytest.mark.parametrize(
    "given, expected",
    [
        ("O'Reilly", 'O"Reilly'),
        ("It's John's book", 'It"s John"s book'),
        ("Hello World", "Hello World"),
        ("", ""),
        (None, None),
        (0, "0"),
        (123.456, "123.456"),
        ("'''", '"""'),
    ],
)
def test_escape_sql(given: Optional[str | int], expected: Optional[str]) -> None:
    assert escape_sql(given) == expected
