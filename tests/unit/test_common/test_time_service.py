from datetime import date, datetime

import pytest

from common.time_service import TimeService


def test_today(time_service: TimeService, now: datetime) -> None:
    expected = date(now.year, now.month, now.day)
    assert time_service.today == expected


@pytest.mark.parametrize(
    "days, month, day",
    [
        (0, 6, 3),
        (-1, 6, 4),
        (1, 6, 2),
        (3, 5, 31),
    ],
)
def test_days_ago(time_service: TimeService, days: int, month: int, day: int) -> None:
    expected = date(2024, month, day)

    result = time_service.days_ago(days)
    assert result == expected


def test_last_days_from_today(time_service: TimeService, now: datetime) -> None:
    days = 3
    expected = [
        date(now.year, now.month, now.day),
        date(now.year, now.month, now.day - 1),
        date(now.year, now.month, now.day - 2),
    ]

    result = list(time_service.last_days(days))
    assert result == expected


def test_last_days_from_yesterday(time_service: TimeService, now: datetime) -> None:
    days = 3
    expected = [
        date(now.year, now.month, now.day - 1),
        date(now.year, now.month, now.day - 2),
    ]

    result = list(time_service.last_days(days, start=1))
    assert result == expected


def test_days_range_when_valid_range(time_service: TimeService) -> None:
    start = date(2024, 6, 1)
    end = date(2024, 6, 3)

    expected = [start, date(2024, 6, 2), end]

    result = list(time_service.days_range(start, end))
    assert result == expected


def test_days_range_when_invalid_range(time_service: TimeService) -> None:
    start = date(2024, 6, 3)
    end = date(2024, 6, 1)

    expected: list[date] = []

    result = list(time_service.days_range(start, end))
    assert result == expected


@pytest.mark.parametrize(
    "test_time, expected",
    [
        (datetime(2024, 9, 3, 19, 17, 11, 11), datetime(2024, 9, 3, 19)),
        (datetime(2024, 9, 3, 19), datetime(2024, 9, 3, 19)),
        (datetime(2024, 1, 1), datetime(2024, 1, 1)),
        (datetime(2024, 1, 1, 23, 59, 59, 999), datetime(2024, 1, 1, 23)),
    ],
)
def test_last_hour(test_time: datetime, expected: datetime) -> None:
    actual = TimeService(test_time).last_hour
    assert actual == expected


@pytest.mark.parametrize(
    "test_time, expected_day, expected_number",
    [
        (datetime(2025, 1, 6, 19, 17, 11, 11), "Monday", 0),
        (datetime(2025, 1, 7, 19), "Tuesday", 1),
        (datetime(2025, 1, 8), "Wednesday", 2),
        (datetime(2025, 1, 9, 23, 59, 59, 999), "Thursday", 3),
        (datetime(2025, 1, 10), "Friday", 4),
        (datetime(2025, 1, 11), "Saturday", 5),
        (datetime(2025, 1, 12), "Sunday", 6),
    ],
)
def test_day_of_week(test_time: datetime, expected_day: str, expected_number: int) -> None:
    # arrange
    time_service = TimeService(test_time)

    # act
    actual_day = time_service.day_of_week
    actual_number = time_service.day_of_week_number

    # assert
    assert actual_day == expected_day
    assert actual_number == expected_number
