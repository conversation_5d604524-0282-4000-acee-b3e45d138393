from typing import Optional

import pytest

from common.config import MattermostAlertingConfig

INFO_CHANNEL = "info-channel"
WARNING_CHANNEL = "warning-channel"
ERROR_P1_CHANNEL = "error-p1-channel"
ERROR_P2_CHANNEL = "error-p2-channel"


@pytest.fixture(scope="session")
def mattermost_alerting_config() -> MattermostAlertingConfig:
    return MattermostAlertingConfig(
        webhook_url="webhook_url",
        info_channel=INFO_CHANNEL,
        warning_channel=WARNING_CHANNEL,
        error_p1_channel=ERROR_P1_CHANNEL,
        error_p2_channel=ERROR_P2_CHANNEL,
    )


@pytest.mark.parametrize(
    "severity, criticality, expected",
    [
        (None, None, None),
        (None, "p1", None),
        ("ABC", None, None),
        ("abc", "p1", None),
        ("def", "p2", None),
        ("info", None, INFO_CHANNEL),
        ("Info", None, INFO_CHANNEL),
        ("INFO", None, INFO_CHANNEL),
        ("INFO", "p1", INFO_CHANNEL),
        ("warning", None, WARNING_CHANNEL),
        ("WARNING", None, WARNING_CHANNEL),
        ("Warning", "p1", WARNING_CHANNEL),
        ("error", "p1", ERROR_P1_CHANNEL),
        ("ERROR", "P1", ERROR_P1_CHANNEL),
        ("ERROR", None, ERROR_P2_CHANNEL),
        ("ERROR", "p2", ERROR_P2_CHANNEL),
        ("error", "p3", ERROR_P2_CHANNEL),
    ],
)
def test_get_channel(
    mattermost_alerting_config: MattermostAlertingConfig,
    severity: Optional[str],
    criticality: Optional[str],
    expected: Optional[str],
) -> None:
    actual = mattermost_alerting_config.get_channel(severity, criticality)

    assert actual == expected
