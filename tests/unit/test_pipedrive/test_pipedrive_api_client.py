import json
from typing import Any, <PERSON><PERSON>
from unittest.mock import Mock, patch

import pytest
import requests
from pipedrive_api_client import (
    PipedriveApiClient,
    PipedriveApiClientError,
    PipedriveApiClientPagination,
)
from pipedrive_model import PipedriveOrganization, <PERSON><PERSON><PERSON><PERSON><PERSON>
from requests import Response

from common.time_service import TimeService


@patch.object(requests, "get")
def test_call_endpoint_should_combine_all_uuids_into_one_dict(
    requests_mock: Mock, time_service: TimeService, pipedrive_api_client_fake: PipedriveApiClient
) -> None:
    # arrange
    pagination = PipedriveApiClientPagination(more_items_in_collection=False).to_dict()
    uuid_fields, response_data = get_fake_data(time_service)

    given_data = [{**response_data | uuid_fields}]
    expected_extra_fields = [{"field_key": k, "field_value": v} for k, v in uuid_fields.items()]
    expected = [PipedriveOrganization.from_dict({**response_data | {"extra_fields": expected_extra_fields}})]

    given = Response()
    given._content = json.dumps({"data": given_data, "additional_data": {"pagination": pagination}}).encode()
    requests_mock.return_value = given

    # act
    actual = pipedrive_api_client_fake.call_endpoint(endpoint_name="organizations", entity=PipedriveOrganization)

    # assert
    assert actual == expected


@patch.object(requests, "get")
def test_call_endpoint_should_raise_exception_when_no_data(
    requests_mock: Mock, time_service: TimeService, pipedrive_api_client_fake: PipedriveApiClient
) -> None:
    # Arrange
    given = Response()
    given._content = json.dumps({}).encode()
    requests_mock.return_value = given

    # Act & Assert
    with pytest.raises(PipedriveApiClientError, match="No data found for 'persons' endpoint!"):
        pipedrive_api_client_fake.call_endpoint(endpoint_name="persons", entity=PipedrivePerson)


@patch.object(requests, "get")
def test_call_endpoint_should_raise_exception_when_no_metadata(
    requests_mock: Mock, time_service: TimeService, pipedrive_api_client_fake: PipedriveApiClient
) -> None:
    # Arrange
    uuid_fields, response_data = get_fake_data(time_service)
    given_data = [{**response_data | uuid_fields}]

    given = Response()
    given._content = json.dumps({"data": given_data}).encode()
    requests_mock.return_value = given

    # Act & Assert
    with pytest.raises(
        PipedriveApiClientError, match="No 'additional_data.pagination' data found for 'organizations' endpoint"
    ):
        pipedrive_api_client_fake.call_endpoint(endpoint_name="organizations", entity=PipedriveOrganization)


def get_fake_data(time_service: TimeService) -> Tuple[dict[str, Any], dict[str, Any]]:
    uuid_fields = {
        "22365c84c628e6514b1681e77ea674a347578302": "foo",
        "ee751859985823a86bcf508c0bc652c5cf87e6d2": "bar",
        "bd7cf16944d5475ec858d2a69fe97a6186b11934": "baz",
    }
    response_data = {
        "id": 1,
        "company_id": 1,
        "name": "Test organization",
        "open_deals_count": 1,
        "related_open_deals_count": 1,
        "closed_deals_count": 1,
        "related_closed_deals_count": 1,
        "email_messages_count": 1,
        "people_count": 1,
        "activities_count": 1,
        "done_activities_count": 1,
        "undone_activities_count": 1,
        "files_count": 1,
        "notes_count": 1,
        "followers_count": 1,
        "won_deals_count": 1,
        "related_won_deals_count": 1,
        "lost_deals_count": 1,
        "related_lost_deals_count": 1,
        "active_flag": "True",
        "country_code": "AT",
        "update_time": time_service.now.isoformat(),
        "delete_time": None,
        "add_time": time_service.now.isoformat(),
        "address": "Some address",
        "owner_name": "John Doe",
        "cc_email": "<EMAIL>",
        "not_needed_field_1": "foo",
        "not_needed_field_2": 2,
    }

    return uuid_fields, response_data
