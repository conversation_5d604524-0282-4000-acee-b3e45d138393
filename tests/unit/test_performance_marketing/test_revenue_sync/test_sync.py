import os
from datetime import date
from unittest.mock import Mock, patch

import pandas as pd
import pytest
from pandas import DataFrame
from pandas._testing import assert_frame_equal
from revenue_sync_model import RevenueDataSchema, RevenueSyncConfig
from sync import RevenueSync

from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService


def test_report_date_from_env(revenue_sync: RevenueSync) -> None:
    # Arrange
    expected_date = date(2025, 5, 16)
    os.environ["REPORT_DATE"] = expected_date.isoformat()

    # Act
    actual_date = revenue_sync.report_date

    # Assert
    assert actual_date == expected_date


def test_report_date_default(revenue_sync: RevenueSync, time_service: TimeService) -> None:
    # Arrange
    os.environ.pop("REPORT_DATE", None)

    # Act
    result = revenue_sync.report_date

    # Assert
    assert result == time_service.today


def test_extract(
    config: RevenueSyncConfig, revenue_sync: RevenueSync, pg_repo_mock: Mock, revenue_data: DataFrameWithSchema
) -> None:
    # Arrange
    pg_repo_mock.select_revenue_data.return_value = revenue_data

    # Act
    actual_data = revenue_sync.extract()

    # Assert
    pg_repo_mock.select_revenue_data.assert_called_once_with(revenue_sync.report_date, config.time_zone)
    assert_frame_equal(actual_data.dataframe_with_schema, revenue_data.dataframe_with_schema)


def test_load(revenue_sync: RevenueSync, google_sheets_client_mock: Mock, revenue_data: DataFrameWithSchema) -> None:
    # Act
    revenue_sync.load(revenue_data)

    # Assert
    google_sheets_client_mock.reload_google_sheet.assert_called_once_with(df=revenue_data.dataframe_with_schema)


def test_load_empty_dataframe(revenue_sync: RevenueSync, google_sheets_client_mock: Mock) -> None:
    # Arrange
    empty_data = DataFrameWithSchema(schema=RevenueDataSchema.SCHEMA, dataframe=pd.DataFrame())

    # Act & Assert
    with pytest.raises(ValueError, match="No revenue data to load!"):
        revenue_sync.load(empty_data)

    google_sheets_client_mock.reload_google_sheet.assert_not_called()


def test_run(revenue_sync: RevenueSync, revenue_data: DataFrame) -> None:
    with patch.object(revenue_sync, "extract", return_value=revenue_data) as mock_extract:
        with patch.object(revenue_sync, "load") as mock_load:
            # Act
            revenue_sync.run()

            # Assert
            mock_extract.assert_called_once()
            mock_load.assert_called_once_with(revenue_data)
