from unittest.mock import Mock

import pytest
from pandas import DataFrame
from revenue_sync_model import RevenueDataSchema, RevenueSyncConfig
from revenue_sync_pg_repository import RevenueSyncPgRepository
from sync import RevenueSync

from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService


@pytest.fixture
def revenue_data() -> DataFrameWithSchema:
    df = DataFrame(
        {
            RevenueDataSchema.HOUR: [9, 10, 11, 12, 13],
            RevenueDataSchema.COUNTRY: ["DE", "FR", "ES", "IT", "UK"],
            RevenueDataSchema.REVENUE: [1000.50, 750.25, 500.75, 1200.00, 950.50],
            RevenueDataSchema.DISCOUNT: [100.00, 75.00, 50.00, 120.00, 95.00],
            RevenueDataSchema.FINAL_REVENUE: [900.50, 675.25, 450.75, 1080.00, 855.50],
        }
    ).astype(RevenueDataSchema.SCHEMA.data_types)

    return DataFrameWithSchema(schema=RevenueDataSchema.SCHEMA, dataframe=df)


@pytest.fixture
def pg_repo_mock() -> Mock:
    return Mock(spec=RevenueSyncPgRepository)


@pytest.fixture
def config() -> RevenueSyncConfig:
    return RevenueSyncConfig()


@pytest.fixture
def revenue_sync(
    config: RevenueSyncConfig, pg_repo_mock: Mock, google_sheets_client_mock: Mock, time_service: TimeService
) -> RevenueSync:
    return RevenueSync(
        config=config,
        pg_repo=pg_repo_mock,
        google_sheets_client=google_sheets_client_mock,
        time_service=time_service,
    )
