from unittest.mock import Mock

import pytest
from microsoft_ads_api_client import MicrosoftAdsApiClient
from microsoft_ads_etl_etl import MicrosoftAdsEtl, MicrosoftAdsEtlError
from microsoft_ads_etl_model import MicrosoftAdsReportEtlSchema
from pandas import DataFrame

from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService


def test_file_name(microsoft_ads_etl: MicrosoftAdsEtl) -> None:
    # Act & Assert
    assert microsoft_ads_etl.local_file_path.name == "test_microsoft_ads_costs_20240603_235959000.parquet"


def test_data_lake_partition(microsoft_ads_etl: MicrosoftAdsEtl) -> None:
    # Act & Assert
    assert microsoft_ads_etl.data_lake_partition.base_path == "test/microsoft_ads/costs/run_id=20240603_235959000"


def test_extract(
    microsoft_ads_etl: MicrosoftAdsEtl,
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    microsoft_ads_report_df: DataFrameWithSchema,
) -> None:
    # arrange
    microsoft_ads_api_client.get_performance_report = Mock(return_value=microsoft_ads_report_df)

    # act
    result = microsoft_ads_etl.extract()

    # assert
    microsoft_ads_api_client.get_performance_report.assert_called_once()
    assert result == microsoft_ads_report_df


def test_transform(
    microsoft_ads_etl: MicrosoftAdsEtl, microsoft_ads_report_df: DataFrameWithSchema, time_service: TimeService
) -> None:
    # Act
    result = microsoft_ads_etl.transform(microsoft_ads_report_df)

    # Assert
    assert isinstance(result, DataFrameWithSchema)
    assert result.schema == MicrosoftAdsReportEtlSchema.SCHEMA
    assert all(time_service.now == result.dataframe_with_schema[MicrosoftAdsReportEtlSchema.UPDATED_AT])
    # mock has 4 rows, one of which had Spend = 0 and one Spend = -1
    assert len(result.dataframe_with_schema) == 2
    assert all(result.dataframe_with_schema[MicrosoftAdsReportEtlSchema.SPEND] > 0)


def test_transform_logs_error_on_empty_df(microsoft_ads_etl: MicrosoftAdsEtl) -> None:
    # arrange: empty DataFrameWithSchema
    empty_df = DataFrameWithSchema(dataframe=DataFrame(), schema=MicrosoftAdsReportEtlSchema.SCHEMA)

    # act and assert
    with pytest.raises(MicrosoftAdsEtlError, match="No data to transform."):
        microsoft_ads_etl.transform(empty_df)


def test_transform_logs_error_on_nulls(
    microsoft_ads_etl: MicrosoftAdsEtl, microsoft_ads_report_df: DataFrameWithSchema
) -> None:
    # arrange: add null
    microsoft_ads_report_df.dataframe.loc[0, MicrosoftAdsReportEtlSchema.SPEND] = None

    # act and assert
    with pytest.raises(MicrosoftAdsEtlError, match="Nulls found in dataframe."):
        microsoft_ads_etl.transform(microsoft_ads_report_df)


def test_load_data_lake(
    microsoft_ads_etl: MicrosoftAdsEtl,
    microsoft_ads_report_df: DataFrameWithSchema,
    data_lake_repository_mock: Mock,
) -> None:
    # Act
    result = microsoft_ads_etl.transform(microsoft_ads_report_df)
    microsoft_ads_etl.load_data_lake(result)

    # Assert
    data_lake_repository_mock.write_file.assert_called_with(
        microsoft_ads_etl.local_file_path, microsoft_ads_etl.data_lake_partition
    )


def test_load_big_query(microsoft_ads_etl: MicrosoftAdsEtl, bq_repository_mock: Mock) -> None:
    # Arrange
    bq_repository_mock.load_costs_table.return_value = 1
    bq_repository_mock.check_costs_table.return_value = 2

    # Act
    actual = microsoft_ads_etl.load_big_query()

    # Assert
    bq_repository_mock.load_costs_table.assert_called_once_with(microsoft_ads_etl.run_id)
    assert actual == 3


def test_load_calls_both_methods(
    microsoft_ads_etl: MicrosoftAdsEtl,
    microsoft_ads_report_df: DataFrameWithSchema,
    data_lake_repository_mock: Mock,
    bq_repository_mock: Mock,
) -> None:
    bq_repository_mock.load_costs_table.return_value = 0
    bq_repository_mock.check_costs_table.return_value = 0

    # Act
    result = microsoft_ads_etl.transform(microsoft_ads_report_df)
    microsoft_ads_etl.load(result)

    # Assert
    data_lake_repository_mock.write_file.assert_called_with(
        microsoft_ads_etl.local_file_path, microsoft_ads_etl.data_lake_partition
    )

    bq_repository_mock.load_costs_table.assert_called_with(microsoft_ads_etl.run_id)
