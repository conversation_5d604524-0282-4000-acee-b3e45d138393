import io
import zipfile
from unittest.mock import Mock, patch

import pytest
import requests
from microsoft_ads_api_client import MicrosoftAdsApiClient, MicrosoftAdsApiError
from microsoft_ads_api_model import PollReportResponse, ReportRequestResponse
from pandas.testing import assert_frame_equal

from common.pandas_utils import DataFrameWithSchema


def test_wrapper_get_performance_report(
    microsoft_ads_api_client_mock_performance_report: MicrosoftAdsApiClient,
    access_token: str,
    report_request_response: ReportRequestResponse,
    microsoft_ads_report_df: DataFrameWithSchema,
) -> None:
    """Test the main get_performance_report wrapper method orchestrates all internal methods correctly."""
    # act
    result = microsoft_ads_api_client_mock_performance_report.get_performance_report()

    # assert
    # Verify the calls were made correctly
    microsoft_ads_api_client_mock_performance_report.get_access_token.assert_called_once()  # noqa
    microsoft_ads_api_client_mock_performance_report.submit_report_request.assert_called_once_with(  # noqa
        access_token=access_token
    )
    microsoft_ads_api_client_mock_performance_report.poll_and_download_report.assert_called_once_with(  # noqa
        report_response=report_request_response, access_token=access_token
    )

    # Verify the final result
    assert result == microsoft_ads_report_df


def test_wrapper_poll_and_download_report(
    microsoft_ads_api_client_mock_poll_download_report: MicrosoftAdsApiClient,
    report_request_response: ReportRequestResponse,
    access_token: str,
    report_download_url: str,
    test_content_bytes: bytes,
    microsoft_ads_report_df: DataFrameWithSchema,
) -> None:
    """Test the poll_and_download_report wrapper method orchestrates polling, downloading, and parsing correctly."""
    # act
    result = microsoft_ads_api_client_mock_poll_download_report.poll_and_download_report(
        report_response=report_request_response, access_token=access_token
    )

    # assert
    # Verify the calls were made correctly
    microsoft_ads_api_client_mock_poll_download_report.poll_report_status.assert_called_once_with(  # noqa
        report_response=report_request_response, access_token=access_token
    )
    microsoft_ads_api_client_mock_poll_download_report.download_report.assert_called_once_with(  # noqa
        download_url=report_download_url
    )
    microsoft_ads_api_client_mock_poll_download_report.parse_zip_report.assert_called_once_with(  # noqa
        content=test_content_bytes
    )

    # Verify the final result
    assert result == microsoft_ads_report_df


@pytest.mark.parametrize(
    "method_name,http_method,args",
    [
        ("get_access_token", "post", []),
        ("submit_report_request", "post", ["access_token"]),
        ("poll_report_status", "post", ["report_response", "access_token"]),
        ("download_report", "get", ["download_url"]),
    ],
)
def test_http_error_handling(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    method_name: str,
    http_method: str,
    args: list[str],
    access_token: str,
    report_request_response: ReportRequestResponse,
    mock_http_error_response: Mock,
    report_download_url: str,
) -> None:
    """Test handling of HTTP errors across all request methods"""
    # arrange
    with patch(f"requests.{http_method}") as mock_request:
        mock_request.return_value = mock_http_error_response

        method = getattr(microsoft_ads_api_client, method_name)
        method_args = {
            "access_token": access_token,
            "report_response": report_request_response,
            "download_url": report_download_url,
        }

        # act - assert
        with pytest.raises(requests.HTTPError):
            method(**{arg: method_args[arg] for arg in args})


@pytest.mark.parametrize(
    "method_name,http_method,args",
    [
        ("get_access_token", "post", []),
        ("submit_report_request", "post", ["access_token"]),
        ("poll_report_status", "post", ["report_response", "access_token"]),
        ("download_report", "get", ["download_url"]),
    ],
)
def test_connection_error_handling(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    method_name: str,
    http_method: str,
    args: list[str],
    access_token: str,
    report_request_response: ReportRequestResponse,
    mock_connection_error: Mock,
    report_download_url: str,
) -> None:
    """Test handling of connection errors across all request methods"""
    # arrange
    with patch(f"requests.{http_method}", mock_connection_error):
        method = getattr(microsoft_ads_api_client, method_name)
        method_args = {
            "access_token": access_token,
            "report_response": report_request_response,
            "download_url": report_download_url,
        }

        # act - assert
        with pytest.raises(requests.ConnectionError):
            method(**{arg: method_args[arg] for arg in args})


def test_poll_report_status_error(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    access_token: str,
    report_request_response: ReportRequestResponse,
    report_request_status_error: PollReportResponse,
) -> None:
    """Test that error status from polling raises appropriate error"""
    with patch("requests.post") as mock_post:
        mock_post.return_value.json.return_value = report_request_status_error

        with pytest.raises(MicrosoftAdsApiError, match="Report generation failed"):
            microsoft_ads_api_client.poll_report_status(
                report_response=report_request_response, access_token=access_token
            )


def test_poll_report_status_timeout(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    access_token: str,
    report_request_response: ReportRequestResponse,
    report_request_status_pending: PollReportResponse,
) -> None:
    """Test that pending status eventually times out"""
    # arrange - time out quicker for the test
    microsoft_ads_api_client.MAX_POLL_ITERATIONS = 1
    microsoft_ads_api_client.POLL_INTERVAL_SECONDS = 1

    with patch("requests.post") as mock_post:
        mock_post.return_value.json.return_value = report_request_status_pending

        # act - assert
        with pytest.raises(MicrosoftAdsApiError, match="Report polling timed out"):
            microsoft_ads_api_client.poll_report_status(
                report_response=report_request_response, access_token=access_token
            )


def test_download_report_empty_response(
    microsoft_ads_api_client: MicrosoftAdsApiClient, report_download_url: str, empty_content_bytes: bytes
) -> None:
    """Test that empty response content raises error"""
    with patch("requests.get") as mock_get:
        mock_get.return_value.content = empty_content_bytes

        with pytest.raises(MicrosoftAdsApiError, match="Download response is empty"):
            microsoft_ads_api_client.download_report(report_download_url)


def test_download_report_wrong_content_type(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    report_download_url: str,
    test_content_bytes: bytes,
    wrong_download_report_headers: dict[str, str],
) -> None:
    """Test that non-ZIP content type raises error"""
    # arrange
    with patch("requests.get") as mock_get:
        mock_get.return_value.content = test_content_bytes
        mock_get.return_value.headers = wrong_download_report_headers

        # act - assert
        with pytest.raises(MicrosoftAdsApiError, match="Expected ZIP file but got Content-Type:"):
            microsoft_ads_api_client.download_report(report_download_url)


def test_download_report_invalid_zip(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    report_download_url: str,
    test_content_bytes: bytes,
    correct_download_report_headers: dict[str, str],
) -> None:
    """Test that non-ZIP content raises error"""
    # arrange
    with patch("requests.get") as mock_get:
        mock_get.return_value.content = test_content_bytes
        mock_get.return_value.headers = correct_download_report_headers

        # act - assert
        with pytest.raises(MicrosoftAdsApiError, match="Download response is not a valid ZIP file"):
            microsoft_ads_api_client.download_report(report_download_url)


def test_parse_zip_report_empty_zip(microsoft_ads_api_client: MicrosoftAdsApiClient) -> None:
    """
    Test that empty ZIP file raises error.

    ZIP file structure explanation:
    1. PK\\x05\\x06 - End of central directory signature
    2. Followed by 18 null bytes (\\x00) which represent:
       - Disk numbers
       - Number of entries
       - Size and offset of central directory

    This creates a valid but empty ZIP file with no files inside.
    See: https://en.wikipedia.org/wiki/ZIP_(file_format)#End_of_central_directory_record_(EOCD)
    """
    # arrange
    # Create empty ZIP in memory using the ZIP file format specification
    empty_zip_bytes = io.BytesIO()
    with zipfile.ZipFile(empty_zip_bytes, mode="w"):
        # The empty context will create a valid empty ZIP
        pass

    # act - assert
    with pytest.raises(MicrosoftAdsApiError, match="ZIP file is empty"):
        microsoft_ads_api_client.parse_zip_report(empty_zip_bytes.getvalue())


def test_parse_zip_report_success(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    zipped_csv_with_footer: bytes,
    microsoft_ads_report_df: DataFrameWithSchema,
) -> None:
    """Test successful parsing of a properly formatted ZIP report"""
    # arrange
    content = zipped_csv_with_footer

    # act
    actual = microsoft_ads_api_client.parse_zip_report(content)

    # assert
    assert_frame_equal(actual.dataframe, microsoft_ads_report_df.dataframe)
