import io
import zipfile
from unittest.mock import Mock

import pandas as pd
import pytest
import requests
from microsoft_ads_api_client import MicrosoftAdsApiClient, MicrosoftAdsApiConfig
from microsoft_ads_api_model import (
    DownloadReportResponse,
    PollReportResponse,
    ReportRequestResponse,
    ReportRequestStatus,
    ReportStatus,
)
from microsoft_ads_etl_bq_repo import MicrosoftAdsBqRepository
from microsoft_ads_etl_etl import MicrosoftAdsEtl
from microsoft_ads_etl_model import MicrosoftAdsEtlConfig, MicrosoftAdsReportEtlSchema

from common.data_lake_repository import DataLakeRepository
from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService


# MICROSOFT ADS API FIXTURES
@pytest.fixture(scope="session")
def microsoft_ads_api_config() -> MicrosoftAdsApiConfig:
    return MicrosoftAdsApiConfig(
        tenant_id="tenant_test_123",
        client_id="client_test_123",
        client_secret="client_secret_test_123",
        refresh_token="refresh_token_test_123",
        developer_token="developer_test_123",
        customer_id="customer_test_123",
        account_id="account_test_123",
    )


@pytest.fixture(scope="session")
def access_token() -> str:
    return "access_token_test_123"


@pytest.fixture(scope="session")
def report_request_response() -> ReportRequestResponse:
    return ReportRequestResponse(ReportRequestId="test_report_request_id_123")


@pytest.fixture(scope="session")
def report_download_url() -> str:
    return "https://test-download-url"


@pytest.fixture(scope="session")
def report_request_status_success(report_download_url: str) -> ReportRequestStatus:
    return ReportRequestStatus(Status=ReportStatus.SUCCESS, ReportDownloadUrl=report_download_url)


@pytest.fixture(scope="session")
def report_request_status_error() -> PollReportResponse:
    return PollReportResponse(ReportRequestStatus=ReportRequestStatus(Status=ReportStatus.ERROR, ReportDownloadUrl=""))


@pytest.fixture(scope="session")
def report_request_status_pending() -> PollReportResponse:
    return PollReportResponse(
        ReportRequestStatus=ReportRequestStatus(Status=ReportStatus.PENDING, ReportDownloadUrl="")
    )


@pytest.fixture(scope="session")
def test_content_bytes() -> bytes:
    return b"many_bytes_much_wow"


@pytest.fixture(scope="session")
def empty_content_bytes() -> bytes:
    return b""


@pytest.fixture(scope="function")
def microsoft_ads_report_df() -> DataFrameWithSchema:
    """
    Test DataFrame for Microsoft Ads report.

    :returns: DataFrameWithSchema with test data matching the expected schema
    """
    # Create a DataFrame with the expected schema for Microsoft Ads report
    data = {
        MicrosoftAdsReportEtlSchema.CAMPAIGN_NAME: ["Campaign1", "Campaign1", "Campaign2", "Campaign3"],
        MicrosoftAdsReportEtlSchema.HOUR_OF_DAY: [9, 12, 15, 19],
        MicrosoftAdsReportEtlSchema.IMPRESSIONS: [100, 50, 200, 300],
        MicrosoftAdsReportEtlSchema.SPEND: [550.5, 550.5, 0, -1],
    }
    return DataFrameWithSchema(dataframe=pd.DataFrame(data), schema=MicrosoftAdsReportEtlSchema.SCHEMA)


@pytest.fixture(scope="function")
def microsoft_ads_api_client(microsoft_ads_api_config: MicrosoftAdsApiConfig) -> MicrosoftAdsApiClient:
    """A real MicrosoftAdsApiClient instance for testing."""
    return MicrosoftAdsApiClient(microsoft_ads_api_config)


@pytest.fixture(scope="function")
def microsoft_ads_api_client_mock_performance_report(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    access_token: str,
    report_request_response: ReportRequestResponse,
    microsoft_ads_report_df: DataFrameWithSchema,
) -> MicrosoftAdsApiClient:
    """MicrosoftAdsApiClient with mocked internal methods to test get_performance_report() wrapper."""
    # Patch the internal methods with mocks
    microsoft_ads_api_client.get_access_token = Mock(return_value=access_token)
    microsoft_ads_api_client.submit_report_request = Mock(return_value=report_request_response)
    microsoft_ads_api_client.poll_and_download_report = Mock(return_value=microsoft_ads_report_df)

    return microsoft_ads_api_client


@pytest.fixture(scope="function")
def microsoft_ads_api_client_mock_poll_download_report(
    microsoft_ads_api_client: MicrosoftAdsApiClient,
    report_request_status_success: ReportRequestStatus,
    test_content_bytes: bytes,
    microsoft_ads_report_df: DataFrameWithSchema,
) -> MicrosoftAdsApiClient:
    """MicrosoftAdsApiClient with mocked internal methods to test poll_and_download_report() wrapper."""
    # Patch the internal methods with mocks
    microsoft_ads_api_client.poll_report_status = Mock(return_value=report_request_status_success)
    microsoft_ads_api_client.download_report = Mock(return_value=test_content_bytes)
    microsoft_ads_api_client.parse_zip_report = Mock(return_value=microsoft_ads_report_df)

    return microsoft_ads_api_client


@pytest.fixture(scope="session")
def mock_http_error_response() -> Mock:
    """Mock response that raises HTTPError when raise_for_status is called."""
    response = Mock(spec=requests.Response)
    response.raise_for_status.side_effect = requests.HTTPError()
    return response


@pytest.fixture(scope="session")
def mock_connection_error() -> Mock:
    """Mock that raises ConnectionError when called."""
    mock = Mock()
    mock.side_effect = requests.ConnectionError()
    return mock


@pytest.fixture(scope="session")
def empty_zip_bytes() -> bytes:
    """Create a valid but empty ZIP file in memory."""
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, mode="w"):
        pass  # Creates empty ZIP
    return zip_buffer.getvalue()


@pytest.fixture(scope="session")
def correct_download_report_headers() -> dict[str, str]:
    return {DownloadReportResponse.header_key: DownloadReportResponse.header_value}


@pytest.fixture(scope="session")
def wrong_download_report_headers() -> dict[str, str]:
    return {DownloadReportResponse.header_key: "text/plain"}


@pytest.fixture(scope="function")
def zipped_csv_with_footer(microsoft_ads_report_df: DataFrameWithSchema) -> bytes:
    """
    Mock zipped CSV with footer for report download.
    Downloaded raw csv and saved (trimmed from 2.9k lines) in this folder for validation.

    This mock must implement the full requests.Response interface that the code expects:
    - content: returns the zipped CSV bytes
    - raise_for_status: checks for HTTP errors (does nothing in successful test scenarios)

    :param microsoft_ads_report_df: test DataFrame
    :returns: bytes representing a zipped CSV file
    """
    report_header = (
        '"Report Name: My Account Performance Report"\n'
        '"Report Time: 7/14/2025"\n'
        '"Time Zone: (GMT+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna"\n'
        '"Last Completed Available Day: 7/15/2025 2:55:00 PM (GMT)"\n'
        '"Last Completed Available Hour: 7/15/2025 2:55:00 PM (GMT)"\n'
        '"Report Aggregation: Hour of day"\n'
        '"Report Filter:"\n'
        '"Potential Incomplete Data: true"\n'
        '"Rows: 3"\n'
        "\n"
    )
    csv_data = microsoft_ads_report_df.dataframe.to_csv(index=False)
    csv_data_with_header_and_footer = (
        report_header
        + csv_data
        + '"@2024 Microsoft Corporation. All rights reserved. All data is provided ""as-is"""\n'
    )
    zip_bytes = io.BytesIO()
    with zipfile.ZipFile(zip_bytes, mode="w") as zf:
        zf.writestr("report.csv", csv_data_with_header_and_footer.encode("utf-8"))
    zip_bytes.seek(0)

    return zip_bytes.read()


# MICROSOFT ADS ETL FIXTURES
@pytest.fixture(scope="session")
def microsoft_ads_etl_config() -> MicrosoftAdsEtlConfig:
    return MicrosoftAdsEtlConfig(
        file_prefix="test_microsoft_ads_costs",
        data_lake_base_path="test/microsoft_ads/costs",
    )


@pytest.fixture(scope="session")
def data_lake_repository_mock() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="session")
def bq_repository_mock() -> Mock:
    mock = Mock(spec=MicrosoftAdsBqRepository)
    mock.get_last_run_date.return_value = None

    return mock


@pytest.fixture(scope="function")
def microsoft_ads_etl(
    microsoft_ads_etl_config: MicrosoftAdsEtlConfig,
    microsoft_ads_api_client_mock_performance_report: MicrosoftAdsApiClient,
    data_lake_repository_mock: Mock,
    bq_repository_mock: Mock,
    time_service: TimeService,
) -> MicrosoftAdsEtl:
    return MicrosoftAdsEtl(
        config=microsoft_ads_etl_config,
        microsoft_ads_api_client=microsoft_ads_api_client_mock_performance_report,
        raw_data_lake_repository=data_lake_repository_mock,
        bq_repository=bq_repository_mock,
        time_service=time_service,
    )
