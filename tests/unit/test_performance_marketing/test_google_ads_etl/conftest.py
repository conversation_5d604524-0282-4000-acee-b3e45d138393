from datetime import date
from unittest.mock import Mock

import pytest
from ads_etl import GoogleAdsEtl
from google_ads_bq_repo import GoogleAdsCostsBqRepository
from google_ads_client import GoogleAdsClient
from google_ads_client_model import GoogleAdsCost, GoogleAdsCustomer
from google_ads_etl_model import GoogleAdsConfig, GoogleAdsDataSchema
from pandas import DataFrame

from common.data_lake_repository import DataLakeRepository
from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService


@pytest.fixture(scope="session")
def google_ads_config() -> GoogleAdsConfig:
    return GoogleAdsConfig(
        file_prefix="test_google_ads_costs", data_lake_base_path="test/google_ads/costs", max_workers=2
    )


@pytest.fixture(scope="session")
def google_ads_client_mock() -> Mock:
    mock = Mock(spec=GoogleAdsClient)
    mock.get_customers.return_value = []
    mock.get_costs.return_value = []

    return mock


@pytest.fixture(scope="session")
def data_lake_repository_mock() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="session")
def bq_repository_mock() -> Mock:
    mock = Mock(spec=GoogleAdsCostsBqRepository)
    mock.get_last_run_date.return_value = None

    return mock


@pytest.fixture(scope="session")
def google_ads_customers() -> list[GoogleAdsCustomer]:
    return [
        GoogleAdsCustomer(id="**********", name="Test Customer 1"),
        GoogleAdsCustomer(id="**********", name="Test Customer 2"),
    ]


@pytest.fixture(scope="session")
def google_ads_costs() -> list[GoogleAdsCost]:
    return [
        GoogleAdsCost(
            cost_date=date(2024, 1, 15),
            cost_hour=0,
            client_account="Test Customer 1",
            campaign_name="Campaign 1",
            costs=10.50,
        ),
        GoogleAdsCost(
            cost_date=date(2024, 1, 15),
            cost_hour=1,
            client_account="Test Customer 1",
            campaign_name="Campaign 1",
            costs=15.75,
        ),
        GoogleAdsCost(
            cost_date=date(2024, 1, 15),
            cost_hour=0,
            client_account="Test Customer 2",
            campaign_name="Campaign 2",
            costs=8.25,
        ),
    ]


@pytest.fixture(scope="session")
def google_ads_costs_dataframe(google_ads_costs: list[GoogleAdsCost], time_service: TimeService) -> DataFrameWithSchema:
    df = DataFrame(google_ads_costs)
    df[GoogleAdsDataSchema.UPDATED_AT] = time_service.now

    return DataFrameWithSchema(
        schema=GoogleAdsDataSchema.SCHEMA, dataframe=df.astype(GoogleAdsDataSchema.SCHEMA.data_types)
    )


@pytest.fixture(scope="session")
def google_ads_etl(
    google_ads_config: GoogleAdsConfig,
    google_ads_client_mock: Mock,
    data_lake_repository_mock: Mock,
    bq_repository_mock: Mock,
    time_service: TimeService,
) -> GoogleAdsEtl:
    return GoogleAdsEtl(
        config=google_ads_config,
        google_ads_client=google_ads_client_mock,
        raw_data_lake_repository=data_lake_repository_mock,
        bq_repository=bq_repository_mock,
        time_service=time_service,
    )
