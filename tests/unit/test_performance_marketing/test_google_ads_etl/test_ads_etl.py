from datetime import date
from unittest.mock import Mock

import pytest
from ads_etl import GoogleAdsEtl
from google_ads_client_model import GoogleAdsCost, GoogleAdsCustomer
from google_ads_etl_model import GoogleAdsDataSchema

from common.pandas_utils import DataFrameWithSchema
from common.time_service import TimeService


def test_file_name(google_ads_etl: GoogleAdsEtl) -> None:
    # Act & Assert
    assert google_ads_etl.local_file_path.name == "test_google_ads_costs_20240603_235959000.parquet"


def test_data_lake_partition(google_ads_etl: GoogleAdsEtl) -> None:
    # Act & Assert
    assert google_ads_etl.data_lake_partition.base_path == "test/google_ads/costs/run_id=20240603_235959000"


@pytest.mark.parametrize(
    "customers, expected_calls",
    [
        ([], 0),
        ([GoogleAdsCustomer(id="123", name="Customer 1")], 1),
        ([GoogleAdsCustomer(id="123", name="Customer 1"), GoogleAdsCustomer(id="456", name="Customer 2")], 2),
    ],
)
def test_extract_customers_calls(
    google_ads_etl: GoogleAdsEtl,
    google_ads_client_mock: Mock,
    customers: list[GoogleAdsCustomer],
    expected_calls: int,
) -> None:
    # Arrange
    google_ads_client_mock.reset_mock()
    google_ads_client_mock.get_customers.return_value = customers
    google_ads_client_mock.get_costs.return_value = []

    # Act
    google_ads_etl.extract()

    # Assert
    google_ads_client_mock.get_customers.assert_called_once()
    assert google_ads_client_mock.get_costs.call_count == expected_calls


def test_extract_aggregates_costs(
    google_ads_etl: GoogleAdsEtl,
    google_ads_client_mock: Mock,
    google_ads_customers: list[GoogleAdsCustomer],
    google_ads_costs: list[GoogleAdsCost],
) -> None:
    # Arrange
    google_ads_client_mock.get_customers.return_value = google_ads_customers
    google_ads_client_mock.get_costs.side_effect = [
        google_ads_costs[:2],  # First customer gets first 2 costs
        google_ads_costs[2:],  # Second customer gets remaining costs
    ]

    # Act
    actual = google_ads_etl.extract()

    # Assert
    assert actual == google_ads_costs


def test_transform(
    google_ads_etl: GoogleAdsEtl, google_ads_costs: list[GoogleAdsCost], time_service: TimeService
) -> None:
    # Act
    result = google_ads_etl.transform(google_ads_costs)

    # Assert
    assert isinstance(result, DataFrameWithSchema)
    assert GoogleAdsDataSchema.UPDATED_AT in result.dataframe_with_schema.columns
    assert all(result.dataframe_with_schema[GoogleAdsDataSchema.UPDATED_AT] == time_service.now)


@pytest.mark.parametrize(
    "costs_data, expected_schema",
    [
        ([], GoogleAdsDataSchema.SCHEMA),  # Empty data should have 6 columns (5 original + updated_at)
        (
            [
                GoogleAdsCost(
                    cost_date=date(2024, 1, 15),
                    cost_hour=0,
                    client_account="Test",
                    campaign_name="Campaign",
                    costs=10.0,
                )
            ],
            GoogleAdsDataSchema.SCHEMA,
        ),
    ],
)
def test_transform_dataframe_schema(
    google_ads_etl: GoogleAdsEtl, costs_data: list[GoogleAdsCost], expected_schema: tuple[int, int]
) -> None:
    # Act
    result = google_ads_etl.transform(costs_data)

    # Assert
    assert result.schema == expected_schema


def test_load_data_lake(
    google_ads_etl: GoogleAdsEtl,
    google_ads_costs_dataframe: DataFrameWithSchema,
    data_lake_repository_mock: Mock,
) -> None:
    # Act
    google_ads_etl.load_data_lake(google_ads_costs_dataframe)

    # Assert
    data_lake_repository_mock.write_file.assert_called_with(
        google_ads_etl.local_file_path, google_ads_etl.data_lake_partition
    )


def test_load_big_query(google_ads_etl: GoogleAdsEtl, bq_repository_mock: Mock) -> None:
    # Arrange
    bq_repository_mock.load_costs_table.return_value = 1
    bq_repository_mock.check_costs_table.return_value = 2

    # Act
    actual = google_ads_etl.load_big_query()

    # Assert
    bq_repository_mock.load_costs_table.assert_called_once_with(google_ads_etl.run_id)
    assert actual == 3


def test_load_calls_both_methods(
    google_ads_etl: GoogleAdsEtl,
    google_ads_costs_dataframe: DataFrameWithSchema,
    data_lake_repository_mock: Mock,
    bq_repository_mock: Mock,
) -> None:
    bq_repository_mock.load_costs_table.return_value = 0
    bq_repository_mock.check_costs_table.return_value = 0

    # Act
    google_ads_etl.load(google_ads_costs_dataframe)

    # Assert
    data_lake_repository_mock.write_file.assert_called_with(
        google_ads_etl.local_file_path, google_ads_etl.data_lake_partition
    )

    bq_repository_mock.load_costs_table.assert_called_with(google_ads_etl.run_id)
