from datetime import datetime

import pytest
from alerting_shared.notifications import (
    NotificationMessage,
    create_notification_markdown,
)

from common.cloud_logging_client.model import LogEntry
from common.consts import GCP_CONSOLE_BASE_URL, AlertSeverity


@pytest.fixture
def message(now: datetime) -> NotificationMessage:
    return NotificationMessage(
        started_at=now,
        owner="@tomasz.saluszewski",
        backup="@krzysztof",
        resource_name="test_resource",
        resource_url=f"{GCP_CONSOLE_BASE_URL}/functions/details/europe-west3/nrm-etl",
        incident_url=f"{GCP_CONSOLE_BASE_URL}/monitoring/alerting/policies/4887759640129958342",
        logs_url=f"{GCP_CONSOLE_BASE_URL}/logs",
        severity=AlertSeverity.ERROR,
        log_entries=[LogEntry(timestamp=now, payload={"foo": "bar"})],
    )


def test_create_notification_markdown(message: NotificationMessage) -> None:
    expected = f"""| Start Date              | Resource Name   | Resource URL                                                                        | Incident URL                                                                                  | Logs URL                                      | Owner               | Backup     |
|:------------------------|:----------------|:------------------------------------------------------------------------------------|:----------------------------------------------------------------------------------------------|:----------------------------------------------|:--------------------|:-----------|
| Tue, 04/06/24, 01:59:59 | test_resource   | [resource]({GCP_CONSOLE_BASE_URL}/functions/details/europe-west3/nrm-etl) | [incident]({GCP_CONSOLE_BASE_URL}/monitoring/alerting/policies/4887759640129958342) | [logs]({GCP_CONSOLE_BASE_URL}/logs) | @tomasz.saluszewski | @krzysztof |\n"""  # noqa: E501

    expected += """- :x: `ERROR` at `2024-06-03T23:59:59+00:00`
```json
{
 "foo": "bar"
}
```"""

    actual = create_notification_markdown(message)
    assert actual == expected
