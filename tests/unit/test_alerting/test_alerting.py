from unittest.mock import Mock

import pytest
from _pytest.mark import param
from alerting_config import (
    DataEngineeringLabels,
    get_test_function_incident,
    get_test_job_incident,
    get_test_service_incident,
)
from alerting_shared.notifications import NotificationMessage
from incident_model import Alert
from unit.test_alerting.conftest import (
    get_function_alert,
    get_job_alert,
    get_service_alert,
)

from alerting.incident_alerting import IncidentAlerting
from common.cloud_events import create_http_event
from common.cloud_logging_client.client import CloudLoggingClient
from common.cloud_run_client.function_client import CloudRunFunctionClient
from common.cloud_run_client.job_client import CloudRunJobClient
from common.cloud_run_client.model import CloudRunJobExecution, CloudRunResource
from common.cloud_run_client.service_client import CloudRunServiceClient
from common.config import MattermostAlertingConfig
from common.consts import AlertCriticality
from common.mattermost_client import MattermostMessage


@pytest.mark.parametrize(
    "given, expected",
    [
        param(
            get_test_job_incident(),
            get_job_alert(),
            id="job",
        ),
        param(
            get_test_function_incident(),
            get_function_alert(),
            id="function",
        ),
        param(
            get_test_service_incident(),
            get_service_alert(),
            id="service",
        ),
    ],
)
def test_parse(incident_alerting: IncidentAlerting, given: str, expected: Alert) -> None:
    # arrange
    incident_event = create_http_event(given)

    # act
    actual = incident_alerting.parse(incident_event)

    # assert
    assert actual == expected


def test_job_alert_to_notification_message(
    incident_alerting: IncidentAlerting,
    job_alert: Alert,
    cloud_run_job: CloudRunResource,
    cloud_run_job_execution: CloudRunJobExecution,
    data_eng_labels: DataEngineeringLabels,
    job_notification_message: NotificationMessage,
) -> None:
    # act
    actual = incident_alerting.to_notification_message(
        job_alert, cloud_run_job, data_eng_labels, cloud_run_job_execution
    )

    # assert
    assert actual == job_notification_message


def test_function_alert_to_notification_message(
    incident_alerting: IncidentAlerting,
    function_alert: Alert,
    cloud_run_function: CloudRunResource,
    data_eng_labels: DataEngineeringLabels,
    function_notification_message: NotificationMessage,
) -> None:
    # act
    actual = incident_alerting.to_notification_message(function_alert, cloud_run_function, data_eng_labels)

    # assert
    assert actual == function_notification_message


@pytest.mark.parametrize(
    "labels",
    [
        param(
            DataEngineeringLabels(owner="test", backup="test", criticality=AlertCriticality.P2, alerting_enabled=False),
            id="should_not_send",
        ),
        param(
            DataEngineeringLabels(owner="test", backup="test", criticality=AlertCriticality.P2, alerting_enabled=True),
            id="should_send",
        ),
    ],
)
def test_send_message(
    alerting_config: MattermostAlertingConfig,
    cloud_run_job_client_mock: CloudRunJobClient,
    cloud_function_client_mock: CloudRunFunctionClient,
    cloud_run_service_client_mock: CloudRunServiceClient,
    cloud_logging_client_mock: CloudLoggingClient,
    mattermost_client_mock: Mock,
    labels: DataEngineeringLabels,
) -> None:
    # arrange
    alerting = IncidentAlerting(
        config=alerting_config,
        mattermost_client=mattermost_client_mock,
        cloud_run_job_client=cloud_run_job_client_mock,
        cloud_function_client=cloud_function_client_mock,
        cloud_run_service_client=cloud_run_service_client_mock,
        cloud_logging_client=cloud_logging_client_mock,
    )
    message = MattermostMessage(text="foo")

    # act
    alerting.send_message(message, labels)

    # assert
    if not labels.alerting_enabled:
        mattermost_client_mock.send_message.assert_not_called()
    else:
        mattermost_client_mock.send_message.assert_called_once()
