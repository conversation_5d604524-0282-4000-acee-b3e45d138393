from unittest.mock import Mock, patch

import pytest
from pandas import Data<PERSON>rame
from pandas._testing import assert_frame_equal
from zendesk_auto_responder_config import ZendeskAutoResponderError

from common.pandas_utils import DataFrameWithSchema
from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from zendesk_auto_responder.etl import ZendeskAutoResponderEtl
from zendesk_auto_responder.model import AggregatedEmails, PipedriveData


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_extract(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    etl: ZendeskAutoResponderEtl,
    mock_pg_repo: Mock,
) -> None:
    """Test the extract step, ensuring correct data retrieval from repository."""
    # arrange
    pipedrive_data = DataFrameWithSchema(
        dataframe=DataFrame(columns=PipedriveData.SCHEMA.columns), schema=PipedriveData.SCHEMA
    )
    mock_pg_repo.select_pipedrive_date.return_value = pipedrive_data

    # act
    actual = etl.extract()

    # assert
    assert_frame_equal(actual, pipedrive_data.dataframe_with_schema)
    mock_pg_repo.select_pipedrive_date.assert_called_once()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform_extracted_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    etl: ZendeskAutoResponderEtl,
    mock_pipedrive_data: DataFrame,
) -> None:
    """Test the transform step, verifying output columns and row count."""
    # arrange
    extracted = DataFrameWithSchema(dataframe=mock_pipedrive_data, schema=PipedriveData.SCHEMA)
    expected_columns = AggregatedEmails.SCHEMA.columns
    # Expected rows = the number of different suppliers
    expected_shape = (5, len(expected_columns))

    # act
    actual = etl.transform(extracted.dataframe_with_schema)
    actual_columns = actual.columns.values.tolist()

    # assert
    assert sorted(actual_columns) == sorted(expected_columns)
    assert actual.shape == expected_shape
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform_empty_data(
    mock_start_processing_step: Mock, mock_finish_processing_step: Mock, etl: ZendeskAutoResponderEtl
) -> None:
    """Test transformation behavior when input data is empty."""
    # arrange
    extracted = DataFrameWithSchema(
        dataframe=DataFrame(columns=PipedriveData.SCHEMA.columns), schema=PipedriveData.SCHEMA
    )

    # act & assert: Ensure that an ZendeskAutoResponderError is raised
    with pytest.raises(ZendeskAutoResponderError, match="Input data is empty"):
        etl.transform(extracted.dataframe_with_schema)

    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_transformed_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    etl: ZendeskAutoResponderEtl,
    mock_pipedrive_data: DataFrame,
) -> None:
    """Test the load step, ensuring successful load of transformed data."""
    # arrange
    extracted = DataFrameWithSchema(dataframe=mock_pipedrive_data, schema=PipedriveData.SCHEMA)
    transformed = etl.transform(extracted.dataframe_with_schema)

    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()

    # act & assert: Ensure that load happens without any issues
    mock_start_processing_step.reset_mock()
    mock_finish_processing_step.reset_mock()
    etl.load(transformed)

    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_empty_data(
    mock_start_processing_step: Mock, mock_finish_processing_step: Mock, etl: ZendeskAutoResponderEtl
) -> None:
    """Test load behavior when transformed data is empty."""
    # arrange
    transformed = DataFrameWithSchema(
        dataframe=DataFrame(columns=AggregatedEmails.SCHEMA.columns), schema=AggregatedEmails.SCHEMA
    )

    # act & assert: Ensure that an ZendeskAutoResponderError is raised
    with pytest.raises(ZendeskAutoResponderError, match="Transformed data is empty"):
        etl.load(transformed.dataframe_with_schema)

    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
