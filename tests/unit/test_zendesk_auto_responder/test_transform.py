import pandas as pd
import pytest
from model import AggregatedEmails, PipedriveData
from pandas import DataFrame
from pandas.testing import assert_frame_equal
from transform import aggregate_merchant_emails
from zendesk_auto_responder_config import ZendeskAutoResponderError

from common.pandas_utils import DataFrameWithSchema


def test_aggregate_merchant_emails(mock_pipedrive_data: pd.DataFrame) -> None:
    """Test that aggregate_merchant_emails correctly aggregates emails."""
    # arrange
    expected_data = {
        AggregatedEmails.MERCHANT_NAME: ["Merchant A", "Merchant B", "Merchant C", "Merchant D", "Merchant E"],
        AggregatedEmails.MERCHANT_ID: [1, 2, 3, 4, 5],
        AggregatedEmails.ACCOUNT_MANAGER: ["Manager X", "Manager Y", "Manager Z", "Manager W", "Manager V"],
        AggregatedEmails.EMAILS: [
            "<EMAIL>,<EMAIL>,<EMAIL>",
            "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "",
        ],
        AggregatedEmails.STATE: ["active", "locked", "locked", "active", "locked"],
    }
    expected_df = pd.DataFrame(expected_data)
    expected = DataFrameWithSchema(dataframe=expected_df, schema=AggregatedEmails.SCHEMA)

    # act
    result = aggregate_merchant_emails(mock_pipedrive_data).dataframe_with_schema

    # assert
    assert_frame_equal(result, expected.dataframe_with_schema)


def test_aggregate_merchant_emails_empty_pipedrive_data() -> None:
    """Test aggregate emails function behavior when pipedrive data is empty."""
    # arrange
    pipedrive_data = DataFrameWithSchema(
        dataframe=DataFrame(columns=PipedriveData.SCHEMA.columns), schema=PipedriveData.SCHEMA
    )

    # act & assert: Ensure that an ZendeskAutoResponderError is raised
    with pytest.raises(ZendeskAutoResponderError, match="Pipedrive data is empty"):
        aggregate_merchant_emails(pipedrive_data.dataframe_with_schema)
