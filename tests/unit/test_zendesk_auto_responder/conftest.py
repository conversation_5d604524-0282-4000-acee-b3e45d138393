from unittest.mock import Mock

import pandas as pd
import pytest
from model import PipedriveData
from pg_repository import ZendeskAutoResponderPgRepository

from common.pipeline_logging.pipeline_model import PipelineRun
from zendesk_auto_responder.etl import ZendeskAutoResponderEtl


@pytest.fixture(scope="session")
def mock_pipedrive_data() -> pd.DataFrame:
    """Fixture that provides dummy Pipedrive data for testing.

    This data simulates various merchant scenarios to test the ETL pipeline's
    ability to handle different cases, such as multiple emails per merchant
    and missing account manager information.
    """
    data = {
        PipedriveData.MERCHANT_NAME: [
            "Merchant A",
            "Merchant A",
            "Merchant B",
            "Merchant B",
            "Merchant B",
            "Merchant C",
            "Merchant D",
            "Merchant E",
        ],
        PipedriveData.MERCHANT_ID: [1, 1, 2, 2, 2, 3, 4, 5],
        PipedriveData.ACCOUNT_MANAGER: [
            "Manager X",
            "Manager X",
            "Manager Y",
            "Manager Y",
            "Manager Y",
            "Manager Z",
            "Manager W",
            "Manager V",
        ],
        PipedriveData.ACCOUNT_MANAGER_EMAIL: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "",
            "",
        ],
        PipedriveData.PRIMARY_EMAIL: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "",
            "<EMAIL>",
            "",
        ],
        PipedriveData.STATE: [
            "active",
            "active",
            "locked",
            "locked",
            "locked",
            "locked",
            "active",
            "locked",
        ],
    }

    return pd.DataFrame(data)


@pytest.fixture
def mock_pg_repo() -> Mock:
    """Fixture that provides a mocked Postgres repository.

    This mock allows us to simulate interactions with BigQuery without
    needing to execute real queries.
    """
    return Mock(spec=ZendeskAutoResponderPgRepository)


@pytest.fixture
def mock_config() -> Mock:
    """Fixture that provides a mocked configuration object.

    This mock is used to simulate the configuration settings for the
    Zendesk Auto Responder ETL.
    """
    return Mock(spreadsheet_id="test", sheet_name="test", starting_cell="test")


@pytest.fixture
def etl(
    test_pipeline_run: PipelineRun, mock_pg_repo: Mock, mock_config: Mock, google_sheets_client_mock: Mock
) -> ZendeskAutoResponderEtl:
    """Fixture that provides an instance of the ZendeskAutoResponderEtl
    with mocked dependencies.

    This instance is used to test the ETL process without relying on
    external services.
    """
    return ZendeskAutoResponderEtl(
        config=mock_config,
        pipeline_run=test_pipeline_run,
        pg_repo=mock_pg_repo,
        google_sheets_client=google_sheets_client_mock,
    )
