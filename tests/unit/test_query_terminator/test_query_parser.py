from typing import Optional

import pytest
from query_parser import get_query_slug, sanitize_line


@pytest.mark.parametrize(
    "query, expected",
    [
        (None, None),
        ("", None),
        (" ", None),
        ("select * from public.orders", None),
        ('{"user_id":1,"history_slug":"abc","instance_slug":"abcde"}', None),
        ('-- Looker Query Context \'"history_slug":"abc"\'', None),
        ('-- Looker Query Context \'"history_slug":"abc","instance_slug":"abc", "user_id":123\'', None),
        ('--Looker Query Context \'{"user_id":123,"history_slug":"abc","instance_slug":"xyz"}\'', "abc"),
        ('-- Looker Query Context \'{"history_slug":"abc","instance_slug":"xyz","user_id":123}\' ', "abc"),
        ('--    Looker Query Context \'{"history_slug":"abc"}\'\n   -- SQL text ', "abc"),
    ],
)
def test_query_slug(query: Optional[str], expected: Optional[str]) -> None:
    actual = get_query_slug(query)

    assert actual == expected


@pytest.mark.parametrize(
    "line, expected",
    [
        (None, None),
        ("", ""),
        ("      ", ""),
        ("  a || b      ", "a \\|\\| b"),
        ("a.value || ' ' || unit", "a.value \\|\\| ' ' \\|\\| unit"),
    ],
)
def test_sanitize_line(line: Optional[str], expected: Optional[str]) -> None:
    actual = sanitize_line(line)

    assert actual == expected
