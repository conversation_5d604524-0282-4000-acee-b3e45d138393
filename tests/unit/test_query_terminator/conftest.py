from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock

import pytest

from common.time_service import TimeService
from src.query_terminator.query_terminator import QueryTerminator
from src.query_terminator.query_timeout_model import (
    QueryTerminatorConfig,
    TerminationStatus,
    TimeoutQueriesRow,
)
from src.query_terminator.query_timeout_repository import QueryTimeoutRepository

current_dir = Path(__file__).resolve().parent


@pytest.fixture(scope="session")
def timeout_queries(time_service: TimeService) -> list[TimeoutQueriesRow]:
    return [
        TimeoutQueriesRow(
            status=TerminationStatus.REPORTED,
            duration="00:31:59",
            program_name="looker",
            login_name="platform",
            # -- noqa: disable = all
            sql_text="""WITH exclude_orders AS (SELECT
              id,
              ARRAY_AGG(TRIM(reason))::text as reason
            FROM analytics.exclude_orders
            GROUP BY id )
  ,  order_item_refunds_dt AS (select
             oi.id as order_item_id,
             min(orr.created_at) as min_refund_created,
             sum(oir.refunded) as total_refunded
          from order_item_refunds oir
          join order_items oi on oir.order_item_id = oi.id
          join order_refunds orr on oir.order_refund_id = orr.id
          where orr.status = 'succeeded'
          group by 1       )
SELECT
    orders."id"  AS "orders.id",
    order_items."id"  AS "order_items.id",
        (DATE((orders."paid_at" )::timestamptz AT TIME ZONE 'Europe/Vienna')) AS "orders.paid_date",
    (products."name")||' | '||(instances."name")   AS "instances.product_instance_name",
    offers."grading" """,  # noqa
            start_time=time_service.now - timedelta(minutes=32),
            session_id=1,
            host_ip="**********",
            config_id=1,
            description="Report on all platform queries running longer than 20 minutes",
        ),
        TimeoutQueriesRow(
            status=TerminationStatus.TERMINATED,
            duration="01:00:01",
            program_name="looker",
            login_name="platform",
            # -- noqa: disable = all
            sql_text="""-- Looker Query Context '{"user_id":112398,"history_slug":"f6ef0f4e9232a098e2c3ddbdc97fcb23","instance_slug":"030e3995d13f016f0512e2ef1e8677ad"}'
            WITH categories_dt AS (( SELECT c.*, pc.name_en as product_category
            FROM categories c LEFT JOIN categories pc ON pc.id = subpath(c.path, -1, 1)::TEXT::INT
            WHERE c.path <> '' )
            UNION ALL ( SELECT *, name_en as product_category
            FROM categories where nlevel(path) = 0 ) ) ,
            instances_attributes AS (
            WITH attributes as (
                SELECT i.id as instance_id, a.name as attribute_name,
                CASE WHEN unit IS NULL THEN COALESCE((value->>'de'), av.value_numeric::text)
                ELSE COALESCE((value->>'de'), av.value_numeric::text) || ' ' || unit
                END as attribute_value
                FROM public.products p
                LEFT JOIN public.instances i ON p.id = i.product_id
                INNER JOIN public.product_attribute_values pv ON pv.product_id = p.id""",  # noqa
            start_time=time_service.now - timedelta(hours=1),
            session_id=2,
            host_ip="**********",
            config_id=2,
            description="Terminate all looker queries running over 30 minutes",
        ),
        TimeoutQueriesRow(
            status=TerminationStatus.REPORTED,
            duration="00:29:57",
            program_name="looker",
            login_name="platform",
            # -- noqa: disable = all
            sql_text="""-- Looker Query Context '{"user_id":40041,"history_slug":"872c5c3a612c70f184d5bd6f3f73f558","instance_slug":"030e3995d13f016f0512e2ef1e8677ad"}'
            WITH order_item_refunds_dt AS (select
                 oi.id as order_item_id,
                 min(orr.created_at) as min_refund_created,
                 sum(oir.refunded) as total_refunded
              from order_item_refunds oir
              join order_items oi on oir.order_item_id = oi.id
              join order_refunds orr on oir.order_refund_id = orr.id
              where orr.status = 'succeeded'
              group by 1       )
            ,nps_orders AS (
                SELECT order_id,received_at,nps_score,nps_type,user_email,klavyio_flow FROM analytics.nps_orders
                UNION ALL
                SELECT order_id,received_at,nps_score,nps_type,user_email,'bloomreach' as klavyio_flow
                FROM analytics.nps_orders_bloomreach
            )
            SELECT
                COUNT(DISTINCT ( orders."id"  ) ) AS "orders.n_orders",
                COUNT(DISTINCT CASE WHEN ( (COALESCE(order_item_refunds_dt.total_refunded / ((order_items."charged") + order_items.dis""",  # noqa
            start_time=datetime.strptime("2025-01-09 17:30:16", "%Y-%m-%d %H:%M:%S"),
            session_id=3,
            host_ip="**************",
            config_id=1,
            description="Report on all platform queries running longer than 20 minutes",
        ),
    ]


@pytest.fixture(scope="function")
def mattermost_markdown() -> list[str]:
    return [
        """| Status     | hh:mm:ss   | Program   | Login    | History Slug                     | SQL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Start Time                |   Session | Host       |   Config | Description                                                   |
|:-----------|:-----------|:----------|:---------|:---------------------------------|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|:--------------------------|----------:|:-----------|---------:|:--------------------------------------------------------------|
| reported   | 00:31:59   | looker    | platform |                                  | WITH exclude_orders AS (SELECT id, ARRAY_AGG(TRIM(reason))::text as reason FROM analytics.exclude_orders GROUP BY id ) ,  order_item_refunds_dt AS (select oi.id as order_item_id, min(orr.created_at) as min_refund_created, sum(oir.refunded) as total_refunded from order_item_refunds oir join order_items oi on oir.order_item_id = oi.id join order_refunds orr on oir.order_refund_id = orr.id where orr.status = 'succeeded' group by 1       ) SELECT orders."id"  AS "orders.id", order_items."id"  AS "order_items.id", (DATE((orders."paid_at" )::timestamptz AT TIME ZONE 'Europe/Vienna')) AS "orders.paid_date", (products."name")\|\|' \| '\|\|(instances."name")   AS "instances.product_instance_name", offers."grading" | 2024-06-03 23:27:59+00:00 |         1 | ********** |        1 | Report on all platform queries running longer than 20 minutes |
| terminated | 01:00:01   | looker    | platform | f6ef0f4e9232a098e2c3ddbdc97fcb23 | WITH categories_dt AS (( SELECT c.*, pc.name_en as product_category FROM categories c LEFT JOIN categories pc ON pc.id = subpath(c.path, -1, 1)::TEXT::INT WHERE c.path <> '' ) UNION ALL ( SELECT *, name_en as product_category FROM categories where nlevel(path) = 0 ) ) , instances_attributes AS ( WITH attributes as ( SELECT i.id as instance_id, a.name as attribute_name, CASE WHEN unit IS NULL THEN COALESCE((value->>'de'), av.value_numeric::text) ELSE COALESCE((value->>'de'), av.value_numeric::text) \|\| ' ' \|\| unit END as attribute_value FROM public.products p LEFT JOIN public.instances i ON p.id = i.product_id INNER JOIN public.product_attribute_values pv ON pv.product_id = p.id                          | 2024-06-03 22:59:59+00:00 |         2 | ********** |        2 | Terminate all looker queries running over 30 minutes          |""",  # noqa
        """| Status   | hh:mm:ss   | Program   | Login    | History Slug                     | SQL                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | Start Time          |   Session | Host           |   Config | Description                                                   |
|:---------|:-----------|:----------|:---------|:---------------------------------|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|:--------------------|----------:|:---------------|---------:|:--------------------------------------------------------------|
| reported | 00:29:57   | looker    | platform | 872c5c3a612c70f184d5bd6f3f73f558 | WITH order_item_refunds_dt AS (select oi.id as order_item_id, min(orr.created_at) as min_refund_created, sum(oir.refunded) as total_refunded from order_item_refunds oir join order_items oi on oir.order_item_id = oi.id join order_refunds orr on oir.order_refund_id = orr.id where orr.status = 'succeeded' group by 1       ) ,nps_orders AS ( SELECT order_id,received_at,nps_score,nps_type,user_email,klavyio_flow FROM analytics.nps_orders UNION ALL SELECT order_id,received_at,nps_score,nps_type,user_email,'bloomreach' as klavyio_flow FROM analytics.nps_orders_bloomreach ) SELECT COUNT(DISTINCT ( orders."id"  ) ) AS "orders.n_orders", COUNT(DISTINCT CASE WHEN ( (COALESCE(order_item_refunds_dt.total_refunded / ((order_items."charged") + order_items.dis | 2025-01-09 17:30:16 |         3 | ************** |        1 | Report on all platform queries running longer than 20 minutes |""",  # noqa
    ]


@pytest.fixture(scope="function")
def repository() -> Mock:
    return Mock(spec=QueryTimeoutRepository)


@pytest.fixture(scope="function")
def query_terminator_config() -> QueryTerminatorConfig:
    """
    Limit number of rows in a table to 2 for testing purposes.
    """
    return QueryTerminatorConfig(mattermost_table_size=2)


@pytest.fixture(scope="function")
def query_terminator(
    query_terminator_config: QueryTerminatorConfig, mattermost_client_mock: Mock, repository: Mock
) -> QueryTerminator:
    return QueryTerminator(config=query_terminator_config, client=mattermost_client_mock, repository=repository)
