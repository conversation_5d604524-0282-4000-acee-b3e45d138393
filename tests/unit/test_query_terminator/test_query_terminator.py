from unittest.mock import Mo<PERSON>

import pytest

from src.query_terminator.query_terminator import QueryTerminator
from src.query_terminator.query_timeout_model import (
    TerminationStatus,
    TimeoutQueriesRow,
)


def test_query_terminator_when_no_queries(
    query_terminator: QueryTerminator, mattermost_client_mock: Mock, repository: Mock
) -> None:
    # arrange
    repository.timeout_queries.return_value = []

    # act
    result = query_terminator.run()

    # assert
    assert result is False
    repository.timeout_queries.assert_called_once()
    repository.log_timeout_queries.assert_not_called()
    mattermost_client_mock.send_message.assert_not_called()


def test_query_terminator_when_queries(
    query_terminator: QueryTerminator,
    mattermost_client_mock: Mock,
    repository: Mock,
    timeout_queries: list[TimeoutQueriesRow],
    mattermost_markdown: list[str],
) -> None:
    # arrange
    repository.timeout_queries.return_value = timeout_queries

    # act
    result = query_terminator.run()

    # assert
    assert result is True
    repository.timeout_queries.assert_called_once()
    repository.log_timeout_queries.assert_called_once()

    mattermost_client_mock.send_message.assert_called()
    assert mattermost_client_mock.send_message.call_count == len(mattermost_markdown)


def test_to_mattermost_markdown_when_empty(query_terminator: QueryTerminator) -> None:
    actual = query_terminator.to_mattermost_markdown([])

    assert not actual


def test_to_mattermost_markdown_when_data(
    query_terminator: QueryTerminator, timeout_queries: list[TimeoutQueriesRow], mattermost_markdown: list[str]
) -> None:
    actual = query_terminator.to_mattermost_markdown(timeout_queries)

    assert actual == mattermost_markdown


@pytest.mark.parametrize("data", [None, []])
def test_to_queries_log_when_no_data(data: list[TimeoutQueriesRow]) -> None:
    actual = QueryTerminator.to_queries_log(data)

    assert actual == []


def test_to_queries_log_when_data(timeout_queries: list[TimeoutQueriesRow]) -> None:
    actual = QueryTerminator.to_queries_log(timeout_queries)

    assert len(actual) == len(timeout_queries)
    for index, item in enumerate(timeout_queries):
        assert actual[index].start_time == item.start_time
        assert actual[index].duration == item.duration
        assert actual[index].terminated == (True if item.status == TerminationStatus.TERMINATED else False)
        assert actual[index].login_name == item.login_name
        assert actual[index].program_name == item.program_name
        assert actual[index].sql_text == item.sql_text
        assert actual[index].sql_hash == item.sql_text.__hash__()
        assert actual[index].history_slug == item.history_slug
        assert actual[index].config_id == item.config_id
        assert actual[index].host_ip == item.host_ip
