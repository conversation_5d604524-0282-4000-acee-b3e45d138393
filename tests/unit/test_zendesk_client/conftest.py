import json

import pytest
from circuit_breakers_model import CircuitBreakersZendeskConfig
from zendesk_client import TicketPayload, ZendeskConfig

# Some sandbox refurbedhelp1714995320 constants for testing
# Refurbed developers auto-reply user ID in Sandbox
REFURBED_DEVS_SANDBOX_ID = 21056766009501
# Brand ID
SANDBOX_REFURBED = 18669468996253
# Ticket form ID
INTERNAL_COLLABORATION_SANDBOX_ID = 18669530973085
# Field IDs for ticket properties
INTERNAL_TOPIC = "supplier_performance__internal_"
SUPPLIER_1_TEST = "supplier_1"


@pytest.fixture(scope="session")
def circuit_breakers_zendesk_consts() -> CircuitBreakersZendeskConfig:
    return CircuitBreakersZendeskConfig()


@pytest.fixture(scope="session")
def zendesk_secret_json() -> str:
    return json.dumps(
        {
            "email": "<EMAIL>",
            "subdomain": "sandbox",
            "token": "testtoken456",
        }
    )


@pytest.fixture(scope="session")
def zendesk_config(zendesk_secret_json: str) -> ZendeskConfig:
    """Mock config for Zendesk"""
    return ZendeskConfig.from_json(zendesk_secret_json)


@pytest.fixture(scope="session")
def extra_valid_ticket_payload(circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig) -> TicketPayload:
    """Fixture that provides a valid TicketPayload instance with extra SRP fields."""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
        # I move this to new line > black for some reason moves it back > flake8 complains > noqa
        primary_supplier_services_topic_field_id=circuit_breakers_zendesk_consts.primary_supplier_services_topic_field_id,  # noqa
        primary_supplier_services_topic=INTERNAL_TOPIC,
        supplier_id_field_id=circuit_breakers_zendesk_consts.supplier_field_id,
        supplier_id=SUPPLIER_1_TEST,
        email_ccs=["<EMAIL>"],
    )


@pytest.fixture(scope="session")
def valid_ticket_payload() -> TicketPayload:
    """Fixture that provides a valid TicketPayload instance"""
    return TicketPayload(
        brand_id=SANDBOX_REFURBED,
        ticket_form_id=INTERNAL_COLLABORATION_SANDBOX_ID,
        requester_id=REFURBED_DEVS_SANDBOX_ID,
        submitter_id=REFURBED_DEVS_SANDBOX_ID,
        subject="Test Ticket",
        body="This is a test ticket.",
    )
