import pytest
from circuit_breakers_model import CircuitBreakersZendeskConfig
from zendesk_client import TicketPayload, ZendeskConfig


##############################################################################
# Test: TicketPayload to_dict Method
##############################################################################
def test_ticket_payload_to_dict_basic(
    valid_ticket_payload: TicketPayload, circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig
) -> None:
    """
    Tests that the to_dict method of TicketPayload correctly formats the payload
    for a basic ticket without optional fields.
    """
    payload_dict = valid_ticket_payload.to_dict()

    # Check the structure
    assert "ticket" in payload_dict, "Expected 'ticket' key in payload dictionary"
    ticket = payload_dict["ticket"]

    # Check required fields
    assert ticket["brand_id"] == valid_ticket_payload.brand_id
    assert ticket["ticket_form_id"] == valid_ticket_payload.ticket_form_id
    assert ticket["subject"] == valid_ticket_payload.subject
    assert ticket["requester_id"] == valid_ticket_payload.requester_id
    assert ticket["description"] == valid_ticket_payload.body

    # Check that email_ccs is an empty list for a basic payload
    assert ticket["email_ccs"] == [], "Expected empty email_ccs list"

    # Check fields dictionary
    assert "fields" in ticket, "Expected 'fields' key in ticket dictionary"


def test_ticket_payload_to_dict_with_optional_fields(
    extra_valid_ticket_payload: TicketPayload, circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig
) -> None:
    """
    Tests that the to_dict method of TicketPayload correctly formats the payload
    for a ticket with all optional fields.
    """
    payload_dict = extra_valid_ticket_payload.to_dict()

    # Check the structure
    assert "ticket" in payload_dict, "Expected 'ticket' key in payload dictionary"
    ticket = payload_dict["ticket"]

    # Check required fields
    assert ticket["brand_id"] == extra_valid_ticket_payload.brand_id
    assert ticket["ticket_form_id"] == extra_valid_ticket_payload.ticket_form_id
    assert ticket["subject"] == extra_valid_ticket_payload.subject
    assert ticket["requester_id"] == extra_valid_ticket_payload.requester_id
    assert ticket["description"] == extra_valid_ticket_payload.body

    # Check email_ccs formatting
    assert len(ticket["email_ccs"]) == len(extra_valid_ticket_payload.email_ccs)
    for i, cc in enumerate(extra_valid_ticket_payload.email_ccs):
        assert ticket["email_ccs"][i]["user_email"] == cc

    # Check fields dictionary
    assert "fields" in ticket, "Expected 'fields' key in ticket dictionary"
    assert circuit_breakers_zendesk_consts.primary_supplier_services_topic_field_id in ticket["fields"]
    assert circuit_breakers_zendesk_consts.supplier_field_id in ticket["fields"]
    assert (
        ticket["fields"][circuit_breakers_zendesk_consts.primary_supplier_services_topic_field_id]
        == extra_valid_ticket_payload.primary_supplier_services_topic
    )
    assert ticket["fields"][circuit_breakers_zendesk_consts.supplier_field_id] == extra_valid_ticket_payload.supplier_id


##############################################################################
# Test: ZendeskConfig Properties
##############################################################################
def test_zendesk_config_properties(zendesk_config: ZendeskConfig) -> None:
    """
    Tests that the ZendeskConfig properties correctly construct the expected URLs and user string.
    """
    # Test user property
    assert zendesk_config.user == f"{zendesk_config.email}/token"

    # Test base_url property
    assert zendesk_config.base_url == f"https://{zendesk_config.subdomain}.zendesk.com/api/v2"

    # Test create_ticket_url property
    assert zendesk_config.create_ticket_url == f"{zendesk_config.base_url}/tickets.json"

    # Test user_url property
    assert zendesk_config.user_url == f"{zendesk_config.base_url}/users/search.json"

    # Test ticket_url property
    assert zendesk_config.ticket_url == f"{zendesk_config.base_url}/tickets/"


def test_frozen_dataclass(zendesk_secret_json: str) -> None:
    # Arrange
    config = ZendeskConfig.from_json(zendesk_secret_json)

    # Act & Assert
    with pytest.raises(Exception) as exc_info:
        config.email = "<EMAIL>"  # noqa
    assert "cannot assign to field" in str(exc_info.value)
