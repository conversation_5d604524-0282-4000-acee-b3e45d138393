from pathlib import Path

import pytest
from botocore.client import Base<PERSON>lient
from conftest import S3_TEST_BUCKET_NAME
from unit.test_s3_client.utils import (
    get_actual_file_list,
    get_local_files_content,
    get_test_files_list,
    put_test_files,
)
from utils import TestFile, get_test_file

from common.s3_client import S3Client

s3_test_file_content = get_test_file().file_content
s3_test_bucket_path = "some/s3/path"


@pytest.mark.parametrize("subdir_count, file_count_per_subdir", [(1, 2), (3, 5)])
def test_list_objects_should_return_objects_paths_for_given_location(
    subdir_count: int, file_count_per_subdir: int, s3_client_boto_mock: S3Client, s3_boto_client: BaseClient
) -> None:
    # arrange
    put_test_files(
        bucket_name=S3_TEST_BUCKET_NAME,
        base_path=s3_test_bucket_path,
        subdirectory_count=subdir_count,
        file_count=file_count_per_subdir,
        file_content=s3_test_file_content,
        s3_boto_client=s3_boto_client,
    )

    expected_objects_paths = get_test_files_list(
        path=s3_test_bucket_path,
        subdirectory_count=subdir_count,
        file_count=file_count_per_subdir,
    )

    # act
    actual_objects_paths = list(s3_client_boto_mock.list_objects(s3_prefix=s3_test_bucket_path))

    # assert
    assert actual_objects_paths == expected_objects_paths


@pytest.mark.parametrize(
    "subdir_count, file_count_per_subdir, subdir_prefix, expected_file_list, "
    "expected_files_content, include_prefix_in_local_path",
    [
        (
            1,
            2,
            "test1",
            get_test_files_list(path=f"{s3_test_bucket_path}/test1", subdirectory_count=1, file_count=2),
            [s3_test_file_content] * 1 * 2,
            True,
        ),
        (
            3,
            5,
            "test2",
            get_test_files_list(path=f"{s3_test_bucket_path}/test2", subdirectory_count=3, file_count=5),
            [s3_test_file_content] * 3 * 5,
            False,
        ),
    ],
)
def test_download_files_should_download_all_files_from_given_path(
    subdir_count: int,
    file_count_per_subdir: int,
    subdir_prefix: str,
    expected_file_list: list[str],
    expected_files_content: list[str],
    include_prefix_in_local_path: bool,
    s3_client_boto_mock: S3Client,
    s3_boto_client: BaseClient,
    tmpdir: str,
) -> None:
    # arrange
    uploaded_file_paths = put_test_files(
        bucket_name=S3_TEST_BUCKET_NAME,
        base_path=f"{s3_test_bucket_path}/{subdir_prefix}",
        subdirectory_count=subdir_count,
        file_count=file_count_per_subdir,
        file_content=s3_test_file_content,
        s3_boto_client=s3_boto_client,
    )
    expected_downloaded_file_list = sorted(
        [
            Path(file_path) if include_prefix_in_local_path else Path(file_path).relative_to(s3_test_bucket_path)
            for file_path in uploaded_file_paths
        ]
    )

    # act
    actual_file_list = get_actual_file_list(S3_TEST_BUCKET_NAME, s3_boto_client)
    downloaded_files = s3_client_boto_mock.download_files(
        s3_prefix=s3_test_bucket_path,
        local_path=Path(tmpdir),
        include_prefix_in_local_path=include_prefix_in_local_path,
    )
    actual_downloaded_file_list = sorted([Path(file_path).relative_to(tmpdir) for file_path in downloaded_files])
    actual_files_content = get_local_files_content(downloaded_files)

    # assert
    assert expected_file_list != []
    assert actual_file_list != []
    assert actual_file_list == expected_file_list
    assert actual_downloaded_file_list == expected_downloaded_file_list
    assert expected_files_content != []
    assert actual_files_content != []
    assert actual_files_content == expected_files_content


def test_download_files_should_return_empty_list_when_no_files_to_be_downloaded(
    s3_client_boto_mock: S3Client, tmpdir: str
) -> None:
    # act
    downloaded_files = s3_client_boto_mock.download_files(s3_prefix="non/existing/path", local_path=Path(tmpdir))
    actual_files_content = get_local_files_content(downloaded_files)

    # assert
    assert downloaded_files == []
    assert actual_files_content == []


@pytest.mark.parametrize(
    "given_s3_prefix, include_full_local_path",
    [
        ("/foo=1/bar=2/", True),
        ("/bar=3/baz=4/", False),
        ("/baz=abc", True),
        ("", True),
    ],
)
def test_upload_file(
    given_s3_prefix: str,
    include_full_local_path: bool,
    s3_client_boto_mock: S3Client,
    s3_boto_client: BaseClient,
    test_file: TestFile,
) -> None:
    # arrange
    expected_s3_path = str(
        Path(
            given_s3_prefix,
            test_file.file_path.relative_to("/") if include_full_local_path else test_file.file_path.name,
        )
    )

    # act
    actual_s3_path = s3_client_boto_mock.upload_file(
        file_path=test_file.file_path, s3_prefix=given_s3_prefix, include_full_local_path=include_full_local_path
    )
    s3_response_object = s3_boto_client.get_object(Bucket=s3_client_boto_mock.bucket_name, Key=actual_s3_path)
    actual_file_content = s3_response_object["Body"].read().decode("utf-8")

    # assert
    assert actual_s3_path == expected_s3_path
    assert actual_file_content == test_file.file_content
