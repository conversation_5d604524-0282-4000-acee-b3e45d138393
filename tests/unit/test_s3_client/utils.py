import os
from typing import List

from botocore.client import BaseClient


def get_test_files_list(
    path: str,
    subdirectory_count: int,
    file_count: int,
) -> List[str]:
    """
    Helper function to get a list of test files.

    :param path: base path for the file list
    :param subdirectory_count: number of subdirectories
    :param file_count: number of files inside subdirectory
    :returns: list of files
    """

    return [
        f"{path}/{subdir:02d}/{file:02d}"
        for subdir in range(1, subdirectory_count + 1)
        for file in range(1, file_count + 1)
    ]


def get_actual_file_list(bucket_name: str, s3_boto_client: BaseClient) -> List[str]:
    """
    Helper function to get a list of actual files from the bucket by using low level Boto s3 client.

    :param bucket_name: base path for the file list
    :param s3_boto_client: low level Boto s3 client
    :returns: list of actual files
    """

    return [s3_object["Key"] for s3_object in s3_boto_client.list_objects(Bucket=bucket_name)["Contents"]]


def get_local_files_content(local_files: List[str]) -> List[str]:
    """
    Helper function to get the actual content of local files.

    :param local_files: list of local files
    :returns: list of the content of the files, where each row is an item in the list
    """

    result: List[str] = []
    for filename in local_files:
        if os.path.isfile(filename):
            with open(filename, "r") as f:
                result.append(f.read())

    return result


def put_test_file(bucket_name: str, path: str, file_content: str, s3_boto_client: BaseClient) -> None:
    """
    Helper function to put a file to the s3 bucket by using low level Boto s3 client.

    :param bucket_name: base path for the file list
    :param path: path on the bucket where file will be uploaded
    :param file_content: string representation of file content
    :param s3_boto_client: low level Boto s3 client
    """

    s3_boto_client.put_object(Bucket=bucket_name, Key=path, Body=bytes(file_content, "utf-8"))


def put_test_files(
    bucket_name: str,
    base_path: str,
    subdirectory_count: int,
    file_count: int,
    file_content: str,
    s3_boto_client: BaseClient,
) -> list[str]:
    """
    Helper function to put files to the s3 bucket by using low level Boto s3 client.

    :param bucket_name: name of s3 bucket
    :param base_path: path on the bucket where file will be uploaded
    :param subdirectory_count: number of subdirectories
    :param file_count: number of files inside subdirectory
    :param file_content: string representation of file content
    :param s3_boto_client: low level Boto s3 client
    :returns: list of the uploaded files
    """
    file_paths = get_test_files_list(base_path, subdirectory_count, file_count)
    for file_path in file_paths:
        put_test_file(bucket_name, file_path, file_content, s3_boto_client)

    return file_paths


def create_s3_bucket(bucket_name: str, s3_boto_client: BaseClient) -> None:
    """
    Helper function to create s3 bucket by using low level Boto s3 client.
    :param bucket_name: name of s3 bucket
    :param s3_boto_client: low level Boto s3 client
    """

    s3_boto_client.create_bucket(Bucket=bucket_name)
