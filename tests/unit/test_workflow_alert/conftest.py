import json
from pathlib import Path
from typing import Any

import pytest
from workflow_shared.workflow_model import WorkflowExecution

from common.bq_client import QueryResult
from common.time_service import TimeService

current_dir = Path(__file__).resolve().parent


def load_json(file_path: Path) -> dict[str, Any]:
    with open(file_path, "r") as f:
        return json.load(f)


@pytest.fixture(scope="session")
def data_dir() -> Path:
    return current_dir / "data"


@pytest.fixture(scope="session")
def workflow_ok(data_dir: Path) -> dict[str, Any]:
    return load_json(data_dir / "workflow-ok.json")


@pytest.fixture(scope="session")
def workflow_ok_no_dates(data_dir: Path) -> dict[str, Any]:
    return load_json(data_dir / "workflow-ok-no-dates.json")


@pytest.fixture(scope="session")
def workflow_error1(data_dir: Path) -> dict[str, Any]:
    return load_json(data_dir / "workflow-error-1.json")


@pytest.fixture(scope="session")
def workflow_error2(data_dir: Path) -> dict[str, Any]:
    return load_json(data_dir / "workflow-error-2.json")


@pytest.fixture(scope="session")
def workflow_execution_bq_report(time_service: TimeService) -> WorkflowExecution:
    start_date = time_service.now.timestamp()
    end_date = start_date + 3600

    return WorkflowExecution(
        workflow_id="test",
        execution_id="123-test",
        start=start_date,
        end=end_date,
        bq_cost_report_date=time_service.yesterday,
    )


@pytest.fixture(scope="session")
def bq_cost_report_query_result() -> QueryResult:
    return QueryResult(
        job_result=[["foo", "bar"]],  # noqa
        total_rows=0,
        total_bytes_billed=0,
        total_bytes_processed=0,
    )
