from typing import Any

from mattermost_notification import to_mattermost_message
from workflow_shared.workflow_model import WorkflowExecution


def test_to_mattermost_alert_when_ok(workflow_ok: dict[str, Any]) -> None:
    # arrange
    execution = WorkflowExecution.from_dict(workflow_ok)
    expected = """Workflow `test-workflow` execution [workflow-ok](https://console.cloud.google.com/workflows/workflow/europe-west3/test-workflow/execution/workflow-ok/summary?project=refb-analytics-staging) succeeded.
- **Start date [UTC]**: `2024-05-08 12:53:25`
- **Finish date [UTC]**: `2024-05-08 14:50:05`
- **Duration [hh:mm:ss]**: `01:56:39`
- **Result**:
```json
{
 "analytics-db-refresh-job": {
  "completionTime": "2024-12-16T04:09:43.520398Z",
  "logUri": "https://console.cloud.google.com/logs/viewer?project=refb-analytics&advancedFilter=analytics-db-refresh-6ss72",
  "observedGeneration": 1,
  "startTime": "2024-12-16T00:01:05.569486Z",
  "succeededCount": 1
 },
 "run-parallel-functions": {
  "adtriba-etl-sphere": "OK",
  "adtriba-tcm-etl": "OK",
  "dhl-etl-shipping": "OK",
  "nrm-forecast-etl": "OK",
  "proc-executor -> load_order_item_exchange_rate": "OK",
  "proc-executor -> reload_full_order_item_refunds": "OK"
 },
 "run-parallel-jobs": {
  "pipedrive-etl-job": {
   "completionTime": "2024-12-16T04:12:01.792953Z",
   "logUri": "https://console.cloud.google.com/logs/viewer?project=refb-analytics&advancedFilter=pipedrive-etl-job-8c4k4",
   "observedGeneration": 1,
   "startTime": "2024-12-16T04:11:05.882482Z",
   "succeededCount": 1
  }
 },
 "unmanaged-workflow": {
  "result": 200,
  "url": "https://console.cloud.google.com/workflows/workflow/europe-west3/unmanaged-workflow/executions?project=refb-analytics"
 },
 "zendesk-auto-responder": "OK"
}
```"""  # noqa

    # act
    message = to_mattermost_message(execution)

    # assert
    assert message.text == expected
    assert message.icon_emoji == ":information_source:"


def test_to_mattermost_alert_when_ok_no_dates(workflow_ok_no_dates: dict[str, Any]) -> None:
    # arrange
    execution = WorkflowExecution.from_dict(workflow_ok_no_dates)
    expected = """Workflow `test-workflow` execution [workflow-ok-no-dates](https://console.cloud.google.com/workflows/workflow/europe-west3/test-workflow/execution/workflow-ok-no-dates/summary?project=refb-analytics-staging) succeeded.
- **Result**:
```json
{
 "abc": "OK",
 "def": "OK"
}
```"""  # noqa

    # act
    message = to_mattermost_message(execution)

    # assert
    assert message.text == expected
    assert message.icon_emoji == ":information_source:"


def test_to_mattermost_alert_when_error2(workflow_error2: dict[str, Any]) -> None:
    # arrange
    execution = WorkflowExecution.from_dict(workflow_error2)
    expected = """Workflow `test-workflow` execution [446b0ac3-0e13-4261-9f7b-ce8d90e35529](https://console.cloud.google.com/workflows/workflow/europe-west3/test-workflow/execution/446b0ac3-0e13-4261-9f7b-ce8d90e35529/summary?project=refb-analytics-staging) failed!
- **Error**: UnhandledBranchError: One or more branches or iterations encountered an unhandled runtime error!
   - RuntimeError: "branch error"
in step "run_function", routine "run_http_function", line: 86
in step "run_function", routine "main", line: 33
- **Result**:
```json
{
 "function1": "Running...",
 "function2": "Running..."
}
```"""  # noqa

    # act
    message = to_mattermost_message(execution)

    # assert
    assert message.text == expected
    assert message.icon_emoji == ":fire:"
