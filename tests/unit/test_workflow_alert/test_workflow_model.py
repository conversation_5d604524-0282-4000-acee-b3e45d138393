from datetime import UTC, datetime
from typing import Any, Optional

from workflow_shared.workflow_model import (
    Branch<PERSON>rror,
    ErrorTag,
    HttpError,
    WorkflowError,
    WorkflowExecution,
)

from common.consts import GCP_CONSOLE_BASE_URL, GCP_REGION
from common.pipeline_logging.pipeline_model import SupportsPipelineExecution


def test_workflow_model_when_ok(workflow_ok: dict[str, Any]) -> None:
    """
    Supports pipeline_logging and has start and end dates.
    """

    # arrange
    execution_id = "workflow-ok"
    execution_url = f"{GCP_CONSOLE_BASE_URL}/workflows/workflow/europe-west3/test-workflow/execution/{execution_id}/summary?project=refb-analytics-staging"  # noqa
    result = {
        "unmanaged-workflow": {
            "url": f"{GCP_CONSOLE_BASE_URL}/workflows/workflow/europe-west3/unmanaged-workflow/executions?project=refb-analytics",  # noqa
            "result": 200,
        },
        "analytics-db-refresh-job": {
            "completionTime": "2024-12-16T04:09:43.520398Z",
            "logUri": f"{GCP_CONSOLE_BASE_URL}/logs/viewer?project=refb-analytics&advancedFilter=analytics-db-refresh-6ss72",  # noqa
            "observedGeneration": 1,
            "startTime": "2024-12-16T00:01:05.569486Z",
            "succeededCount": 1,
        },
        "run-parallel-functions": {
            "adtriba-etl-sphere": "OK",
            "adtriba-tcm-etl": "OK",
            "dhl-etl-shipping": "OK",
            "nrm-forecast-etl": "OK",
            "proc-executor -> load_order_item_exchange_rate": "OK",
            "proc-executor -> reload_full_order_item_refunds": "OK",
        },
        "run-parallel-jobs": {
            "pipedrive-etl-job": {
                "completionTime": "2024-12-16T04:12:01.792953Z",
                "logUri": f"{GCP_CONSOLE_BASE_URL}/logs/viewer?project=refb-analytics&advancedFilter=pipedrive-etl-job-8c4k4",  # noqa
                "observedGeneration": 1,
                "startTime": "2024-12-16T04:11:05.882482Z",
                "succeededCount": 1,
            }
        },
        "zendesk-auto-responder": "OK",
    }  # noqa
    start_date = datetime(2024, 5, 8, 12, 53, 25, 281847, tzinfo=UTC)
    end_date = datetime(2024, 5, 8, 14, 50, 5, 123450, tzinfo=UTC)

    # act
    execution = WorkflowExecution.from_dict(workflow_ok)

    # assert
    assert_execution(
        execution,
        execution_id,
        execution_url,
        result,
        start_date=start_date,
        end_date=end_date,
        supports_pipeline_logging=True,
    )


def test_workflow_model_when_ok_no_dates(workflow_ok_no_dates: dict[str, Any]) -> None:
    """
    Supports pipeline_logging, but no start nor end dates required for distributed logging.
    """

    # arrange
    execution_id = "workflow-ok-no-dates"
    execution_url = f"{GCP_CONSOLE_BASE_URL}/workflows/workflow/europe-west3/test-workflow/execution/{execution_id}/summary?project=refb-analytics-staging"  # noqa
    result = {"abc": "OK", "def": "OK"}

    # act
    execution = WorkflowExecution.from_dict(workflow_ok_no_dates)

    # assert
    assert_execution(execution, execution_id, execution_url, result)


def test_workflow_model_when_error1(workflow_error1: dict[str, Any]) -> None:
    """
    Has errors and supports pipeline_logging, but no start nor end dates required for distributed logging.
    """

    # arrange
    execution_id = "ab8a1f43-cfec-401d-9c65-c5aa1e00928e"
    execution_url = f"{GCP_CONSOLE_BASE_URL}/workflows/workflow/europe-west3/test-workflow/execution/{execution_id}/summary?project=refb-analytics-staging"  # noqa
    result = {
        "proc-executor -> load_order_item_exchange_rate": "Running...",
        "proc-executor -> reload_full_order_item_refunds": "OK",
    }
    errors = 1

    # act
    execution = WorkflowExecution.from_dict(workflow_error1)

    # assert
    assert_execution(execution, execution_id, execution_url, result, errors)


def test_workflow_model_when_error2(workflow_error2: dict[str, Any]) -> None:
    """
    Has errors and supports pipeline_logging, but no start nor end dates required for distributed logging.
    """

    # arrange
    execution_id = "446b0ac3-0e13-4261-9f7b-ce8d90e35529"
    execution_url = f"{GCP_CONSOLE_BASE_URL}/workflows/workflow/europe-west3/test-workflow/execution/{execution_id}/summary?project=refb-analytics-staging"  # noqa
    result = {"function1": "Running...", "function2": "Running..."}
    errors = 2

    # act
    execution = WorkflowExecution.from_dict(workflow_error2)

    # assert
    assert_execution(execution, execution_id, execution_url, result, errors)


def assert_execution(
    execution: WorkflowExecution,
    execution_id: str,
    execution_url: str,
    result: dict[str, Any],
    errors: int = 0,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    supports_pipeline_logging: bool = False,
) -> None:
    assert execution.execution_id == execution_id
    assert execution.execution_url == execution_url
    assert execution.location == GCP_REGION
    assert execution.result == result

    assert execution.start_date == start_date
    assert execution.end_date == end_date
    assert SupportsPipelineExecution.supports_pipeline_logging(execution) is supports_pipeline_logging

    assert_workflow_error(execution, errors)


def assert_workflow_error(execution: WorkflowExecution, errors: int) -> None:
    error = execution.error
    if errors == 0:
        assert error is None
        return

    expected_message = "UnhandledBranchError: One or more branches"

    assert isinstance(error, WorkflowError)
    assert execution.error.message == error.message
    assert execution.error_message.startswith(expected_message)
    assert error.message.startswith(expected_message)
    assert error.tags == [ErrorTag.BRANCH_ERROR, ErrorTag.RUNTIME_ERROR]

    assert len(error.branches) == errors
    for branch in error.branches:
        assert_branch_error(branch)


def assert_branch_error(branch_error: BranchError) -> None:
    assert isinstance(branch_error, BranchError)
    error = branch_error.error

    assert isinstance(error, HttpError)
    assert error.context.startswith('RuntimeError: "branch error')

    payload = error.payload
    assert payload.body.startswith("500 Internal Server Error: The server encountered an internal error")
    assert payload.code == 500
    assert payload.headers["Content-Type"] == "text/html; charset=utf-8"
    assert payload.message == "HTTP server responded with error code 500"
    assert payload.tags == [ErrorTag.HTTP_ERROR.value]
