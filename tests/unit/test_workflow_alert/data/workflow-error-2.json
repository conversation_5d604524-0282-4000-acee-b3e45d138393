{"error": {"branches": [{"error": {"context": "RuntimeError: \"branch error\"\nin step \"run_function\", routine \"run_http_function\", line: 86\nin step \"run_function\", routine \"main\", line: 33", "payload": {"body": "500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.", "code": 500, "headers": {"Alt-Svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "Content-Length": "181", "Content-Type": "text/html; charset=utf-8", "Date": "Wed, 04 Dec 2024 08:49:39 GMT", "Server": "Google Frontend", "X-Cloud-Trace-Context": "1048a19811bb23637af3eeab3238f18e"}, "message": "HTTP server responded with error code 500", "tags": ["HttpError"]}}, "id": "1"}, {"error": {"context": "RuntimeError: \"branch error\"\nin step \"run_function\", routine \"run_http_function\", line: 86\nin step \"run_function\", routine \"main\", line: 33", "payload": {"body": "500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.", "code": 500, "headers": {"Alt-Svc": "h3=\":443\"; ma=2592000,h3-29=\":443\"; ma=2592000", "Content-Length": "181", "Content-Type": "text/html; charset=utf-8", "Date": "Wed, 04 Dec 2024 08:49:39 GMT", "Server": "Google Frontend", "X-Cloud-Trace-Context": "f3fdd4b791a6784668a6c90a6d0648e9"}, "message": "HTTP server responded with error code 500", "tags": ["HttpError"]}}, "id": "0"}], "message": "UnhandledBranchError: One or more branches or iterations encountered an unhandled runtime error", "tags": ["UnhandledBranchError", "RuntimeError"], "truncated": false}, "execution_id": "446b0ac3-0e13-4261-9f7b-ce8d90e35529", "location": "europe-west3", "result": {"function1": "Running...", "function2": "Running..."}, "workflow_id": "test-workflow"}