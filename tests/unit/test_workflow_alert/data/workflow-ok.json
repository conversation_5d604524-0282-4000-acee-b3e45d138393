{"workflow_id": "test-workflow", "pipeline_logging": true, "execution_id": "workflow-ok", "start": 1715172805.2818468, "end": 1715179805.12345, "result": {"unmanaged-workflow": {"url": "https://console.cloud.google.com/workflows/workflow/europe-west3/unmanaged-workflow/executions?project=refb-analytics", "result": 200}, "analytics-db-refresh-job": {"completionTime": "2024-12-16T04:09:43.520398Z", "logUri": "https://console.cloud.google.com/logs/viewer?project=refb-analytics&advancedFilter=analytics-db-refresh-6ss72", "observedGeneration": 1, "startTime": "2024-12-16T00:01:05.569486Z", "succeededCount": 1}, "run-parallel-functions": {"adtriba-etl-sphere": "OK", "adtriba-tcm-etl": "OK", "dhl-etl-shipping": "OK", "nrm-forecast-etl": "OK", "proc-executor -> load_order_item_exchange_rate": "OK", "proc-executor -> reload_full_order_item_refunds": "OK"}, "run-parallel-jobs": {"pipedrive-etl-job": {"completionTime": "2024-12-16T04:12:01.792953Z", "logUri": "https://console.cloud.google.com/logs/viewer?project=refb-analytics&advancedFilter=pipedrive-etl-job-8c4k4", "observedGeneration": 1, "startTime": "2024-12-16T04:11:05.882482Z", "succeededCount": 1}}, "zendesk-auto-responder": "OK"}}