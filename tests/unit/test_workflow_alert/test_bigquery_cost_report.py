import dataclasses
from unittest.mock import Mock

from _pytest.logging import LogCapture<PERSON>ixture
from bigquery_cost_report import send_bq_cost_report
from workflow_shared.workflow_model import WorkflowError, WorkflowExecution

from common.bq_client import QueryResult


def test_send_bq_cost_report_when_ok(
    bigquery_repository_mock: Mock,
    mattermost_client_mock: Mock,
    workflow_execution_bq_report: WorkflowExecution,
    bq_cost_report_query_result: QueryResult,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "INFO"
    caplog.set_level(log_level)
    bigquery_repository_mock.call_procedure.return_value = bq_cost_report_query_result
    expected = """:moneybag: **BigQuery cost estimation:**

```
┌───────────────┬────────────────┐
│ Metric name   │   Metric value │
├───────────────┼────────────────┤
│ foo           │            bar │
└───────────────┴────────────────┘
```"""

    # act
    actual = send_bq_cost_report(bigquery_repository_mock, mattermost_client_mock, workflow_execution_bq_report, "test")

    # assert
    assert actual == expected

    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "Generating BQ cost report for '2024-06-02' date..." in caplog.messages
    assert "Sending BQ cost report..." in caplog.messages
    mattermost_client_mock.send_message.assert_called_once()


def test_send_bq_cost_report_when_no_report_date_passed(
    bigquery_repository_mock: Mock,
    mattermost_client_mock: Mock,
    workflow_execution_bq_report: WorkflowExecution,
    bq_cost_report_query_result: QueryResult,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "INFO"
    caplog.set_level(log_level)

    workflow_execution_bq_report = dataclasses.replace(workflow_execution_bq_report, bq_cost_report_date=None)
    bigquery_repository_mock.call_procedure.return_value = bq_cost_report_query_result

    # act
    actual = send_bq_cost_report(bigquery_repository_mock, mattermost_client_mock, workflow_execution_bq_report, "test")

    # assert
    assert actual is None

    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "BQ cost report deactivated." in caplog.messages
    bigquery_repository_mock.call_procedure.assert_not_called()
    mattermost_client_mock.send_message.assert_not_called()


def test_send_bq_cost_report_when_execution_error(
    bigquery_repository_mock: Mock,
    mattermost_client_mock: Mock,
    workflow_execution_bq_report: WorkflowExecution,
    bq_cost_report_query_result: QueryResult,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "INFO"
    caplog.set_level(log_level)

    workflow_execution_bq_report = dataclasses.replace(workflow_execution_bq_report, error=WorkflowError())
    bigquery_repository_mock.call_procedure.return_value = bq_cost_report_query_result

    # act
    actual = send_bq_cost_report(bigquery_repository_mock, mattermost_client_mock, workflow_execution_bq_report, "test")

    # assert
    assert actual is None

    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "BQ cost report deactivated." in caplog.messages
    bigquery_repository_mock.call_procedure.assert_not_called()
    mattermost_client_mock.send_message.assert_not_called()


def test_send_bq_cost_report_when_no_data_found(
    bigquery_repository_mock: Mock,
    mattermost_client_mock: Mock,
    workflow_execution_bq_report: WorkflowExecution,
    bq_cost_report_query_result: QueryResult,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "WARNING"
    caplog.set_level(log_level)
    bigquery_repository_mock.call_procedure.return_value = dataclasses.replace(
        bq_cost_report_query_result, job_result=[]  # noqa
    )

    # act
    actual = send_bq_cost_report(bigquery_repository_mock, mattermost_client_mock, workflow_execution_bq_report, "test")

    # assert
    assert actual is None

    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "No BQ cost report data found for the '2024-06-02' date!" in caplog.messages
    bigquery_repository_mock.call_procedure.assert_called_once()
    mattermost_client_mock.send_message.assert_not_called()
