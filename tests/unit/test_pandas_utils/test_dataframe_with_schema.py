import pytest
from pandas import DataFrame, Int16Dtype, Series, StringDtype
from pandas._testing import assert_frame_equal

from common.pandas_utils import (
    BaseDataFrameSchema,
    DataFrameSchema,
    DataFrameWithSchema,
)


class PersonModel(BaseDataFrameSchema):
    """Model representing a valid person, matching the schema structure."""

    TIMESTAMP: str = "timestamp"
    NAME: str = "name"
    AGE: str = "age"

    SCHEMA = DataFrameSchema(
        data_types={
            TIMESTAMP: StringDtype(),
            NAME: StringDtype(),
            AGE: Int16Dtype(),
        }
    )


@pytest.fixture(scope="session")
def valid_dataframe() -> DataFrame:
    """Fixture for providing a valid dataframe that matches the schema."""
    return DataFrame(
        {
            PersonModel.TIMESTAMP: ["2024-09-16", "2024-09-17"],
            PersonModel.NAME: ["Alice", "<PERSON>"],
            PersonModel.AGE: [30, 25],
        }
    )


@pytest.fixture(scope="session")
def invalid_dataframe() -> DataFrame:
    """Fixture for providing a dataframe that doesn't match the schema and will throw an exception."""
    return DataFrame(
        {
            PersonModel.TIMESTAMP: ["2024-09-16", "2024-09-17"],
            PersonModel.NAME: [123, 456],
            PersonModel.AGE: ["thirty", "twenty"],
        }
    )


@pytest.fixture(scope="session")
def mismatched_dataframe() -> DataFrame:
    """Fixture for providing a dataframe that doesn't match the schema, but will be fixed by the function."""
    return DataFrame(
        {
            PersonModel.TIMESTAMP: ["2024-09-16", "2024-09-17"],
            PersonModel.NAME: ["Alice", "Bob"],
            PersonModel.AGE: ["30", "25"],
        }
    )


@pytest.fixture(scope="session")
def random_dataframe() -> DataFrame:
    """Fixture for providing a dataframe that doesn't match the schema, but will be fixed by the function."""
    return DataFrame(
        {
            "random1": [1, 2, 3],
            "random2": [4, 5, 6],
        }
    )


def test_dataframe_with_valid_schema(valid_dataframe: DataFrame) -> None:
    """
    Test that a valid DataFrame conforms to the provided schema.

    This test ensures that the DataFrame is correctly cast to the specified schema's
    data types and has the appropriate structure.

    :param valid_dataframe: A valid DataFrame that matches the sample schema.
    """
    # Arrange
    df_with_schema = DataFrameWithSchema(valid_dataframe, PersonModel.SCHEMA)
    expected = DataFrame(
        {
            PersonModel.TIMESTAMP: Series(["2024-09-16", "2024-09-17"], dtype="string"),
            PersonModel.NAME: Series(["Alice", "Bob"], dtype="string"),
            PersonModel.AGE: Series([30, 25], dtype="Int16"),
        }
    )

    # Act
    result = df_with_schema.dataframe_with_schema

    # Assert
    assert_frame_equal(expected, result)


def test_dataframe_with_invalid_schema(invalid_dataframe: DataFrame) -> None:
    """
    Test that a DataFrame with invalid data types raises a ValueError.

    This test validates that mismatched data types between the DataFrame
    and the schema will result in an error.

    :param invalid_dataframe: A DataFrame with mismatched types for 'name' and 'age'.
    """
    # Arrange
    df_with_schema = DataFrameWithSchema(invalid_dataframe, PersonModel.SCHEMA)

    # Act & Assert
    with pytest.raises(ValueError):
        _ = df_with_schema.dataframe_with_schema


def test_empty_dataframe() -> None:
    """
    Test that an empty DataFrame conforms to the schema structure.

    This test ensures that when an empty DataFrame is provided, the schema still
    applies column names and types to the result.

    """
    # Arrange
    expected = DataFrame(
        columns=[
            PersonModel.TIMESTAMP,
            PersonModel.NAME,
            PersonModel.AGE,
        ]
    )
    df_with_schema = DataFrameWithSchema(DataFrame(), PersonModel.SCHEMA)

    # Act
    result = df_with_schema.dataframe_with_schema

    # Reorder columns to ensure they match
    expected = expected[["timestamp", "name", "age"]]
    result = result[["timestamp", "name", "age"]]

    # Assert
    assert_frame_equal(expected, result)


def test_dataframe_with_mismatched_types(mismatched_dataframe: DataFrame) -> None:
    """
    Test that a DataFrame with mismatched types can be successfully coerced.

    This test simulates a case where the 'age' column contains numbers as strings,
    but the DataFrame schema correctly converts it to the expected data type.

    :param mismatched_dataframe: A DataFrame with 'age' column as strings.
    """
    # Arrange
    df_with_schema = DataFrameWithSchema(mismatched_dataframe, PersonModel.SCHEMA)
    expected = DataFrame(
        {
            PersonModel.TIMESTAMP: Series(["2024-09-16", "2024-09-17"], dtype="string"),
            PersonModel.NAME: Series(["Alice", "Bob"], dtype="string"),
            PersonModel.AGE: Series([30, 25], dtype="Int16"),
        }
    )

    # Act
    result = df_with_schema.dataframe_with_schema

    # Assert - 'age' should be coerced to int16
    assert_frame_equal(expected, result)


def test_dataframe_with_random_schema(random_dataframe: DataFrame) -> None:
    """
    Test that a DataFrame with random data types raises a KeyError.

    This test validates that mismatched schema and will result in an error.

    :param random_dataframe: A DataFrame with random schema.
    """
    # Arrange
    df_with_schema = DataFrameWithSchema(random_dataframe, PersonModel.SCHEMA)

    # Act & Assert
    with pytest.raises(KeyError):
        _ = df_with_schema.dataframe_with_schema
