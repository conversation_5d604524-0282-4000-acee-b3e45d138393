import os
from pathlib import Path
from unittest.mock import patch

import pytest
from migration_config import FlywayCommand, MigrationConfig, MigrationTarget


def test_default_values(migration_config: MigrationConfig) -> None:
    assert migration_config.migrations_path == Path("../sql-pg").resolve()
    assert migration_config.run_repeatable_scripts is False
    assert migration_config.db_refresh_run_id is None


@pytest.mark.parametrize(
    "env_value,expected_command",
    [
        ("INFO", FlywayCommand.INFO),
        ("MIGRATE", FlywayCommand.MIGRATE),
    ],
)
def test_command_from_env(env_value: str, expected_command: FlywayCommand) -> None:
    os.environ["FLYWAY_COMMAND"] = env_value
    assert FlywayCommand.from_environ() == expected_command


@pytest.mark.parametrize(
    "env_value,expected_target",
    [
        ("POSTGRES", MigrationTarget.POSTGRES),
        ("BIGQUERY", MigrationTarget.BIGQUERY),
    ],
)
@patch.dict("os.environ")
def test_target_from_env(env_value: str, expected_target: MigrationTarget) -> None:
    os.environ["MIGRATION_TARGET"] = env_value
    assert MigrationTarget.from_environ() == expected_target


@pytest.mark.parametrize(
    "env_name,expected_run_test",
    [
        ("local", True),
        ("staging", True),
        ("production", False),
    ],
)
def test_run_test_default_behavior(env_name: str, expected_run_test: bool) -> None:
    # Clear RUN_FLYWAY_TEST to test default behavior
    os.environ.pop("RUN_FLYWAY_TEST", None)
    os.environ["ENV_NAME"] = env_name
    config = MigrationConfig()

    assert config.run_test == expected_run_test


@pytest.mark.parametrize(
    "env_value,expected_run_test",
    [
        ("true", True),
        ("True", True),
        ("TRUE", True),
        ("false", False),
        ("False", False),
        ("FALSE", False),
    ],
)
def test_run_test_from_env_variable(env_value: str, expected_run_test: bool) -> None:
    os.environ["RUN_FLYWAY_TEST"] = env_value
    # Set to production to ensure env variable takes precedence
    os.environ["ENV_NAME"] = "production"

    config = MigrationConfig()
    assert config.run_test == expected_run_test


def test_run_test_env_variable_overrides_production() -> None:
    # In production, default would be False, but env variable should override
    os.environ["ENV_NAME"] = "production"
    os.environ["RUN_FLYWAY_TEST"] = "true"

    config = MigrationConfig()
    assert config.run_test is True


def test_run_test_env_variable_overrides_non_production() -> None:
    # In staging, default would be True, but env variable should override
    os.environ["ENV_NAME"] = "staging"
    os.environ["RUN_FLYWAY_TEST"] = "false"

    config = MigrationConfig()
    assert config.run_test is False
