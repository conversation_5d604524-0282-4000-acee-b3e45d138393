import pytest
from migrate_database import MigrateDatabase
from migration_config import MigrationConfig

from common.migrate_db import FlywayBqParams, FlywayPgParams
from common.sql_client import DatabaseConfig
from common.time_service import TimeService
from common.utils import get_run_id


@pytest.fixture(scope="function")
def database_config() -> DatabaseConfig:
    return DatabaseConfig()


@pytest.fixture(scope="function")
def migration_config() -> MigrationConfig:
    return MigrationConfig()


@pytest.fixture(scope="function")
def migrate_database(
    migration_config: MigrationConfig,
    database_config: DatabaseConfig,
    time_service: TimeService,
) -> MigrateDatabase:
    return MigrateDatabase(
        config=migration_config,
        database_config=database_config,
        time_service=time_service,
    )


@pytest.fixture(scope="function")
def expected_flyway_pg_params(
    database_config: DatabaseConfig, migration_config: MigrationConfig, time_service: TimeService
) -> FlywayPgParams:
    return FlywayPgParams(
        host=database_config.private_ip,
        port=database_config.port,
        database=database_config.database,
        user=database_config.username,
        password=database_config.password,
        config_files=f"{migration_config.migrations_path}/flyway.toml",
        locations=f"filesystem:{migration_config.migrations_path}",
        environment=migration_config.env,
        placeholders=["runTest=true", f"changeReason={get_run_id(time_service.now)}"],
    )


@pytest.fixture(scope="function")
def expected_flyway_bq_params(migration_config: MigrationConfig, time_service: TimeService) -> FlywayBqParams:
    return FlywayBqParams(
        project_id=migration_config.project_id,
        config_files=f"{migration_config.migrations_path}/flyway.toml",
        locations=f"filesystem:{migration_config.migrations_path}",
        environment=migration_config.env,
        placeholders=["runTest=true", f"changeReason={get_run_id(time_service.now)}"],
    )
