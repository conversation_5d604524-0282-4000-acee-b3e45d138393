from unittest.mock import patch

import pytest
from migrate_database import MigrateDatabase
from migration_config import MigrationConfig, MigrationTarget

from common.migrate_db import FlywayBqParams, FlywayParams
from common.sql_client import DatabaseConfig
from common.time_service import TimeService


@pytest.mark.parametrize(
    "target, expected_params_fixture",
    [
        (MigrationTarget.POSTGRES, "expected_flyway_pg_params"),
        (MigrationTarget.BIGQUERY, "expected_flyway_bq_params"),
    ],
)
def test_get_flyway_params(
    target: MigrationTarget,
    expected_params_fixture: str,
    database_config: DatabaseConfig,
    time_service: TimeService,
    request: pytest.FixtureRequest,
) -> None:
    # Arrange
    config = MigrationConfig(run_repeatable_scripts=True, target=target)
    migrate_database = MigrateDatabase(config=config, database_config=database_config, time_service=time_service)
    expected_params: FlywayParams = request.getfixturevalue(expected_params_fixture)

    # Act
    actual = migrate_database.get_flyway_params()

    # Assert
    assert actual == expected_params

    # Additional assertions for BigQuery to ensure project_id is excluded from command_params
    if target == MigrationTarget.BIGQUERY:
        assert isinstance(actual, FlywayBqParams)
        assert "-projectId=" not in actual.command_params
        assert f"ProjectId={config.project_id}" in actual.url


@pytest.mark.parametrize(
    "run_flyway_test_env, expected_value",
    [
        ("True", "runTest=true"),
        ("False", "runTest=false"),
    ],
)
def test_runtime_placeholders_run_test(
    run_flyway_test_env: str,
    expected_value: str,
    database_config: DatabaseConfig,
    migration_config: MigrationConfig,
) -> None:
    with patch.dict("os.environ", {"RUN_FLYWAY_TEST": run_flyway_test_env}):
        migrate_database = MigrateDatabase(config=migration_config, database_config=database_config)
        result = migrate_database.runtime_placeholders

        assert expected_value in result


@pytest.mark.parametrize(
    "change_reason,expected",
    [
        ("test-change-reason", True),
        (None, False),
    ],
)
def test_flyway_placeholders_change_reason(
    change_reason: str,
    expected: str,
    migrate_database: MigrateDatabase,
    database_config: DatabaseConfig,
) -> None:
    config = MigrationConfig(db_refresh_run_id=change_reason)
    migrate_database = MigrateDatabase(config=config, database_config=database_config)
    result = migrate_database.runtime_placeholders

    if expected:
        assert f"changeReason={change_reason}" in result
    else:
        assert [] == [p for p in result if p.startswith("changeReason=")]
