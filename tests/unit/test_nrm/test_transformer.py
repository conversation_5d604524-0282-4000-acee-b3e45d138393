from nrm_model import (
    MARKETING_COUNTRIES_SCHEMA,
    NEURAL_FORECAST_SCHEMA,
    REVENUE_COUNTRIES_SCHEMA,
)
from pandas import DataFrame
from pandas._testing import assert_frame_equal

from nrm.nrm_transformer import (
    concat_transformed,
    transform_marketing_countries,
    transform_revenue_countries,
)


def test_transform_revenue_countries_when_empty() -> None:
    # arrange
    expected = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    # act
    actual = transform_revenue_countries(DataFrame(columns=REVENUE_COUNTRIES_SCHEMA.columns))

    # assert
    assert_frame_equal(actual, expected)


def test_transform_revenue_countries_when_data(revenue_countries: DataFrame, nrm_revenue_countries: DataFrame) -> None:
    # act
    actual = transform_revenue_countries(revenue_countries)

    # assert
    assert_frame_equal(actual, nrm_revenue_countries, check_like=True)


def test_transform_marketing_countries_when_empty() -> None:
    # arrange
    expected = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    # act
    actual = transform_marketing_countries(DataFrame(columns=MARKETING_COUNTRIES_SCHEMA.columns))

    # assert
    assert_frame_equal(actual, expected)


def test_transform_marketing_countries_when_data(
    marketing_countries: DataFrame, nrm_marketing_countries: DataFrame
) -> None:
    # act
    actual = transform_marketing_countries(marketing_countries)

    # assert
    assert_frame_equal(actual, nrm_marketing_countries, check_like=True)


def test_concat_transformed_when_both_empty() -> None:
    # arrange
    revenue_countries = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)
    marketing_countries = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    # act
    actual = concat_transformed(revenue_countries, marketing_countries)

    # assert
    assert_frame_equal(actual, marketing_countries)


def test_concat_transformed_when_revenue_empty(marketing_countries: DataFrame) -> None:
    # arrange
    revenue_countries = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    # act
    actual = concat_transformed(revenue_countries, marketing_countries)

    # assert
    assert_frame_equal(actual, marketing_countries)


def test_concat_transformed_when_marketing_empty(revenue_countries: DataFrame) -> None:
    # arrange
    marketing_countries = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    # act
    actual = concat_transformed(revenue_countries, marketing_countries)

    # assert
    assert_frame_equal(actual, revenue_countries)


def test_concat_transformed_when_data(revenue_countries: DataFrame, marketing_countries: DataFrame) -> None:
    # arrange
    expected_columns = set(revenue_countries.columns.values.tolist() + marketing_countries.columns.values.tolist())
    expected_shape = (len(revenue_countries) + len(marketing_countries), len(expected_columns))

    # act
    actual = concat_transformed(revenue_countries, marketing_countries)
    actual_columns = set(actual.columns.values.tolist())

    # assert
    assert actual_columns == expected_columns
    assert actual.shape == expected_shape
