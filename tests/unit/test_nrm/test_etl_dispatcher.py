from unittest.mock import Mock, call, patch

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.time_service import TimeService
from nrm.nrm_etl_dispatcher import NrmEtlDispatcher


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_get_days_to_load_when_no_day_loaded(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mo<PERSON>,
    nrm_etl_dispatcher: NrmEtlDispatcher,
    time_service: TimeService,
    transformed_repository: Mock,
) -> None:
    # arrange
    reload_days = 3
    transformed_repository.count_rows.return_value = 0

    expected_days = list(time_service.last_days(reload_days + 1, start=1))
    expected_calls = [call(day) for day in expected_days]

    # act
    actual_days = nrm_etl_dispatcher.get_days_to_load(reload_days)

    # assert
    assert actual_days == expected_days
    transformed_repository.count_rows.assert_has_calls(expected_calls)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_get_days_to_load_when_all_days_loaded(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    nrm_etl_dispatcher: NrmEtlDispatcher,
    time_service: TimeService,
    transformed_repository: Mock,
) -> None:
    # arrange
    reload_days = 3
    transformed_repository.count_rows.return_value = 1

    last_days = list(time_service.last_days(reload_days + 1, start=1))
    expected_calls = [call(day) for day in last_days]

    # act
    actual_days = nrm_etl_dispatcher.get_days_to_load(reload_days)

    # assert
    assert actual_days == []
    transformed_repository.count_rows.assert_has_calls(expected_calls)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
