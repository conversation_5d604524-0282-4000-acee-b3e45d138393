from datetime import date
from pathlib import Path
from unittest.mock import Mock, call, patch

from _pytest.logging import LogC<PERSON><PERSON><PERSON>ixture
from nrm_model import NEURAL_FORECAST_SCHEMA
from pandas import DataFrame
from pandas._testing import assert_frame_equal

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from nrm.etl import Extracted, NrmEtl, Transformed
from nrm.nrm_model import (
    MARKETING_COUNTRIES_SCHEMA,
    REVENUE_COUNTRIES_SCHEMA,
    MarketingCountriesColumns,
    NeuralForecastColumns,
    NrmConfig,
    RevenueCountriesColumns,
)


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_extract(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    nrm_etl: NrmEtl,
    nrm_config: NrmConfig,
    pg_repository: Mock,
    bq_repository: Mock,
) -> None:
    # arrange
    revenue_countries = DataFrame(columns=REVENUE_COUNTRIES_SCHEMA.columns)
    pg_repository.select_revenue_countries.return_value = revenue_countries

    marketing_countries = DataFrame(columns=MARKETING_COUNTRIES_SCHEMA.columns)
    bq_repository.select_marketing_countries.return_value = marketing_countries

    # act
    actual = nrm_etl.extract()

    # assert
    assert_frame_equal(actual.revenue_countries, revenue_countries)
    assert_frame_equal(actual.marketing_countries, marketing_countries)

    pg_repository.select_revenue_countries.assert_called_once_with(nrm_config.load_date)
    bq_repository.select_marketing_countries.assert_called_once_with(nrm_config.load_date)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    nrm_etl: NrmEtl,
    revenue_countries: DataFrame,
    marketing_countries: DataFrame,
    nrm: DataFrame,
) -> None:
    # arrange
    extracted = Extracted(
        revenue_countries=revenue_countries,
        marketing_countries=marketing_countries,
    )

    # act
    actual = nrm_etl.transform(extracted)

    # assert
    assert actual.extracted == extracted
    assert_frame_equal(actual.nrm, nrm)
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_when_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    nrm_etl: NrmEtl,
    nrm_config: NrmConfig,
    raw_data_lake: Mock,
    transformed_data_lake: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "INFO"
    caplog.set_level(log_level)

    raw_data_lake.upload_table_partition.return_value = Path("raw_data_lake")
    transformed_data_lake.upload_table_partition.return_value = Path("transformed_data_lake")

    load_date = date(2020, 1, 1)
    revenue_countries = DataFrame(
        {
            RevenueCountriesColumns.DATE: [load_date],
        }
    )
    marketing_countries = DataFrame(
        {
            MarketingCountriesColumns.DATE: [load_date],
        }
    )
    nrm = DataFrame({NeuralForecastColumns.DS: [load_date]})

    transformed = Transformed(
        extracted=Extracted(revenue_countries=revenue_countries, marketing_countries=marketing_countries),
        nrm=nrm,
    )

    expected_raw_calls = [
        call(
            data_frame=revenue_countries,
            table_name="revenue_countries",
            load_date=nrm_config.load_date,
            root_folder=nrm_config.root_folder,
        ),
        call(
            data_frame=marketing_countries,
            table_name="marketing_countries",
            load_date=nrm_config.load_date,
            root_folder=nrm_config.root_folder,
        ),
    ]

    # act
    result = nrm_etl.load(transformed)

    # assert
    assert result == len(nrm)
    raw_data_lake.upload_table_partition.assert_has_calls(expected_raw_calls)
    transformed_data_lake.upload_table_partition.assert_called_once_with(
        data_frame=nrm,
        table_name="nrm",
        load_date=nrm_config.load_date,
        root_folder=nrm_config.root_folder,
    )
    assert len(caplog.messages) == 3
    caplog.messages[0].startswith("Loaded extracted table_name='revenue_countries'")
    caplog.messages[1].startswith("Loaded extracted table_name='marketing_countries'")
    caplog.messages[2].startswith("Loaded NRM")
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_when_no_data(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    nrm_etl: NrmEtl,
    nrm_config: NrmConfig,
    raw_data_lake: Mock,
    transformed_data_lake: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "ERROR"
    caplog.set_level(log_level)

    raw_data_lake.upload_table_partition.return_value = None
    transformed_data_lake.upload_table_partition.return_value = None

    revenue_countries = DataFrame(columns=REVENUE_COUNTRIES_SCHEMA.columns)
    marketing_countries = DataFrame(columns=MARKETING_COUNTRIES_SCHEMA.columns)
    nrm = DataFrame(columns=NEURAL_FORECAST_SCHEMA.columns)

    transformed = Transformed(
        extracted=Extracted(revenue_countries=revenue_countries, marketing_countries=marketing_countries),
        nrm=nrm,
    )

    expected_raw_calls = [
        call(
            data_frame=revenue_countries,
            table_name="revenue_countries",
            load_date=nrm_config.load_date,
            root_folder=nrm_config.root_folder,
        ),
        call(
            data_frame=marketing_countries,
            table_name="marketing_countries",
            load_date=nrm_config.load_date,
            root_folder=nrm_config.root_folder,
        ),
    ]

    # act
    result = nrm_etl.load(transformed)

    # assert
    assert result == len(nrm)
    raw_data_lake.upload_table_partition.assert_has_calls(expected_raw_calls)
    transformed_data_lake.upload_table_partition.assert_called_once_with(
        data_frame=nrm,
        table_name="nrm",
        load_date=nrm_config.load_date,
        root_folder=nrm_config.root_folder,
    )
    assert len(caplog.messages) == 3
    caplog.messages[0].startswith("No data in table_name='revenue_countries'")
    caplog.messages[1].startswith("No data in table_name='marketing_countries'")
    caplog.messages[2].startswith("No NRM data to upload")
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
