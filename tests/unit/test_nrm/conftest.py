from pathlib import Path
from unittest.mock import Mock

import pytest
from nrm_etl_dispatcher import NrmEtlDispatcher
from nrm_transformed_repository import NrmTransformedRepository
from pandas import DataFrame, concat, read_csv

from common.data_lake_repository import DataLakeRepository
from common.pipeline_logging.pipeline_model import PipelineRun
from common.time_service import TimeService
from nrm.etl import NrmEtl
from nrm.nrm_funnel_repository import NrmFunnelRepository
from nrm.nrm_model import (
    MARKETING_COUNTRIES_SCHEMA,
    NEURAL_FORECAST_SCHEMA,
    REVENUE_COUNTRIES_SCHEMA,
    NrmConfig,
)
from nrm.nrm_pg_repository import NrmPgRepository

current_dir = Path(__file__).resolve().parent


@pytest.fixture(scope="session")
def data_dir() -> Path:
    return current_dir / "data"


@pytest.fixture(scope="function")
def revenue_countries(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "revenue_countries.csv",
        # Data type(s) mapping
        dtype=REVENUE_COUNTRIES_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def nrm_revenue_countries(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "nrm_revenue_countries.csv",
        # Data type(s) mapping
        dtype=NEURAL_FORECAST_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def marketing_countries(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "marketing_countries.csv",
        # Data type(s) mapping
        dtype=MARKETING_COUNTRIES_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def nrm_marketing_countries(data_dir: Path) -> DataFrame:
    return read_csv(
        data_dir / "nrm_marketing_countries.csv",
        # Data type(s) mapping
        dtype=NEURAL_FORECAST_SCHEMA.data_types,
        # NaN values handling
        na_filter=False,
        keep_default_na=False,
        # Skip empty lines
        skip_blank_lines=True,
    )


@pytest.fixture(scope="function")
def nrm(nrm_revenue_countries: DataFrame, nrm_marketing_countries: DataFrame) -> DataFrame:
    return concat([nrm_revenue_countries, nrm_marketing_countries], ignore_index=True)


@pytest.fixture(scope="function")
def nrm_config(time_service: TimeService) -> NrmConfig:
    return NrmConfig(load_date=time_service.today)


@pytest.fixture(scope="function")
def pg_repository() -> Mock:
    return Mock(spec=NrmPgRepository)


@pytest.fixture(scope="function")
def bq_repository() -> Mock:
    return Mock(spec=NrmFunnelRepository)


@pytest.fixture(scope="function")
def raw_data_lake() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="function")
def transformed_data_lake() -> Mock:
    return Mock(spec=DataLakeRepository)


@pytest.fixture(scope="function")
def nrm_etl(
    test_pipeline_run: PipelineRun,
    nrm_config: NrmConfig,
    pg_repository: Mock,
    bq_repository: Mock,
    raw_data_lake: Mock,
    transformed_data_lake: Mock,
) -> NrmEtl:
    return NrmEtl(
        config=nrm_config,
        pipeline_run=test_pipeline_run,
        pg_repository=pg_repository,
        bq_repository=bq_repository,
        raw_data_lake=raw_data_lake,
        transformed_data_lake=transformed_data_lake,
    )


@pytest.fixture(scope="function")
def transformed_repository() -> Mock:
    return Mock(spec=NrmTransformedRepository)


@pytest.fixture(scope="function")
def nrm_etl_dispatcher(
    test_pipeline_run: PipelineRun, pg_repository: Mock, time_service: TimeService, transformed_repository: Mock
) -> NrmEtlDispatcher:
    return NrmEtlDispatcher(
        pipeline_run=test_pipeline_run,
        pg_repository=pg_repository,
        time_service=time_service,
        bq_repository=transformed_repository,
    )
