from typing import Any
from unittest.mock import MagicMock

import pandas as pd
import pytest
from circuit_breaker_etl import Circuit<PERSON>reakersEtl, Extracted, IsRunDay
from circuit_breaker_repository import CircuitBreakersPgRepository
from circuit_breakers_model import (
    CircuitBreakerColumns,
    CircuitBreakerSchema,
    CircuitBreakersZendeskConfig,
    ConditionRatePayload,
    CsatScorePayload,
    DefectRatePayload,
    ResponseTimePayload,
)
from zendesk_client import ZendeskClient, ZendeskConfig

from common.pandas_utils import DataFrameWithSchema


@pytest.fixture(scope="function")
def circuit_breakers_zendesk_consts() -> CircuitBreakersZendeskConfig:
    return CircuitBreakersZendeskConfig()


# --------------------- ETL
@pytest.fixture(scope="function")
def mock_zendesk_config() -> ZendeskConfig:
    """Create a Mock ZendeskConfig instance."""
    config = MagicMock(spec=ZendeskConfig)
    config.is_production = False

    return config


@pytest.fixture(scope="function")
def mock_dataframe_with_schema() -> DataFrameWithSchema:
    """Create a test DataFrame with the CircuitBreaker schema for 2 merchants."""
    test_data = {
        CircuitBreakerColumns.MERCHANT_ID: [1, 2],
        CircuitBreakerColumns.MERCHANT_NAME: ["Test Merchant 1", "Test Merchant 2"],
        CircuitBreakerColumns.PRIMARY_EMAIL: ["<EMAIL>", "<EMAIL>"],
    }
    df = pd.DataFrame(test_data)
    return DataFrameWithSchema(dataframe=df, schema=CircuitBreakerSchema)


@pytest.fixture(scope="function")
def mock_dataframe_with_schema_null_email() -> DataFrameWithSchema:
    """Create a test DataFrame with the CircuitBreaker schema for 2 merchants where 1 contains a null email."""
    test_data = {
        CircuitBreakerColumns.MERCHANT_ID: [1, 1, 2],
        CircuitBreakerColumns.MERCHANT_NAME: ["Test Merchant 1", "Test Merchant 1", "Test Merchant 2"],
        CircuitBreakerColumns.PRIMARY_EMAIL: [None, "<EMAIL>", "<EMAIL>"],
    }
    df = pd.DataFrame(test_data)
    return DataFrameWithSchema(dataframe=df, schema=CircuitBreakerSchema)


@pytest.fixture(scope="function")
def mock_zendesk_client() -> MagicMock:
    """Create a mock ZendeskClient with pre-configured responses."""
    client = MagicMock(spec=ZendeskClient)
    client.find_user_id_via_email.return_value = 123
    return client


@pytest.fixture(scope="function")
def mock_pg_repository(mock_dataframe_with_schema: DataFrameWithSchema) -> MagicMock:
    """Create a mock PostgreSQL repository that returns test data."""
    repo = MagicMock(spec=CircuitBreakersPgRepository)
    repo.select_circuit_breaker_data.return_value = mock_dataframe_with_schema
    return repo


@pytest.fixture(scope="function")
def circuit_breaker_etl(
    mock_zendesk_config: ZendeskConfig,
    mock_zendesk_client: ZendeskClient,
    mock_pg_repository: MagicMock,
) -> CircuitBreakersEtl:
    return CircuitBreakersEtl(
        config=mock_zendesk_config,
        zendesk_client=mock_zendesk_client,
        pg_repository=mock_pg_repository,
        run_day=IsRunDay(),
    )


@pytest.fixture(scope="function")
def circuit_breaker_extracted(
    mock_dataframe_with_schema: DataFrameWithSchema,
) -> Extracted:
    return Extracted(
        condition_raw=mock_dataframe_with_schema,
        defect_raw=mock_dataframe_with_schema,
        response_raw=mock_dataframe_with_schema,
        urgent_raw=mock_dataframe_with_schema,
        csat_raw=mock_dataframe_with_schema,
    )


# --------------------- MODEL
@pytest.fixture
def test_merchant_ticket_data() -> dict[str, Any]:
    return {
        "merchant_name": "Test Merchant",
        "merchant_id": 123,
        "requester_id": 456,
        "email_ccs": ["<EMAIL>", "<EMAIL>"],
    }


@pytest.fixture
def condition_rate_payload(test_merchant_ticket_data: dict[str, Any]) -> ConditionRatePayload:
    return ConditionRatePayload(**test_merchant_ticket_data)


@pytest.fixture
def defect_rate_payload(test_merchant_ticket_data: dict[str, Any]) -> DefectRatePayload:
    return DefectRatePayload(**test_merchant_ticket_data)


@pytest.fixture
def response_time_payload(test_merchant_ticket_data: dict[str, Any]) -> ResponseTimePayload:
    return ResponseTimePayload(**test_merchant_ticket_data)


@pytest.fixture
def csat_score_payload(test_merchant_ticket_data: dict[str, Any]) -> CsatScorePayload:
    return CsatScorePayload(**test_merchant_ticket_data)
