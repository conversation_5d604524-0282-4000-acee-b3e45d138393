from datetime import date
from unittest.mock import call

import pytest
from circuit_breaker_etl import CircuitBreakersEtl, Extracted, IsRunDay, create_run_day
from circuit_breakers_model import (
    ConditionRatePayload,
    CsatScorePayload,
    DefectRatePayload,
    ResponseTimePayload,
)

from common.pandas_utils import DataFrameWithSchema


@pytest.mark.parametrize(
    "current_date, condition_defect_response, urgent, csat",
    [
        # Non Tuesday
        (date(year=2025, month=3, day=1), False, False, False),
        # First Tuesday of month
        (date(year=2025, month=3, day=4), True, True, False),
        # Second Tuesday of month
        (date(year=2025, month=3, day=11), True, False, True),
        # Third Tuesday of month
        (date(year=2025, month=3, day=18), True, False, False),
    ],
)
def test_is_run_day(current_date: date, condition_defect_response: bool, urgent: bool, csat: bool) -> None:
    # act
    is_run_day = create_run_day(current_date=current_date)

    # assert
    assert is_run_day.condition_defect_response is condition_defect_response
    assert is_run_day.urgent is urgent
    assert is_run_day.csat is csat


def test_extract(
    circuit_breaker_extracted: Extracted,
    circuit_breaker_etl: CircuitBreakersEtl,
) -> None:
    # act
    actual = circuit_breaker_etl.extract()

    # assert
    assert actual == circuit_breaker_extracted
    # Verify each extraction has the expected shape of 2 logs x 3 columns
    assert actual.condition_raw.dataframe.shape == (2, 3)
    assert actual.defect_raw.dataframe.shape == (2, 3)
    assert actual.response_raw.dataframe.shape == (2, 3)
    assert actual.urgent_raw.dataframe.shape == (2, 3)
    assert actual.csat_raw.dataframe.shape == (2, 3)


@pytest.mark.parametrize(
    "run_day, expected_condition_defect_response, expected_urgent, expected_csat",
    [
        # Non Tuesday
        (create_run_day(current_date=date(year=2025, month=3, day=1)), 0, 0, 0),
        # First Tuesday of month
        (create_run_day(current_date=date(year=2025, month=3, day=4)), 6, 2, 0),
        # Second Tuesday of month
        (create_run_day(current_date=date(year=2025, month=3, day=11)), 6, 0, 2),
        # Third Tuesday of month
        (create_run_day(current_date=date(year=2025, month=3, day=18)), 6, 0, 0),
    ],
)
def test_transform(
    circuit_breaker_extracted: Extracted,
    circuit_breaker_etl: CircuitBreakersEtl,
    run_day: IsRunDay,
    expected_condition_defect_response: int,
    expected_urgent: int,
    expected_csat: int,
) -> None:
    # arrange
    circuit_breaker_etl.run_day = run_day

    # act
    actual = circuit_breaker_etl.transform(extracted=circuit_breaker_extracted)

    # assert
    # In conftest, we created 2 sellers per alert. So depending on the date we will 2 sellers * alerts of the day
    assert len(actual) == expected_condition_defect_response + expected_csat
    # Check we have 2 of each payload type when
    payload_types = [type(payload) for payload in actual]
    # / 3 since there are 2 for each of these, running on all Tuesdays!
    assert payload_types.count(ConditionRatePayload) == expected_condition_defect_response / 3
    assert payload_types.count(DefectRatePayload) == expected_condition_defect_response / 3
    assert payload_types.count(ResponseTimePayload) == expected_condition_defect_response / 3
    assert payload_types.count(CsatScorePayload) == expected_csat


@pytest.mark.parametrize(
    "has_null_email",
    [
        False,  # No nulls
        True,  # Has null email
    ],
)
def test_construct_payload(
    circuit_breaker_etl: CircuitBreakersEtl,
    mock_dataframe_with_schema: DataFrameWithSchema,
    mock_dataframe_with_schema_null_email: DataFrameWithSchema,
    has_null_email: bool,
) -> None:
    # arrange
    df = mock_dataframe_with_schema_null_email if has_null_email else mock_dataframe_with_schema

    # act for Condition test
    actual = circuit_breaker_etl._construct_payload(
        df=df.dataframe,
        payload_class=ConditionRatePayload,
    )

    # assert
    assert len(actual) == 2
    assert all(isinstance(payload, ConditionRatePayload) for payload in actual)
    assert actual[0].supplier_id == "supplier_1"
    assert actual[0].subject == "Test Merchant 1 - Condition Score Notification refurbed™"
    assert actual[0].email_ccs == []
    assert actual[1].supplier_id == "supplier_2"
    assert actual[1].subject == "Test Merchant 2 - Condition Score Notification refurbed™"
    assert actual[1].email_ccs == []


def test_load(
    circuit_breaker_etl: CircuitBreakersEtl,
    condition_rate_payload: ConditionRatePayload,
    defect_rate_payload: DefectRatePayload,
) -> None:
    # arrange
    payloads = [condition_rate_payload, defect_rate_payload]

    # act
    circuit_breaker_etl.load(transformed=payloads)

    # assert
    assert circuit_breaker_etl.zendesk_client.create_ticket.call_count == 2  # noqa
    circuit_breaker_etl.zendesk_client.create_ticket.assert_has_calls(  # noqa
        [
            call(payload=condition_rate_payload, delete_after_creation=True),
            call(payload=defect_rate_payload, delete_after_creation=True),
        ]
    )


@pytest.mark.parametrize(
    "run_day, expected_total",
    [
        # Non Tuesday
        (create_run_day(current_date=date(year=2025, month=3, day=1)), 0),
        # First Tuesday of month
        (create_run_day(current_date=date(year=2025, month=3, day=4)), 6),
        # Second Tuesday of month
        (create_run_day(current_date=date(year=2025, month=3, day=11)), 8),
        # Third Tuesday of month
        (create_run_day(current_date=date(year=2025, month=3, day=18)), 6),
    ],
)
def test_run(
    circuit_breaker_etl: CircuitBreakersEtl,
    circuit_breaker_extracted: Extracted,
    run_day: IsRunDay,
    expected_total: int,
) -> None:
    # arrange
    circuit_breaker_etl.run_day = run_day

    # act
    circuit_breaker_etl.run()

    # assert
    # Verify that extract (real method), transform (real method), and load were all called
    # For load, we can check that create_ticket was called the expected times
    # (2 for each of the alerts, depending on the day)
    assert circuit_breaker_etl.zendesk_client.create_ticket.call_count == expected_total  # noqa
