from typing import Any

from circuit_breakers_model import (
    CircuitBreakersZendeskConfig,
    ConditionRatePayload,
    CsatScorePayload,
    DefectRatePayload,
    ResponseTimePayload,
)


def test_condition_rate_payload_initialization(
    circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig,
    condition_rate_payload: ConditionRatePayload,
    test_merchant_ticket_data: dict[str, Any],
) -> None:
    """Test that ConditionRatePayload is initialized correctly with all fields."""
    assert condition_rate_payload.brand_id == circuit_breakers_zendesk_consts.brand_id_supplier_performance
    assert condition_rate_payload.ticket_form_id == circuit_breakers_zendesk_consts.ticket_form_id_supplier_services
    assert (
        condition_rate_payload.subject
        == f"{test_merchant_ticket_data['merchant_name']} - Condition Score Notification refurbed™"
    )
    assert condition_rate_payload.requester_id == test_merchant_ticket_data["requester_id"]
    assert condition_rate_payload.email_ccs == test_merchant_ticket_data["email_ccs"]
    assert condition_rate_payload.primary_supplier_services_topic == circuit_breakers_zendesk_consts.condition
    assert condition_rate_payload.supplier_id == f"supplier_{test_merchant_ticket_data['merchant_id']}"
    assert "condition score" in condition_rate_payload.body.lower()


def test_defect_rate_payload_initialization(
    circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig,
    defect_rate_payload: DefectRatePayload,
    test_merchant_ticket_data: dict[str, Any],
) -> None:
    """Test that DefectRatePayload is initialized correctly with all fields."""
    assert defect_rate_payload.brand_id == circuit_breakers_zendesk_consts.brand_id_supplier_performance
    assert defect_rate_payload.ticket_form_id == circuit_breakers_zendesk_consts.ticket_form_id_supplier_services
    assert (
        defect_rate_payload.subject
        == f"{test_merchant_ticket_data['merchant_name']} - Defect Score Notification refurbed™"
    )
    assert defect_rate_payload.requester_id == test_merchant_ticket_data["requester_id"]
    assert defect_rate_payload.email_ccs == test_merchant_ticket_data["email_ccs"]
    assert defect_rate_payload.primary_supplier_services_topic == circuit_breakers_zendesk_consts.defect
    assert defect_rate_payload.supplier_id == f"supplier_{test_merchant_ticket_data['merchant_id']}"
    assert "defect score" in defect_rate_payload.body.lower()


def test_response_time_payload_initialization(
    circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig,
    response_time_payload: ResponseTimePayload,
    test_merchant_ticket_data: dict[str, Any],
) -> None:
    """Test that ResponseTimePayload is initialized correctly with all fields."""
    assert response_time_payload.brand_id == circuit_breakers_zendesk_consts.brand_id_supplier_performance
    assert response_time_payload.ticket_form_id == circuit_breakers_zendesk_consts.ticket_form_id_supplier_services
    assert (
        response_time_payload.subject
        == f"{test_merchant_ticket_data['merchant_name']} - Response Time Notification refurbed™"
    )
    assert response_time_payload.requester_id == test_merchant_ticket_data["requester_id"]
    assert response_time_payload.email_ccs == test_merchant_ticket_data["email_ccs"]
    assert response_time_payload.primary_supplier_services_topic == circuit_breakers_zendesk_consts.response_time
    assert response_time_payload.supplier_id == f"supplier_{test_merchant_ticket_data['merchant_id']}"
    assert "response time" in response_time_payload.body.lower()


def test_csat_score_payload_initialization(
    circuit_breakers_zendesk_consts: CircuitBreakersZendeskConfig,
    csat_score_payload: CsatScorePayload,
    test_merchant_ticket_data: dict[str, Any],
) -> None:
    """Test that CsatScorePayload is initialized correctly with all fields."""
    assert csat_score_payload.brand_id == circuit_breakers_zendesk_consts.brand_id_supplier_performance
    assert csat_score_payload.ticket_form_id == circuit_breakers_zendesk_consts.ticket_form_id_supplier_services
    assert (
        csat_score_payload.subject
        == f"{test_merchant_ticket_data['merchant_name']} - CSAT Score Notification refurbed™"
    )
    assert csat_score_payload.requester_id == test_merchant_ticket_data["requester_id"]
    assert csat_score_payload.email_ccs == test_merchant_ticket_data["email_ccs"]
    assert csat_score_payload.primary_supplier_services_topic == circuit_breakers_zendesk_consts.csat
    assert csat_score_payload.supplier_id == f"supplier_{test_merchant_ticket_data['merchant_id']}"
    assert "csat score" in csat_score_payload.body.lower()
