from unittest.mock import Mock

import pytest
from analytics_bq_repository import AnalyticsBQRepository
from export_etl import PlatformExportEtl
from platform_repository import PlatformRepository

from common.sql_model import SqlTable
from common.time_service import TimeService
from platform_export.export_shared.export_model import ExportConfig, ExportType
from platform_export.export_shared.export_target import PRODUCTS_SOURCE, PRODUCTS_TARGET


@pytest.fixture(scope="function")
def source_table() -> SqlTable:
    return PRODUCTS_SOURCE


@pytest.fixture(scope="function")
def export_type() -> ExportType:
    return ExportType.INCREMENTAL


@pytest.fixture(scope="function")
def target_table() -> str:
    return PRODUCTS_TARGET


@pytest.fixture(scope="function")
def export_config(time_service: TimeService, source_table: SqlTable) -> ExportConfig:
    return ExportConfig(
        timestamp=time_service.timestamp_in_sec,
        source=source_table,
    )


@pytest.fixture(scope="function")
def platform_repository() -> Mock:
    return Mock(spec=PlatformRepository)


@pytest.fixture(scope="session")
def dataset_name() -> str:
    return "dataset"


@pytest.fixture(scope="function")
def analytics_bq_repository(dataset_name: str) -> Mock:
    mock = Mock(spec=AnalyticsBQRepository)
    mock.DATASET = dataset_name

    return mock


@pytest.fixture(scope="function")
def platform_export_etl(
    export_config: ExportConfig,
    platform_repository: Mock,
    analytics_bq_repository: Mock,
) -> PlatformExportEtl:
    return PlatformExportEtl(
        config=export_config,
        platform_repository=platform_repository,
        analytics_bq_repository=analytics_bq_repository,
    )
