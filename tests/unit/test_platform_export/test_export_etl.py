from unittest.mock import Mock, patch

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.sql_model import SqlTable
from platform_export.etl.export_etl import PlatformExportEtl


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_run(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    platform_export_etl: PlatformExportEtl,
    analytics_bq_repository: Mock,
    platform_repository: Mock,
    source_table: SqlTable,
    target_table: str,
) -> None:
    # arrange
    condition = "id > 1000"
    platform_repository.build_change_condition.return_value = condition
    analytics_bq_repository.extract_from_source.return_value = 1
    analytics_bq_repository.load_from_platform.return_value = 2
    analytics_bq_repository.check_table.return_value = True

    # act
    result = platform_export_etl.run()

    # assert
    assert result == 1 + 2
    analytics_bq_repository.get_export_status.assert_called_with(table=target_table)
    analytics_bq_repository.extract_from_source.assert_called_with(
        source=source_table, target=target_table, condition=condition
    )
    analytics_bq_repository.load_from_platform.assert_called_with(table_name=target_table)
    analytics_bq_repository.check_table.assert_called_with(table_name=target_table)

    assert mock_start_processing_step.call_count == 2
    assert mock_finish_processing_step.call_count == 2
