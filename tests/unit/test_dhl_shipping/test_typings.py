from pathlib import Path

import pytest
from schema import ShippingBillingDataLakePartition


@pytest.mark.parametrize(
    "partition, expected_path",
    [
        (
            ShippingBillingDataLakePartition(
                base_path="foo", year=2024, month=1, day=1, country="au", provider="refurbed"
            ),
            Path("foo/country=au/provider=refurbed/year=2024/month=1/day=1/"),
        ),
        (
            ShippingBillingDataLakePartition(
                base_path="baz", year=2024, month=12, day=31, country="foo", provider="bar"
            ),
            Path("baz/country=foo/provider=bar/year=2024/month=12/day=31/"),
        ),
    ],
)
def test_shipping_billing_data_lake_partition(partition: ShippingBillingDataLakePartition, expected_path: Path) -> None:
    actual_path = partition.path

    assert actual_path == expected_path
