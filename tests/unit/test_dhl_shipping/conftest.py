from datetime import UTC, datetime
from pathlib import Path
from typing import Optional
from unittest.mock import Mock

import numpy as np
import pytest
from dhl_shared.shipping_config import ShippingConfig
from dhl_shipping.dhl_repository import DhlRepository
from dhl_shipping.etl import DhlShippingEtl, ExtractedType, TransformedData
from dhl_shipping.schema import SB_PARTITIONS
from pandas import DataFrame

from common.pipeline_logging.pipeline_model import PipelineRun
from common.sftp_client import SftpClient
from common.typings import FileInfo
from tests.dhl.mocks import (
    CSV_FILES,
    DATA_DIR,
    DE_TEST_CSV,
    INVALID_CONTENT_CSV,
    INVALID_SCHEMA_CSV,
    IT_TEST_CSV,
    MULTIPLE_CHARGES_CSV,
    NL_TEST_CSV,
    QUOTED_IDENTIFIER_CSV,
    mock_invalid_content,
    mock_multiple_charges,
    mock_quoted_identifier,
    mock_quoted_identifier_order_items,
    mock_quoted_identifier_shipping_billing,
)


def get_file_info(file_path: Path, dt: Optional[datetime] = None) -> FileInfo:
    stats = file_path.stat()
    created = dt if dt else datetime.fromtimestamp(stats.st_mtime or 0, tz=UTC)

    return FileInfo(path=file_path, created=created, size=stats.st_size)


@pytest.fixture(scope="session")
def shipping_config() -> ShippingConfig:
    return ShippingConfig()


@pytest.fixture(scope="session")
def extracted_files() -> ExtractedType:
    return {str(f): get_file_info(f) for f in CSV_FILES}


@pytest.fixture(scope="session")
def quoted_identifier_info() -> FileInfo:
    return get_file_info(QUOTED_IDENTIFIER_CSV, datetime(2024, 3, 28, 9, 2, 12, tzinfo=UTC))


@pytest.fixture(scope="session")
def quoted_identifier_file(quoted_identifier_info: FileInfo) -> ExtractedType:
    return {str(quoted_identifier_info.path): quoted_identifier_info}


@pytest.fixture(scope="session")
def quoted_identifier_data(quoted_identifier_info: FileInfo) -> DataFrame:
    return mock_quoted_identifier(quoted_identifier_info)


@pytest.fixture(scope="session")
def quoted_identifier_transformed(
    quoted_identifier_file: ExtractedType, quoted_identifier_info: FileInfo
) -> TransformedData:
    shipping_billing = mock_quoted_identifier_shipping_billing(quoted_identifier_info)
    order_items = mock_quoted_identifier_order_items()

    return TransformedData(
        extracted_files=quoted_identifier_file, shipping_billing=shipping_billing, order_items=order_items
    )


@pytest.fixture(scope="session")
def shipping_billing_partitioned(
    shipping_config: ShippingConfig, quoted_identifier_transformed: TransformedData, quoted_identifier_info: FileInfo
) -> DataFrame:
    shipping_billing = quoted_identifier_transformed.shipping_billing.copy()
    cols = SB_PARTITIONS

    shipping_billing[[cols.country, cols.provider, cols.year, cols.month, cols.day]] = [
        shipping_config.country,
        shipping_config.provider,
        np.int32(quoted_identifier_info.created.year),
        np.int32(quoted_identifier_info.created.month),
        np.int32(quoted_identifier_info.created.day),
    ]

    return shipping_billing


@pytest.fixture(scope="session")
def order_items_partitioned(
    shipping_config: ShippingConfig, quoted_identifier_transformed: TransformedData, quoted_identifier_info: FileInfo
) -> DataFrame:
    order_items = quoted_identifier_transformed.order_items.copy()
    cols = SB_PARTITIONS

    order_items[[cols.country, cols.provider, cols.year, cols.month, cols.day]] = [
        shipping_config.country,
        shipping_config.provider,
        np.int32(quoted_identifier_info.created.year),
        np.int32(quoted_identifier_info.created.month),
        np.int32(quoted_identifier_info.created.day),
    ]

    return order_items


@pytest.fixture(scope="session")
def multiple_charges_info() -> FileInfo:
    return get_file_info(MULTIPLE_CHARGES_CSV, datetime(2024, 3, 28, 9, 2, 6, tzinfo=UTC))


@pytest.fixture(scope="session")
def multiple_charges_file(multiple_charges_info: FileInfo) -> ExtractedType:
    return {str(multiple_charges_info.path): multiple_charges_info}


@pytest.fixture(scope="session")
def multiple_charges_data(multiple_charges_info: FileInfo) -> DataFrame:
    return mock_multiple_charges(multiple_charges_info)


@pytest.fixture(scope="session")
def invalid_content_info() -> FileInfo:
    return get_file_info(INVALID_CONTENT_CSV, datetime(2024, 3, 28, 9, 2, 4, tzinfo=UTC))


@pytest.fixture(scope="session")
def invalid_content_file(invalid_content_info: FileInfo) -> ExtractedType:
    return {str(invalid_content_info.path): invalid_content_info}


@pytest.fixture(scope="session")
def invalid_content_data(invalid_content_info: FileInfo) -> DataFrame:
    return mock_invalid_content(invalid_content_info)


@pytest.fixture(scope="session")
def invalid_schema_file() -> ExtractedType:
    return {str(INVALID_SCHEMA_CSV): get_file_info(INVALID_SCHEMA_CSV)}


@pytest.fixture(scope="session")
def de_test_file() -> ExtractedType:
    return {str(DE_TEST_CSV): get_file_info(DE_TEST_CSV)}


@pytest.fixture(scope="session")
def nl_test_file() -> ExtractedType:
    return {str(NL_TEST_CSV): get_file_info(NL_TEST_CSV)}


@pytest.fixture(scope="session")
def it_test_file() -> ExtractedType:
    return {str(IT_TEST_CSV): get_file_info(IT_TEST_CSV)}


@pytest.fixture(scope="session")
def sftp_client(extracted_files: ExtractedType) -> Mock:
    mock = Mock(spec_set=SftpClient)
    mock.download_files.return_value = extracted_files

    return mock


@pytest.fixture(scope="session")
def dhl_repository() -> Mock:
    return Mock(spec_set=DhlRepository)


@pytest.fixture(scope="function")
def dhl_shipping_etl(
    test_pipeline_run: PipelineRun,
    shipping_config: ShippingConfig,
    sftp_client: Mock,
    dhl_repository: Mock,
    data_lake_repository_mock: Mock,
) -> DhlShippingEtl:
    return DhlShippingEtl(
        config=shipping_config,
        pipeline_run=test_pipeline_run,
        local_path=DATA_DIR,
        sftp_client=sftp_client,
        dhl_repository=dhl_repository,
        raw_data_repository=data_lake_repository_mock,
        transformed_data_repository=data_lake_repository_mock,
    )
