from dataclasses import replace

import pytest
from dhl_shipping.etl import DhlShippingEtl, ExtractedType
from pandas import DataFrame
from pandas.testing import assert_frame_equal


def test_read_from_csv_when_no_files(dhl_shipping_etl: DhlShippingEtl) -> None:
    actual = dhl_shipping_etl.read_from_csv({})

    assert actual.empty is True


def test_read_from_csv_when_quoted_identifier(
    dhl_shipping_etl: DhlShippingEtl, quoted_identifier_file: ExtractedType, quoted_identifier_data: DataFrame
) -> None:
    actual = dhl_shipping_etl.read_from_csv(quoted_identifier_file)

    assert actual.empty is False
    assert_frame_equal(actual, quoted_identifier_data)


def test_read_from_csv_when_multiple_charges(
    dhl_shipping_etl: DhlShippingEtl, multiple_charges_file: ExtractedType, multiple_charges_data: DataFrame
) -> None:
    actual = dhl_shipping_etl.read_from_csv(multiple_charges_file)

    assert actual.empty is False
    assert_frame_equal(actual, multiple_charges_data)


def test_read_from_csv_when_invalid_content(
    dhl_shipping_etl: DhlShippingEtl, invalid_content_file: ExtractedType, invalid_content_data: DataFrame
) -> None:
    actual = dhl_shipping_etl.read_from_csv(invalid_content_file)

    assert actual.empty is False
    assert_frame_equal(actual, invalid_content_data)


def test_read_from_csv_when_invalid_schema(
    dhl_shipping_etl: DhlShippingEtl, invalid_schema_file: ExtractedType
) -> None:
    with pytest.raises(ValueError, match="Number of passed names did not match number of header fields in the file"):
        dhl_shipping_etl.read_from_csv(invalid_schema_file)


def test_read_from_csv_when_de_test_file(dhl_shipping_etl: DhlShippingEtl, de_test_file: ExtractedType) -> None:
    dhl_shipping_etl._config = replace(dhl_shipping_etl._config, decimal_sep=",")
    actual = dhl_shipping_etl.read_from_csv(de_test_file)

    assert actual.empty is False
    assert len(actual) == 4


def test_read_from_csv_when_nl_test_file(dhl_shipping_etl: DhlShippingEtl, nl_test_file: ExtractedType) -> None:
    actual = dhl_shipping_etl.read_from_csv(nl_test_file)

    assert actual.empty is False
    assert len(actual) == 18


def test_read_from_csv_when_it_test_file(dhl_shipping_etl: DhlShippingEtl, it_test_file: ExtractedType) -> None:
    actual = dhl_shipping_etl.read_from_csv(it_test_file)

    assert actual.empty is False
    assert len(actual) == 3
