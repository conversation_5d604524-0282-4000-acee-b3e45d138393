import pandas as pd
import pytest
from dhl_shared.shipping_config import AccountConfig, ShippingConfig
from dhl_shipping.etl import TransformedData
from dhl_shipping.schema import SB_COLUMNS, SBC_COLUMNS, ShippingBillingValues
from dhl_shipping.transformer import (
    TransformationError,
    add_charge_columns,
    add_file_info,
    add_package_type,
    extract_order_item_ids,
    partition_order_items,
    partition_shipping_billing,
)
from pandas import DataFrame
from pandas.testing import assert_frame_equal

from common.consts import MAX_SERIAL
from common.typings import FileInfo
from tests.dhl.mocks import (
    NetGross,
    XCValue,
    mock_charge_columns,
    mock_order_items,
    mock_shipment_references,
    mock_xc_columns,
)


def test_add_file_info(quoted_identifier_info: FileInfo) -> None:
    # arrange
    rows = 3
    numbers = list(range(rows))
    input_df = DataFrame.from_dict({SB_COLUMNS.shipment_number: numbers})

    expected = DataFrame.from_dict(
        {
            SB_COLUMNS.shipment_number: numbers,
            SBC_COLUMNS.file_path.name: [quoted_identifier_info.path] * rows,
            SBC_COLUMNS.file_created.name: [quoted_identifier_info.created] * rows,
            SBC_COLUMNS.file_size.name: [quoted_identifier_info.size] * rows,
        }
    ).astype(SBC_COLUMNS.file_dtypes)

    # act
    actual = add_file_info(input_df, quoted_identifier_info)

    # assert
    assert_frame_equal(actual, expected)


def test_add_package_type_when_no_billing_account() -> None:
    # act & assert
    with pytest.raises(TransformationError, match="No billing_account"):
        add_package_type(pd.DataFrame(), AccountConfig())


def test_add_package_type_when_no_data() -> None:
    # arrange
    expected = ShippingBillingValues.create_empty_df()
    expected[SBC_COLUMNS.package_type.name] = pd.Series(dtype=SBC_COLUMNS.package_type.dtype)

    # act
    actual = add_package_type(ShippingBillingValues.create_empty_df(), AccountConfig())

    # assert
    assert_frame_equal(actual, expected)


def test_add_package_type_when_billing_accounts() -> None:
    # arrange
    config = AccountConfig()
    accounts = [config.outbound_account, config.inbound_account, "xyz", ""]
    input_df = DataFrame.from_dict({SB_COLUMNS.billing_account: accounts})

    expected = DataFrame.from_dict(
        {
            SB_COLUMNS.billing_account: accounts,
            SBC_COLUMNS.package_type.name: [
                ShippingBillingValues.OUTBOUND,
                ShippingBillingValues.INBOUND,
                ShippingBillingValues.NA,
                ShippingBillingValues.NA,
            ],
        }
    ).astype(SBC_COLUMNS.package_type.astype)

    # act
    actual = add_package_type(input_df, config)

    # assert
    assert_frame_equal(actual, expected)


def test_add_charge_columns_when_no_charges() -> None:
    # arrange
    input_df = mock_xc_columns()
    expected = mock_charge_columns(input_df.copy())

    # act
    actual = add_charge_columns(input_df)

    # assert
    assert_frame_equal(actual, expected)


def test_add_charge_columns_when_invalid_codes() -> None:
    # arrange
    input_df = mock_xc_columns(
        xc1=XCValue(code="oo", net=0.1, gross=0.2),
        xc2=XCValue(code="BO", net=1.1, gross=1.2),
        xc3=XCValue(code="", net=2.1, gross=2.2),
        xc4=XCValue(code=" DD", net=3.1, gross=3.2),
        xc5=XCValue(code="DD ", net=4.1, gross=4.2),
        xc6=XCValue(code=" DD ", net=5.1, gross=5.2),
        xc7=XCValue(code="_DD", net=6.1, gross=6.2),
        xc8=XCValue(code="DD_", net=7.1, gross=7.2),
        xc9=XCValue(code="_DD_", net=7.1, gross=7.2),
    )
    expected = mock_charge_columns(input_df.copy())

    # act
    actual = add_charge_columns(input_df)

    # assert
    assert_frame_equal(actual, expected)


def test_add_charge_columns_when_unique_xc_codes_oo_to_fd() -> None:
    """test adding charge codes from OO to FD"""
    # arrange
    oo = NetGross(0.12, 0.13)
    ob = NetGross(1.45, 1.56)
    ff = NetGross(2.56, 2.67)
    ka = NetGross(3.68, 3.79)
    dd = NetGross(4.71, 4.82)
    cr = NetGross(5.82, 5.93)
    wo = NetGross(9.98, 9.99)
    fe = NetGross(100.0001, 100.0009)
    fd = NetGross(0.01, 0.09)

    input_df = mock_xc_columns(
        xc1=XCValue(code="OO", net=oo.net, gross=oo.gross),
        xc2=XCValue(code="OB", net=ob.net, gross=ob.gross),
        xc3=XCValue(code="FF", net=ff.net, gross=ff.gross),
        xc4=XCValue(code="KA", net=ka.net, gross=ka.gross),
        xc5=XCValue(code="DD", net=dd.net, gross=dd.gross),
        xc6=XCValue(code="CR", net=cr.net, gross=cr.gross),
        xc7=XCValue(code="WO", net=wo.net, gross=wo.gross),
        xc8=XCValue(code="FE", net=fe.net, gross=fe.gross),
        xc9=XCValue(code="FD", net=fd.net, gross=fd.gross),
    )

    expected = mock_charge_columns(
        input_df.copy(),
        remote_area_delivery=oo,
        remote_area_pickup=ob,
        fuel_surcharge=ff,
        change_of_billing=ka,
        duty_tax_paid=dd,
        emergency_situation=cr,
        export_declaration=wo,
        carbon_reduced_fe=fe,
        carbon_reduced_fd=fd,
    )

    # act
    actual = add_charge_columns(input_df)

    # assert
    assert_frame_equal(actual, expected)


def test_add_charge_columns_when_unique_xc_codes_ft_to_nx() -> None:
    """test adding charge codes from FT to NX"""
    # arrange
    ft = NetGross(9.98, 9.99)
    xx = NetGross(5.82, 5.93)
    xb = NetGross(4.71, 4.82)
    yb = NetGross(3.68, 3.79)
    yy = NetGross(2.56, 2.67)
    gp = NetGross(1.45, 1.56)
    ii = NetGross(0.12, 0.13)
    nx = NetGross(20, 25)

    input_df = mock_xc_columns(
        xc1=XCValue(code="FT", net=ft.net, gross=ft.gross),
        xc2=XCValue(code="XX", net=xx.net, gross=xx.gross),
        xc3=XCValue(code="XB", net=xb.net, gross=xb.gross),
        xc4=XCValue(code="YB", net=yb.net, gross=yb.gross),
        xc5=XCValue(code="YY", net=yy.net, gross=yy.gross),
        xc6=XCValue(code="GP", net=gp.net, gross=gp.gross),
        xc7=XCValue(code="II", net=ii.net, gross=ii.gross),
        xc8=XCValue(code="NX", net=nx.net, gross=nx.gross),
    )

    expected = mock_charge_columns(
        input_df.copy(),
        carbon_reduced_ft=ft,
        import_export_duties=xx,
        import_export_taxes=xb,
        oversize_piece=yb,
        overweight_piece=yy,
        plastic_flyer=gp,
        shipment_insurance=ii,
        demand_surcharge=nx,
    )

    # act
    actual = add_charge_columns(input_df)

    # assert
    assert_frame_equal(actual, expected)


def test_add_charge_columns_when_duplicate_xc_codes() -> None:
    # arrange
    ii = NetGross(0.50, 0.75)
    gp = NetGross(1.49, 1.74)
    yy = NetGross(2.50, 2.50)
    yb = NetGross(3.01, 3.02)

    input_df = mock_xc_columns(
        xc1=XCValue(code="II", net=ii.net, gross=ii.gross),
        xc2=XCValue(code="II", net=ii.net + 1, gross=ii.gross + 1),
        xc3=XCValue(code="GP", net=gp.net, gross=gp.gross),
        xc4=XCValue(code="GP", net=gp.net + 1, gross=gp.gross + 1),
        xc5=XCValue(code="YY", net=yy.net, gross=yy.gross),
        xc6=XCValue(code="YY", net=yy.net + 1, gross=yy.gross + 1),
        xc7=XCValue(code="YB", net=yb.net, gross=yb.gross),
        xc8=XCValue(code="YB", net=yb.net + 1, gross=yb.gross + 1),
        xc9=XCValue(code="II", net=ii.net + 2, gross=ii.gross + 2),
    )
    expected = mock_charge_columns(
        input_df.copy(),
        shipment_insurance=ii,
        plastic_flyer=gp,
        overweight_piece=yy,
        oversize_piece=yb,
    )

    # act
    actual = add_charge_columns(input_df)

    # assert
    assert_frame_equal(actual, expected)


def test_extract_order_item_ids_when_invalid_numbers() -> None:
    # arrange
    input_df = mock_shipment_references(
        shipment_numbers=[None, "", "   "],
        shipment_references=["1", "2", "3"],
    )
    copy = input_df.copy()

    # act
    actual = extract_order_item_ids(input_df)

    # assert
    assert_frame_equal(input_df, copy)
    assert actual.empty


def test_extract_order_item_ids_when_invalid_references() -> None:
    # arrange
    input_df = mock_shipment_references(
        shipment_numbers=["None", "Empty1", "Empty2", "NA", "String", "Dash", "Too big", "Separators"],
        shipment_references=[None, " ", "     ", pd.NA, "string", "-", MAX_SERIAL + 1, ", , ,"],
    )
    copy = input_df.copy()

    # act
    actual = extract_order_item_ids(input_df)

    # assert
    assert_frame_equal(input_df, copy)
    assert actual.empty


def test_extract_order_item_ids_when_valid_items() -> None:
    # arrange
    input_df = mock_shipment_references(
        shipment_numbers=[
            "12345",
            "1-2-3",
            "10-20-30",
            "99-A",
            "Negative",
            "Space separator",
            "2399066891",
            "2715239962",
        ],
        shipment_references=[
            "12345",
            "0,1,2",
            "10 , 20, 30 ",
            "99,A",
            "-1",
            "1 2 3",
            "RF-OIID-15105403",
            "RF-OIID-15072154,RF-OIID-15169120",
        ],
    )
    copy = input_df.copy()
    expected = mock_order_items(
        shipment_numbers=[
            "12345",
            "1-2-3",
            "1-2-3",
            "1-2-3",
            "10-20-30",
            "10-20-30",
            "10-20-30",
            "99-A",
            "Negative",
            "Space separator",
            "2399066891",
            "2715239962",
            "2715239962",
        ],
        order_items=[12345, 0, 1, 2, 10, 20, 30, 99, 1, 123, 15105403, 15072154, 15169120],
    )

    # act
    actual = extract_order_item_ids(input_df)

    # assert
    assert_frame_equal(input_df, copy)
    assert_frame_equal(actual, expected)


def test_partition_shipping_billing(
    shipping_config: ShippingConfig,
    quoted_identifier_transformed: TransformedData,
    shipping_billing_partitioned: DataFrame,
) -> None:
    # arrange
    given = quoted_identifier_transformed.shipping_billing
    expected = shipping_billing_partitioned

    # act
    actual = partition_shipping_billing(given, shipping_config)

    # assert
    assert_frame_equal(actual, expected)


def test_partition_order_items(
    quoted_identifier_transformed: TransformedData,
    shipping_billing_partitioned: DataFrame,
    order_items_partitioned: DataFrame,
) -> None:
    # arrange
    order_items = quoted_identifier_transformed.order_items
    expected = order_items_partitioned

    # act
    actual = partition_order_items(shipping_billing_partitioned, order_items)

    # assert
    assert_frame_equal(actual, expected)
