from datetime import datetime
from unittest.mock import Mock, patch

from _pytest.logging import LogCaptureFixture
from dhl_shared.shipping_config import ShippingConfig
from pandas.testing import assert_frame_equal
from schema import ShippingBillingError

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from dhl.dhl_shipping.etl import DhlShippingEtl, ExtractedType, TransformedData


def test_extract(dhl_shipping_etl: DhlShippingEtl, sftp_client: Mock, extracted_files: ExtractedType) -> None:
    # act
    result = dhl_shipping_etl.extract()

    # assert
    sftp_client.download_files.assert_called()
    assert result == extracted_files


def test_transform(
    dhl_shipping_etl: DhlShippingEtl,
    quoted_identifier_file: ExtractedType,
    quoted_identifier_transformed: TransformedData,
) -> None:
    # arrange
    expected = quoted_identifier_transformed

    # act
    result = dhl_shipping_etl.transform(extracted=quoted_identifier_file)

    # assert
    assert result.extracted_files == expected.extracted_files
    assert_frame_equal(result.shipping_billing, expected.shipping_billing)


def test_load(
    dhl_shipping_etl: DhlShippingEtl,
    dhl_repository: Mock,
    data_lake_repository_mock: Mock,
    quoted_identifier_transformed: TransformedData,
) -> None:
    extracted_count = len(quoted_identifier_transformed.extracted_files)
    result = dhl_shipping_etl.load(quoted_identifier_transformed)

    assert result == extracted_count
    dhl_repository.insert_into_import.assert_called()
    dhl_repository.upsert_into_analytics.assert_called()
    assert data_lake_repository_mock.write_file.call_count == extracted_count
    assert data_lake_repository_mock.write_data_frame.call_count == 2


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_run(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    dhl_shipping_etl: DhlShippingEtl,
    sftp_client: Mock,
    quoted_identifier_file: ExtractedType,
    quoted_identifier_transformed: TransformedData,
) -> None:
    # arrange
    dhl_shipping_etl._dhl_repository.insert.return_value = 0
    sftp_client.download_files.return_value = quoted_identifier_file
    extracted_count = len(quoted_identifier_transformed.extracted_files)

    # act
    with patch.object(ShippingConfig, "remove_processed_files", return_value=True):
        result = dhl_shipping_etl.run()

    # assert
    assert result == extracted_count
    assert sftp_client.remove_files.call_count == extracted_count
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


def test_check_shipping_billing_errors_when_no_errors(
    dhl_shipping_etl: DhlShippingEtl,
    dhl_repository: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "WARNING"
    caplog.set_level(log_level)

    dhl_repository.get_shipping_billing_errors.return_value = set()

    # act
    result = dhl_shipping_etl.check_shipping_billing_errors()

    # assert
    assert result == 0
    assert caplog.records == []


def test_check_shipping_billing_errors_when_errors(
    dhl_shipping_etl: DhlShippingEtl,
    dhl_repository: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "WARNING"
    caplog.set_level(log_level)

    expected = ShippingBillingError(
        error="error",
        id=1,
        billing_source="billing_source",
        billing_account="billing_account",
        shipment_number="shipment_number",
        file_path="file_path",
        file_created=datetime.fromisoformat("2025-01-01T00:00:00Z"),
    )
    dhl_repository.get_shipping_billing_errors.return_value = {expected}

    # act
    result = dhl_shipping_etl.check_shipping_billing_errors()

    # assert
    assert result == 1
    assert len(caplog.records) == 1

    # Assert the log content contains the expected error properties
    record = caplog.records[0]
    assert record.levelname == log_level
    assert record.message == str(expected)
