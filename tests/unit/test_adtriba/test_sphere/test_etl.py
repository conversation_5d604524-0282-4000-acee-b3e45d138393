from io import Bytes<PERSON>
from pathlib import Path
from typing import List
from unittest.mock import Mock, patch

import pytest
from _pytest.logging import LogCaptureFixture
from botocore.client import BaseClient
from pandas import DataFrame
from sphere_etl import AdtribaSphereEtl
from sphere_model import AdtribaSphereConfig

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
@pytest.mark.parametrize(
    "given_file_paths",
    [
        [Path("foo=1/bar=2/file_1.parquet"), Path("foo=1/bar=2/file_2.parquet")],
        [Path("baz=abc/file_3.parquet"), Path("file_4.parquet")],
    ],
)
def test_extract(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    given_file_paths: list[Path],
    adtriba_sphere_etl: AdtribaSphereEtl,
    adtriba_sphere_config: AdtribaSphereConfig,
    adtriba_sphere_test_df: DataFrame,
    s3_boto_client: BaseClient,
) -> None:
    # arrange
    upload_test_files(adtriba_sphere_test_df, adtriba_sphere_config, given_file_paths, s3_boto_client)
    expected_file_paths = [Path(adtriba_sphere_etl.run_suffix, file_path) for file_path in given_file_paths]

    # act
    actual_file_paths = sorted(adtriba_sphere_etl.extract())

    # assert
    assert actual_file_paths == expected_file_paths
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
@pytest.mark.parametrize(
    "given_file_paths",
    [
        [Path("foo=1/bar=2/file_1.parquet"), Path("foo=1/bar=2/_SUCCESS"), Path("_SUCCESS")],
        [Path("baz=abc/file_3.parquet"), Path("file_4.parquet")],
    ],
)
def test_transform(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    given_file_paths: list[Path],
    adtriba_sphere_etl: AdtribaSphereEtl,
    adtriba_sphere_config: AdtribaSphereConfig,
    adtriba_sphere_test_df: DataFrame,
) -> None:
    # arrange
    expected_file_paths = [
        Path(adtriba_sphere_etl.run_suffix, file_path)
        for file_path in given_file_paths
        for file_name_to_ignore in adtriba_sphere_config.file_names_to_ignore
        if file_path.name != file_name_to_ignore
    ]
    extracted_path = adtriba_sphere_etl.local_path
    save_test_files(adtriba_sphere_test_df, adtriba_sphere_etl.local_path, given_file_paths)

    # act
    actual_file_paths = sorted(adtriba_sphere_etl.transform(extracted_path))

    # assert
    assert actual_file_paths == expected_file_paths
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    adtriba_sphere_etl: AdtribaSphereEtl,
    data_lake_repository_mock: Mock,
    adtriba_sphere_bq_repository_mock: Mock,
) -> None:
    # act
    adtriba_sphere_etl.load([Path("test")])

    # assert
    data_lake_repository_mock.write_directory.assert_called_once()
    adtriba_sphere_bq_repository_mock.load_adtriba_table.assert_called_once()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(AdtribaSphereEtl, "extract")
@patch.object(AdtribaSphereEtl, "transform")
@patch.object(AdtribaSphereEtl, "load", return_value=[0, 0])
def test_run(
    load_mock: Mock,
    transform_mock: Mock,
    extract_mock: Mock,
    adtriba_sphere_etl: AdtribaSphereEtl,
) -> None:
    # act
    adtriba_sphere_etl.run()

    # assert
    extract_mock.assert_called_once()
    transform_mock.assert_called_once()
    load_mock.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_run_should_skip_processing_when_no_adtriba_files_found(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    adtriba_sphere_etl: AdtribaSphereEtl,
    data_lake_repository_mock: Mock,
    adtriba_sphere_bq_repository_mock: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "WARNING"
    caplog.set_level(log_level)

    # act
    adtriba_sphere_etl.run()

    # assert
    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "No Adtriba Sphere files to be loaded. Finishing." in caplog.messages
    data_lake_repository_mock.write_directory.assert_not_called()
    adtriba_sphere_bq_repository_mock.load_adtriba_table.assert_not_called()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


def save_test_files(df: DataFrame, temp_base_path: Path, given_file_paths: List[Path]) -> None:
    """
    Helper test function to save Adtriba files locally.

    :param df: pandas DataFrame to be saved as a parquet file
    :param temp_base_path: etl temporary local base path
    :param given_file_paths: list of files to be saved in based path
    """
    for file_path in given_file_paths:
        given_file_path = Path(temp_base_path, file_path)
        given_file_path.parent.mkdir(parents=True, exist_ok=True)
        df.to_parquet(given_file_path)


def upload_test_files(
    df: DataFrame, adtriba_config: AdtribaSphereConfig, given_file_paths: List[Path], s3_boto_client: BaseClient
) -> None:
    """
    Helper test function to upload Adtriba files to S3 mocked bucket (via moto).

    :param df: pandas DataFrame to be uploaded as a parquet file
    :param adtriba_config: Adtriba etl config
    :param given_file_paths: list of files to be saved in based path
    :param s3_boto_client: S3 low level boto client (mocked via moto package)
    """
    buffer = BytesIO()
    df.to_parquet(buffer)  # noqa

    for file_path in given_file_paths:
        given_file_path = f"{adtriba_config.source_path}/{file_path}"

        s3_boto_client.put_object(Bucket=adtriba_config.source_bucket, Key=given_file_path, Body=buffer.getvalue())
        buffer.seek(0)  # to have the content for the next iteration
