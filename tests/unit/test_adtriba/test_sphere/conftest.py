from pathlib import Path
from unittest.mock import Mock

import pytest
from conftest import S3_TEST_BUCKET_NAME
from pandas import DataFrame
from sphere_bq_repo import AdtribaSphereBqRepository
from sphere_etl import AdtribaSphereEtl
from sphere_model import AdtribaSphereConfig

from common.pipeline_logging.pipeline_model import PipelineRun
from common.s3_client import S3Client
from common.sql_repository import SqlRepository


@pytest.fixture(scope="session")
def adtriba_sphere_config() -> AdtribaSphereConfig:
    return AdtribaSphereConfig(source_bucket=S3_TEST_BUCKET_NAME)


@pytest.fixture(scope="session")
def adtriba_sphere_test_df() -> DataFrame:
    return DataFrame({"country": ["at, de"]})


@pytest.fixture(scope="function")
def adtriba_sphere_bq_repository_mock() -> Mock:
    return Mock(spec=AdtribaSphereBqRepository)


@pytest.fixture(scope="function")
def adtriba_sphere_pg_repository_mock() -> Mock:
    return Mock(spec=SqlRepository)


@pytest.fixture(scope="function")
def adtriba_sphere_etl(
    adtriba_sphere_config: AdtribaSphereConfig,
    test_pipeline_run: PipelineRun,
    s3_client_boto_mock: S3Client,
    data_lake_repository_mock: Mock,
    adtriba_sphere_bq_repository_mock: Mock,
    adtriba_sphere_pg_repository_mock: Mock,
    tmp_path: Path,
) -> AdtribaSphereEtl:
    return AdtribaSphereEtl(
        config=adtriba_sphere_config,
        pipeline_run=test_pipeline_run,
        s3_client=s3_client_boto_mock,
        raw_data_lake_repository=data_lake_repository_mock,
        bq_repository=adtriba_sphere_bq_repository_mock,
        pg_repository=adtriba_sphere_pg_repository_mock,
        local_temp_path=tmp_path,
    )
