from pathlib import Path
from unittest.mock import Mock

import pytest
from conftest import S3_TEST_BUCKET_NAME
from tcm_bq_repo import AdtribaTcmBqRepository
from tcm_etl import AdtribaTcmEtl
from tcm_model import AdtribaTcmConfig, BackendConversions
from tcm_pg_repo import AdtribaTcmPgRepository

from common.pipeline_logging.pipeline_model import PipelineRun
from common.s3_client import S3Client
from common.time_service import TimeService


@pytest.fixture(scope="function")
def adtriba_tcm_config() -> AdtribaTcmConfig:
    return AdtribaTcmConfig(export_bucket=S3_TEST_BUCKET_NAME, export_enabled=True)


@pytest.fixture(scope="function")
def adtriba_tcm_bq_repository_mock() -> Mock:
    return Mock(spec=AdtribaTcmBqRepository)


@pytest.fixture(scope="function")
def adtriba_tcm_pg_repository_mock() -> Mock:
    return Mock(spec_set=AdtribaTcmPgRepository)


@pytest.fixture(scope="session")
def backend_conversions(time_service: TimeService) -> list[BackendConversions]:
    return [
        BackendConversions(
            date=time_service.yesterday,
            costumer_type="new",
            device_type="web",
            total_conversions=100,
            total_revenue=123.456,
            brand="refurbed",
            country="AT",
            conversion_name="Transaction",
            currency="EUR",
        ),
        BackendConversions(
            date=time_service.today,
            costumer_type="returning",
            device_type="web",
            total_conversions=200,
            total_revenue=789.999,
            brand="refurbed",
            country="DE",
            conversion_name="Transaction",
            currency="EUR",
        ),
    ]


@pytest.fixture(scope="function")
def adtriba_tcm_etl(
    adtriba_tcm_config: AdtribaTcmConfig,
    test_pipeline_run: PipelineRun,
    s3_client_boto_mock: S3Client,
    data_lake_repository_mock: Mock,
    adtriba_tcm_bq_repository_mock: Mock,
    adtriba_tcm_pg_repository_mock: Mock,
    time_service: TimeService,
    tmp_path: Path,
) -> AdtribaTcmEtl:
    return AdtribaTcmEtl(
        config=adtriba_tcm_config,
        pipeline_run=test_pipeline_run,
        s3_client=s3_client_boto_mock,
        raw_data_lake_repository=data_lake_repository_mock,
        bq_repository=adtriba_tcm_bq_repository_mock,
        pg_repository=adtriba_tcm_pg_repository_mock,
        time_service=time_service,
        local_temp_path=tmp_path,
    )
