import dataclasses
from datetime import date, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

import pytest
from _pytest.logging import LogCaptureFixture
from conftest import YESTERDAY
from pyarrow import parquet
from tcm_etl import AdtribaTcmEtl
from tcm_model import AdtribaTcmConfig, BackendConversions, ExportDates

from common.pipeline_logging.pipeline_logger import PipelineExecutionLogger
from common.s3_client import S3Client
from common.time_service import TimeService

DATE_1_DAY_BEFORE_YD = YESTERDAY - timedelta(days=1)
DATE_5_DAYS_BEFORE_YD = YESTERDAY - timedelta(days=5)
DATE_26_DAYS_BEFORE_YD = YESTERDAY - timedelta(days=26)
DATE_30_DAYS_BEFORE_YD = YESTERDAY - timedelta(days=30)
DATE_100_DAYS_AFTER_YD = YESTERDAY + timedelta(days=100)


@pytest.mark.parametrize(
    "given_date, max_dates, expected",
    [
        # no export date found in BQ
        (None, 2, ExportDates(YESTERDAY, YESTERDAY)),
        # negative max number of dates
        (YESTERDAY, -1, ExportDates(YESTERDAY, YESTERDAY)),
        # last export yesterday (rerun)
        (YESTERDAY, 3, ExportDates(YESTERDAY, YESTERDAY)),
        # anomaly: export date in BQ greater than yesterday
        (DATE_100_DAYS_AFTER_YD, 1, ExportDates(YESTERDAY, YESTERDAY)),
        # last export 1 day before yesterday
        (DATE_1_DAY_BEFORE_YD, 7, ExportDates(DATE_1_DAY_BEFORE_YD, YESTERDAY)),
        # last export 30 days before, limit days to 5
        (DATE_30_DAYS_BEFORE_YD, 5, ExportDates(DATE_30_DAYS_BEFORE_YD, DATE_26_DAYS_BEFORE_YD)),
        # last export 5 days before, last export day + 7 days is greater than yesterday
        (DATE_5_DAYS_BEFORE_YD, 7, ExportDates(DATE_5_DAYS_BEFORE_YD, YESTERDAY)),
    ],
)
def test_get_export_dates(
    given_date: date,
    max_dates: int,
    expected: list[date],
    adtriba_tcm_etl: AdtribaTcmEtl,
) -> None:
    # act
    actual = adtriba_tcm_etl.get_export_dates(given_date, max_dates)

    # assert
    assert actual == expected


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_transform(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    adtriba_tcm_etl: AdtribaTcmEtl,
    backend_conversions: list[BackendConversions],
) -> None:
    # arrange
    expected_file_paths = [
        Path(
            f"{adtriba_tcm_etl.run_suffix}/date={bc.date.isoformat()}",
            f"backend-conversions-{bc.date.isoformat()}.parquet",
        )
        for bc in backend_conversions
    ]

    # act
    actual_file_paths = sorted(adtriba_tcm_etl.transform(backend_conversions))
    actual = [
        BackendConversions.from_dict(record) for record in parquet.read_table(adtriba_tcm_etl.local_path).to_pylist()
    ]

    # assert
    assert actual_file_paths == expected_file_paths
    assert actual == backend_conversions
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    adtriba_tcm_etl: AdtribaTcmEtl,
    data_lake_repository_mock: Mock,
    backend_conversions: list[BackendConversions],
    s3_client_boto_mock: S3Client,
) -> None:
    # arrange
    given_paths = adtriba_tcm_etl.transform(backend_conversions)
    expected_file_count = len(backend_conversions)
    expected_file_paths = [
        f"dwh/backend_conversions/backend-conversions-{bc.date.isoformat()}.parquet" for bc in backend_conversions
    ]
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
    mock_start_processing_step.reset_mock()
    mock_finish_processing_step.reset_mock()

    # act
    actual_dl_file_count, actual_exported_file_count = adtriba_tcm_etl.load(given_paths)
    actual_file_paths = list(s3_client_boto_mock.list_objects())

    # assert
    assert actual_dl_file_count == expected_file_count
    assert actual_exported_file_count == expected_file_count
    assert actual_file_paths == expected_file_paths
    data_lake_repository_mock.write_directory.assert_called_once()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
def test_load_should_log_warning_when_export_disabled(
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    adtriba_tcm_etl: AdtribaTcmEtl,
    adtriba_tcm_config: AdtribaTcmConfig,
    s3_client_mock: Mock,
    s3_client_boto_mock: S3Client,
    data_lake_repository_mock: Mock,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    log_level = "WARNING"
    caplog.set_level(log_level)
    adtriba_tcm_etl._config = dataclasses.replace(adtriba_tcm_config, export_enabled=False)

    etl = adtriba_tcm_etl

    # act
    etl.load([])
    actual_file_paths = list(s3_client_boto_mock.list_objects())

    # assert
    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "Export to Adtriba S3 bucket disabled!" in caplog.messages
    assert actual_file_paths == []
    data_lake_repository_mock.write_directory.assert_called_once()
    s3_client_mock.upload_file.assert_not_called()
    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()


@patch.object(AdtribaTcmEtl, "extract")
@patch.object(AdtribaTcmEtl, "transform")
@patch.object(AdtribaTcmEtl, "load", return_value=[0, 0])
def test_run(
    load_mock: Mock,
    transform_mock: Mock,
    extract_mock: Mock,
    adtriba_tcm_etl: AdtribaTcmEtl,
) -> None:
    # act
    adtriba_tcm_etl.run()

    # assert
    extract_mock.assert_called_once()
    transform_mock.assert_called_once()
    load_mock.assert_called_once()


@patch.object(PipelineExecutionLogger, "finish_processing_step")
@patch.object(PipelineExecutionLogger, "start_processing_step")
@patch.object(AdtribaTcmEtl, "transform")
@patch.object(AdtribaTcmEtl, "load", return_value=[0, 0])
def test_run_should_skip_processing_when_no_extracted_data_found(
    load_mock: Mock,
    transform_mock: Mock,
    mock_start_processing_step: Mock,
    mock_finish_processing_step: Mock,
    adtriba_tcm_etl: AdtribaTcmEtl,
    adtriba_tcm_bq_repository_mock: Mock,
    adtriba_tcm_pg_repository_mock: Mock,
    time_service: TimeService,
    caplog: LogCaptureFixture,
) -> None:
    # arrange
    adtriba_tcm_bq_repository_mock.get_last_export_date.return_value = time_service.yesterday
    adtriba_tcm_pg_repository_mock.get_backend_conversions.return_value = []
    log_level = "WARNING"
    caplog.set_level(log_level)

    # act
    adtriba_tcm_etl.run()

    # assert
    assert caplog.records != []
    assert caplog.records[0].levelname == log_level
    assert "No backend conversions to be exported. Finishing." in caplog.messages
    transform_mock.assert_not_called()
    load_mock.assert_not_called()

    mock_start_processing_step.assert_called_once()
    mock_finish_processing_step.assert_called_once()
