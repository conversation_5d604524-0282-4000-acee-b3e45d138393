import sys
from dataclasses import dataclass
from pathlib import Path

from dataclasses_json import DataClassJsonMixin
from pandas._testing import assert_frame_equal

from src.common.pandas_utils import DataFrameWithSchema

current_dir = Path(__file__).resolve().parent


def set_source_paths(paths: list[str]) -> None:
    """
    Include given paths in search PATH to allow import without `src` prefix.
    This is to mimic lambda environment where `src` code is copied into root folder.

    :param paths: list of file paths to be added to PATH variable
    """
    for path in paths:
        if path not in sys.path:
            sys.path.append(path)


@dataclass(frozen=True)
class TestFile(DataClassJsonMixin):
    # https://stackoverflow.com/a/72465142
    __test__ = False

    file_path: Path
    file_content: str


@dataclass(frozen=True)
class TestMessage(DataClassJsonMixin):
    __test__ = False

    id: int
    value: str


def get_test_file() -> TestFile:
    test_file_path = current_dir / "test.txt"
    with open(test_file_path) as f:
        file_content = f.read()

    return TestFile(file_path=test_file_path, file_content=file_content)


def assert_data_frame_schema(actual: DataFrameWithSchema, expected: DataFrameWithSchema) -> None:
    """
    Assert `actual` and `expected` DataFrameWithSchema matches.
    """
    assert_frame_equal(actual.dataframe_with_schema, expected.dataframe_with_schema)
    assert actual.schema == expected.schema
