from dataclasses import dataclass
from datetime import datetime
from pathlib import Path

import pandas as pd
from dataclasses_json import DataClassJsonMixin
from pandas import DataFrame
from pandas.core.dtypes.base import ExtensionDtype

from common.typings import FileInfo
from dhl.dhl_shipping.schema import (
    SB_COLUMNS,
    SBC_COLUMNS,
    ShippingBillingSchema,
    ShippingBillingValues,
)

INBOUND = ShippingBillingValues.INBOUND
OUTBOUND = ShippingBillingValues.OUTBOUND

CURRENT_DIR = Path(__file__).resolve().parent
DATA_DIR = CURRENT_DIR / "data"
CSV_FILES = [f for f in DATA_DIR.iterdir() if f.is_file()]

QUOTED_IDENTIFIER_CSV = DATA_DIR / "QUOTED_IDENTIFIER.csv"
MULTIPLE_CHARGES_CSV = DATA_DIR / "MULTIPLE_CHARGES.csv"
INVALID_CONTENT_CSV = DATA_DIR / "INVALID_CONTENT.csv"
INVALID_SCHEMA_CSV = DATA_DIR / "INVALID_SCHEMA.csv"
DE_TEST_CSV = DATA_DIR / "DE_TEST.csv"
NL_TEST_CSV = DATA_DIR / "NL_TEST.csv"
IT_TEST_CSV = DATA_DIR / "IT_TEST.csv"


@dataclass(frozen=True)
class NetGross(DataClassJsonMixin):
    net: float = 0.0
    gross: float = 0.0


@dataclass(frozen=True)
class XCValue(NetGross):
    code: str = ""


def as_date(date_time: int) -> datetime:
    return datetime.fromisoformat(str(date_time))


def as_shipping_billing_types(df: DataFrame) -> DataFrame:
    """
    Need to explicitly cast for string and float, date columns are correctly aligned with python datetime.
    Required for `assert_frame_equal` assertion.

    :param df: input data frame
    :returns: type cast data frame
    """
    dtypes = {col: pd.Float32Dtype() for col in ShippingBillingSchema.FLOAT_COLUMNS}
    dtypes.update({col: pd.StringDtype() for col in ShippingBillingSchema.STRING_COLUMNS})
    dtypes.update(SBC_COLUMNS.file_dtypes)

    return df.astype(dtypes)


def mock_quoted_identifier(file_info: FileInfo) -> DataFrame:
    """
    QUOTED_IDENTIFIER.csv as DataFrame
    """
    df = DataFrame.from_dict(
        {
            SB_COLUMNS.line_type: ["S"],
            SB_COLUMNS.billing_source: ["ES-TD"],
            SB_COLUMNS.billing_account: ["*********"],
            SB_COLUMNS.billing_country_code: ["ES"],
            SB_COLUMNS.invoice_date: [as_date(********)],
            SB_COLUMNS.invoice_number: ["xxxx"],
            SB_COLUMNS.shipment_number: ["**********"],
            SB_COLUMNS.shipment_date: [as_date(********)],
            SB_COLUMNS.shipment_reference_1: ["1001"],
            SB_COLUMNS.shipment_reference_2: [""],
            SB_COLUMNS.shipment_reference_3: [""],
            SB_COLUMNS.weight_kg: [13.50],
            SB_COLUMNS.weight_flag: ["B"],
            SB_COLUMNS.senders_name: ["Supplier A"],
            SB_COLUMNS.senders_country: ["TH"],
            SB_COLUMNS.receivers_country: ["ES"],
            SB_COLUMNS.currency: ["EUR"],
            SB_COLUMNS.amount_net: [90.40],
            SB_COLUMNS.amount_gross: [90.40],
            SB_COLUMNS.xc1_code: ["FF"],
            SB_COLUMNS.xc1_net: [21.50],
            SB_COLUMNS.xc1_gross: [21.50],
            SB_COLUMNS.xc2_code: [0],
            SB_COLUMNS.xc2_net: [0],
            SB_COLUMNS.xc2_gross: [0],
            SB_COLUMNS.xc3_code: [0],
            SB_COLUMNS.xc3_net: [0],
            SB_COLUMNS.xc3_gross: [0],
            SB_COLUMNS.xc4_code: [0],
            SB_COLUMNS.xc4_net: [0],
            SB_COLUMNS.xc4_gross: [0],
            SB_COLUMNS.xc5_code: [0],
            SB_COLUMNS.xc5_net: [0],
            SB_COLUMNS.xc5_gross: [0],
            SB_COLUMNS.xc6_code: [0],
            SB_COLUMNS.xc6_net: [0],
            SB_COLUMNS.xc6_gross: [0],
            SB_COLUMNS.xc7_code: [0],
            SB_COLUMNS.xc7_net: [0],
            SB_COLUMNS.xc7_gross: [0],
            SB_COLUMNS.xc8_code: [0],
            SB_COLUMNS.xc8_net: [0],
            SB_COLUMNS.xc8_gross: [0],
            SB_COLUMNS.xc9_code: [0],
            SB_COLUMNS.xc9_net: [0],
            SB_COLUMNS.xc9_gross: [0],
            SBC_COLUMNS.file_path.name: [str(file_info.path)],
            SBC_COLUMNS.file_created.name: [file_info.created],
            SBC_COLUMNS.file_size.name: [file_info.size],
        },
    )

    return as_shipping_billing_types(df)


def mock_quoted_identifier_shipping_billing(file_info: FileInfo) -> DataFrame:
    df = mock_quoted_identifier(file_info)

    # add_package_type
    df[SBC_COLUMNS.package_type.name] = [OUTBOUND]
    df = df.astype(SBC_COLUMNS.package_type.astype)

    # add_charge_columns
    return mock_charge_columns(df, fuel_surcharge=NetGross(21.50, 21.50))


def mock_quoted_identifier_order_items() -> DataFrame:
    return mock_order_items(shipment_numbers=["**********"], order_items=[1001])


def mock_multiple_charges(file_info: FileInfo) -> DataFrame:
    """
    MULTIPLE_CHARGES.csv as DataFrame
    """
    rows = 8
    df = DataFrame.from_dict(
        {
            SB_COLUMNS.line_type: ["S"] * rows,
            SB_COLUMNS.billing_source: ["ES-TD"] * rows,
            SB_COLUMNS.billing_account: ["*********"] * 5 + ["*********", "*********", "*********"],
            SB_COLUMNS.billing_country_code: ["ES"] * rows,
            SB_COLUMNS.invoice_date: [as_date(********)] * 3
            + [as_date(********), as_date(********), as_date(********), as_date(********), as_date(********)],
            SB_COLUMNS.invoice_number: ["ES/*********/********"] * 3
            + [
                "ES/*********/********",
                "ES/*********/********",
                "ES/*********/********",
                "ES/*********/********",
                "ES/*********/********",
            ],
            SB_COLUMNS.shipment_number: [
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
            ],
            SB_COLUMNS.shipment_date: [as_date(********)] * 3
            + [as_date(********), as_date(********), as_date(20240303), as_date(20240303), as_date(20240304)],
            SB_COLUMNS.shipment_reference_1: ["1001", "1002, 1003", "1004", "1005", "1006", "1005", "1006", "2001"],
            SB_COLUMNS.shipment_reference_2: [""] * rows,
            SB_COLUMNS.shipment_reference_3: ["outbound"] * 5 + ["inbound", "inbound", "outbound"],
            SB_COLUMNS.weight_kg: [5.00, 5.00, 3.50, 5.00, 5.00, 5.00, 5.00, 3.00],
            SB_COLUMNS.weight_flag: ["B"] * rows,
            SB_COLUMNS.senders_name: [
                "Supplier A",
                "Supplier A",
                "Supplier A",
                "Supplier B",
                "Supplier C",
                "Supplier B",
                "Supplier C",
                "Supplier D",
            ],
            SB_COLUMNS.senders_country: ["TH", "TH", "TH", "DE", "DE", "ES", "ES", "UK"],
            SB_COLUMNS.receivers_country: ["ES"] * 5 + ["DE", "DE", "ES"],
            SB_COLUMNS.currency: ["EUR"] * rows,
            SB_COLUMNS.amount_net: [30.00, 50.00, 10.40, 45.00, 45.00, 45.00, 45.00, 75.00],
            SB_COLUMNS.amount_gross: [36.00, 60.00, 12.48, 54.00, 54.00, 54.00, 54.00, 90.00],
            SB_COLUMNS.xc1_code: ["FF"] * rows,
            SB_COLUMNS.xc1_net: [10.5, 10.5, 10.5, 8, 8, 8, 8, 5],
            SB_COLUMNS.xc1_gross: [12.6, 12.6, 12.6, 9.6, 9.6, 9.6, 9.6, 6],
            SB_COLUMNS.xc2_code: [0, 0, "DD", 0, 0, 0, 0, "OO"],
            SB_COLUMNS.xc2_net: [0, 0, 5, 0, 0, 0, 0, 0.1],
            SB_COLUMNS.xc2_gross: [0, 0, 6, 0, 0, 0, 0, 0.12],
            SB_COLUMNS.xc3_code: [0] * 7 + ["OB"],
            SB_COLUMNS.xc3_net: [0] * 7 + [0.2],
            SB_COLUMNS.xc3_gross: [0] * 7 + [0.24],
            SB_COLUMNS.xc4_code: [0] * 7 + ["II"],
            SB_COLUMNS.xc4_net: [0] * 7 + [0.3],
            SB_COLUMNS.xc4_gross: [0] * 7 + [0.36],
            SB_COLUMNS.xc5_code: [0] * 7 + ["DD"],
            SB_COLUMNS.xc5_net: [0] * 7 + [0.4],
            SB_COLUMNS.xc5_gross: [0] * 7 + [0.48],
            SB_COLUMNS.xc6_code: [0] * 7 + ["CR"],
            SB_COLUMNS.xc6_net: [0] * 7 + [0.5],
            SB_COLUMNS.xc6_gross: [0] * 7 + [0.6],
            SB_COLUMNS.xc7_code: [0] * 7 + ["WO"],
            SB_COLUMNS.xc7_net: [0] * 7 + [0.6],
            SB_COLUMNS.xc7_gross: [0] * 7 + [0.72],
            SB_COLUMNS.xc8_code: [0] * 7 + ["FE"],
            SB_COLUMNS.xc8_net: [0] * 7 + [0.7],
            SB_COLUMNS.xc8_gross: [0] * 7 + [0.84],
            SB_COLUMNS.xc9_code: [0] * 7 + ["YB"],
            SB_COLUMNS.xc9_net: [0] * 7 + [0.8],
            SB_COLUMNS.xc9_gross: [0] * 7 + [0.96],
            SBC_COLUMNS.file_path.name: [str(file_info.path)] * rows,
            SBC_COLUMNS.file_created.name: [file_info.created] * rows,
            SBC_COLUMNS.file_size.name: [file_info.size] * rows,
        },
    )

    return as_shipping_billing_types(df)


def mock_multiple_charges_shipping_billing(file_info: FileInfo) -> DataFrame:
    df = mock_multiple_charges(file_info)

    # add_package_type
    df[SBC_COLUMNS.package_type.name] = [OUTBOUND] * 5 + [INBOUND, INBOUND, OUTBOUND]
    df = df.astype(SBC_COLUMNS.package_type.astype)

    # add_charge_columns
    return mock_charge_columns(
        df,
        remote_area_delivery=[NetGross()] * 7 + [NetGross(0.1, 0.12)],
        remote_area_pickup=[NetGross()] * 7 + [NetGross(0.2, 0.24)],
        fuel_surcharge=[NetGross()] * 7 + [NetGross(5, 6)],
        change_of_billing=[NetGross()] * 8,
        duty_tax_paid=[NetGross()] * 2 + [NetGross(5, 6)] + [NetGross()] * 4 + [NetGross(0.4, 0.48)],
        emergency_situation=[NetGross()] * 7 + [NetGross(0.5, 0.6)],
        export_declaration=[NetGross()] * 7 + [NetGross(0.6, 0.72)],
        carbon_reduced_fe=[NetGross()] * 7 + [NetGross(0.7, 0.84)],
        carbon_reduced_fd=[NetGross()] * 8,
        carbon_reduced_ft=[NetGross()] * 8,
        import_export_duties=[NetGross()] * 8,
        import_export_taxes=[NetGross()] * 8,
        oversize_piece=[NetGross()] * 7 + [NetGross(0.8, 0.96)],
        overweight_piece=[NetGross()] * 8,
        plastic_flyer=[NetGross()] * 8,
        shipment_insurance=[NetGross()] * 7 + [NetGross(0.3, 0.36)],
        demand_surcharge=[NetGross()] * 8,
    )


def mock_multiple_charges_order_items() -> DataFrame:
    """Duplicate pair of <shipment_number : order_item_id> = <**********, 1001>"""
    return mock_order_items(
        shipment_numbers=["**********"] * 2
        + ["**********"] * 2
        + ["**********", "**********", "**********", "**********", "**********", "**********"],
        order_items=[1001] * 2 + [1002, 1003, 1004, 1005, 1006, 1005, 1006, 2001],
    )


def mock_invalid_content(file_info: FileInfo) -> DataFrame:
    """
    INVALID_CONTENT.csv as DataFrame
    """
    rows = 8
    df = DataFrame.from_dict(
        {
            SB_COLUMNS.line_type: ["S"] * rows,
            SB_COLUMNS.billing_source: ["ES-TD"] * rows,
            SB_COLUMNS.billing_account: ["*********", "*********", "*********", "*********", "*********"]
            + ["*********"] * 3,
            SB_COLUMNS.billing_country_code: ["ES"] * rows,
            SB_COLUMNS.invoice_date: [as_date(********)] * 2
            + [as_date(********)] * 3
            + [as_date(********), as_date(********), as_date(********)],
            SB_COLUMNS.invoice_number: ["ES/*********/********"] * 2
            + ["ES/*********/********"] * 3
            + ["ES/*********/********", "ES/*********/********", "ES/*********/********"],
            SB_COLUMNS.shipment_number: [
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "**********",
                "",
            ],
            SB_COLUMNS.shipment_date: [as_date(********)] * 2
            + [as_date(********)] * 3
            + [as_date(********)] * 2
            + [pd.NaT],
            SB_COLUMNS.shipment_reference_1: ["21005", "21006", "", "invalid", "[abc, 1, efg]", "21005", "21005", ""],
            SB_COLUMNS.shipment_reference_2: [""] * 5 + ["Duplicate shipment", "Duplicate charges"] + [""],
            SB_COLUMNS.shipment_reference_3: ["outbound"] * 2 + ["inbound"] * 3 + ["outbound"] * 2 + [""],
            SB_COLUMNS.weight_kg: [5.00] * 5 + [3.00] * 2 + [pd.NA],
            SB_COLUMNS.weight_flag: ["B"] * 7 + [""],
            SB_COLUMNS.senders_name: [
                "Supplier A",
                "Supplier A",
                "Supplier A",
                "Supplier B",
                "Supplier C",
                "Supplier A",
                "Supplier B",
                "",
            ],
            SB_COLUMNS.senders_country: ["Jakov-Lind-Strasse 7"] * 2 + ["Madrid"] * 3 + ["London"] * 2 + [""],
            SB_COLUMNS.receivers_country: ["ES"] * 2 + ["DE"] * 3 + ["ES", "ES", ""],
            SB_COLUMNS.currency: ["EUR"] * 7 + [""],
            SB_COLUMNS.amount_net: [45] * 5 + [75] * 2 + [pd.NA],
            SB_COLUMNS.amount_gross: [54] * 5 + [90] * 2 + [pd.NA],
            SB_COLUMNS.xc1_code: ["FF"] * 7 + [""],
            SB_COLUMNS.xc1_net: [8] * 5 + [5, 0.1, pd.NA],
            SB_COLUMNS.xc1_gross: [9.6] * 5 + [6, 0.12, pd.NA],
            SB_COLUMNS.xc2_code: [0] * 7 + [""],
            SB_COLUMNS.xc2_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc2_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc3_code: [0] * 7 + [""],
            SB_COLUMNS.xc3_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc3_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc4_code: [0] * 7 + [""],
            SB_COLUMNS.xc4_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc4_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc5_code: [0] * 7 + [""],
            SB_COLUMNS.xc5_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc5_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc6_code: [0] * 7 + [""],
            SB_COLUMNS.xc6_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc6_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc7_code: [0] * 7 + [""],
            SB_COLUMNS.xc7_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc7_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc8_code: [0] * 7 + [""],
            SB_COLUMNS.xc8_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc8_gross: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc9_code: [0] * 7 + [""],
            SB_COLUMNS.xc9_net: [0] * 7 + [pd.NA],
            SB_COLUMNS.xc9_gross: [0] * 7 + [pd.NA],
            SBC_COLUMNS.file_path.name: [str(file_info.path)] * rows,
            SBC_COLUMNS.file_created.name: [file_info.created] * rows,
            SBC_COLUMNS.file_size.name: [file_info.size] * rows,
        },
    )

    return as_shipping_billing_types(df)


def mock_xc_columns(
    xc1: XCValue = XCValue(),
    xc2: XCValue = XCValue(),
    xc3: XCValue = XCValue(),
    xc4: XCValue = XCValue(),
    xc5: XCValue = XCValue(),
    xc6: XCValue = XCValue(),
    xc7: XCValue = XCValue(),
    xc8: XCValue = XCValue(),
    xc9: XCValue = XCValue(),
) -> DataFrame:
    df = DataFrame.from_dict(
        {
            SB_COLUMNS.xc1_code: [xc1.code],
            SB_COLUMNS.xc1_net: [xc1.net],
            SB_COLUMNS.xc1_gross: [xc1.gross],
            SB_COLUMNS.xc2_code: [xc2.code],
            SB_COLUMNS.xc2_net: [xc2.net],
            SB_COLUMNS.xc2_gross: [xc2.gross],
            SB_COLUMNS.xc3_code: [xc3.code],
            SB_COLUMNS.xc3_net: [xc3.net],
            SB_COLUMNS.xc3_gross: [xc3.gross],
            SB_COLUMNS.xc4_code: [xc4.code],
            SB_COLUMNS.xc4_net: [xc4.net],
            SB_COLUMNS.xc4_gross: [xc4.gross],
            SB_COLUMNS.xc5_code: [xc5.code],
            SB_COLUMNS.xc5_net: [xc5.net],
            SB_COLUMNS.xc5_gross: [xc5.gross],
            SB_COLUMNS.xc6_code: [xc6.code],
            SB_COLUMNS.xc6_net: [xc6.net],
            SB_COLUMNS.xc6_gross: [xc6.gross],
            SB_COLUMNS.xc7_code: [xc7.code],
            SB_COLUMNS.xc7_net: [xc7.net],
            SB_COLUMNS.xc7_gross: [xc7.gross],
            SB_COLUMNS.xc8_code: [xc8.code],
            SB_COLUMNS.xc8_net: [xc8.net],
            SB_COLUMNS.xc8_gross: [xc8.gross],
            SB_COLUMNS.xc9_code: [xc9.code],
            SB_COLUMNS.xc9_net: [xc9.net],
            SB_COLUMNS.xc9_gross: [xc9.gross],
        }
    )

    dtypes: dict[str, ExtensionDtype] = {col: pd.StringDtype() for col in ShippingBillingSchema.XC_CODES}
    dtypes.update({col: pd.Float32Dtype() for col in ShippingBillingSchema.XC_FLOATS})

    return df.astype(dtypes)


def mock_charge_columns(
    df: DataFrame,
    remote_area_delivery: NetGross | list[NetGross] = NetGross(),
    remote_area_pickup: NetGross | list[NetGross] = NetGross(),
    fuel_surcharge: NetGross | list[NetGross] = NetGross(),
    change_of_billing: NetGross | list[NetGross] = NetGross(),
    duty_tax_paid: NetGross | list[NetGross] = NetGross(),
    emergency_situation: NetGross | list[NetGross] = NetGross(),
    export_declaration: NetGross | list[NetGross] = NetGross(),
    carbon_reduced_fe: NetGross | list[NetGross] = NetGross(),
    carbon_reduced_fd: NetGross | list[NetGross] = NetGross(),
    carbon_reduced_ft: NetGross | list[NetGross] = NetGross(),
    import_export_duties: NetGross | list[NetGross] = NetGross(),
    import_export_taxes: NetGross | list[NetGross] = NetGross(),
    oversize_piece: NetGross | list[NetGross] = NetGross(),
    overweight_piece: NetGross | list[NetGross] = NetGross(),
    plastic_flyer: NetGross | list[NetGross] = NetGross(),
    shipment_insurance: NetGross | list[NetGross] = NetGross(),
    demand_surcharge: NetGross | list[NetGross] = NetGross(),
) -> DataFrame:
    df[SBC_COLUMNS.remote_area_delivery.net_column] = (
        remote_area_delivery.net
        if isinstance(remote_area_delivery, NetGross)
        else [v.net for v in remote_area_delivery]
    )
    df[SBC_COLUMNS.remote_area_delivery.gross_column] = (
        remote_area_delivery.gross
        if isinstance(remote_area_delivery, NetGross)
        else [v.gross for v in remote_area_delivery]
    )

    df[SBC_COLUMNS.remote_area_pickup.net_column] = (
        remote_area_pickup.net if isinstance(remote_area_pickup, NetGross) else [v.net for v in remote_area_pickup]
    )
    df[SBC_COLUMNS.remote_area_pickup.gross_column] = (
        remote_area_pickup.gross if isinstance(remote_area_pickup, NetGross) else [v.gross for v in remote_area_pickup]
    )

    df[SBC_COLUMNS.fuel_surcharge.net_column] = (
        fuel_surcharge.net if isinstance(fuel_surcharge, NetGross) else [v.net for v in fuel_surcharge]
    )
    df[SBC_COLUMNS.fuel_surcharge.gross_column] = (
        fuel_surcharge.gross if isinstance(fuel_surcharge, NetGross) else [v.gross for v in fuel_surcharge]
    )

    df[SBC_COLUMNS.change_of_billing.net_column] = (
        change_of_billing.net if isinstance(change_of_billing, NetGross) else [v.net for v in change_of_billing]
    )
    df[SBC_COLUMNS.change_of_billing.gross_column] = (
        change_of_billing.gross if isinstance(change_of_billing, NetGross) else [v.gross for v in change_of_billing]
    )

    df[SBC_COLUMNS.duty_tax_paid.net_column] = (
        duty_tax_paid.net if isinstance(duty_tax_paid, NetGross) else [v.net for v in duty_tax_paid]
    )
    df[SBC_COLUMNS.duty_tax_paid.gross_column] = (
        duty_tax_paid.gross if isinstance(duty_tax_paid, NetGross) else [v.gross for v in duty_tax_paid]
    )

    df[SBC_COLUMNS.emergency_situation.net_column] = (
        emergency_situation.net if isinstance(emergency_situation, NetGross) else [v.net for v in emergency_situation]
    )
    df[SBC_COLUMNS.emergency_situation.gross_column] = (
        emergency_situation.gross
        if isinstance(emergency_situation, NetGross)
        else [v.gross for v in emergency_situation]
    )

    df[SBC_COLUMNS.export_declaration.net_column] = (
        export_declaration.net if isinstance(export_declaration, NetGross) else [v.net for v in export_declaration]
    )
    df[SBC_COLUMNS.export_declaration.gross_column] = (
        export_declaration.gross if isinstance(export_declaration, NetGross) else [v.gross for v in export_declaration]
    )

    df[SBC_COLUMNS.carbon_reduced_fe.net_column] = (
        carbon_reduced_fe.net if isinstance(carbon_reduced_fe, NetGross) else [v.net for v in carbon_reduced_fe]
    )
    df[SBC_COLUMNS.carbon_reduced_fe.gross_column] = (
        carbon_reduced_fe.gross if isinstance(carbon_reduced_fe, NetGross) else [v.gross for v in carbon_reduced_fe]
    )

    df[SBC_COLUMNS.carbon_reduced_fd.net_column] = (
        carbon_reduced_fd.net if isinstance(carbon_reduced_fd, NetGross) else [v.net for v in carbon_reduced_fd]
    )
    df[SBC_COLUMNS.carbon_reduced_fd.gross_column] = (
        carbon_reduced_fd.gross if isinstance(carbon_reduced_fd, NetGross) else [v.gross for v in carbon_reduced_fd]
    )

    df[SBC_COLUMNS.carbon_reduced_ft.net_column] = (
        carbon_reduced_ft.net if isinstance(carbon_reduced_ft, NetGross) else [v.net for v in carbon_reduced_ft]
    )
    df[SBC_COLUMNS.carbon_reduced_ft.gross_column] = (
        carbon_reduced_ft.gross if isinstance(carbon_reduced_ft, NetGross) else [v.gross for v in carbon_reduced_ft]
    )

    df[SBC_COLUMNS.import_export_duties.net_column] = (
        import_export_duties.net
        if isinstance(import_export_duties, NetGross)
        else [v.net for v in import_export_duties]
    )
    df[SBC_COLUMNS.import_export_duties.gross_column] = (
        import_export_duties.gross
        if isinstance(import_export_duties, NetGross)
        else [v.gross for v in import_export_duties]
    )

    df[SBC_COLUMNS.import_export_taxes.net_column] = (
        import_export_taxes.net if isinstance(import_export_taxes, NetGross) else [v.net for v in import_export_taxes]
    )
    df[SBC_COLUMNS.import_export_taxes.gross_column] = (
        import_export_taxes.gross
        if isinstance(import_export_taxes, NetGross)
        else [v.gross for v in import_export_taxes]
    )

    df[SBC_COLUMNS.oversize_piece.net_column] = (
        oversize_piece.net if isinstance(oversize_piece, NetGross) else [v.net for v in oversize_piece]
    )
    df[SBC_COLUMNS.oversize_piece.gross_column] = (
        oversize_piece.gross if isinstance(oversize_piece, NetGross) else [v.gross for v in oversize_piece]
    )

    df[SBC_COLUMNS.overweight_piece.net_column] = (
        overweight_piece.net if isinstance(overweight_piece, NetGross) else [v.net for v in overweight_piece]
    )
    df[SBC_COLUMNS.overweight_piece.gross_column] = (
        overweight_piece.gross if isinstance(overweight_piece, NetGross) else [v.gross for v in overweight_piece]
    )

    df[SBC_COLUMNS.plastic_flyer.net_column] = (
        plastic_flyer.net if isinstance(plastic_flyer, NetGross) else [v.net for v in plastic_flyer]
    )
    df[SBC_COLUMNS.plastic_flyer.gross_column] = (
        plastic_flyer.gross if isinstance(plastic_flyer, NetGross) else [v.gross for v in plastic_flyer]
    )

    df[SBC_COLUMNS.shipment_insurance.net_column] = (
        shipment_insurance.net if isinstance(shipment_insurance, NetGross) else [v.net for v in shipment_insurance]
    )
    df[SBC_COLUMNS.shipment_insurance.gross_column] = (
        shipment_insurance.gross if isinstance(shipment_insurance, NetGross) else [v.gross for v in shipment_insurance]
    )

    df[SBC_COLUMNS.demand_surcharge.net_column] = (
        demand_surcharge.net if isinstance(demand_surcharge, NetGross) else [v.net for v in demand_surcharge]
    )
    df[SBC_COLUMNS.demand_surcharge.gross_column] = (
        demand_surcharge.gross if isinstance(demand_surcharge, NetGross) else [v.gross for v in demand_surcharge]
    )

    return df.astype(SBC_COLUMNS.charge_columns_dtypes)


def mock_shipment_references(
    shipment_numbers: list[str | int | None],
    shipment_references: list[str | int | None],
) -> DataFrame:
    return DataFrame.from_dict(
        {
            SB_COLUMNS.shipment_number: shipment_numbers,
            SB_COLUMNS.shipment_reference_1: shipment_references,
            # add some extra column to be sure it would be filtered out by `extract_order_item_ids`
            SB_COLUMNS.line_type: [ShippingBillingValues.DETAIL_LINE_TYPE] * len(shipment_references),
        }
    ).astype(pd.StringDtype())


def mock_order_items(
    shipment_numbers: list[str],
    order_items: list[int],
) -> DataFrame:
    order_item_id = SBC_COLUMNS.order_item_id.name

    dtypes = {SB_COLUMNS.shipment_number: pd.StringDtype()}
    dtypes.update(SBC_COLUMNS.order_item_id.astype)

    return DataFrame.from_dict(
        {
            SB_COLUMNS.shipment_number: shipment_numbers,
            order_item_id: order_items,
        }
    ).astype(dtypes)
