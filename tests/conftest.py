import os
from datetime import UTC, datetime, timedelta
from typing import Iterator
from unittest.mock import Mock

import boto3
import pytest
from botocore.client import BaseClient
from moto import mock_aws
from unit.test_s3_client.utils import create_s3_bucket
from utils import TestFile, get_test_file

from common.bq_repository import BigQueryRepository
from common.cloud_storage_client import CloudStorageClient
from common.config import Config
from common.data_lake_repository import DataLakeRepository
from common.google_sheet_client import GoogleSheetsClient
from common.mattermost_client import MattermostClient
from common.pipeline_logging.pipeline_model import PipelineRun
from common.s3_client import S3Client
from common.time_service import TimeService

S3_TEST_BUCKET_NAME = "s3-test-bucket"
NOW = datetime(2024, 6, 3, 23, 59, 59, tzinfo=UTC)
TODAY = NOW.date()
YESTERDAY = TODAY - timedelta(days=1)


@pytest.fixture(scope="session")
def config() -> Config:
    return Config()


@pytest.fixture(scope="session")
def test_file() -> TestFile:
    return get_test_file()


@pytest.fixture(scope="session")
def test_pipeline_run() -> PipelineRun:
    return PipelineRun()


@pytest.fixture(scope="session")
def now() -> datetime:
    return NOW


@pytest.fixture(scope="session")
def time_service(now: datetime) -> TimeService:
    return TimeService(now)


@pytest.fixture(scope="session")
def cloud_storage_client_mock() -> Mock:
    return Mock(spec_set=CloudStorageClient)


@pytest.fixture(scope="function")
def data_lake_repository_mock() -> Mock:
    return Mock(spec_set=DataLakeRepository)


@pytest.fixture(scope="function")
def bigquery_repository_mock() -> Mock:
    return Mock(spec_set=BigQueryRepository)


@pytest.fixture(scope="function")
def mattermost_client_mock() -> Mock:
    return Mock(spec=MattermostClient)


@pytest.fixture(scope="session")
def aws_credentials() -> None:
    """Mocked AWS Credentials"""

    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"


@pytest.fixture(scope="session")
def s3_boto_client(aws_credentials: None) -> BaseClient:
    """S3 low level Boto client"""

    return boto3.client("s3")


@pytest.fixture(scope="function")
def s3_client_boto_mock(s3_boto_client: BaseClient) -> Iterator[S3Client]:
    """S3Client boto mock"""

    s3_mock = mock_aws()
    s3_mock.start()
    create_s3_bucket(S3_TEST_BUCKET_NAME, s3_boto_client)

    yield S3Client(S3_TEST_BUCKET_NAME)

    s3_mock.stop()


@pytest.fixture(scope="function")
def s3_client_mock() -> Mock:
    return Mock(spec_set=S3Client)


@pytest.fixture
def google_sheets_client_mock() -> Mock:
    """
    Google Sheets Client mock.
    """

    return Mock(spec=GoogleSheetsClient)
