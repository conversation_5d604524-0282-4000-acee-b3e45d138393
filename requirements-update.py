"""
Updates all requirements*.txt files in the project with the latest versions.
"""

import os
import re
import subprocess
import sys
from typing import Optional

from common.config import Config
from common.logger import setup_logger

REQUIREMENTS_FILES = ["requirements.txt", "requirements-dev.txt"]
SKIP_DIRS = [".terraform", ".temp", ".venv", "infra", "resources", "tests"]

config = Config()
logger = setup_logger(config)

# Cache for package versions to avoid repeated lookups
package_cache: dict[str, Optional[str]] = {}


def get_latest_version(package_name: str) -> Optional[str]:
    """
    Get the latest stable version of a package from PyPI.

    :param package_name: The name of the package.
    :returns: The latest stable version of the package, or None if not found.
    """
    # Check cache first
    if package_name in package_cache:
        return package_cache[package_name]

    try:
        # Remove any extras from the package name for the pip search
        base_package = package_name.split("[")[0]
        result = subprocess.run(
            [sys.executable, "-m", "pip", "index", "versions", base_package], capture_output=True, text=True, check=True
        )
        output = result.stdout

        # Extract the latest stable version
        match = re.search(r"Available versions: (.*)", output)
        if match:
            versions = match.group(1).split(", ")
            # Filter out pre-release versions (those with a, b, rc, dev, etc.)
            stable_versions = [v for v in versions if not re.search(r"[a-zA-Z]", v.split(".")[-1])]
            if stable_versions:
                package_cache[package_name] = stable_versions[0]
                return stable_versions[0]

        # If we couldn't find a version using the index command, try pip show
        result = subprocess.run([sys.executable, "-m", "pip", "show", base_package], capture_output=True, text=True)
        output = result.stdout

        # Extract the version from pip show output
        match = re.search(r"Version: ([\d.]+)", output)
        if match:
            version = match.group(1)
            package_cache[package_name] = version
            return version

        logger.warning(f"Could not determine latest version for {package_name}")
        package_cache[package_name] = None
        return None
    except subprocess.CalledProcessError as e:
        logger.error(f"Error getting version for {package_name}: {e}")
        package_cache[package_name] = None
        return None


def update_requirements_file(file_path: str) -> None:
    """
    Update a requirements.txt file with the latest versions.

    param file_path: The path to the requirements.txt file.
    """
    logger.info(f"Updating {file_path}...")

    with open(file_path, "r") as f:
        lines = f.readlines()

    updated_lines = []
    for line in lines:
        line = line.strip()
        if not line or line.startswith("#"):
            updated_lines.append(line)
            continue

        updated = False
        # Parse the package name and version
        match = re.match(r"([^=<>]+)(==|>=|<=|<|>)(.+)", line)
        if match:
            package_name = match.group(1).strip()
            operator = match.group(2)
            current_version = match.group(3).strip()

            # Only update exact version requirements (==)
            if operator == "==":
                latest_version = get_latest_version(package_name)
                if latest_version is None:
                    logger.info(f"  Error getting version for {package_name}: {current_version}")
                elif latest_version != current_version:
                    logger.info(f"  Updating {package_name}: {current_version} -> {latest_version}")
                    updated_lines.append(f"{package_name}=={latest_version}")
                    updated = True
                else:
                    logger.info(f"  Already up to date {package_name}: {current_version} == {latest_version}")

        if updated is False:
            # If the line wasn't updated, keep it as is
            updated_lines.append(line)

    # Write the updated file
    with open(file_path, "w") as f:
        f.write("\n".join(updated_lines) + "\n")

    logger.info(f"Updated {file_path}")


def find_requirements_files(root_dir: str = ".") -> list[str]:
    """
    Find all requirements.txt files in the project.

    :param root_dir: The root directory to start the search from.
    :returns: A list of paths to the requirements.txt files.
    """
    requirements_files = []
    for root, dirs, files in os.walk(root_dir):
        if any(skip_dir in root for skip_dir in SKIP_DIRS):
            continue

        for file in files:
            if file in REQUIREMENTS_FILES:
                requirements_files.append(os.path.join(root, file))

    return requirements_files


def main() -> None:
    """
    Find all requirements.txt files in the project and update each file.
    """

    requirements_files = find_requirements_files()
    logger.info(f"Found {len(requirements_files)} requirements.txt files")

    for file_path in requirements_files:
        update_requirements_file(file_path)


if __name__ == "__main__":
    main()
